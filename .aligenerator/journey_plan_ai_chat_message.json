// 右键菜单，可以一键生成哦。
// 配置格式参见 https://yuque.antfin-inc.com/xd6pvi/dr962p 
{
	"anno":false,
	"antZdal":false,
	"appList":[],
	"appName":"",
	"blobEnable":false,
	"businessPackageName":"",
	"businessTargetProject":"../tripcrm/tripcrm-service/src/main/java",
	"catlog":"",
	"columnName":"id",
	"connectionURL":"",
	"createBaseType":false,
	"disableNamespace":false,
	"domainObjectName":"JourneyPlanAiChatMessage",
	"driverClass":"",
	"enableDeleteMethod":true,
	"fileEncode":"UTF-8",
	"generateBusiness":false,
	"generateKey":false,
	"groupKey":"",
	"ignoreColumn":false,
	"ignoreColumnValue":"id",
	"javaClientGeneratorTargetPackage":"com.taobao.trip.jourprod.mapper",
	"javaClientGeneratorTargetProject":"f-ai-ask-dal/src/main/java",
	"javaClientGeneratorType":"XMLMAPPER",
	"javaModelGeneratorTargetPackage":"com.taobao.trip.jourprod.domain",
	"javaModelGeneratorTargetProject":"f-ai-ask-dal/src/main/java",
	"lastBizPackage":"",
	"lastBizProjectPath":"../tripcrm/tripcrm-service/src/main/java",
	"lastTypeHadler":"",
	"lastTypeJava":"",
	"mapperName":"JourneyPlanAiChatMessageDAO",
	"mergePolicy":1,
	"modelType":"flat",
	"myDataBase":{
		"tables":[
			{
				"columns":[
					{
						"autoIncrement":true,
						"generatedColumn":false,
						"length":20,
						"name":"id",
						"scale":0,
						"comment":"主键",
						"type":"bigint(20)",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":0,
						"name":"gmt_create",
						"scale":0,
						"comment":"创建时间",
						"type":"datetime",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":0,
						"name":"gmt_modified",
						"scale":0,
						"comment":"修改时间",
						"type":"datetime",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":256,
						"name":"message_id",
						"scale":0,
						"comment":"消息id",
						"type":"varchar(256)",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":128,
						"name":"user_id",
						"scale":0,
						"comment":"userId",
						"type":"varchar(128)",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":128,
						"name":"utdid",
						"scale":0,
						"comment":"设备唯一识别id",
						"type":"varchar(128)",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":128,
						"name":"ttid",
						"scale":0,
						"comment":"渠道id",
						"type":"varchar(128)",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":128,
						"name":"session_id",
						"scale":0,
						"comment":"会话id",
						"type":"varchar(128)",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":64,
						"name":"role",
						"scale":0,
						"comment":"消息发送角色",
						"type":"varchar(64)",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":0,
						"name":"content",
						"scale":0,
						"comment":"消息内容",
						"type":"mediumtext",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":64,
						"name":"wrate",
						"scale":0,
						"comment":"用户评价",
						"type":"varchar(64)",
						"enableNull":true
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":0,
						"name":"extra",
						"scale":0,
						"comment":"扩展字段",
						"type":"text",
						"enableNull":true
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":0,
						"name":"timestamp",
						"scale":0,
						"comment":"对话时间",
						"type":"datetime",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":256,
						"name":"original_message_id",
						"scale":0,
						"comment":"关联的原始messageId",
						"type":"varchar(256)",
						"enableNull":true
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":64,
						"name":"trace_id",
						"scale":0,
						"comment":"traceId",
						"type":"varchar(64)",
						"enableNull":true
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":2,
						"name":"delete_flag",
						"scale":0,
						"comment":"删除标志位 0-未删除 1-已删除",
						"type":"varchar(2)",
						"enableNull":true
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":32,
						"name":"source",
						"scale":0,
						"comment":"访问来源：journey_ask行程问一问，search_ask首页搜索问一问",
						"type":"varchar(32)",
						"enableNull":true
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":1024,
						"name":"user_chat",
						"scale":0,
						"comment":"用户查询chat",
						"type":"varchar(1024)",
						"enableNull":true
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":32,
						"name":"env",
						"scale":0,
						"comment":"环境标识",
						"type":"varchar(32)",
						"enableNull":true
					}
				],
				"name":"journey_plan_ai_chat_message",
				"comment":"ai问一问消息列表",
				"primaryKey":"id"
			}
		],
		"name":""
	},
	"password":"",
	"pattern":"_[0-9]+$",
	"replaceIs":false,
	"saveParameter":true,
	"saveParameterPath":".aligenerator",
	"security":false,
	"serializable":false,
	"simpleMethod":true,
	"split":false,
	"sqlMapGeneratorTargetPackage":"mybatis",
	"sqlMapGeneratorTargetProject":"f-ai-ask-dal/src/main/resources",
	"sqlStatement":"JDBC",
	"tableName":"journey_plan_ai_chat_message",
	"tableSchema":"",
	"targetRuntime":"MyBatis3",
	"tddl":true,
	"tddlMap":{},
	"typeHandlerMap":{},
	"typeMap":{},
	"useBaseDao":false,
	"useDBTime":false,
	"useDao":true,
	"useLombok":true,
	"useSqlCreate":true,
	"userId":"",
	"whenPresent":true,
	"generatedBy":"Aligenerator",
	"schemaVersion":"1.0"
}