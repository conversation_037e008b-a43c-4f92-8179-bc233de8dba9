// 右键菜单，可以一键生成哦。
// 配置格式参见 https://yuque.antfin-inc.com/xd6pvi/dr962p 
{
	"anno":false,
	"antZdal":false,
	"appList":[],
	"appName":"",
	"blobEnable":false,
	"businessPackageName":"",
	"businessTargetProject":"../../poi-dataflow/poi-dataflow-biz/src/main/java",
	"catlog":"",
	"columnName":"id",
	"connectionURL":"",
	"createBaseType":false,
	"disableNamespace":false,
	"domainObjectName":"JourneyPlanAiChatSession",
	"driverClass":"",
	"enableDeleteMethod":true,
	"fileEncode":"UTF-8",
	"generateBusiness":false,
	"generateKey":false,
	"groupKey":"",
	"ignoreColumn":false,
	"ignoreColumnValue":"id",
	"javaClientGeneratorTargetPackage":"com.taobao.trip.jourprod.mapper",
	"javaClientGeneratorTargetProject":"f-ai-ask-dal/src/main/java",
	"javaClientGeneratorType":"XMLMAPPER",
	"javaModelGeneratorTargetPackage":"com.taobao.trip.jourprod.domain",
	"javaModelGeneratorTargetProject":"f-ai-ask-dal/src/main/java",
	"lastBizPackage":"",
	"lastBizProjectPath":"../../poi-dataflow/poi-dataflow-biz/src/main/java",
	"lastTypeHadler":"",
	"lastTypeJava":"",
	"mapperName":"JourneyPlanAiChatSessionDAO",
	"mergePolicy":1,
	"modelType":"flat",
	"myDataBase":{
		"tables":[
			{
				"columns":[
					{
						"autoIncrement":true,
						"generatedColumn":false,
						"length":20,
						"name":"id",
						"scale":0,
						"comment":"主键",
						"type":"bigint(20)",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":0,
						"name":"gmt_create",
						"scale":0,
						"comment":"创建时间",
						"type":"datetime",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":0,
						"name":"gmt_modified",
						"scale":0,
						"comment":"修改时间",
						"type":"datetime",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":128,
						"name":"user_id",
						"scale":0,
						"comment":"用户id",
						"type":"varchar(128)",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":128,
						"name":"session_id",
						"scale":0,
						"comment":"对话id",
						"type":"varchar(128)",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":1024,
						"name":"session_name",
						"scale":0,
						"comment":"对话名称",
						"type":"varchar(1024)",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":2,
						"name":"delete_flag",
						"scale":0,
						"comment":"删除标识位 0-未删除 1-删除",
						"type":"char(2)",
						"enableNull":true
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":0,
						"name":"ext_info",
						"scale":0,
						"comment":"扩展字段",
						"type":"text",
						"enableNull":true
					}
				],
				"name":"journey_plan_ai_chat_session",
				"comment":"问一问对话session列表",
				"primaryKey":"id"
			}
		],
		"name":"f_ai_ask"
	},
	"password":"",
	"pattern":"_[0-9]+$",
	"replaceIs":false,
	"saveParameter":true,
	"saveParameterPath":".aligenerator",
	"security":false,
	"serializable":false,
	"simpleMethod":true,
	"split":false,
	"sqlMapGeneratorTargetPackage":"ai",
	"sqlMapGeneratorTargetProject":"f-ai-ask-dal/src/main/resources/mybatis",
	"sqlStatement":"JDBC",
	"tableName":"journey_plan_ai_chat_session",
	"tableSchema":"",
	"targetRuntime":"MyBatis3",
	"tddl":true,
	"tddlMap":{},
	"typeHandlerMap":{},
	"typeMap":{},
	"useBaseDao":false,
	"useDBTime":false,
	"useDao":true,
	"useLombok":true,
	"useSqlCreate":true,
	"userId":"",
	"whenPresent":true,
	"generatedBy":"Aligenerator",
	"schemaVersion":"1.0"
}