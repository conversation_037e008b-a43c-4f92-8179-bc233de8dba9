---
description: 
globs: 
alwaysApply: false
---
# 项目结构

这是一个多模块的 Maven 项目。关键模块包括：

-   `[f-ai-ask-start](mdc:f-ai-ask-start)`: 应用启动模块。
-   `[f-ai-ask-service](mdc:f-ai-ask-service)`: 核心业务逻辑。
-   `[f-ai-ask-facade](mdc:f-ai-ask-facade)`: 对外 API 定义。
-   `[f-ai-ask-sal](mdc:f-ai-ask-sal)`: 服务接入层，用于集成外部系统。
-   `[f-ai-ask-dal](mdc:f-ai-ask-dal)`: 数据访问层，用于数据库交互。

主要的 Maven 配置位于 `[pom.xml](mdc:pom.xml)` 文件中。
更多详细信息请查阅 `[README.md](mdc:README.md)` 文件。
