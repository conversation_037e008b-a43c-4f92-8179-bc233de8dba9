# 基础镜像的Dockerfile ： https://yuque.antfin.com/aone/docker/rm2g1d
# 基于基础镜像
FROM hub.docker.alibaba-inc.com/aone-base/f-ai-ask_410:20250405212647

############# production #################
# 设置spring profile或者自定义的jvm参数。如果需要则打开下面的注释内容
ENV SERVICE_OPTS=-Dspring.profiles.active=production
ENV ENVIRONMENT=online
# 将构建出的主包复制到指定镜像目录中
COPY ${APP_NAME}.tgz /home/<USER>/${APP_NAME}/target/${APP_NAME}.tgz

COPY environment/common/bin/setenv.sh /home/<USER>/$BUILD_APP_NAME/bin
COPY environment/common/bin/appctl.sh /home/<USER>/$BUILD_APP_NAME/bin

# 设置文件操作权限
RUN chmod -R a+x /home/<USER>/${APP_NAME}/bin/ /home/<USER>/cai/bin/

RUN echo ${APP_NAME}host > /etc/ilogtail/user_defined_id