#!/usr/bin/env bash

. /etc/init.d/functions
check_mtop_status(){
    TITLE="mtop-uncenter plugin"
    URL="mtop.status"
    URL_PORT="12220"
    CHECK_TXT="success"

    CURL_BIN=/usr/bin/curl
    SPACE_STR="..................................................................................................."
    OUTIF=`/sbin/route -n | tail -1  | sed -e 's/.* \([^ ]*$\)/\1/'`
    LOCAL_IP=`/sbin/ifconfig | grep -A1 ${OUTIF} | grep inet | awk '{print $2}'`
    TEMP_IP=`echo "${LOCAL_IP}" | fgrep ':'`
    if [ -n "${TEMP_IP}" ]; then
        LOCAL_IP=`echo ${LOCAL_IP} | awk -F':' '{print $2}'`
    fi;
    REQ_URL="http://${LOCAL_IP}:${URL_PORT}/${URL}"
    echo "$CURL_BIN" "${REQ_URL}"
    if [ "$TITLE" == "" ]; then
       TITLE=$URL
    fi
    len=`echo $TITLE | wc -c`
    len=`expr 60 - $len`
    echo -n -e "$TITLE ...${SPACE_STR:1:$len}"
    TMP_FILE=`${CURL_BIN} -m 150 "${REQ_URL}" 2>&1`
    checkret=`echo "$TMP_FILE" | fgrep "$CHECK_TXT"`
    if [ "$checkret" == "" ]; then
        echo "[FAILED]"
        echo "please check the error message:"
        error_detail=`${CURL_BIN} -i -m 150 "${REQ_URL}" 2>&1`
        echo ${error_detail}
        status=0
        error=1
    else
        echo "[  OK  ]"
        status=1
        error=0
    fi;
    echo
    return ${error}
}

####################################
for (( i = 0; i < 3; i++)); do
    check_mtop_status
    if [ "$?" == "0" ]; then
        break
    else
       exit 888 # mtop-uncenter online failed
    fi
    sleep 3
done;