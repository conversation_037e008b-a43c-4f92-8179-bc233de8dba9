# 行程规划模板渲染示例

## 1. 调整后的JSON结构说明

### 1.1 核心变化
- **Activity结构简化**：每个activity只包含`type`、`id`、`title`三个基础属性
- **Cards数组**：所有其他属性（包括描述、卡片数据等）都放在`cards`数组中
- **灵活性提升**：每个activity可以包含多个card，支持更复杂的布局

### 1.2 Activity结构对比

**调整前：**
```json
{
  "type": "timeline",
  "id": 5,
  "title": "去程",
  "description": "建议乘坐飞机前往吉隆坡...",
  "card": {
    "id": 6,
    "itemType": "poi_link_card",
    "poiId": "xxx"
  }
}
```

**调整后：**
```json
{
  "type": "timeline",
  "id": 5,
  "title": "去程",
  "cards": [
    {
      "id": 6,
      "description": "建议乘坐飞机前往吉隆坡..."
    },
    {
      "id": 7,
      "itemType": "poi_link_card",
      "poiId": "xxx",
      "description": "推荐理由..."
    }
  ]
}
```

## 2. 模板渲染逻辑

### 2.1 卡片类型处理
模板会根据card的属性自动判断渲染方式：

1. **有itemType的card**：生成XML标签 + 描述文本
2. **无itemType的card**：只输出描述文本

### 2.2 模板渲染流程
```
Activity -> Timeline标签
  ├── Card1 (有itemType) -> XML标签 + 描述
  ├── Card2 (无itemType) -> 纯文本描述  
  └── Card3 (有itemType) -> XML标签 + 描述
```

## 3. 渲染结果示例

### 3.1 输入JSON
```json
{
  "type": "timeline",
  "id": 15,
  "title": "下午",
  "cards": [
    {
      "id": 16,
      "itemType": "poi_link_card",
      "poiId": "14945455",
      "poiName": "热浪岛海滩",
      "day": 2,
      "playTime": "15:00 - 18:00",
      "last": false,
      "description": "长滩白细如粉，海水能见度达20米，适合拍摄日落时分的光影变化。"
    },
    {
      "id": 17,
      "itemType": "router_plan_card",
      "depLocation": "103.01440453544521,5.784700555363592",
      "arrLocation": "103.006501,5.785744",
      "detailPath": false
    }
  ]
}
```

### 3.2 渲染输出
```
<f_timeline&15>
**下午**    
<f_card&16>≌itemType:poi_link_card;poiId:14945455;poiName:热浪岛海滩;day:2;playTime:15:00 - 18:00;last:false≌</f_card&16>
长滩白细如粉，海水能见度达20米，适合拍摄日落时分的光影变化。
<f_card&17>≌itemType:router_plan_card;depLocation:103.01440453544521,5.784700555363592;arrLocation:103.006501,5.785744;detailPath:false≌</f_card&17>
</f_timeline&15>
```

## 4. 优势分析

### 4.1 结构清晰
- Activity专注于时间线结构
- Card专注于具体内容和数据
- 职责分离，便于维护

### 4.2 扩展性强
- 支持一个activity包含多个card
- 支持纯文本card和XML card混合
- 便于添加新的card类型

### 4.3 兼容性好
- 生成的XML结构与原有系统完全兼容
- 现有的NodeProcessor无需修改
- 支持渐进式迁移

## 5. ID管理策略

### 5.1 ID分配原则
- 所有ID必须连续，从1开始
- Activity和Card都需要唯一ID
- 大模型负责生成连续的ID序列

### 5.2 ID验证机制
- 模板渲染前会验证ID连续性
- 发现ID不连续会抛出异常
- 确保生成的XML结构正确

## 6. 测试用例

### 6.1 基础功能测试
- ✅ 完整行程规划渲染
- ✅ 多卡片activity渲染
- ✅ 纯文本card渲染
- ✅ ID连续性验证

### 6.2 边界情况测试
- ✅ 空cards数组处理
- ✅ 无效ID序列处理
- ✅ 缺失字段处理

## 7. 性能优化

### 7.1 并行处理
- 每日行程可以并行渲染
- 不同模块（简介、概览、注意事项等）并行处理
- 显著提升整体渲染速度

### 7.2 模板缓存
- Freemarker模板自动缓存
- 避免重复解析模板文件
- 提升渲染性能

## 8. 使用建议

### 8.1 大模型输出要求
1. 确保ID连续性，从1开始
2. 合理分配activity和card
3. 为每个card提供适当的描述

### 8.2 模板维护
1. 定期检查模板语法
2. 及时更新模板以支持新的card类型
3. 保持模板结构的一致性

### 8.3 监控要点
1. 渲染性能监控
2. ID验证失败率
3. 模板渲染错误率
