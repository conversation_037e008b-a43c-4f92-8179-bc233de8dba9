package com.taobao.trip.jourprod.intent;

import com.taobao.trip.jourprod.intent.enums.IntentProviderTypeEnum;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 意图识别工厂类
 * <AUTHOR>
 */
@Slf4j
@Component
public class IntentRecognitionFactory {
    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(IntentRecognitionFactory.class);

    /** 提供者类型 -> 服务实例的映射 */
    private final Map<String, IntentRecognitionI> providerServiceMap = new ConcurrentHashMap<>();

    @Autowired
    public void setProviderServiceMap(List<IntentRecognitionI> intentRecognitionIS) {
        if (intentRecognitionIS != null) {
            for (IntentRecognitionI service : intentRecognitionIS) {
                String providerType = service.getProviderType();
                providerServiceMap.put(providerType, service);
                LOGGER.recordOutput(new LogModel("setProviderServiceMap").message(String.format("注册意图识别服务: %s", providerType)));
            }
        }
    }

    /**
     * 根据提供者类型获取意图识别服务
     * @param providerType 提供者类型
     * @return 意图识别服务
     */
    public IntentRecognitionI getServiceByProviderType(String providerType) {
        if (StringUtils.isEmpty(providerType)) {
            return getDefaultService();
        }
        
        IntentRecognitionI service = providerServiceMap.get(providerType);
        if (service == null) {
            LOGGER.recordOutput(new LogModel("getServiceByProviderType").message(String.format("未找到提供者类型为 %s 的意图识别服务，使用默认服务", providerType)));
            return getDefaultService();
        }
        
        return service;
    }

    /**
     * 获取默认的意图识别服务
     * @return 默认服务
     */
    private IntentRecognitionI getDefaultService() {
        IntentRecognitionI localRuleService = providerServiceMap.get(IntentProviderTypeEnum.FAI.getCode());
        if (localRuleService != null) {
            return localRuleService;
        }

        // 如果本地规则服务不存在，使用第一个可用的服务
        if (!providerServiceMap.isEmpty()) {
            return providerServiceMap.values().iterator().next();
        }

        throw new RuntimeException("没有可用的意图识别服务");
    }
}
