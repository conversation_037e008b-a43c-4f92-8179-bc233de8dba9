package com.taobao.trip.jourprod.intent;

import com.taobao.trip.jourprod.intent.domain.IntentRecognitionRequest;
import com.taobao.trip.jourprod.intent.domain.IntentRecognitionResponse;

/**
 * 意图识别服务
 * <AUTHOR>
 */
public interface IntentRecognitionI {

    /**
     * 识别用户意图
     * @param request 意图识别请求
     * @return 意图识别结果
     */
    IntentRecognitionResponse recognize(IntentRecognitionRequest request);

    /**
     * 获取服务提供者类型
     * @return 提供者类型
     */
    String getProviderType();
}
