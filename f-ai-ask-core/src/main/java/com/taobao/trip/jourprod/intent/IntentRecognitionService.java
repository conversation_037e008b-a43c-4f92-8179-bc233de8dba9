package com.taobao.trip.jourprod.intent;

import com.alibaba.fastjson.JSON;
import com.taobao.trip.jourprod.intent.domain.IntentRecognitionRequest;
import com.taobao.trip.jourprod.intent.domain.IntentRecognitionResponse;
import com.taobao.trip.jourprod.utils.JSONUtil;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Optional;

/**
 * 意图识别门面服务
 * 提供统一的意图识别入口
 * <AUTHOR>
 */
@Slf4j
@Service
public class IntentRecognitionService {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(IntentRecognitionService.class);


    @Autowired
    private IntentRecognitionFactory intentRecognitionFactory;

    /**
     * 识别用户意图
     * @param request 意图识别请求
     * @return 意图识别结果
     */
    public IntentRecognitionResponse recognize(IntentRecognitionRequest request) {
        long startTime =System.currentTimeMillis();
        IntentRecognitionResponse response = null;
        try {
            LOGGER.recordOutput(new LogModel("recognize").request(JSON.toJSONString(request)).message("使用意图识别服务"));
            // 参数校验
            if (request == null || StringUtils.isEmpty(request.getQuery())) {
                IntentRecognitionResponse errorResponse = new IntentRecognitionResponse();
                errorResponse.setSuccess(false);
                errorResponse.setErrorMessage("请求参数不能为空");
                return errorResponse;
            }

            // 根据应用ID获取对应的意图识别服务
            IntentRecognitionI service = intentRecognitionFactory.getServiceByProviderType(request.getProviderType().name());

            // 执行意图识别
            response = service.recognize(request);
            LOGGER.recordOutput(new LogModel("recognize")
                    .message("意图识别结果打印")
                    .response(JSONUtil.removeVerticalBar(JSON.toJSONString(response))));
        } catch (Exception e) {
            LOGGER.recordNormalException(new LogModel("recognize")
                    .request(JSON.toJSONString(request))
                    .code("core")
                    .message("意图识别服务异常")
                    .e(e));
            IntentRecognitionResponse errorResponse = new IntentRecognitionResponse();
            errorResponse.setSuccess(false);
            errorResponse.setErrorMessage("意图识别服务异常: " + ExceptionUtils.getStackTrace(e));
            return errorResponse;
        } finally {
            long endTime = System.currentTimeMillis();
            LOGGER.recordOutput(new LogModel("recognize")
                    .message("意图识别服务耗时")
                    .cost(endTime - startTime));
            Optional.ofNullable(response).ifPresent(r -> r.setCostTime(endTime - startTime));
        }
        return response;
    }
}
