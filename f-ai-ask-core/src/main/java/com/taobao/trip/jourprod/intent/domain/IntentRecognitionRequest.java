package com.taobao.trip.jourprod.intent.domain;

import com.taobao.trip.jourprod.intent.enums.IntentProviderTypeEnum;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 意图识别请求参数
 * <AUTHOR>
 */
@Data
public class IntentRecognitionRequest {

    /**
     * 用户输入
     */
    private String query;

    /**
     * 历史消息（上下文）
     */
    private List<String> historyMessageList;

    /**
     * 意图识别走哪个服务
     */
    private IntentProviderTypeEnum providerType;

    /**
     * 扩展参数
     * 一般存储非通用结构的数据
     */
    private Map<String, Object> extParams;
}
