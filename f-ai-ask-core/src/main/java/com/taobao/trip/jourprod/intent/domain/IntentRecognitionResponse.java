package com.taobao.trip.jourprod.intent.domain;

import lombok.Data;

/**
 * 意图识别响应参数
 * <AUTHOR>
 */
@Data
public class IntentRecognitionResponse {

    /**
     * 意图识别返回原始结果，一般为json结构
     */
    private String result;

    /**
     * 是否识别成功
     */
    private boolean success = true;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 耗时
     */
    private long costTime;
}
