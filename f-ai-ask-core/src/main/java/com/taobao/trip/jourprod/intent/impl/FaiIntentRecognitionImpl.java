package com.taobao.trip.jourprod.intent.impl;

import com.alibaba.fastjson.JSON;
import com.fliggy.fai.client.FaiResponse;
import com.fliggy.fai.client.workflow.cmd.WorkflowClientStartCmd;
import com.fliggy.fai.client.workflow.model.WorkflowRunLiteDTO;
import com.fliggy.fai.client.workflow.service.FaiWorkflowClientService;
import com.google.common.collect.Maps;
import com.taobao.trip.jourprod.intent.IntentRecognitionI;
import com.taobao.trip.jourprod.intent.domain.IntentRecognitionRequest;
import com.taobao.trip.jourprod.intent.domain.IntentRecognitionResponse;
import com.taobao.trip.jourprod.intent.enums.IntentProviderTypeEnum;
import com.taobao.trip.wireless.common.util.DateTimeUtils;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * FAI意图识别实现
 * <AUTHOR>
 */
@Slf4j
@Service
public class FaiIntentRecognitionImpl implements IntentRecognitionI {
    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(FaiIntentRecognitionImpl.class);
    @Resource
    FaiWorkflowClientService faiWorkflowClientService;

    @Override
    public IntentRecognitionResponse recognize(IntentRecognitionRequest request) {
        IntentRecognitionResponse response = new IntentRecognitionResponse();
        WorkflowClientStartCmd startCmd = new WorkflowClientStartCmd();
        startCmd.setAppId(632L);
        startCmd.setFlowId(2183L);
        Map<String, Object> variables = Maps.newHashMap();
        variables.put("query", request.getQuery());
        variables.put("history", request.getHistoryMessageList());
        variables.put("date_now", DateTimeUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        startCmd.setVariables(variables);
        FaiResponse<WorkflowRunLiteDTO> res = faiWorkflowClientService.start(startCmd);
        if (res == null || BooleanUtils.isNotTrue(res.getSuccess())) {
            String errorMsg = Objects.nonNull(res) ? res.getErrorMessage() : StringUtils.EMPTY;
            LOGGER.recordDangerException(new LogModel("recognize")
                    .message("FAI意图识别工作流返回异常, error=" + errorMsg)
                    .request(JSON.toJSONString(startCmd)));

            response.setErrorMessage(errorMsg);
            return response;
        }
        Map<String, Object> outputs = res.getData().getOutputs();
        String result = JSON.toJSONString(outputs);
        response.setResult(result);
        response.setSuccess(true);
        return response;
    }

    @Override
    public String getProviderType() {
        return IntentProviderTypeEnum.FAI.name();
    }
}
