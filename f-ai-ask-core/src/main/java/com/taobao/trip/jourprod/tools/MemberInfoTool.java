package com.taobao.trip.jourprod.tools;

import com.fliggy.ffa.touch.prod.customize.client.common.param.MemberDTO;
import com.taobao.trip.jourprod.tools.impl.MemberInfoToolImpl;

import java.util.List;

/**
 * 提供给workflow的会员查询的工具接口
 * <AUTHOR>
 */
public interface MemberInfoTool {

    /**
     * 根据uid查询用户飞猪会员等级，F1这种
     * @param uid   用户id
     * @return
     */
    MemberDTO queryMemberLevelByUid(Long uid);

    /**
     * 根据uid查询用户酒店的会员等级，高星集团这种，如“万豪、希尔顿、凯悦、万达、君澜、雅高“
     * @param uid   用户id
     * @return
     */
    List<MemberInfoToolImpl.HotelMemberVO> queryHotelMemberLevelByUid(Long uid);
}
