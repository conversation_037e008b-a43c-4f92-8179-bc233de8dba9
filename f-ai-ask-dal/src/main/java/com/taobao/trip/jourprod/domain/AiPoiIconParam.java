package com.taobao.trip.jourprod.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * This class was generated by Ali<PERSON>Generator
 * <AUTHOR>
 */
public class AiPoiIconParam {
    /**
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated
     */
    @Deprecated
    protected boolean distinct;

    /**
     *
     * @mbg.generated
     */
    protected boolean page;

    /**
     *
     * @mbg.generated
     */
    protected int pageIndex;

    /**
     *
     * @mbg.generated
     */
    protected int pageSize;

    /**
     *
     * @mbg.generated
     */
    protected int pageStart;

    /**
     *
     * @mbg.generated
     */
    protected String distinctSql;

    /**
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     *
     * @mbg.generated
     */
    public AiPoiIconParam() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * @param orderCondition
     * @param sortType
     * @return
     *
     * @mbg.generated
     */
    public AiPoiIconParam appendOrderByClause(OrderCondition orderCondition, SortType sortType) {
        if (null != orderByClause) {
            orderByClause = orderByClause + ", " + orderCondition.getColumnName() + " " + sortType.getValue();
        } else {
            orderByClause = orderCondition.getColumnName() + " " + sortType.getValue();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * @param distinct
     *
     * @mbg.generated
     */
    @Deprecated
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Deprecated
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * @param page
     * @return
     *
     * @mbg.generated
     */
    public AiPoiIconParam setPage(boolean page) {
        this.page = page;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public boolean isPage() {
        return page;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageIndex() {
        return pageIndex;
    }

    /**
     * @param pageSize
     * @return
     *
     * @mbg.generated
     */
    public AiPoiIconParam setPageSize(int pageSize) {
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * @param pageStart
     * @return
     *
     * @mbg.generated
     */
    public AiPoiIconParam setPageStart(int pageStart) {
        this.pageStart = pageStart < 1 ? 1 : pageStart;
        this.pageIndex = (this.pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageStart() {
        return pageStart;
    }

    /**
     * @param pageStart
     * @param pageSize
     *
     * @mbg.generated
     */
    public void setPagination(int pageStart, int pageSize) {
        this.page = true;
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
    }

    /**
     * @param condition
     * @return
     *
     * @mbg.generated
     */
    public AiPoiIconParam appendDistinct(OrderCondition condition) {
        if (null != distinctSql){
            distinctSql = distinctSql + ", " + condition.getColumnName();
        } else {
            distinctSql = condition.getColumnName();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * @param criteria
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     *
     * @mbg.generated
     */
    protected abstract static class AbstractGeneratedCriteria {
        protected List<Criterion> criteria;

        protected AbstractGeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andPoiIdIsNull() {
            addCriterion("poi_id is null");
            return (Criteria) this;
        }

        public Criteria andPoiIdIsNotNull() {
            addCriterion("poi_id is not null");
            return (Criteria) this;
        }

        public Criteria andPoiIdEqualTo(Long value) {
            addCriterion("poi_id =", value, "poiId");
            return (Criteria) this;
        }

        public Criteria andPoiIdNotEqualTo(Long value) {
            addCriterion("poi_id <>", value, "poiId");
            return (Criteria) this;
        }

        public Criteria andPoiIdGreaterThan(Long value) {
            addCriterion("poi_id >", value, "poiId");
            return (Criteria) this;
        }

        public Criteria andPoiIdGreaterThanOrEqualTo(Long value) {
            addCriterion("poi_id >=", value, "poiId");
            return (Criteria) this;
        }

        public Criteria andPoiIdLessThan(Long value) {
            addCriterion("poi_id <", value, "poiId");
            return (Criteria) this;
        }

        public Criteria andPoiIdLessThanOrEqualTo(Long value) {
            addCriterion("poi_id <=", value, "poiId");
            return (Criteria) this;
        }

        public Criteria andPoiIdIn(List<Long> values) {
            addCriterion("poi_id in", values, "poiId");
            return (Criteria) this;
        }

        public Criteria andPoiIdNotIn(List<Long> values) {
            addCriterion("poi_id not in", values, "poiId");
            return (Criteria) this;
        }

        public Criteria andPoiIdBetween(Long value1, Long value2) {
            addCriterion("poi_id between", value1, value2, "poiId");
            return (Criteria) this;
        }

        public Criteria andPoiIdNotBetween(Long value1, Long value2) {
            addCriterion("poi_id not between", value1, value2, "poiId");
            return (Criteria) this;
        }

        public Criteria andPoiNameIsNull() {
            addCriterion("poi_name is null");
            return (Criteria) this;
        }

        public Criteria andPoiNameIsNotNull() {
            addCriterion("poi_name is not null");
            return (Criteria) this;
        }

        public Criteria andPoiNameEqualTo(String value) {
            addCriterion("poi_name =", value, "poiName");
            return (Criteria) this;
        }

        public Criteria andPoiNameNotEqualTo(String value) {
            addCriterion("poi_name <>", value, "poiName");
            return (Criteria) this;
        }

        public Criteria andPoiNameGreaterThan(String value) {
            addCriterion("poi_name >", value, "poiName");
            return (Criteria) this;
        }

        public Criteria andPoiNameGreaterThanOrEqualTo(String value) {
            addCriterion("poi_name >=", value, "poiName");
            return (Criteria) this;
        }

        public Criteria andPoiNameLessThan(String value) {
            addCriterion("poi_name <", value, "poiName");
            return (Criteria) this;
        }

        public Criteria andPoiNameLessThanOrEqualTo(String value) {
            addCriterion("poi_name <=", value, "poiName");
            return (Criteria) this;
        }

        public Criteria andPoiNameLike(String value) {
            addCriterion("poi_name like", value, "poiName");
            return (Criteria) this;
        }

        public Criteria andPoiNameNotLike(String value) {
            addCriterion("poi_name not like", value, "poiName");
            return (Criteria) this;
        }

        public Criteria andPoiNameIn(List<String> values) {
            addCriterion("poi_name in", values, "poiName");
            return (Criteria) this;
        }

        public Criteria andPoiNameNotIn(List<String> values) {
            addCriterion("poi_name not in", values, "poiName");
            return (Criteria) this;
        }

        public Criteria andPoiNameBetween(String value1, String value2) {
            addCriterion("poi_name between", value1, value2, "poiName");
            return (Criteria) this;
        }

        public Criteria andPoiNameNotBetween(String value1, String value2) {
            addCriterion("poi_name not between", value1, value2, "poiName");
            return (Criteria) this;
        }

        public Criteria andIconIsNull() {
            addCriterion("icon is null");
            return (Criteria) this;
        }

        public Criteria andIconIsNotNull() {
            addCriterion("icon is not null");
            return (Criteria) this;
        }

        public Criteria andIconEqualTo(String value) {
            addCriterion("icon =", value, "icon");
            return (Criteria) this;
        }

        public Criteria andIconNotEqualTo(String value) {
            addCriterion("icon <>", value, "icon");
            return (Criteria) this;
        }

        public Criteria andIconGreaterThan(String value) {
            addCriterion("icon >", value, "icon");
            return (Criteria) this;
        }

        public Criteria andIconGreaterThanOrEqualTo(String value) {
            addCriterion("icon >=", value, "icon");
            return (Criteria) this;
        }

        public Criteria andIconLessThan(String value) {
            addCriterion("icon <", value, "icon");
            return (Criteria) this;
        }

        public Criteria andIconLessThanOrEqualTo(String value) {
            addCriterion("icon <=", value, "icon");
            return (Criteria) this;
        }

        public Criteria andIconLike(String value) {
            addCriterion("icon like", value, "icon");
            return (Criteria) this;
        }

        public Criteria andIconNotLike(String value) {
            addCriterion("icon not like", value, "icon");
            return (Criteria) this;
        }

        public Criteria andIconIn(List<String> values) {
            addCriterion("icon in", values, "icon");
            return (Criteria) this;
        }

        public Criteria andIconNotIn(List<String> values) {
            addCriterion("icon not in", values, "icon");
            return (Criteria) this;
        }

        public Criteria andIconBetween(String value1, String value2) {
            addCriterion("icon between", value1, value2, "icon");
            return (Criteria) this;
        }

        public Criteria andIconNotBetween(String value1, String value2) {
            addCriterion("icon not between", value1, value2, "icon");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Short value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Short value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Short value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Short value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Short value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Short value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Short> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Short> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Short value1, Short value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Short value1, Short value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andIdEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id =", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <>", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id not in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id not between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create =", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <>", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create not in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified =", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <>", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified not in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andPoiIdEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("poi_id =", value, "poiId");
            }
            return (Criteria) this;
        }

        public Criteria andPoiIdNotEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("poi_id <>", value, "poiId");
            }
            return (Criteria) this;
        }

        public Criteria andPoiIdGreaterThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("poi_id >", value, "poiId");
            }
            return (Criteria) this;
        }

        public Criteria andPoiIdGreaterThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("poi_id >=", value, "poiId");
            }
            return (Criteria) this;
        }

        public Criteria andPoiIdLessThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("poi_id <", value, "poiId");
            }
            return (Criteria) this;
        }

        public Criteria andPoiIdLessThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("poi_id <=", value, "poiId");
            }
            return (Criteria) this;
        }

        public Criteria andPoiIdInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("poi_id in", values, "poiId");
            }
            return (Criteria) this;
        }

        public Criteria andPoiIdNotInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("poi_id not in", values, "poiId");
            }
            return (Criteria) this;
        }

        public Criteria andPoiIdBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("poi_id between", value1, value2, "poiId");
            }
            return (Criteria) this;
        }

        public Criteria andPoiIdNotBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("poi_id not between", value1, value2, "poiId");
            }
            return (Criteria) this;
        }

        public Criteria andPoiNameEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("poi_name =", value, "poiName");
            }
            return (Criteria) this;
        }

        public Criteria andLlmEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("llm =", value, "llm");
            }
            return (Criteria) this;
        }

        public Criteria andPoiNameNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("poi_name <>", value, "poiName");
            }
            return (Criteria) this;
        }

        public Criteria andPoiNameGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("poi_name >", value, "poiName");
            }
            return (Criteria) this;
        }

        public Criteria andPoiNameGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("poi_name >=", value, "poiName");
            }
            return (Criteria) this;
        }

        public Criteria andPoiNameLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("poi_name <", value, "poiName");
            }
            return (Criteria) this;
        }

        public Criteria andPoiNameLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("poi_name <=", value, "poiName");
            }
            return (Criteria) this;
        }

        public Criteria andPoiNameLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("poi_name like", value, "poiName");
            }
            return (Criteria) this;
        }

        public Criteria andPoiNameNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("poi_name not like", value, "poiName");
            }
            return (Criteria) this;
        }

        public Criteria andPoiNameInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("poi_name in", values, "poiName");
            }
            return (Criteria) this;
        }

        public Criteria andPoiNameNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("poi_name not in", values, "poiName");
            }
            return (Criteria) this;
        }

        public Criteria andPoiNameBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("poi_name between", value1, value2, "poiName");
            }
            return (Criteria) this;
        }

        public Criteria andPoiNameNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("poi_name not between", value1, value2, "poiName");
            }
            return (Criteria) this;
        }

        public Criteria andIconEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("icon =", value, "icon");
            }
            return (Criteria) this;
        }

        public Criteria andIconNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("icon <>", value, "icon");
            }
            return (Criteria) this;
        }

        public Criteria andIconGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("icon >", value, "icon");
            }
            return (Criteria) this;
        }

        public Criteria andIconGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("icon >=", value, "icon");
            }
            return (Criteria) this;
        }

        public Criteria andIconLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("icon <", value, "icon");
            }
            return (Criteria) this;
        }

        public Criteria andIconLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("icon <=", value, "icon");
            }
            return (Criteria) this;
        }

        public Criteria andIconLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("icon like", value, "icon");
            }
            return (Criteria) this;
        }

        public Criteria andIconNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("icon not like", value, "icon");
            }
            return (Criteria) this;
        }

        public Criteria andIconInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("icon in", values, "icon");
            }
            return (Criteria) this;
        }

        public Criteria andIconNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("icon not in", values, "icon");
            }
            return (Criteria) this;
        }

        public Criteria andIconBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("icon between", value1, value2, "icon");
            }
            return (Criteria) this;
        }

        public Criteria andIconNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("icon not between", value1, value2, "icon");
            }
            return (Criteria) this;
        }

        public Criteria andStatusEqualToWhenPresent(Short value) {
            if(value != null) {
                addCriterion("status =", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualToWhenPresent(Short value) {
            if(value != null) {
                addCriterion("status <>", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanWhenPresent(Short value) {
            if(value != null) {
                addCriterion("status >", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualToWhenPresent(Short value) {
            if(value != null) {
                addCriterion("status >=", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusLessThanWhenPresent(Short value) {
            if(value != null) {
                addCriterion("status <", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualToWhenPresent(Short value) {
            if(value != null) {
                addCriterion("status <=", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusInWhenPresent(List<Short> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("status in", values, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusNotInWhenPresent(List<Short> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("status not in", values, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusBetweenWhenPresent(Short value1, Short value2) {
            if(value1 != null && value2 != null){
                addCriterion("status between", value1, value2, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusNotBetweenWhenPresent(Short value1, Short value2) {
            if(value1 != null && value2 != null){
                addCriterion("status not between", value1, value2, "status");
            }
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends AbstractGeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum OrderCondition {
        /**
         *主键
         */
        ID("id"),
        /**
         *创建时间
         */
        GMTCREATE("gmt_create"),
        /**
         *修改时间
         */
        GMTMODIFIED("gmt_modified"),
        /**
         *poi id
         */
        POIID("poi_id"),
        /**
         *poi名称
         */
        POINAME("poi_name"),
        /**
         *icon图标
         */
        ICON("icon"),
        /**
         *状态 1 不可用 2 可用
         */
        STATUS("status");

        private String columnName;

        OrderCondition(String columnName) {
            this.columnName = columnName;
        }

        public String getColumnName() {
            return columnName;
        }

        public static OrderCondition getEnumByName(String name) {
            OrderCondition[] orderConditions = OrderCondition.values();
            for (OrderCondition orderCondition : orderConditions) {
                if (orderCondition.name().equalsIgnoreCase(name)) {
                    return orderCondition;
                }
            }
            throw new RuntimeException("OrderCondition of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return columnName;
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum SortType {
        /**
         * 升序
         */
        ASC("asc"),
        /**
         * 降序
         */
        DESC("desc");

        private String value;

        SortType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static SortType getEnumByName(String name) {
            SortType[] sortTypes = SortType.values();
            for (SortType sortType : sortTypes) {
                if (sortType.name().equalsIgnoreCase(name)) {
                    return sortType;
                }
            }
            throw new RuntimeException("SortType of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return value;
        }
    }
}