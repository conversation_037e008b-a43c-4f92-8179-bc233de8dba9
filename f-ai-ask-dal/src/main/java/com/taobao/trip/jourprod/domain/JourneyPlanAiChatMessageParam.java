package com.taobao.trip.jourprod.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * This class was generated by Ali<PERSON>Generator
 * <AUTHOR>
 */
public class JourneyPlanAiChatMessageParam {
    /**
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated
     */
    @Deprecated
    protected boolean distinct;

    /**
     *
     * @mbg.generated
     */
    protected boolean page;

    /**
     *
     * @mbg.generated
     */
    protected int pageIndex;

    /**
     *
     * @mbg.generated
     */
    protected int pageSize;

    /**
     *
     * @mbg.generated
     */
    protected int pageStart;

    /**
     *
     * @mbg.generated
     */
    protected String distinctSql;

    /**
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     *
     * @mbg.generated
     */
    public JourneyPlanAiChatMessageParam() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * @param orderCondition
     * @param sortType
     * @return
     *
     * @mbg.generated
     */
    public JourneyPlanAiChatMessageParam appendOrderByClause(OrderCondition orderCondition, SortType sortType) {
        if (null != orderByClause) {
            orderByClause = orderByClause + ", " + orderCondition.getColumnName() + " " + sortType.getValue();
        } else {
            orderByClause = orderCondition.getColumnName() + " " + sortType.getValue();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * @param distinct
     *
     * @mbg.generated
     */
    @Deprecated
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Deprecated
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * @param page
     * @return
     *
     * @mbg.generated
     */
    public JourneyPlanAiChatMessageParam setPage(boolean page) {
        this.page = page;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public boolean isPage() {
        return page;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageIndex() {
        return pageIndex;
    }

    /**
     * @param pageSize
     * @return
     *
     * @mbg.generated
     */
    public JourneyPlanAiChatMessageParam setPageSize(int pageSize) {
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * @param pageStart
     * @return
     *
     * @mbg.generated
     */
    public JourneyPlanAiChatMessageParam setPageStart(int pageStart) {
        this.pageStart = pageStart < 1 ? 1 : pageStart;
        this.pageIndex = (this.pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageStart() {
        return pageStart;
    }

    /**
     * @param pageStart
     * @param pageSize
     *
     * @mbg.generated
     */
    public void setPagination(int pageStart, int pageSize) {
        this.page = true;
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
    }

    /**
     * @param condition
     * @return
     *
     * @mbg.generated
     */
    public JourneyPlanAiChatMessageParam appendDistinct(OrderCondition condition) {
        if (null != distinctSql){
            distinctSql = distinctSql + ", " + condition.getColumnName();
        } else {
            distinctSql = condition.getColumnName();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * @param criteria
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     *
     * @mbg.generated
     */
    protected abstract static class AbstractGeneratedCriteria {
        protected List<Criterion> criteria;

        protected AbstractGeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andMessageIdIsNull() {
            addCriterion("message_id is null");
            return (Criteria) this;
        }

        public Criteria andMessageIdIsNotNull() {
            addCriterion("message_id is not null");
            return (Criteria) this;
        }

        public Criteria andMessageIdEqualTo(String value) {
            addCriterion("message_id =", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotEqualTo(String value) {
            addCriterion("message_id <>", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdGreaterThan(String value) {
            addCriterion("message_id >", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdGreaterThanOrEqualTo(String value) {
            addCriterion("message_id >=", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdLessThan(String value) {
            addCriterion("message_id <", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdLessThanOrEqualTo(String value) {
            addCriterion("message_id <=", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdLike(String value) {
            addCriterion("message_id like", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotLike(String value) {
            addCriterion("message_id not like", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdIn(List<String> values) {
            addCriterion("message_id in", values, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotIn(List<String> values) {
            addCriterion("message_id not in", values, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdBetween(String value1, String value2) {
            addCriterion("message_id between", value1, value2, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotBetween(String value1, String value2) {
            addCriterion("message_id not between", value1, value2, "messageId");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(String value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(String value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(String value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(String value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(String value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLike(String value) {
            addCriterion("user_id like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotLike(String value) {
            addCriterion("user_id not like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<String> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<String> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(String value1, String value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(String value1, String value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUtdidIsNull() {
            addCriterion("utdid is null");
            return (Criteria) this;
        }

        public Criteria andUtdidIsNotNull() {
            addCriterion("utdid is not null");
            return (Criteria) this;
        }

        public Criteria andUtdidEqualTo(String value) {
            addCriterion("utdid =", value, "utdid");
            return (Criteria) this;
        }

        public Criteria andUtdidNotEqualTo(String value) {
            addCriterion("utdid <>", value, "utdid");
            return (Criteria) this;
        }

        public Criteria andUtdidGreaterThan(String value) {
            addCriterion("utdid >", value, "utdid");
            return (Criteria) this;
        }

        public Criteria andUtdidGreaterThanOrEqualTo(String value) {
            addCriterion("utdid >=", value, "utdid");
            return (Criteria) this;
        }

        public Criteria andUtdidLessThan(String value) {
            addCriterion("utdid <", value, "utdid");
            return (Criteria) this;
        }

        public Criteria andUtdidLessThanOrEqualTo(String value) {
            addCriterion("utdid <=", value, "utdid");
            return (Criteria) this;
        }

        public Criteria andUtdidLike(String value) {
            addCriterion("utdid like", value, "utdid");
            return (Criteria) this;
        }

        public Criteria andUtdidNotLike(String value) {
            addCriterion("utdid not like", value, "utdid");
            return (Criteria) this;
        }

        public Criteria andUtdidIn(List<String> values) {
            addCriterion("utdid in", values, "utdid");
            return (Criteria) this;
        }

        public Criteria andUtdidNotIn(List<String> values) {
            addCriterion("utdid not in", values, "utdid");
            return (Criteria) this;
        }

        public Criteria andUtdidBetween(String value1, String value2) {
            addCriterion("utdid between", value1, value2, "utdid");
            return (Criteria) this;
        }

        public Criteria andUtdidNotBetween(String value1, String value2) {
            addCriterion("utdid not between", value1, value2, "utdid");
            return (Criteria) this;
        }

        public Criteria andTtidIsNull() {
            addCriterion("ttid is null");
            return (Criteria) this;
        }

        public Criteria andTtidIsNotNull() {
            addCriterion("ttid is not null");
            return (Criteria) this;
        }

        public Criteria andTtidEqualTo(String value) {
            addCriterion("ttid =", value, "ttid");
            return (Criteria) this;
        }

        public Criteria andTtidNotEqualTo(String value) {
            addCriterion("ttid <>", value, "ttid");
            return (Criteria) this;
        }

        public Criteria andTtidGreaterThan(String value) {
            addCriterion("ttid >", value, "ttid");
            return (Criteria) this;
        }

        public Criteria andTtidGreaterThanOrEqualTo(String value) {
            addCriterion("ttid >=", value, "ttid");
            return (Criteria) this;
        }

        public Criteria andTtidLessThan(String value) {
            addCriterion("ttid <", value, "ttid");
            return (Criteria) this;
        }

        public Criteria andTtidLessThanOrEqualTo(String value) {
            addCriterion("ttid <=", value, "ttid");
            return (Criteria) this;
        }

        public Criteria andTtidLike(String value) {
            addCriterion("ttid like", value, "ttid");
            return (Criteria) this;
        }

        public Criteria andTtidNotLike(String value) {
            addCriterion("ttid not like", value, "ttid");
            return (Criteria) this;
        }

        public Criteria andTtidIn(List<String> values) {
            addCriterion("ttid in", values, "ttid");
            return (Criteria) this;
        }

        public Criteria andTtidNotIn(List<String> values) {
            addCriterion("ttid not in", values, "ttid");
            return (Criteria) this;
        }

        public Criteria andTtidBetween(String value1, String value2) {
            addCriterion("ttid between", value1, value2, "ttid");
            return (Criteria) this;
        }

        public Criteria andTtidNotBetween(String value1, String value2) {
            addCriterion("ttid not between", value1, value2, "ttid");
            return (Criteria) this;
        }

        public Criteria andSessionIdIsNull() {
            addCriterion("session_id is null");
            return (Criteria) this;
        }

        public Criteria andSessionIdIsNotNull() {
            addCriterion("session_id is not null");
            return (Criteria) this;
        }

        public Criteria andSessionIdEqualTo(String value) {
            addCriterion("session_id =", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotEqualTo(String value) {
            addCriterion("session_id <>", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThan(String value) {
            addCriterion("session_id >", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThanOrEqualTo(String value) {
            addCriterion("session_id >=", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThan(String value) {
            addCriterion("session_id <", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThanOrEqualTo(String value) {
            addCriterion("session_id <=", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLike(String value) {
            addCriterion("session_id like", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotLike(String value) {
            addCriterion("session_id not like", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdIn(List<String> values) {
            addCriterion("session_id in", values, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotIn(List<String> values) {
            addCriterion("session_id not in", values, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdBetween(String value1, String value2) {
            addCriterion("session_id between", value1, value2, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotBetween(String value1, String value2) {
            addCriterion("session_id not between", value1, value2, "sessionId");
            return (Criteria) this;
        }

        public Criteria andRoleIsNull() {
            addCriterion("role is null");
            return (Criteria) this;
        }

        public Criteria andRoleIsNotNull() {
            addCriterion("role is not null");
            return (Criteria) this;
        }

        public Criteria andRoleEqualTo(String value) {
            addCriterion("role =", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleNotEqualTo(String value) {
            addCriterion("role <>", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleGreaterThan(String value) {
            addCriterion("role >", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleGreaterThanOrEqualTo(String value) {
            addCriterion("role >=", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleLessThan(String value) {
            addCriterion("role <", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleLessThanOrEqualTo(String value) {
            addCriterion("role <=", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleLike(String value) {
            addCriterion("role like", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleNotLike(String value) {
            addCriterion("role not like", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleIn(List<String> values) {
            addCriterion("role in", values, "role");
            return (Criteria) this;
        }

        public Criteria andRoleNotIn(List<String> values) {
            addCriterion("role not in", values, "role");
            return (Criteria) this;
        }

        public Criteria andRoleBetween(String value1, String value2) {
            addCriterion("role between", value1, value2, "role");
            return (Criteria) this;
        }

        public Criteria andRoleNotBetween(String value1, String value2) {
            addCriterion("role not between", value1, value2, "role");
            return (Criteria) this;
        }

        public Criteria andWrateIsNull() {
            addCriterion("wrate is null");
            return (Criteria) this;
        }

        public Criteria andWrateIsNotNull() {
            addCriterion("wrate is not null");
            return (Criteria) this;
        }

        public Criteria andWrateEqualTo(String value) {
            addCriterion("wrate =", value, "wrate");
            return (Criteria) this;
        }

        public Criteria andWrateNotEqualTo(String value) {
            addCriterion("wrate <>", value, "wrate");
            return (Criteria) this;
        }

        public Criteria andWrateGreaterThan(String value) {
            addCriterion("wrate >", value, "wrate");
            return (Criteria) this;
        }

        public Criteria andWrateGreaterThanOrEqualTo(String value) {
            addCriterion("wrate >=", value, "wrate");
            return (Criteria) this;
        }

        public Criteria andWrateLessThan(String value) {
            addCriterion("wrate <", value, "wrate");
            return (Criteria) this;
        }

        public Criteria andWrateLessThanOrEqualTo(String value) {
            addCriterion("wrate <=", value, "wrate");
            return (Criteria) this;
        }

        public Criteria andWrateLike(String value) {
            addCriterion("wrate like", value, "wrate");
            return (Criteria) this;
        }

        public Criteria andWrateNotLike(String value) {
            addCriterion("wrate not like", value, "wrate");
            return (Criteria) this;
        }

        public Criteria andWrateIn(List<String> values) {
            addCriterion("wrate in", values, "wrate");
            return (Criteria) this;
        }

        public Criteria andWrateNotIn(List<String> values) {
            addCriterion("wrate not in", values, "wrate");
            return (Criteria) this;
        }

        public Criteria andWrateBetween(String value1, String value2) {
            addCriterion("wrate between", value1, value2, "wrate");
            return (Criteria) this;
        }

        public Criteria andWrateNotBetween(String value1, String value2) {
            addCriterion("wrate not between", value1, value2, "wrate");
            return (Criteria) this;
        }

        public Criteria andTimestampIsNull() {
            addCriterion("timestamp is null");
            return (Criteria) this;
        }

        public Criteria andTimestampIsNotNull() {
            addCriterion("timestamp is not null");
            return (Criteria) this;
        }

        public Criteria andTimestampEqualTo(Date value) {
            addCriterion("timestamp =", value, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampNotEqualTo(Date value) {
            addCriterion("timestamp <>", value, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampGreaterThan(Date value) {
            addCriterion("timestamp >", value, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampGreaterThanOrEqualTo(Date value) {
            addCriterion("timestamp >=", value, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampLessThan(Date value) {
            addCriterion("timestamp <", value, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampLessThanOrEqualTo(Date value) {
            addCriterion("timestamp <=", value, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampIn(List<Date> values) {
            addCriterion("timestamp in", values, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampNotIn(List<Date> values) {
            addCriterion("timestamp not in", values, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampBetween(Date value1, Date value2) {
            addCriterion("timestamp between", value1, value2, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampNotBetween(Date value1, Date value2) {
            addCriterion("timestamp not between", value1, value2, "timestamp");
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdIsNull() {
            addCriterion("original_message_id is null");
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdIsNotNull() {
            addCriterion("original_message_id is not null");
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdEqualTo(String value) {
            addCriterion("original_message_id =", value, "originalMessageId");
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdNotEqualTo(String value) {
            addCriterion("original_message_id <>", value, "originalMessageId");
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdGreaterThan(String value) {
            addCriterion("original_message_id >", value, "originalMessageId");
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdGreaterThanOrEqualTo(String value) {
            addCriterion("original_message_id >=", value, "originalMessageId");
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdLessThan(String value) {
            addCriterion("original_message_id <", value, "originalMessageId");
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdLessThanOrEqualTo(String value) {
            addCriterion("original_message_id <=", value, "originalMessageId");
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdLike(String value) {
            addCriterion("original_message_id like", value, "originalMessageId");
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdNotLike(String value) {
            addCriterion("original_message_id not like", value, "originalMessageId");
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdIn(List<String> values) {
            addCriterion("original_message_id in", values, "originalMessageId");
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdNotIn(List<String> values) {
            addCriterion("original_message_id not in", values, "originalMessageId");
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdBetween(String value1, String value2) {
            addCriterion("original_message_id between", value1, value2, "originalMessageId");
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdNotBetween(String value1, String value2) {
            addCriterion("original_message_id not between", value1, value2, "originalMessageId");
            return (Criteria) this;
        }

        public Criteria andTraceIdIsNull() {
            addCriterion("trace_id is null");
            return (Criteria) this;
        }

        public Criteria andTraceIdIsNotNull() {
            addCriterion("trace_id is not null");
            return (Criteria) this;
        }

        public Criteria andTraceIdEqualTo(String value) {
            addCriterion("trace_id =", value, "traceId");
            return (Criteria) this;
        }

        public Criteria andTraceIdNotEqualTo(String value) {
            addCriterion("trace_id <>", value, "traceId");
            return (Criteria) this;
        }

        public Criteria andTraceIdGreaterThan(String value) {
            addCriterion("trace_id >", value, "traceId");
            return (Criteria) this;
        }

        public Criteria andTraceIdGreaterThanOrEqualTo(String value) {
            addCriterion("trace_id >=", value, "traceId");
            return (Criteria) this;
        }

        public Criteria andTraceIdLessThan(String value) {
            addCriterion("trace_id <", value, "traceId");
            return (Criteria) this;
        }

        public Criteria andTraceIdLessThanOrEqualTo(String value) {
            addCriterion("trace_id <=", value, "traceId");
            return (Criteria) this;
        }

        public Criteria andTraceIdLike(String value) {
            addCriterion("trace_id like", value, "traceId");
            return (Criteria) this;
        }

        public Criteria andTraceIdNotLike(String value) {
            addCriterion("trace_id not like", value, "traceId");
            return (Criteria) this;
        }

        public Criteria andTraceIdIn(List<String> values) {
            addCriterion("trace_id in", values, "traceId");
            return (Criteria) this;
        }

        public Criteria andTraceIdNotIn(List<String> values) {
            addCriterion("trace_id not in", values, "traceId");
            return (Criteria) this;
        }

        public Criteria andTraceIdBetween(String value1, String value2) {
            addCriterion("trace_id between", value1, value2, "traceId");
            return (Criteria) this;
        }

        public Criteria andTraceIdNotBetween(String value1, String value2) {
            addCriterion("trace_id not between", value1, value2, "traceId");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNull() {
            addCriterion("delete_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNotNull() {
            addCriterion("delete_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagEqualTo(String value) {
            addCriterion("delete_flag =", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotEqualTo(String value) {
            addCriterion("delete_flag <>", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThan(String value) {
            addCriterion("delete_flag >", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThanOrEqualTo(String value) {
            addCriterion("delete_flag >=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThan(String value) {
            addCriterion("delete_flag <", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThanOrEqualTo(String value) {
            addCriterion("delete_flag <=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLike(String value) {
            addCriterion("delete_flag like", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotLike(String value) {
            addCriterion("delete_flag not like", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIn(List<String> values) {
            addCriterion("delete_flag in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotIn(List<String> values) {
            addCriterion("delete_flag not in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagBetween(String value1, String value2) {
            addCriterion("delete_flag between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotBetween(String value1, String value2) {
            addCriterion("delete_flag not between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("source is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("source is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(String value) {
            addCriterion("source =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(String value) {
            addCriterion("source <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(String value) {
            addCriterion("source >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(String value) {
            addCriterion("source >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(String value) {
            addCriterion("source <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(String value) {
            addCriterion("source <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLike(String value) {
            addCriterion("source like", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotLike(String value) {
            addCriterion("source not like", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<String> values) {
            addCriterion("source in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<String> values) {
            addCriterion("source not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(String value1, String value2) {
            addCriterion("source between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(String value1, String value2) {
            addCriterion("source not between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andUserChatIsNull() {
            addCriterion("user_chat is null");
            return (Criteria) this;
        }

        public Criteria andUserChatIsNotNull() {
            addCriterion("user_chat is not null");
            return (Criteria) this;
        }

        public Criteria andUserChatEqualTo(String value) {
            addCriterion("user_chat =", value, "userChat");
            return (Criteria) this;
        }

        public Criteria andUserChatNotEqualTo(String value) {
            addCriterion("user_chat <>", value, "userChat");
            return (Criteria) this;
        }

        public Criteria andUserChatGreaterThan(String value) {
            addCriterion("user_chat >", value, "userChat");
            return (Criteria) this;
        }

        public Criteria andUserChatGreaterThanOrEqualTo(String value) {
            addCriterion("user_chat >=", value, "userChat");
            return (Criteria) this;
        }

        public Criteria andUserChatLessThan(String value) {
            addCriterion("user_chat <", value, "userChat");
            return (Criteria) this;
        }

        public Criteria andUserChatLessThanOrEqualTo(String value) {
            addCriterion("user_chat <=", value, "userChat");
            return (Criteria) this;
        }

        public Criteria andUserChatLike(String value) {
            addCriterion("user_chat like", value, "userChat");
            return (Criteria) this;
        }

        public Criteria andUserChatNotLike(String value) {
            addCriterion("user_chat not like", value, "userChat");
            return (Criteria) this;
        }

        public Criteria andUserChatIn(List<String> values) {
            addCriterion("user_chat in", values, "userChat");
            return (Criteria) this;
        }

        public Criteria andUserChatNotIn(List<String> values) {
            addCriterion("user_chat not in", values, "userChat");
            return (Criteria) this;
        }

        public Criteria andUserChatBetween(String value1, String value2) {
            addCriterion("user_chat between", value1, value2, "userChat");
            return (Criteria) this;
        }

        public Criteria andUserChatNotBetween(String value1, String value2) {
            addCriterion("user_chat not between", value1, value2, "userChat");
            return (Criteria) this;
        }

        public Criteria andEnvIsNull() {
            addCriterion("env is null");
            return (Criteria) this;
        }

        public Criteria andEnvIsNotNull() {
            addCriterion("env is not null");
            return (Criteria) this;
        }

        public Criteria andEnvEqualTo(String value) {
            addCriterion("env =", value, "env");
            return (Criteria) this;
        }

        public Criteria andEnvNotEqualTo(String value) {
            addCriterion("env <>", value, "env");
            return (Criteria) this;
        }

        public Criteria andEnvGreaterThan(String value) {
            addCriterion("env >", value, "env");
            return (Criteria) this;
        }

        public Criteria andEnvGreaterThanOrEqualTo(String value) {
            addCriterion("env >=", value, "env");
            return (Criteria) this;
        }

        public Criteria andEnvLessThan(String value) {
            addCriterion("env <", value, "env");
            return (Criteria) this;
        }

        public Criteria andEnvLessThanOrEqualTo(String value) {
            addCriterion("env <=", value, "env");
            return (Criteria) this;
        }

        public Criteria andEnvLike(String value) {
            addCriterion("env like", value, "env");
            return (Criteria) this;
        }

        public Criteria andEnvNotLike(String value) {
            addCriterion("env not like", value, "env");
            return (Criteria) this;
        }

        public Criteria andEnvIn(List<String> values) {
            addCriterion("env in", values, "env");
            return (Criteria) this;
        }

        public Criteria andEnvNotIn(List<String> values) {
            addCriterion("env not in", values, "env");
            return (Criteria) this;
        }

        public Criteria andEnvBetween(String value1, String value2) {
            addCriterion("env between", value1, value2, "env");
            return (Criteria) this;
        }

        public Criteria andEnvNotBetween(String value1, String value2) {
            addCriterion("env not between", value1, value2, "env");
            return (Criteria) this;
        }

        public Criteria andContentIsNull() {
            addCriterion("content is null");
            return (Criteria) this;
        }

        public Criteria andContentIsNotNull() {
            addCriterion("content is not null");
            return (Criteria) this;
        }

        public Criteria andContentEqualTo(String value) {
            addCriterion("content =", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotEqualTo(String value) {
            addCriterion("content <>", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentGreaterThan(String value) {
            addCriterion("content >", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentGreaterThanOrEqualTo(String value) {
            addCriterion("content >=", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLessThan(String value) {
            addCriterion("content <", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLessThanOrEqualTo(String value) {
            addCriterion("content <=", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLike(String value) {
            addCriterion("content like", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotLike(String value) {
            addCriterion("content not like", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentIn(List<String> values) {
            addCriterion("content in", values, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotIn(List<String> values) {
            addCriterion("content not in", values, "content");
            return (Criteria) this;
        }

        public Criteria andContentBetween(String value1, String value2) {
            addCriterion("content between", value1, value2, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotBetween(String value1, String value2) {
            addCriterion("content not between", value1, value2, "content");
            return (Criteria) this;
        }

        public Criteria andExtraIsNull() {
            addCriterion("extra is null");
            return (Criteria) this;
        }

        public Criteria andExtraIsNotNull() {
            addCriterion("extra is not null");
            return (Criteria) this;
        }

        public Criteria andExtraEqualTo(String value) {
            addCriterion("extra =", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraNotEqualTo(String value) {
            addCriterion("extra <>", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraGreaterThan(String value) {
            addCriterion("extra >", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraGreaterThanOrEqualTo(String value) {
            addCriterion("extra >=", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraLessThan(String value) {
            addCriterion("extra <", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraLessThanOrEqualTo(String value) {
            addCriterion("extra <=", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraLike(String value) {
            addCriterion("extra like", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraNotLike(String value) {
            addCriterion("extra not like", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraIn(List<String> values) {
            addCriterion("extra in", values, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraNotIn(List<String> values) {
            addCriterion("extra not in", values, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraBetween(String value1, String value2) {
            addCriterion("extra between", value1, value2, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraNotBetween(String value1, String value2) {
            addCriterion("extra not between", value1, value2, "extra");
            return (Criteria) this;
        }

        public Criteria andIdEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id =", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <>", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id not in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id not between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create =", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <>", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create not in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified =", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <>", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified not in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andMessageIdEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("message_id =", value, "messageId");
            }
            return (Criteria) this;
        }

        public Criteria andMessageIdNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("message_id <>", value, "messageId");
            }
            return (Criteria) this;
        }

        public Criteria andMessageIdGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("message_id >", value, "messageId");
            }
            return (Criteria) this;
        }

        public Criteria andMessageIdGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("message_id >=", value, "messageId");
            }
            return (Criteria) this;
        }

        public Criteria andMessageIdLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("message_id <", value, "messageId");
            }
            return (Criteria) this;
        }

        public Criteria andMessageIdLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("message_id <=", value, "messageId");
            }
            return (Criteria) this;
        }

        public Criteria andMessageIdLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("message_id like", value, "messageId");
            }
            return (Criteria) this;
        }

        public Criteria andMessageIdNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("message_id not like", value, "messageId");
            }
            return (Criteria) this;
        }

        public Criteria andMessageIdInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("message_id in", values, "messageId");
            }
            return (Criteria) this;
        }

        public Criteria andMessageIdNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("message_id not in", values, "messageId");
            }
            return (Criteria) this;
        }

        public Criteria andMessageIdBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("message_id between", value1, value2, "messageId");
            }
            return (Criteria) this;
        }

        public Criteria andMessageIdNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("message_id not between", value1, value2, "messageId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id =", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id <>", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id >", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id >=", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id <", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id <=", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id like", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id not like", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("user_id in", values, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("user_id not in", values, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("user_id between", value1, value2, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("user_id not between", value1, value2, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUtdidEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("utdid =", value, "utdid");
            }
            return (Criteria) this;
        }

        public Criteria andUtdidNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("utdid <>", value, "utdid");
            }
            return (Criteria) this;
        }

        public Criteria andUtdidGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("utdid >", value, "utdid");
            }
            return (Criteria) this;
        }

        public Criteria andUtdidGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("utdid >=", value, "utdid");
            }
            return (Criteria) this;
        }

        public Criteria andUtdidLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("utdid <", value, "utdid");
            }
            return (Criteria) this;
        }

        public Criteria andUtdidLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("utdid <=", value, "utdid");
            }
            return (Criteria) this;
        }

        public Criteria andUtdidLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("utdid like", value, "utdid");
            }
            return (Criteria) this;
        }

        public Criteria andUtdidNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("utdid not like", value, "utdid");
            }
            return (Criteria) this;
        }

        public Criteria andUtdidInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("utdid in", values, "utdid");
            }
            return (Criteria) this;
        }

        public Criteria andUtdidNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("utdid not in", values, "utdid");
            }
            return (Criteria) this;
        }

        public Criteria andUtdidBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("utdid between", value1, value2, "utdid");
            }
            return (Criteria) this;
        }

        public Criteria andUtdidNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("utdid not between", value1, value2, "utdid");
            }
            return (Criteria) this;
        }

        public Criteria andTtidEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("ttid =", value, "ttid");
            }
            return (Criteria) this;
        }

        public Criteria andTtidNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("ttid <>", value, "ttid");
            }
            return (Criteria) this;
        }

        public Criteria andTtidGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("ttid >", value, "ttid");
            }
            return (Criteria) this;
        }

        public Criteria andTtidGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("ttid >=", value, "ttid");
            }
            return (Criteria) this;
        }

        public Criteria andTtidLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("ttid <", value, "ttid");
            }
            return (Criteria) this;
        }

        public Criteria andTtidLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("ttid <=", value, "ttid");
            }
            return (Criteria) this;
        }

        public Criteria andTtidLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("ttid like", value, "ttid");
            }
            return (Criteria) this;
        }

        public Criteria andTtidNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("ttid not like", value, "ttid");
            }
            return (Criteria) this;
        }

        public Criteria andTtidInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("ttid in", values, "ttid");
            }
            return (Criteria) this;
        }

        public Criteria andTtidNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("ttid not in", values, "ttid");
            }
            return (Criteria) this;
        }

        public Criteria andTtidBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("ttid between", value1, value2, "ttid");
            }
            return (Criteria) this;
        }

        public Criteria andTtidNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("ttid not between", value1, value2, "ttid");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_id =", value, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_id <>", value, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_id >", value, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_id >=", value, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_id <", value, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_id <=", value, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_id like", value, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_id not like", value, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("session_id in", values, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("session_id not in", values, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("session_id between", value1, value2, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("session_id not between", value1, value2, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andRoleEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("role =", value, "role");
            }
            return (Criteria) this;
        }

        public Criteria andRoleNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("role <>", value, "role");
            }
            return (Criteria) this;
        }

        public Criteria andRoleGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("role >", value, "role");
            }
            return (Criteria) this;
        }

        public Criteria andRoleGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("role >=", value, "role");
            }
            return (Criteria) this;
        }

        public Criteria andRoleLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("role <", value, "role");
            }
            return (Criteria) this;
        }

        public Criteria andRoleLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("role <=", value, "role");
            }
            return (Criteria) this;
        }

        public Criteria andRoleLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("role like", value, "role");
            }
            return (Criteria) this;
        }

        public Criteria andRoleNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("role not like", value, "role");
            }
            return (Criteria) this;
        }

        public Criteria andRoleInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("role in", values, "role");
            }
            return (Criteria) this;
        }

        public Criteria andRoleNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("role not in", values, "role");
            }
            return (Criteria) this;
        }

        public Criteria andRoleBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("role between", value1, value2, "role");
            }
            return (Criteria) this;
        }

        public Criteria andRoleNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("role not between", value1, value2, "role");
            }
            return (Criteria) this;
        }

        public Criteria andWrateEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("wrate =", value, "wrate");
            }
            return (Criteria) this;
        }

        public Criteria andWrateNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("wrate <>", value, "wrate");
            }
            return (Criteria) this;
        }

        public Criteria andWrateGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("wrate >", value, "wrate");
            }
            return (Criteria) this;
        }

        public Criteria andWrateGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("wrate >=", value, "wrate");
            }
            return (Criteria) this;
        }

        public Criteria andWrateLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("wrate <", value, "wrate");
            }
            return (Criteria) this;
        }

        public Criteria andWrateLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("wrate <=", value, "wrate");
            }
            return (Criteria) this;
        }

        public Criteria andWrateLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("wrate like", value, "wrate");
            }
            return (Criteria) this;
        }

        public Criteria andWrateNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("wrate not like", value, "wrate");
            }
            return (Criteria) this;
        }

        public Criteria andWrateInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("wrate in", values, "wrate");
            }
            return (Criteria) this;
        }

        public Criteria andWrateNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("wrate not in", values, "wrate");
            }
            return (Criteria) this;
        }

        public Criteria andWrateBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("wrate between", value1, value2, "wrate");
            }
            return (Criteria) this;
        }

        public Criteria andWrateNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("wrate not between", value1, value2, "wrate");
            }
            return (Criteria) this;
        }

        public Criteria andTimestampEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("timestamp =", value, "timestamp");
            }
            return (Criteria) this;
        }

        public Criteria andTimestampNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("timestamp <>", value, "timestamp");
            }
            return (Criteria) this;
        }

        public Criteria andTimestampGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("timestamp >", value, "timestamp");
            }
            return (Criteria) this;
        }

        public Criteria andTimestampGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("timestamp >=", value, "timestamp");
            }
            return (Criteria) this;
        }

        public Criteria andTimestampLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("timestamp <", value, "timestamp");
            }
            return (Criteria) this;
        }

        public Criteria andTimestampLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("timestamp <=", value, "timestamp");
            }
            return (Criteria) this;
        }

        public Criteria andTimestampInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("timestamp in", values, "timestamp");
            }
            return (Criteria) this;
        }

        public Criteria andTimestampNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("timestamp not in", values, "timestamp");
            }
            return (Criteria) this;
        }

        public Criteria andTimestampBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("timestamp between", value1, value2, "timestamp");
            }
            return (Criteria) this;
        }

        public Criteria andTimestampNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("timestamp not between", value1, value2, "timestamp");
            }
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("original_message_id =", value, "originalMessageId");
            }
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("original_message_id <>", value, "originalMessageId");
            }
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("original_message_id >", value, "originalMessageId");
            }
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("original_message_id >=", value, "originalMessageId");
            }
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("original_message_id <", value, "originalMessageId");
            }
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("original_message_id <=", value, "originalMessageId");
            }
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("original_message_id like", value, "originalMessageId");
            }
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("original_message_id not like", value, "originalMessageId");
            }
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("original_message_id in", values, "originalMessageId");
            }
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("original_message_id not in", values, "originalMessageId");
            }
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("original_message_id between", value1, value2, "originalMessageId");
            }
            return (Criteria) this;
        }

        public Criteria andOriginalMessageIdNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("original_message_id not between", value1, value2, "originalMessageId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("trace_id =", value, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("trace_id <>", value, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("trace_id >", value, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("trace_id >=", value, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("trace_id <", value, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("trace_id <=", value, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("trace_id like", value, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("trace_id not like", value, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("trace_id in", values, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("trace_id not in", values, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("trace_id between", value1, value2, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("trace_id not between", value1, value2, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("delete_flag =", value, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("delete_flag <>", value, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("delete_flag >", value, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("delete_flag >=", value, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("delete_flag <", value, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("delete_flag <=", value, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("delete_flag like", value, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("delete_flag not like", value, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("delete_flag in", values, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("delete_flag not in", values, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("delete_flag between", value1, value2, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("delete_flag not between", value1, value2, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andSourceEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("source =", value, "source");
            }
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("source <>", value, "source");
            }
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("source >", value, "source");
            }
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("source >=", value, "source");
            }
            return (Criteria) this;
        }

        public Criteria andSourceLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("source <", value, "source");
            }
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("source <=", value, "source");
            }
            return (Criteria) this;
        }

        public Criteria andSourceLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("source like", value, "source");
            }
            return (Criteria) this;
        }

        public Criteria andSourceNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("source not like", value, "source");
            }
            return (Criteria) this;
        }

        public Criteria andSourceInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("source in", values, "source");
            }
            return (Criteria) this;
        }

        public Criteria andSourceNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("source not in", values, "source");
            }
            return (Criteria) this;
        }

        public Criteria andSourceBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("source between", value1, value2, "source");
            }
            return (Criteria) this;
        }

        public Criteria andSourceNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("source not between", value1, value2, "source");
            }
            return (Criteria) this;
        }

        public Criteria andUserChatEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_chat =", value, "userChat");
            }
            return (Criteria) this;
        }

        public Criteria andUserChatNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_chat <>", value, "userChat");
            }
            return (Criteria) this;
        }

        public Criteria andUserChatGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_chat >", value, "userChat");
            }
            return (Criteria) this;
        }

        public Criteria andUserChatGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_chat >=", value, "userChat");
            }
            return (Criteria) this;
        }

        public Criteria andUserChatLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_chat <", value, "userChat");
            }
            return (Criteria) this;
        }

        public Criteria andUserChatLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_chat <=", value, "userChat");
            }
            return (Criteria) this;
        }

        public Criteria andUserChatLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_chat like", value, "userChat");
            }
            return (Criteria) this;
        }

        public Criteria andUserChatNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_chat not like", value, "userChat");
            }
            return (Criteria) this;
        }

        public Criteria andUserChatInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("user_chat in", values, "userChat");
            }
            return (Criteria) this;
        }

        public Criteria andUserChatNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("user_chat not in", values, "userChat");
            }
            return (Criteria) this;
        }

        public Criteria andUserChatBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("user_chat between", value1, value2, "userChat");
            }
            return (Criteria) this;
        }

        public Criteria andUserChatNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("user_chat not between", value1, value2, "userChat");
            }
            return (Criteria) this;
        }

        public Criteria andEnvEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("env =", value, "env");
            }
            return (Criteria) this;
        }

        public Criteria andEnvNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("env <>", value, "env");
            }
            return (Criteria) this;
        }

        public Criteria andEnvGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("env >", value, "env");
            }
            return (Criteria) this;
        }

        public Criteria andEnvGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("env >=", value, "env");
            }
            return (Criteria) this;
        }

        public Criteria andEnvLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("env <", value, "env");
            }
            return (Criteria) this;
        }

        public Criteria andEnvLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("env <=", value, "env");
            }
            return (Criteria) this;
        }

        public Criteria andEnvLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("env like", value, "env");
            }
            return (Criteria) this;
        }

        public Criteria andEnvNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("env not like", value, "env");
            }
            return (Criteria) this;
        }

        public Criteria andEnvInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("env in", values, "env");
            }
            return (Criteria) this;
        }

        public Criteria andEnvNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("env not in", values, "env");
            }
            return (Criteria) this;
        }

        public Criteria andEnvBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("env between", value1, value2, "env");
            }
            return (Criteria) this;
        }

        public Criteria andEnvNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("env not between", value1, value2, "env");
            }
            return (Criteria) this;
        }

        public Criteria andContentEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("content =", value, "content");
            }
            return (Criteria) this;
        }

        public Criteria andContentNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("content <>", value, "content");
            }
            return (Criteria) this;
        }

        public Criteria andContentGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("content >", value, "content");
            }
            return (Criteria) this;
        }

        public Criteria andContentGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("content >=", value, "content");
            }
            return (Criteria) this;
        }

        public Criteria andContentLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("content <", value, "content");
            }
            return (Criteria) this;
        }

        public Criteria andContentLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("content <=", value, "content");
            }
            return (Criteria) this;
        }

        public Criteria andContentLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("content like", value, "content");
            }
            return (Criteria) this;
        }

        public Criteria andContentNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("content not like", value, "content");
            }
            return (Criteria) this;
        }

        public Criteria andContentInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("content in", values, "content");
            }
            return (Criteria) this;
        }

        public Criteria andContentNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("content not in", values, "content");
            }
            return (Criteria) this;
        }

        public Criteria andContentBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("content between", value1, value2, "content");
            }
            return (Criteria) this;
        }

        public Criteria andContentNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("content not between", value1, value2, "content");
            }
            return (Criteria) this;
        }

        public Criteria andExtraEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("extra =", value, "extra");
            }
            return (Criteria) this;
        }

        public Criteria andExtraNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("extra <>", value, "extra");
            }
            return (Criteria) this;
        }

        public Criteria andExtraGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("extra >", value, "extra");
            }
            return (Criteria) this;
        }

        public Criteria andExtraGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("extra >=", value, "extra");
            }
            return (Criteria) this;
        }

        public Criteria andExtraLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("extra <", value, "extra");
            }
            return (Criteria) this;
        }

        public Criteria andExtraLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("extra <=", value, "extra");
            }
            return (Criteria) this;
        }

        public Criteria andExtraLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("extra like", value, "extra");
            }
            return (Criteria) this;
        }

        public Criteria andExtraNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("extra not like", value, "extra");
            }
            return (Criteria) this;
        }

        public Criteria andExtraInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("extra in", values, "extra");
            }
            return (Criteria) this;
        }

        public Criteria andExtraNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("extra not in", values, "extra");
            }
            return (Criteria) this;
        }

        public Criteria andExtraBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("extra between", value1, value2, "extra");
            }
            return (Criteria) this;
        }

        public Criteria andExtraNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("extra not between", value1, value2, "extra");
            }
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends AbstractGeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum OrderCondition {
        /**
         *主键
         */
        ID("id"),
        /**
         *创建时间
         */
        GMTCREATE("gmt_create"),
        /**
         *修改时间
         */
        GMTMODIFIED("gmt_modified"),
        /**
         *消息id
         */
        MESSAGEID("message_id"),
        /**
         *userId
         */
        USERID("user_id"),
        /**
         *设备唯一识别id
         */
        UTDID("utdid"),
        /**
         *渠道id
         */
        TTID("ttid"),
        /**
         *会话id
         */
        SESSIONID("session_id"),
        /**
         *消息发送角色
         */
        ROLE("role"),
        /**
         *用户评价
         */
        WRATE("wrate"),
        /**
         *对话时间
         */
        TIMESTAMP("timestamp"),
        /**
         *关联的原始messageId
         */
        ORIGINALMESSAGEID("original_message_id"),
        /**
         *traceId
         */
        TRACEID("trace_id"),
        /**
         *删除标志位 0-未删除 1-已删除
         */
        DELETEFLAG("delete_flag"),
        /**
         *访问来源：journey_ask行程问一问，search_ask首页搜索问一问
         */
        SOURCE("source"),
        /**
         *用户查询chat
         */
        USERCHAT("user_chat"),
        /**
         *环境标识
         */
        ENV("env"),
        /**
         *消息内容
         */
        CONTENT("content"),
        /**
         *扩展字段
         */
        EXTRA("extra");

        private String columnName;

        OrderCondition(String columnName) {
            this.columnName = columnName;
        }

        public String getColumnName() {
            return columnName;
        }

        public static OrderCondition getEnumByName(String name) {
            OrderCondition[] orderConditions = OrderCondition.values();
            for (OrderCondition orderCondition : orderConditions) {
                if (orderCondition.name().equalsIgnoreCase(name)) {
                    return orderCondition;
                }
            }
            throw new RuntimeException("OrderCondition of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return columnName;
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum SortType {
        /**
         * 升序
         */
        ASC("asc"),
        /**
         * 降序
         */
        DESC("desc");

        private String value;

        SortType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static SortType getEnumByName(String name) {
            SortType[] sortTypes = SortType.values();
            for (SortType sortType : sortTypes) {
                if (sortType.name().equalsIgnoreCase(name)) {
                    return sortType;
                }
            }
            throw new RuntimeException("SortType of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return value;
        }
    }
}