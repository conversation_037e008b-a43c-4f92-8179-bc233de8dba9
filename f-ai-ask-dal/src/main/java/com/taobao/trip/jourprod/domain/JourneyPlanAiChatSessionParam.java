package com.taobao.trip.jourprod.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class JourneyPlanAiChatSessionParam {
    /**
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated
     */
    @Deprecated
    protected boolean distinct;

    /**
     *
     * @mbg.generated
     */
    protected boolean page;

    /**
     *
     * @mbg.generated
     */
    protected int pageIndex;

    /**
     *
     * @mbg.generated
     */
    protected int pageSize;

    /**
     *
     * @mbg.generated
     */
    protected int pageStart;

    /**
     *
     * @mbg.generated
     */
    protected String distinctSql;

    /**
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     *
     * @mbg.generated
     */
    public JourneyPlanAiChatSessionParam() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * @param orderCondition
     * @param sortType
     * @return
     *
     * @mbg.generated
     */
    public JourneyPlanAiChatSessionParam appendOrderByClause(OrderCondition orderCondition, SortType sortType) {
        if (null != orderByClause) {
            orderByClause = orderByClause + ", " + orderCondition.getColumnName() + " " + sortType.getValue();
        } else {
            orderByClause = orderCondition.getColumnName() + " " + sortType.getValue();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * @param distinct
     *
     * @mbg.generated
     */
    @Deprecated
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Deprecated
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * @param page
     * @return
     *
     * @mbg.generated
     */
    public JourneyPlanAiChatSessionParam setPage(boolean page) {
        this.page = page;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public boolean isPage() {
        return page;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageIndex() {
        return pageIndex;
    }

    /**
     * @param pageSize
     * @return
     *
     * @mbg.generated
     */
    public JourneyPlanAiChatSessionParam setPageSize(int pageSize) {
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * @param pageStart
     * @return
     *
     * @mbg.generated
     */
    public JourneyPlanAiChatSessionParam setPageStart(int pageStart) {
        this.pageStart = pageStart < 1 ? 1 : pageStart;
        this.pageIndex = (this.pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageStart() {
        return pageStart;
    }

    /**
     * @param pageStart
     * @param pageSize
     *
     * @mbg.generated
     */
    public void setPagination(int pageStart, int pageSize) {
        this.page = true;
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
    }

    /**
     * @param condition
     * @return
     *
     * @mbg.generated
     */
    public JourneyPlanAiChatSessionParam appendDistinct(OrderCondition condition) {
        if (null != distinctSql){
            distinctSql = distinctSql + ", " + condition.getColumnName();
        } else {
            distinctSql = condition.getColumnName();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * @param criteria
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     *
     * @mbg.generated
     */
    protected abstract static class AbstractGeneratedCriteria {
        protected List<Criterion> criteria;

        protected AbstractGeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(String value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(String value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(String value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(String value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(String value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLike(String value) {
            addCriterion("user_id like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotLike(String value) {
            addCriterion("user_id not like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<String> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<String> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(String value1, String value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(String value1, String value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andSessionIdIsNull() {
            addCriterion("session_id is null");
            return (Criteria) this;
        }

        public Criteria andSessionIdIsNotNull() {
            addCriterion("session_id is not null");
            return (Criteria) this;
        }

        public Criteria andSessionIdEqualTo(String value) {
            addCriterion("session_id =", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotEqualTo(String value) {
            addCriterion("session_id <>", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThan(String value) {
            addCriterion("session_id >", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThanOrEqualTo(String value) {
            addCriterion("session_id >=", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThan(String value) {
            addCriterion("session_id <", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThanOrEqualTo(String value) {
            addCriterion("session_id <=", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLike(String value) {
            addCriterion("session_id like", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotLike(String value) {
            addCriterion("session_id not like", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdIn(List<String> values) {
            addCriterion("session_id in", values, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotIn(List<String> values) {
            addCriterion("session_id not in", values, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdBetween(String value1, String value2) {
            addCriterion("session_id between", value1, value2, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotBetween(String value1, String value2) {
            addCriterion("session_id not between", value1, value2, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionNameIsNull() {
            addCriterion("session_name is null");
            return (Criteria) this;
        }

        public Criteria andSessionNameIsNotNull() {
            addCriterion("session_name is not null");
            return (Criteria) this;
        }

        public Criteria andSessionNameEqualTo(String value) {
            addCriterion("session_name =", value, "sessionName");
            return (Criteria) this;
        }

        public Criteria andSessionNameNotEqualTo(String value) {
            addCriterion("session_name <>", value, "sessionName");
            return (Criteria) this;
        }

        public Criteria andSessionNameGreaterThan(String value) {
            addCriterion("session_name >", value, "sessionName");
            return (Criteria) this;
        }

        public Criteria andSessionNameGreaterThanOrEqualTo(String value) {
            addCriterion("session_name >=", value, "sessionName");
            return (Criteria) this;
        }

        public Criteria andSessionNameLessThan(String value) {
            addCriterion("session_name <", value, "sessionName");
            return (Criteria) this;
        }

        public Criteria andSessionNameLessThanOrEqualTo(String value) {
            addCriterion("session_name <=", value, "sessionName");
            return (Criteria) this;
        }

        public Criteria andSessionNameLike(String value) {
            addCriterion("session_name like", value, "sessionName");
            return (Criteria) this;
        }

        public Criteria andSessionNameNotLike(String value) {
            addCriterion("session_name not like", value, "sessionName");
            return (Criteria) this;
        }

        public Criteria andSessionNameIn(List<String> values) {
            addCriterion("session_name in", values, "sessionName");
            return (Criteria) this;
        }

        public Criteria andSessionNameNotIn(List<String> values) {
            addCriterion("session_name not in", values, "sessionName");
            return (Criteria) this;
        }

        public Criteria andSessionNameBetween(String value1, String value2) {
            addCriterion("session_name between", value1, value2, "sessionName");
            return (Criteria) this;
        }

        public Criteria andSessionNameNotBetween(String value1, String value2) {
            addCriterion("session_name not between", value1, value2, "sessionName");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNull() {
            addCriterion("delete_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNotNull() {
            addCriterion("delete_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagEqualTo(String value) {
            addCriterion("delete_flag =", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotEqualTo(String value) {
            addCriterion("delete_flag <>", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThan(String value) {
            addCriterion("delete_flag >", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThanOrEqualTo(String value) {
            addCriterion("delete_flag >=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThan(String value) {
            addCriterion("delete_flag <", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThanOrEqualTo(String value) {
            addCriterion("delete_flag <=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLike(String value) {
            addCriterion("delete_flag like", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotLike(String value) {
            addCriterion("delete_flag not like", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIn(List<String> values) {
            addCriterion("delete_flag in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotIn(List<String> values) {
            addCriterion("delete_flag not in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagBetween(String value1, String value2) {
            addCriterion("delete_flag between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotBetween(String value1, String value2) {
            addCriterion("delete_flag not between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNull() {
            addCriterion("ext_info is null");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNotNull() {
            addCriterion("ext_info is not null");
            return (Criteria) this;
        }

        public Criteria andExtInfoEqualTo(String value) {
            addCriterion("ext_info =", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotEqualTo(String value) {
            addCriterion("ext_info <>", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThan(String value) {
            addCriterion("ext_info >", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThanOrEqualTo(String value) {
            addCriterion("ext_info >=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThan(String value) {
            addCriterion("ext_info <", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThanOrEqualTo(String value) {
            addCriterion("ext_info <=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLike(String value) {
            addCriterion("ext_info like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotLike(String value) {
            addCriterion("ext_info not like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoIn(List<String> values) {
            addCriterion("ext_info in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotIn(List<String> values) {
            addCriterion("ext_info not in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoBetween(String value1, String value2) {
            addCriterion("ext_info between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotBetween(String value1, String value2) {
            addCriterion("ext_info not between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andIdEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id =", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <>", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id not in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id not between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create =", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <>", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create not in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified =", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <>", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified not in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id =", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id <>", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id >", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id >=", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id <", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id <=", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id like", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id not like", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("user_id in", values, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("user_id not in", values, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("user_id between", value1, value2, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("user_id not between", value1, value2, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_id =", value, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_id <>", value, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_id >", value, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_id >=", value, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_id <", value, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_id <=", value, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_id like", value, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_id not like", value, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("session_id in", values, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("session_id not in", values, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("session_id between", value1, value2, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionIdNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("session_id not between", value1, value2, "sessionId");
            }
            return (Criteria) this;
        }

        public Criteria andSessionNameEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_name =", value, "sessionName");
            }
            return (Criteria) this;
        }

        public Criteria andSessionNameNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_name <>", value, "sessionName");
            }
            return (Criteria) this;
        }

        public Criteria andSessionNameGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_name >", value, "sessionName");
            }
            return (Criteria) this;
        }

        public Criteria andSessionNameGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_name >=", value, "sessionName");
            }
            return (Criteria) this;
        }

        public Criteria andSessionNameLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_name <", value, "sessionName");
            }
            return (Criteria) this;
        }

        public Criteria andSessionNameLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_name <=", value, "sessionName");
            }
            return (Criteria) this;
        }

        public Criteria andSessionNameLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_name like", value, "sessionName");
            }
            return (Criteria) this;
        }

        public Criteria andSessionNameNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("session_name not like", value, "sessionName");
            }
            return (Criteria) this;
        }

        public Criteria andSessionNameInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("session_name in", values, "sessionName");
            }
            return (Criteria) this;
        }

        public Criteria andSessionNameNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("session_name not in", values, "sessionName");
            }
            return (Criteria) this;
        }

        public Criteria andSessionNameBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("session_name between", value1, value2, "sessionName");
            }
            return (Criteria) this;
        }

        public Criteria andSessionNameNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("session_name not between", value1, value2, "sessionName");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("delete_flag =", value, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("delete_flag <>", value, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("delete_flag >", value, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("delete_flag >=", value, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("delete_flag <", value, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("delete_flag <=", value, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("delete_flag like", value, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("delete_flag not like", value, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("delete_flag in", values, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("delete_flag not in", values, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("delete_flag between", value1, value2, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("delete_flag not between", value1, value2, "deleteFlag");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("ext_info =", value, "extInfo");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("ext_info <>", value, "extInfo");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("ext_info >", value, "extInfo");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("ext_info >=", value, "extInfo");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("ext_info <", value, "extInfo");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("ext_info <=", value, "extInfo");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("ext_info like", value, "extInfo");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("ext_info not like", value, "extInfo");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("ext_info in", values, "extInfo");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("ext_info not in", values, "extInfo");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("ext_info between", value1, value2, "extInfo");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("ext_info not between", value1, value2, "extInfo");
            }
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends AbstractGeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum OrderCondition {
        /**
         *主键
         */
        ID("id"),
        /**
         *创建时间
         */
        GMTCREATE("gmt_create"),
        /**
         *修改时间
         */
        GMTMODIFIED("gmt_modified"),
        /**
         *用户id
         */
        USERID("user_id"),
        /**
         *对话id
         */
        SESSIONID("session_id"),
        /**
         *对话名称
         */
        SESSIONNAME("session_name"),
        /**
         *删除标识位 0-未删除 1-删除
         */
        DELETEFLAG("delete_flag"),
        /**
         *扩展字段
         */
        EXTINFO("ext_info");

        private String columnName;

        OrderCondition(String columnName) {
            this.columnName = columnName;
        }

        public String getColumnName() {
            return columnName;
        }

        public static OrderCondition getEnumByName(String name) {
            OrderCondition[] orderConditions = OrderCondition.values();
            for (OrderCondition orderCondition : orderConditions) {
                if (orderCondition.name().equalsIgnoreCase(name)) {
                    return orderCondition;
                }
            }
            throw new RuntimeException("OrderCondition of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return columnName;
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum SortType {
        /**
         * 升序
         */
        ASC("asc"),
        /**
         * 降序
         */
        DESC("desc");

        private String value;

        SortType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static SortType getEnumByName(String name) {
            SortType[] sortTypes = SortType.values();
            for (SortType sortType : sortTypes) {
                if (sortType.name().equalsIgnoreCase(name)) {
                    return sortType;
                }
            }
            throw new RuntimeException("SortType of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return value;
        }
    }
}