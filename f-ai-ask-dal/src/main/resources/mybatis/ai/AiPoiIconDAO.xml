<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.taobao.trip.jourprod.mapper.AiPoiIconDAO">
  <resultMap id="BaseResultMap" type="com.taobao.trip.jourprod.domain.AiPoiIconDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="poi_id" jdbcType="BIGINT" property="poiId" />
    <result column="poi_name" jdbcType="VARCHAR" property="poiName" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="llm" jdbcType="VARCHAR" property="llm" />
  </resultMap>
  <sql id="Param_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Param_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="aiPoiIconParam.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, gmt_create, gmt_modified, poi_id, poi_name, icon, status, city_code, city_name, llm
  </sql>
  <select id="selectByParam" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinctSql != null">
      distinct ${distinctSql}
    </if>
    <if test="distinctSql == null">
      <include refid="Base_Column_List" />
    </if>
    from ai_poi_icon
    <if test="_parameter != null">
      <include refid="Param_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="page">
      limit #{pageIndex},#{pageSize}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from ai_poi_icon
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from ai_poi_icon
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByParam">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from ai_poi_icon
    <if test="_parameter != null">
      <include refid="Param_Where_Clause" />
    </if>
    <if test="page">
      limit #{pageSize}
    </if>
  </delete>
  <insert id="insert" keyProperty="id" parameterType="com.taobao.trip.jourprod.domain.AiPoiIconDO" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into ai_poi_icon (gmt_create, gmt_modified, poi_id, 
      poi_name, icon, status, city_code, city_name, llm
      )
    values (now(), now(), #{poiId,jdbcType=BIGINT}, 
      #{poiName,jdbcType=VARCHAR}, #{icon,jdbcType=VARCHAR}, #{status,jdbcType=SMALLINT}, #{cityCode,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR}, #{llm,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyProperty="id" parameterType="com.taobao.trip.jourprod.domain.AiPoiIconDO" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into ai_poi_icon
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="poiId != null">
        poi_id,
      </if>
      <if test="poiName != null">
        poi_name,
      </if>
      <if test="icon != null">
        icon,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="llm != null">
        llm,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        now(),
      </if>
      <if test="gmtModified != null">
        now(),
      </if>
      <if test="poiId != null">
        #{poiId,jdbcType=BIGINT},
      </if>
      <if test="poiName != null">
        #{poiName,jdbcType=VARCHAR},
      </if>
      <if test="icon != null">
        #{icon,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="llm != null">
        #{llm,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByParam" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from ai_poi_icon
    <if test="_parameter != null">
      <include refid="Param_Where_Clause" />
    </if>
  </select>
  <update id="updateByParamSelective">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update ai_poi_icon
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = now(),
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = now(),
      </if>
      <if test="record.poiId != null">
        poi_id = #{record.poiId,jdbcType=BIGINT},
      </if>
      <if test="record.poiName != null">
        poi_name = #{record.poiName,jdbcType=VARCHAR},
      </if>
      <if test="record.icon != null">
        icon = #{record.icon,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=SMALLINT},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityName != null">
        city_name = #{record.cityName,jdbcType=VARCHAR},
      </if>
      <if test="record.llm != null">
        llm = #{record.llm,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Param_Where_Clause" />
    </if>
  </update>
  <update id="updateByParam">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update ai_poi_icon
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      poi_id = #{record.poiId,jdbcType=BIGINT},
      poi_name = #{record.poiName,jdbcType=VARCHAR},
      icon = #{record.icon,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=SMALLINT},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      city_name = #{record.cityName,jdbcType=VARCHAR},
      llm = #{record.llm,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Param_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.taobao.trip.jourprod.domain.AiPoiIconDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update ai_poi_icon
    <set>
      <if test="gmtCreate != null">
        gmt_create = now(),
      </if>
      <if test="gmtModified != null">
        gmt_modified = now(),
      </if>
      <if test="poiId != null">
        poi_id = #{poiId,jdbcType=BIGINT},
      </if>
      <if test="poiName != null">
        poi_name = #{poiName,jdbcType=VARCHAR},
      </if>
      <if test="icon != null">
        icon = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=SMALLINT},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="llm != null">
        llm = #{llm,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.taobao.trip.jourprod.domain.AiPoiIconDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update ai_poi_icon
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      poi_id = #{poiId,jdbcType=BIGINT},
      poi_name = #{poiName,jdbcType=VARCHAR},
      icon = #{icon,jdbcType=VARCHAR},
      status = #{status,jdbcType=SMALLINT},
      city_code = #{cityCode,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      llm = #{llm,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyProperty="id" parameterType="com.taobao.trip.jourprod.domain.AiPoiIconDO" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into ai_poi_icon
    <trim prefix="(" suffix=")" suffixOverrides=",">
      gmt_create,gmt_modified,poi_id,poi_name,icon,status,city_code,city_name,llm,
    </trim>
    values
    <foreach collection="list" index="index" item="item" separator=",">
      (
      <trim suffixOverrides=",">
        now(),now(),#{item.poiId,jdbcType=BIGINT},#{item.poiName,jdbcType=VARCHAR},#{item.icon,jdbcType=VARCHAR},#{item.status,jdbcType=SMALLINT},#{item.cityCode,jdbcType=VARCHAR},#{item.cityName,jdbcType=VARCHAR},#{item.llm,jdbcType=VARCHAR}
      </trim>
      )
    </foreach>
  </insert>
</mapper>