<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.taobao.trip.jourprod.mapper.JourneyPlanAiChatMessageDAO">
    <resultMap id="BaseResultMap" type="com.taobao.trip.jourprod.domain.JourneyPlanAiChatMessageDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="message_id" jdbcType="VARCHAR" property="messageId"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="utdid" jdbcType="VARCHAR" property="utdid"/>
        <result column="ttid" jdbcType="VARCHAR" property="ttid"/>
        <result column="session_id" jdbcType="VARCHAR" property="sessionId"/>
        <result column="role" jdbcType="VARCHAR" property="role"/>
        <result column="wrate" jdbcType="VARCHAR" property="wrate"/>
        <result column="timestamp" jdbcType="TIMESTAMP" property="timestamp"/>
        <result column="original_message_id" jdbcType="VARCHAR" property="originalMessageId"/>
        <result column="trace_id" jdbcType="VARCHAR" property="traceId"/>
        <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag"/>
        <result column="source" jdbcType="VARCHAR" property="source"/>
        <result column="user_chat" jdbcType="VARCHAR" property="userChat"/>
        <result column="env" jdbcType="VARCHAR" property="env"/>
        <result column="content" jdbcType="LONGNVARCHAR" property="content"/>
        <result column="extra" jdbcType="LONGNVARCHAR" property="extra"/>
    </resultMap>
    <sql id="Param_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Param_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="journeyPlanAiChatMessageParam.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, gmt_create, gmt_modified, message_id, user_id, utdid, ttid, session_id, role,
        wrate, timestamp, original_message_id, trace_id, delete_flag, source, user_chat,
        env, content, extra
    </sql>
    <select id="selectByParam" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <if test="distinctSql != null">
            distinct ${distinctSql}
        </if>
        <if test="distinctSql == null">
            <include refid="Base_Column_List"/>
        </if>
        from journey_plan_ai_chat_message
        <if test="_parameter != null">
            <include refid="Param_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="page">
            limit #{pageIndex},#{pageSize}
        </if>
    </select>

    <select id="selectByParamWithoutContent" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <if test="distinctSql != null">
            distinct ${distinctSql}
        </if>
        <if test="distinctSql == null">
            id, gmt_create, gmt_modified, message_id, user_id, utdid, ttid, session_id, role,
            wrate, timestamp, original_message_id, trace_id, delete_flag, source, user_chat,
            env, extra
        </if>
        from journey_plan_ai_chat_message
        <if test="_parameter != null">
            <include refid="Param_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="page">
            limit #{pageIndex},#{pageSize}
        </if>
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from journey_plan_ai_chat_message
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from journey_plan_ai_chat_message
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByParam">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from journey_plan_ai_chat_message
        <if test="_parameter != null">
            <include refid="Param_Where_Clause"/>
        </if>
        <if test="page">
            limit #{pageSize}
        </if>
    </delete>
    <insert id="insert" parameterType="com.taobao.trip.jourprod.domain.JourneyPlanAiChatMessageDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into journey_plan_ai_chat_message (id, gmt_create, gmt_modified,
        message_id, user_id, utdid,
        ttid, session_id, role,
        wrate, timestamp, original_message_id,
        trace_id, delete_flag, source,
        user_chat, env, content,
        extra)
        values (#{id,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP},
        #{messageId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{utdid,jdbcType=VARCHAR},
        #{ttid,jdbcType=VARCHAR}, #{sessionId,jdbcType=VARCHAR}, #{role,jdbcType=VARCHAR},
        #{wrate,jdbcType=VARCHAR}, #{timestamp,jdbcType=TIMESTAMP}, #{originalMessageId,jdbcType=VARCHAR},
        #{traceId,jdbcType=VARCHAR}, #{deleteFlag,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR},
        #{userChat,jdbcType=VARCHAR}, #{env,jdbcType=VARCHAR}, #{content,jdbcType=LONGNVARCHAR},
        #{extra,jdbcType=LONGNVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.taobao.trip.jourprod.domain.JourneyPlanAiChatMessageDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into journey_plan_ai_chat_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="messageId != null">
                message_id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="utdid != null">
                utdid,
            </if>
            <if test="ttid != null">
                ttid,
            </if>
            <if test="sessionId != null">
                session_id,
            </if>
            <if test="role != null">
                role,
            </if>
            <if test="wrate != null">
                wrate,
            </if>
            <if test="timestamp != null">
                timestamp,
            </if>
            <if test="originalMessageId != null">
                original_message_id,
            </if>
            <if test="traceId != null">
                trace_id,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="source != null">
                source,
            </if>
            <if test="userChat != null">
                user_chat,
            </if>
            <if test="env != null">
                env,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="extra != null">
                extra,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="messageId != null">
                #{messageId,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="utdid != null">
                #{utdid,jdbcType=VARCHAR},
            </if>
            <if test="ttid != null">
                #{ttid,jdbcType=VARCHAR},
            </if>
            <if test="sessionId != null">
                #{sessionId,jdbcType=VARCHAR},
            </if>
            <if test="role != null">
                #{role,jdbcType=VARCHAR},
            </if>
            <if test="wrate != null">
                #{wrate,jdbcType=VARCHAR},
            </if>
            <if test="timestamp != null">
                #{timestamp,jdbcType=TIMESTAMP},
            </if>
            <if test="originalMessageId != null">
                #{originalMessageId,jdbcType=VARCHAR},
            </if>
            <if test="traceId != null">
                #{traceId,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                #{source,jdbcType=VARCHAR},
            </if>
            <if test="userChat != null">
                #{userChat,jdbcType=VARCHAR},
            </if>
            <if test="env != null">
                #{env,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=LONGNVARCHAR},
            </if>
            <if test="extra != null">
                #{extra,jdbcType=LONGNVARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByParam" resultType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select count(*) from journey_plan_ai_chat_message
        <if test="_parameter != null">
            <include refid="Param_Where_Clause"/>
        </if>
    </select>
    <update id="updateByParamSelective">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update journey_plan_ai_chat_message
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.gmtCreate != null">
                gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="record.gmtModified != null">
                gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="record.messageId != null">
                message_id = #{record.messageId,jdbcType=VARCHAR},
            </if>
            <if test="record.userId != null">
                user_id = #{record.userId,jdbcType=VARCHAR},
            </if>
            <if test="record.utdid != null">
                utdid = #{record.utdid,jdbcType=VARCHAR},
            </if>
            <if test="record.ttid != null">
                ttid = #{record.ttid,jdbcType=VARCHAR},
            </if>
            <if test="record.sessionId != null">
                session_id = #{record.sessionId,jdbcType=VARCHAR},
            </if>
            <if test="record.role != null">
                role = #{record.role,jdbcType=VARCHAR},
            </if>
            <if test="record.wrate != null">
                wrate = #{record.wrate,jdbcType=VARCHAR},
            </if>
            <if test="record.timestamp != null">
                timestamp = #{record.timestamp,jdbcType=TIMESTAMP},
            </if>
            <if test="record.originalMessageId != null">
                original_message_id = #{record.originalMessageId,jdbcType=VARCHAR},
            </if>
            <if test="record.traceId != null">
                trace_id = #{record.traceId,jdbcType=VARCHAR},
            </if>
            <if test="record.deleteFlag != null">
                delete_flag = #{record.deleteFlag,jdbcType=VARCHAR},
            </if>
            <if test="record.source != null">
                source = #{record.source,jdbcType=VARCHAR},
            </if>
            <if test="record.userChat != null">
                user_chat = #{record.userChat,jdbcType=VARCHAR},
            </if>
            <if test="record.env != null">
                env = #{record.env,jdbcType=VARCHAR},
            </if>
            <if test="record.content != null">
                content = #{record.content,jdbcType=LONGNVARCHAR},
            </if>
            <if test="record.extra != null">
                extra = #{record.extra,jdbcType=LONGNVARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Param_Where_Clause"/>
        </if>
    </update>
    <update id="updateByParam">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update journey_plan_ai_chat_message
        set id = #{record.id,jdbcType=BIGINT},
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
        message_id = #{record.messageId,jdbcType=VARCHAR},
        user_id = #{record.userId,jdbcType=VARCHAR},
        utdid = #{record.utdid,jdbcType=VARCHAR},
        ttid = #{record.ttid,jdbcType=VARCHAR},
        session_id = #{record.sessionId,jdbcType=VARCHAR},
        role = #{record.role,jdbcType=VARCHAR},
        wrate = #{record.wrate,jdbcType=VARCHAR},
        timestamp = #{record.timestamp,jdbcType=TIMESTAMP},
        original_message_id = #{record.originalMessageId,jdbcType=VARCHAR},
        trace_id = #{record.traceId,jdbcType=VARCHAR},
        delete_flag = #{record.deleteFlag,jdbcType=VARCHAR},
        source = #{record.source,jdbcType=VARCHAR},
        user_chat = #{record.userChat,jdbcType=VARCHAR},
        env = #{record.env,jdbcType=VARCHAR},
        content = #{record.content,jdbcType=LONGNVARCHAR},
        extra = #{record.extra,jdbcType=LONGNVARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Param_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.taobao.trip.jourprod.domain.JourneyPlanAiChatMessageDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update journey_plan_ai_chat_message
        <set>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="messageId != null">
                message_id = #{messageId,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="utdid != null">
                utdid = #{utdid,jdbcType=VARCHAR},
            </if>
            <if test="ttid != null">
                ttid = #{ttid,jdbcType=VARCHAR},
            </if>
            <if test="sessionId != null">
                session_id = #{sessionId,jdbcType=VARCHAR},
            </if>
            <if test="role != null">
                role = #{role,jdbcType=VARCHAR},
            </if>
            <if test="wrate != null">
                wrate = #{wrate,jdbcType=VARCHAR},
            </if>
            <if test="timestamp != null">
                timestamp = #{timestamp,jdbcType=TIMESTAMP},
            </if>
            <if test="originalMessageId != null">
                original_message_id = #{originalMessageId,jdbcType=VARCHAR},
            </if>
            <if test="traceId != null">
                trace_id = #{traceId,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                source = #{source,jdbcType=VARCHAR},
            </if>
            <if test="userChat != null">
                user_chat = #{userChat,jdbcType=VARCHAR},
            </if>
            <if test="env != null">
                env = #{env,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=LONGNVARCHAR},
            </if>
            <if test="extra != null">
                extra = #{extra,jdbcType=LONGNVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.taobao.trip.jourprod.domain.JourneyPlanAiChatMessageDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update journey_plan_ai_chat_message
        set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
        message_id = #{messageId,jdbcType=VARCHAR},
        user_id = #{userId,jdbcType=VARCHAR},
        utdid = #{utdid,jdbcType=VARCHAR},
        ttid = #{ttid,jdbcType=VARCHAR},
        session_id = #{sessionId,jdbcType=VARCHAR},
        role = #{role,jdbcType=VARCHAR},
        wrate = #{wrate,jdbcType=VARCHAR},
        timestamp = #{timestamp,jdbcType=TIMESTAMP},
        original_message_id = #{originalMessageId,jdbcType=VARCHAR},
        trace_id = #{traceId,jdbcType=VARCHAR},
        delete_flag = #{deleteFlag,jdbcType=VARCHAR},
        source = #{source,jdbcType=VARCHAR},
        user_chat = #{userChat,jdbcType=VARCHAR},
        env = #{env,jdbcType=VARCHAR},
        content = #{content,jdbcType=LONGNVARCHAR},
        extra = #{extra,jdbcType=LONGNVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <insert id="batchInsert" parameterType="com.taobao.trip.jourprod.domain.JourneyPlanAiChatMessageDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into journey_plan_ai_chat_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,gmt_create,gmt_modified,message_id,user_id,utdid,ttid,session_id,role,wrate,timestamp,original_message_id,trace_id,delete_flag,source,user_chat,env,content,extra,
        </trim>
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (
            <trim suffixOverrides=",">
                #{item.id,jdbcType=BIGINT},#{item.gmtCreate,jdbcType=TIMESTAMP},#{item.gmtModified,jdbcType=TIMESTAMP},#{item.messageId,jdbcType=VARCHAR},#{item.userId,jdbcType=VARCHAR},#{item.utdid,jdbcType=VARCHAR},#{item.ttid,jdbcType=VARCHAR},#{item.sessionId,jdbcType=VARCHAR},#{item.role,jdbcType=VARCHAR},#{item.wrate,jdbcType=VARCHAR},#{item.timestamp,jdbcType=TIMESTAMP},#{item.originalMessageId,jdbcType=VARCHAR},#{item.traceId,jdbcType=VARCHAR},#{item.deleteFlag,jdbcType=VARCHAR},#{item.source,jdbcType=VARCHAR},#{item.userChat,jdbcType=VARCHAR},#{item.env,jdbcType=VARCHAR},#{item.content,jdbcType=LONGNVARCHAR},#{item.extra,jdbcType=LONGNVARCHAR},
            </trim>
            )
        </foreach>
    </insert>

    <select id="getRecentQueryInfoBySource"
            resultMap="BaseResultMap">
        select id,
        gmt_create,
        gmt_modified,
        message_id,
        user_id,
        utdid,
        ttid,
        session_id,
        role,
        content,
        wrate,
        extra, timestamp, original_message_id, trace_id, delete_flag,source,env
        from journey_plan_ai_chat_message
        where user_id = #{userId,jdbcType=VARCHAR}
        and role = 'user'
        <if test="source != null and source != ''">
            and source = #{source,jdbcType=VARCHAR}
        </if>
        AND (delete_flag = '0' OR delete_flag IS NULL)
        ORDER BY id DESC LIMIT 10
    </select>
</mapper>