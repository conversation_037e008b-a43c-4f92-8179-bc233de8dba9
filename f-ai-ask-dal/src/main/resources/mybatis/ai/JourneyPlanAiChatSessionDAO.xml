<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.taobao.trip.jourprod.mapper.JourneyPlanAiChatSessionDAO">
  <resultMap id="BaseResultMap" type="com.taobao.trip.jourprod.domain.JourneyPlanAiChatSessionDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="session_name" jdbcType="VARCHAR" property="sessionName" />
    <result column="delete_flag" jdbcType="CHAR" property="deleteFlag" />
    <result column="ext_info" jdbcType="LONGNVARCHAR" property="extInfo" />
  </resultMap>
  <sql id="Param_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Param_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="journeyPlanAiChatSessionParam.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, gmt_create, gmt_modified, user_id, session_id, session_name, delete_flag, ext_info
  </sql>
  <select id="selectByParam" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinctSql != null">
      distinct ${distinctSql}
    </if>
    <if test="distinctSql == null">
      <include refid="Base_Column_List" />
    </if>
    from journey_plan_ai_chat_session
    <if test="_parameter != null">
      <include refid="Param_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="page">
      limit #{pageIndex},#{pageSize}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from journey_plan_ai_chat_session
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from journey_plan_ai_chat_session
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByParam">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from journey_plan_ai_chat_session
    <if test="_parameter != null">
      <include refid="Param_Where_Clause" />
    </if>
    <if test="page">
      limit #{pageSize}
    </if>
  </delete>
  <insert id="insert" parameterType="com.taobao.trip.jourprod.domain.JourneyPlanAiChatSessionDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into journey_plan_ai_chat_session (id, gmt_create, gmt_modified, 
      user_id, session_id, session_name, 
      delete_flag, ext_info)
    values (#{id,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{userId,jdbcType=VARCHAR}, #{sessionId,jdbcType=VARCHAR}, #{sessionName,jdbcType=VARCHAR}, 
      #{deleteFlag,jdbcType=CHAR}, #{extInfo,jdbcType=LONGNVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.taobao.trip.jourprod.domain.JourneyPlanAiChatSessionDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into journey_plan_ai_chat_session
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="sessionName != null">
        session_name,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="sessionId != null">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="sessionName != null">
        #{sessionName,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=CHAR},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=LONGNVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByParam" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from journey_plan_ai_chat_session
    <if test="_parameter != null">
      <include refid="Param_Where_Clause" />
    </if>
  </select>
  <update id="updateByParamSelective">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update journey_plan_ai_chat_session
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.sessionId != null">
        session_id = #{record.sessionId,jdbcType=VARCHAR},
      </if>
      <if test="record.sessionName != null">
        session_name = #{record.sessionName,jdbcType=VARCHAR},
      </if>
      <if test="record.deleteFlag != null">
        delete_flag = #{record.deleteFlag,jdbcType=CHAR},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=LONGNVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Param_Where_Clause" />
    </if>
  </update>
  <update id="updateByParam">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update journey_plan_ai_chat_session
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      user_id = #{record.userId,jdbcType=VARCHAR},
      session_id = #{record.sessionId,jdbcType=VARCHAR},
      session_name = #{record.sessionName,jdbcType=VARCHAR},
      delete_flag = #{record.deleteFlag,jdbcType=CHAR},
      ext_info = #{record.extInfo,jdbcType=LONGNVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Param_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.taobao.trip.jourprod.domain.JourneyPlanAiChatSessionDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update journey_plan_ai_chat_session
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="sessionName != null">
        session_name = #{sessionName,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=CHAR},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=LONGNVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.taobao.trip.jourprod.domain.JourneyPlanAiChatSessionDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update journey_plan_ai_chat_session
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      user_id = #{userId,jdbcType=VARCHAR},
      session_id = #{sessionId,jdbcType=VARCHAR},
      session_name = #{sessionName,jdbcType=VARCHAR},
      delete_flag = #{deleteFlag,jdbcType=CHAR},
      ext_info = #{extInfo,jdbcType=LONGNVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="com.taobao.trip.jourprod.domain.JourneyPlanAiChatSessionDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into journey_plan_ai_chat_session
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,gmt_create,gmt_modified,user_id,session_id,session_name,delete_flag,ext_info,
    </trim>
    values
    <foreach collection="list" index="index" item="item" separator=",">
      (
      <trim suffixOverrides=",">
        #{item.id,jdbcType=BIGINT},#{item.gmtCreate,jdbcType=TIMESTAMP},#{item.gmtModified,jdbcType=TIMESTAMP},#{item.userId,jdbcType=VARCHAR},#{item.sessionId,jdbcType=VARCHAR},#{item.sessionName,jdbcType=VARCHAR},#{item.deleteFlag,jdbcType=CHAR},#{item.extInfo,jdbcType=LONGNVARCHAR},
      </trim>
      )
    </foreach>
  </insert>
</mapper>