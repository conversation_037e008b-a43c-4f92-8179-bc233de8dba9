package com.taobao.trip.common;

import lombok.Getter;

/**
 * 问答类型枚举
 */
public enum AskType {

    JOURNEY_ASK("journey_ask", "行程问一问"),
    HONOR_ASK("honor_ask", "荣耀问一问"),
    SEARCH_ASK("search_ask", "首页搜索问一问");

    @Getter
    private final String code;
    @Getter
    private final String desc;

    AskType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    /**
     * 根据code获取枚举值
     *
     * @param code 类型代码
     * @return 对应的枚举值
     */
    public static AskType getByCode(String code) {
        if (code == null) {
            return null;
        }

        for (AskType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
