package com.taobao.trip.facade;

import com.taobao.trip.request.AiJourneyQueryMessageRequest;
import com.taobao.trip.request.AiJourneySessionListRequest;
import com.taobao.trip.response.AiJourneyMessageSearchResponse;
import com.taobao.trip.response.AiJourneySessionListResponse;

/**
 * 问一问会话相关接口
 */
public interface AiJourneyMessageFacade {

    /**
     * 查询会话列表
     */
    AiJourneySessionListResponse querySessionList(AiJourneySessionListRequest request);

    /**
     * 删除会话
     */
    Boolean deleteSession(AiJourneySessionListRequest request);

    /**
     * 搜索历史消息
     */
    AiJourneyMessageSearchResponse queryMessageList(AiJourneyQueryMessageRequest request);

}
