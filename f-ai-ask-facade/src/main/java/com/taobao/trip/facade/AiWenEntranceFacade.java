package com.taobao.trip.facade;

import com.taobao.mtop.common.Result;
import com.taobao.trip.request.AiWenEntranceRequest;
import com.taobao.trip.response.ActionResult;
import com.taobao.trip.response.AiWenEntranceInfo;

/**
 * Ai 问一问入口服务
 * <AUTHOR>
 * @date 2025/3/30
 */
public interface AiWenEntranceFacade {

    /**
     * 获取权限信息
     */
    Result<AiWenEntranceInfo> getUserInfo(AiWenEntranceRequest request);

    /**
     * 校验权限
     */
    Result<AiWenEntranceInfo> checkAuthorization(AiWenEntranceRequest request);

    /**
     * 预约
     */
    Result<ActionResult> reserve(AiWenEntranceRequest request);

    /**
     * 邀请码核销
     */
    Result<ActionResult> register(AiWenEntranceRequest request);
}
