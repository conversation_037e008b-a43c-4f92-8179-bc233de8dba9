package com.taobao.trip.facade;

import com.taobao.mtop.common.Result;
import com.taobao.trip.request.JourneyPlanAiAssistantWhiteUserRequest;

/**
 * 白名单接口
 */
public interface JourneyPlanAiAssistantWhiteUserFacade {

    /**
     * 是否在白名单，修改该逻辑时，注意还有一个接口：AiAskWhiteUserFacade，该接口功能一致，包出来是为了给odps用
     */
    Result<Boolean> inWhiteList(JourneyPlanAiAssistantWhiteUserRequest request);
    /**
     * 添加白名单
     */
    Result<Boolean> addWhiteList(JourneyPlanAiAssistantWhiteUserRequest request);

}
