package com.taobao.trip.facade;

import java.util.List;

import com.taobao.trip.request.JourneyPlanAiStructRouteRequest;
import com.taobao.trip.request.JourneyPlanAiUrlCallbackRequest;
import com.taobao.trip.request.QueryFavoriteParamReq;
import com.taobao.trip.request.QueryRecentMessageRequest;
import com.taobao.trip.response.RecentMessageResponse;
import com.taobao.trip.rsp.JourneyPlanAiStructRouteResult;
import com.taobao.mtop.common.Result;
import com.taobao.trip.rsp.collect.TitleCollectDTO;

/**
 * 白名单接口
 */
public interface JourneyPlanAskRouteFacade {

    /**
     * 根据消息id查看结构化线路
     * @param request request
     * @return
     */
    Result<JourneyPlanAiStructRouteResult> structRoute(JourneyPlanAiStructRouteRequest request);

    /**
     * 根据消息id查询收藏参数数据，用于收藏静态数据替换图片。
     */
    Result<TitleCollectDTO> queryFavoriteParamByMessageId(QueryFavoriteParamReq request);
    /**
     * 手绘地图回流逻辑
     * @param request
     * @return
     */
    Result<Boolean> mapUrlCallBack(JourneyPlanAiUrlCallbackRequest request);

    /**
     * 查询用户下指定source的近10条消息
     */
    Result<List<RecentMessageResponse>> getUserRecentMessageBySource(QueryRecentMessageRequest request);

}
