package com.taobao.trip.request;

import java.util.List;

import com.taobao.trip.wireless.common.mtop.domain.BaseParam;
import lombok.Data;

/**
 * @Description 会话列表请求
 * <AUTHOR>
 * @Date 2025/4/22
 **/
@Data
public class AiJourneySessionListRequest extends BaseParam {

    /**
     * 要删除的会话id列表
     */
    private String sessionListStr;

    private List<String> sessionIdList;

    /**
     * 开始key
     */
    private String startKey;

    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 是否删除全部会话
     */
    private Boolean deleteAll;

}
