package com.taobao.trip.request;

import java.util.Map;

import com.taobao.trip.wireless.common.mtop.domain.BaseParam;
import lombok.Data;

/**
 * @Description 手绘地图回流请求
 * <AUTHOR>
 * @Date 2025/4/1
 **/
@Data
public class JourneyPlanAiUrlCallbackRequest extends BaseParam {

    /**
     * 消息id
     */
    private String messageId;

    /**
     * 地图url
     */
    private String url;

    /**
     * 卡片id
     */
    private String cardId;

    /**
     * item类型
     */
    private String itemType;

    /**
     * 扩展信息
     */
    private Map<String, Object> extinfo;

}
