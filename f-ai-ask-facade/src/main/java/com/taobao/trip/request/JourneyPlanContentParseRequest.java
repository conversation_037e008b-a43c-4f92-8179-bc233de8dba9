package com.taobao.trip.request;

import java.io.Serializable;

public class JourneyPlanContentParseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    private String messageId;

    private String xmlContent;

    private String sceneCode;

    private String ttid;

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getXmlContent() {
        return xmlContent;
    }

    public void setXmlContent(String xmlContent) {
        this.xmlContent = xmlContent;
    }

    public String getSceneCode() {
        return sceneCode;
    }

    public void setSceneCode(String sceneCode) {
        this.sceneCode = sceneCode;
    }

    public String getTtid() {
        return ttid;
    }

    public void setTtid(String ttid) {
        this.ttid = ttid;
    }



    public JourneyPlanContentParseRequest(String messageId, String xmlContent, String sceneCode, String ttid) {
        this.messageId = messageId;
        this.xmlContent = xmlContent;
        this.sceneCode = sceneCode;
        this.ttid = ttid;
    }

    @Override
    public String toString() {
        return "JourneyPlanContentParseRequest{" +
            "messageId='" + messageId + '\'' +
            ", xmlContent='" + xmlContent + '\'' +
            ", sceneCode='" + sceneCode + '\'' +
            '}';
    }
}
