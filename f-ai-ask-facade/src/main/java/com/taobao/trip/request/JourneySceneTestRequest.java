package com.taobao.trip.request;

import lombok.Data;

import static com.taobao.trip.request.JourneySceneTestRequest.FlowType.BAILIAN;

/**
 * <AUTHOR>
 * @Date: 2025/4/12 20:22
 */
@Data
public class JourneySceneTestRequest {
    Long uid;
    String userInput;
    /**
     * 意图识别的应用ID，不填使用默认的
     */
    String appId;

    /**
     * 指定流程类型，不传默认走百炼
     */
    FlowType flowType = BAILIAN;


    public enum FlowType {
        /**
         * 百炼
         */
        BAILIAN,
        /**
         * FAI
         */
        FAI
    }
}
