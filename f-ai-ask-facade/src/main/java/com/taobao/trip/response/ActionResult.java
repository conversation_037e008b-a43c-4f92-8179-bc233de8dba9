package com.taobao.trip.response;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/4/1
 */
@Getter
@Setter
public class ActionResult {
    private boolean success;
    private String code;
    private String msg;

    public static ActionResult success() {
        return success(null);
    }

    public static ActionResult success(String msg) {
        ActionResult result = new ActionResult();
        result.setCode("200");
        result.setMsg(msg);
        result.setSuccess(true);
        return result;
    }

    public static ActionResult error(String code, String msg) {
        ActionResult result = new ActionResult();
        result.setCode(code);
        result.setMsg(msg);
        result.setSuccess(false);
        return result;
    }
}
