package com.taobao.trip.response;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 2025/5/12 14:59
 */
@Data
public class AiHonorResponse {

    // --------------- 基础字段 ---------------
    /**
     * 请求ID (必填)
     */
    private String requestId;

    /**
     * 错误码 (必填)
     */
    private String errorCode;

    /**
     * 错误消息 (必填)
     */
    private String errorMessage;

    /**
     * 会话ID (非必填)
     */
    private String sessionId;

    /**
     * 会话扩展内容 (JSON字符串，可为空)
     */
    private String sessionAttributes;

    /**
     * 模型名称 (必填)
     */
    private String model;

    /**
     * 响应内容 (必填)
     */
    private Choices choices;

    /**
     * Token使用统计 (可为空)
     */
    private Usage usage;

    // --------------- 嵌套对象定义 ---------------

    @Data
    public static class Choices {
        private Message message;
        private String finishReason;
    }

    @Data
    public static class Message {
        private String role;
        private String contentType;
        private HybridContent hybridContent;
    }

    @Data
    public static class HybridContent {
        private Commands commands;
    }

    @Data
    public static class Commands {
        /**
         * 序列号 (固定填写0)
         */
        private Integer sn = 0;

        private Head head;
        private Body body;
    }

    @Data
    public static class Head {
        private String namespace;
    }

    @Data
    public static class Body {
        private Integer cardType;
        private List<Html> htmls;
        private List<Button> buttons; // 新增按钮结构
    }

    /**
     * H5卡片定义
     */
    @Data
    public static class Html {
        /**
         * H5页面地址 (必填)
         */
        private String url;

        /**
         * JSON格式内容 (非必填)
         */
        private String content;

        /**
         * 模板数据来源方式 (0-不追加；1-追加；默认0)
         */
        private Integer mode = 0;

        /**
         * 高度(dp)，-1由端侧控制
         */
        private Integer height;

        /**
         * 宽度(dp)，-1由端侧控制
         */
        private Integer width;
    }

    /**
     * 卡片按钮定义
     */
    @Data
    public static class Button {
        /**
         * H5跳转配置
         */
        private WebButton web;

        /**
         * 快应用跳转配置
         */
        private QuickAppButton quickApp;

        /**
         * 原生APP跳转配置
         */
        private NativeAppButton nativeApp;

        /**
         * 小程序跳转配置
         */
        private SdkButton sdk;

        /**
         * 按钮图片地址
         */
        private String imageUrl;

        /**
         * 按钮文本
         */
        private String text;
    }

    // --------------- 按钮子类型定义 ---------------

    @Data
    public static class WebButton {
        /**
         * H5跳转地址 (必填)
         */
        private String url;
    }

    @Data
    public static class QuickAppButton {
        /**
         * 快应用deeplink地址 (必填)
         */
        private String url;

        private String appName;
        private String pkgName;
        private Integer minPlatformVersion;
        private Integer minVersion;
    }

    @Data
    public static class NativeAppButton {
        /**
         * deeplink地址 (必填)
         */
        private String url;

        /**
         * 应用名 (必填)
         */
        private String appName;

        /**
         * 包名 (必填)
         */
        private String pkgName;

        private Integer minAndroidApiLevel;
        private Integer minVersion;
    }

    @Data
    public static class SdkButton {
        /**
         * JSON参数 (必填)，示例：
         * {"userName":"gh_3cf62f4f1d52","path":"pages/qrcode/index?noback=1&attach=02_rongyaoapp"}
         */
        private String parameters;
    }

    /**
     * Token用量统计
     */
    @Data
    public static class Usage {
        private Integer totalTokens;
    }

}
