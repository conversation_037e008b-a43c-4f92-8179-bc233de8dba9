package com.taobao.trip.response;

import java.util.List;

import lombok.Data;

/**
 * @Description 历史消息搜索结果
 * <AUTHOR>
 * @Date 2025/4/22
 **/
@Data
public class AiJourneyMessageSearchResponse {

    // 搜索消息列表
    private List<SearchMessageItem> searchMessageList;

    @Data
    private static class SearchMessageItem {

        // 用户消息内容
        private String userMessage;

        // 助手回复内容
        private String assistantMessage;

        // 时间戳
        private String timeStamp;

        // 会话ID
        private String sessionId;

    }

}
