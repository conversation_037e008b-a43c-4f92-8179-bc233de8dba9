package com.taobao.trip.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 会话列表
 * <AUTHOR>
 * @Date 2025/4/22
 **/
@Data
public class AiJourneySessionListResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    // 会话列表
    private List<SessionItem> sessionList;

    // 用户信息
    private UserInfo userInfo;

    // 欢迎页标题（注意 JSON 中的键是 "welcomTitle"，保持原样）
    private String welcomeTitle;

    // 下次查询的起始 ID
    private String startKey;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionItem {

        private String sessionId;

        private String sessionName;

        private String timeStamp;

        private String timeStr;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserInfo {

        private String headPic;

        private String userNick;

        private String levelPic;

        private String jumpUrl;

    }

}
