package com.taobao.trip.response;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class AiPoiIconResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *   主键
     */
    @Getter
    @Setter
    private Long id;

    /**
     *   poi id
     */
    @Getter
    @Setter
    private Long poiId;

    /**
     *   poi名称
     */
    @Getter
    @Setter
    private String poiName;

    /**
     *   icon图标
     */
    @Getter
    @Setter
    private String icon;

    /**
     *   状态 1 不可用 2 可用
     */
    @Getter
    @Setter
    private Short status;

    @Getter
    @Setter
    private String cityCode;

    @Getter
    @Setter
    private String cityName;
}