package com.taobao.trip.response;

import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2025/3/30
 */
@Getter
@Setter
public class AiWenEntranceInfo {

    private boolean authorized;

    private List<Map<String, String>> invitationCodes;

    private Map<String, Object> extensions;

    private Map<String, Object> content;

    public static AiWenEntranceInfo init() {
        AiWenEntranceInfo info = new AiWenEntranceInfo();
        info.setAuthorized(false);
        info.setInvitationCodes(Collections.emptyList());
        info.setExtensions(new ConcurrentHashMap<>());
        info.setContent(Collections.emptyMap());
        return info;
    }
}
