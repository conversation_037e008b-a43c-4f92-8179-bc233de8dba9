package com.taobao.trip.response;

import java.io.Serializable;

import lombok.Data;

@Data
public  class RecentMessageResponse implements Serializable {

    private static final long serialVersionUID = 7006548310132318840L;
    /**
     * 消息id
     */
    private String messageId;

    /**
     * 关联的消息
     */
    private String originalMessageId;

    /**
     * 发送角色
     */
    private String role;

    /**
     * 消息内容
     */
    private String info;

    /**
     * 扩展字段
     */
    private String extra;


    /**
     * 消息类型
     */
    private String type;

    /**
     * 消息状态
     */
    private String status;


    /**
     * 用户指令
     */
    private String userChat;

    /**
     * sessionId
     */
    private String sessionId;


}