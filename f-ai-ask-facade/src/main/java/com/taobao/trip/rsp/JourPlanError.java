package com.taobao.trip.rsp;

/**
 * Created by mojin on 2018/9/3.
 */
public class JourPlanError {
    public static final JourPlanError SYSTEM_ERROR = new JourPlanError("SYSTEM_ERROR","系统很累,小二很忙");
    public static final JourPlanError PERMISSION_FAILED = new JourPlanError("PERMISSION_FAILED","亲,你不能修改别人的行程");
    public static final JourPlanError PARAM_INVALID = new JourPlanError("PARAM_INVALID","参数错误");
    public static final JourPlanError TEST_REQUEST_CHECK_FAILED = new JourPlanError("TEST_REQUEST_CHECK_FAILED","内部压测接口，内部流量检查失败");
    public static final JourPlanError UPDATE_FAILED = new JourPlanError("UPDATE_FAILED","更新失败");
    public static final JourPlanError DELETE_FAILED = new JourPlanError("DELETE_FAILED","删除失败");
    public static final JourPlanError TRIPPLAN_EXPIRED = new JourPlanError("TRIPPLAN_EXPIRED","行程已过期，请重新规划");
    public static final JourPlanError TRIPPLAN_DELETED = new JourPlanError("TRIPPLAN_DELETED","您查看的行程已删除");
    public static final JourPlanError COPY_MYSELF = new JourPlanError("COPY_MYSELF","您不能复制自己的行程");
    public static final JourPlanError REPEAT_ADD = new JourPlanError("REPEAT_ADD","亲,你已经添加该线路");
    public static final JourPlanError PLAN_DELETED = new JourPlanError("PLAN_DELETED", "该行程已被删除");
    public static final JourPlanError PLAN_CHANGED = new JourPlanError("PLAN_CHANGED", "行程有变化，请刷新页面");

    public static final JourPlanError GET_CONTENT_ERROR = new JourPlanError("GET_CONTENT_ERROR", "内容采纳获取内容错误");
    public static final JourPlanError TPP_RECOMMEND_ERROR = new JourPlanError("TPP_RECOMMEND_ERROR", "TPP算法推荐错误");
    public static final JourPlanError PLAN_DAY_NUM_ERROR = new JourPlanError("PLAN_DAY_NUM_ERROR", "行程最多添加30天");

    public static final JourPlanError CHILD_PLAN_NOT_UPDATE_TIME = new JourPlanError("CHILD_PLAN_NOT_UPDATE_TIME","子包不能修改时间");

    /**************** 分享错误提示-前端提示使用 ****************/
    public static final JourPlanError SHARE_PLAN_NOT_EXIST = new JourPlanError("SHARE_PLAN_NOT_EXIST", "分享的行程不存在");
    public static final JourPlanError SHARE_PLAN_EXIST_SENSITIVE_WORD = new JourPlanError("SHARE_PLAN_EXIST_SENSITIVE_WORD", "分享的行程存在敏感词");
    public static final JourPlanError SHARE_PLAN_QUERY_ERROR = new JourPlanError("SHARE_PLAN_QUERY_ERROR", "分享行程查询错误");
    public static final JourPlanError SHARE_CARD_NOT_VALID = new JourPlanError("SHARE_CARD_NOT_VALID", "分享的行程无效");
    public static final JourPlanError BATCH_ADD_SHARE_CARD_ERROR = new JourPlanError("BATCH_ADD_SHARE_CARD_ERROR", "批量添加行程失败");

    public static final JourPlanError SHARE_TOKEN_NOT_VALID = new JourPlanError("SHARE_TOKEN_NOT_VALID", "查询的token无效无效");

    public static final JourPlanError SHARE_CARD_GEN_TOKEN_ERROR = new JourPlanError("SHARE_CARD_GEN_TOKEN_ERROR", "分享行程生成token失败");

    /**************** 分享错误提示-日志查看使用 ****************/
    public static final JourPlanError PLAN_INDEX_QUERY_ERROR = new JourPlanError("PLAN_INDEX_QUERY_ERROR", "行程索引信息查询错误");
    public static final JourPlanError PLAN_INSTANCE_QUERY_ERROR = new JourPlanError("PLAN_INSTANCE_QUERY_ERROR", "行程结构化信息查询错误");
    public static final JourPlanError PLAN_DETAIL_QUERY_ERROR = new JourPlanError("PLAN_DETAIL_QUERY_ERROR", "行程详情查询错误");
    public static final JourPlanError PLAN_DETAIL_QUERY_DATA_CONVERT_ERROR = new JourPlanError("PLAN_DETAIL_QUERY_DATA_CONVERT_ERROR", "行程详情查询，数据合并转换错误");

    public static final JourPlanError PLAN_CREATE_LOCK_ERROR = new JourPlanError("PLAN_CREATE_LOCK_ERROR", "行程已创建");
    public static final JourPlanError SHARE_PLAN_DETAIL_QUERY_ERROR = new JourPlanError("SHARE_PLAN_DETAIL_QUERY_ERROR", "打开失败，请重新尝试");


    /**************** 首页查询错误提示-前端提示使用 ****************/
    public static final JourPlanError START_TIME_SORT_QUERY = new JourPlanError("START_TIME_SORT_QUERY", "出发时间降序排序查询失败");
    public static final JourPlanError START_MODIFIED_TIME_QUERY = new JourPlanError("START_MODIFIED_TIME_QUERY", "更新时间小于上一页最后一个包的更新时间&&等于出发时间的有效包查询失败");
    public static final JourPlanError QUERY_NEXT_PAGE_ERROR = new JourPlanError("QUERY_NEXT_PAGE_ERROR", "查询不够一页，继续查询下一页失败");

    public static final JourPlanError TRY_CATCH_ERROR = new JourPlanError("TRY_CATCH_ERROR", "catch捕获的异常失败");
    public static final JourPlanError PLAN_CARD_MAPPING_QUERY_ERROR = new JourPlanError("PLAN_CARD_MAPPING_QUERY_ERROR", "行程包卡映射关系信息查询错误");
    public static final JourPlanError PLAN_CARD_INDEX_QUERY_ERROR = new JourPlanError("PLAN_CARD_INDEX_QUERY_ERROR", "行程卡片索引信息查询错误");

    /**************** 用户创包错误提示-前端提示使用 ****************/
    public static final JourPlanError CREATE_PLAN_CLOSE_ERROR_TIPS = new JourPlanError("CREATE_PLAN_CLOSE_ERROR_TIPS", "功能暂时关闭，近期恢复");

    public static final JourPlanError JOURNEY_INDEX_NOT_EXIT_ERROR = new JourPlanError("JOURNEY_INDEX_NOT_EXIT_ERROR", "查询行程订单索引信息失败");


    public static final JourPlanError JOURNEY_TOKEN_CHECK_ERROR = new JourPlanError("JOURNEY_TOKEN_CHECK_ERROR", "Token验证失败");
    public static final JourPlanError JOURNEY_TOKEN_EXPIRED = new JourPlanError("JOURNEY_TOKEN_EXPIRED", "Token已过期，请重新申请");

    public static final JourPlanError RESULT_IS_NULL = new JourPlanError("RESULT_IS_NULL", "查询结果为NULL");
    public static final JourPlanError INDEX_QUERY_ERROR = new JourPlanError("INDEX_QUERY_ERROR", "查询用户索引失败");

    public static final JourPlanError NOT_HIT_AB_TEST = new JourPlanError("NOT_HIT_AB_TEST", "没有命中AB测试");

    public static final JourPlanError CARRIER_INDEX_NOT_FOUNT = new JourPlanError("CARRIER_INDEX_NOT_FOUNT", "载体索引找不到");

    public static final JourPlanError HOTEL_DETAIL_DEFAULT_DATA = new JourPlanError("HOTEL_DETAIL_DEFAULT_DATA", "酒店单点详情页走兜底数据");

    public static final JourPlanError ATOM_MODEL_IS_EMPTY = new JourPlanError("ATOM_MODEL_IS_EMPTY", "原子载体数据为空");

    public static final JourPlanError SHARE_CAR_PART_ADD_FAILED = new JourPlanError("SHARE_CAR_PART_ADD_FAILED", "添加成功，部分行程已失效");
    public static final JourPlanError SHARE_CAR_ALL_ADD_FAILED = new JourPlanError("SHARE_CAR_ALL_ADD_FAILED", "行程已失效");

    /**************** 同步错误提示-前端提示使用 ****************/
    public static final JourPlanError JOURNEY_SYNC_12306_USER_ERROR = new JourPlanError("JOURNEY_SYNC_12306_USER_ERROR", "12306账号同步失败");

    public static final JourPlanError JOURNEY_SYNC_12306_LOCK_ERROR = new JourPlanError("JOURNEY_SYNC_12306_LOCK_ERROR", "操作太频繁，请稍后再试");

    public static final JourPlanError JOURNEY_SYNC_HAS_SAME_JOURNEY_ERROR = new JourPlanError("JOURNEY_SYNC_HAS_SAME_JOURNEY_ERROR", "有同样的行程，同步失败");

    public static final JourPlanError JOURNEY_SYNC_GEN_INDEX_ERROR = new JourPlanError("JOURNEY_SYNC_GEN_INDEX_ERROR", "生成索引数据失败");

    public static final JourPlanError JOURNEY_SYNC_INDEX_OPERATE_ERROR = new JourPlanError("JOURNEY_SYNC_INDEX_OPERATE_ERROR", "索引操作失败");

    public static final JourPlanError JOURNEY_SYNC_UN_SUPPORT_TYPE_ERROR = new JourPlanError("JOURNEY_SYNC_UN_SUPPORT_TYPE_ERROR", "不支持的类目");

    public static final JourPlanError JOURNEY_SYNC_LOCK_ERROR = new JourPlanError("JOURNEY_SYNC_LOCK_ERROR", "同步动作太频繁");

    public static final JourPlanError JOURNEY_SYNC_CONVERT_JOURNEY_DATA_ERROR = new JourPlanError("JOURNEY_SYNC_CONVERT_JOURNEY_DATA_ERROR", "同步数据转换失败");

    /**************** 火车列表搜索错误提示-前端提示使用 ****************/
    public static final JourPlanError QUERY_TRAIN_LIST_ERROR = new JourPlanError("QUERY_TRAIN_LIST_ERROR", "查询直达火车列表失败");

    /**************** 新版行程规划错误提示-前端提示使用 ****************/
    public static final JourPlanError PLAN_NEW_PASTE_ERROR = new JourPlanError("PLAN_NEW_PASTE_ERROR", "当前链接不支持，换个链接试试");


    public static final JourPlanError DATA_EMPTY = new JourPlanError("DATA_EMPTY", "数据空了");
    /**
     * rpc调用失败
     */
    public static final JourPlanError RPC_INVOKE_ERROR = new JourPlanError("RPC_INVOKE_ERROR", "远程调用失败");


    public static final JourPlanError AI_JOURNEY_SCENE_NULL_ERROR = new JourPlanError("AI_JOURNEY_SCENE_NULL_ERROR", "场景信息分析失败");

    public static final JourPlanError AI_JOURNEY_ALL_READY_ANSWER_ERROR = new JourPlanError("AI_JOURNEY_ALL_READY_ANSWER_ERROR", "已经在回答了");

    public static final JourPlanError AI_JOURNEY_CONTENT_NULL_ERROR = new JourPlanError("AI_JOURNEY_CONTENT_NULL_ERROR", "模型返回结果为空,请重试");
    public static final JourPlanError AI_JOURNEY_CHAT_ERROR = new JourPlanError("AI_JOURNEY_CHAT_ERROR", "对话接口异常");

    private String msgInfo;
    private String msgCode;

    public JourPlanError(String msgCode, String msgInfo) {
        this.msgCode = msgCode;
        this.msgInfo = msgInfo;
    }

    public String getMsgInfo() {
        return msgInfo;
    }

    public String getMsgCode() {
        return msgCode;
    }
}
