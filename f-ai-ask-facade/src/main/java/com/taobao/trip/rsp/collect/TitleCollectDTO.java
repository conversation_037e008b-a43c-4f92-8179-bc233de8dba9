package com.taobao.trip.rsp.collect;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TitleCollectDTO implements Serializable {

    private static final long serialVersionUID = 5223814720829797609L;
    /**
     * 标题
     */
    private String title;

    /**
     * 是否展示收藏
     */
    private Boolean collectEnable = false;
    /**
     * 收藏状态
     */
    private Boolean collectStatus = false;

    /**
     * 调用收藏时使用的参数
     */
    private JourneyCollectDTO collectData;

    /**
     * bizeType 写死。 前端用于区分卡片数据。
     */
    private String bizType = "journeyRoute";



}