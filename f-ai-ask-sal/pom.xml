<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.taobao.trip.jourprod</groupId>
        <artifactId>f-ai-ask</artifactId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>f-ai-ask-sal</artifactId>
    <name>f-ai-ask-sal</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.fliggy.fai</groupId>
            <artifactId>fai-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.csi</groupId>
            <artifactId>csi-common2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.trip.wireless</groupId>
            <artifactId>hwsearch-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-hsf-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-tair-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-tddl-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-switchcenter-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.trip.tripdivision</groupId>
            <artifactId>tripdivision-client</artifactId>
        </dependency>
        <!--玩法互动-->
        <dependency>
            <groupId>com.alibaba.fliggy</groupId>
            <artifactId>fliggy-award-upgrade-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fliggy</groupId>
            <artifactId>f-marketing-play-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fliggy.fliggyplaycore</groupId>
            <artifactId>fliggyplaycore-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fliggy.graph</groupId>
            <artifactId>fliggy-graph-support</artifactId>
            <version>1.0.3</version>
            <scope>provided</scope>
        </dependency>
        <!-- 酒店套餐 -->
        <dependency>
            <groupId>com.alibaba.fliggy</groupId>
            <artifactId>tuan-gateway-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fliggy</groupId>
            <artifactId>tuan-commons</artifactId>
        </dependency>
        <!-- 日志工具 -->
        <dependency>
            <groupId>fliggy.content</groupId>
            <artifactId>utils</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alitrip.search</groupId>
            <artifactId>tripguide-ai-client</artifactId>
        </dependency>
        <!-- 会员信息 -->
        <dependency>
            <groupId>com.fliggy.ffa.touch</groupId>
            <artifactId>ffa-customize-touch-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fliggy.ffa</groupId>
            <artifactId>ffa-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.taobao.trip.jourprod</groupId>
            <artifactId>f-ai-ask-facade</artifactId>
        </dependency>

        <!-- 静态配置 -->
        <dependency>
            <groupId>com.fliggy.fceadmin</groupId>
            <artifactId>fceadmin-client-static-resource</artifactId>
        </dependency>
    </dependencies>
</project>