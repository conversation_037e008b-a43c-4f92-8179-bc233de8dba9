package com.taobao.trip.jourprod.award;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fliggy.award.client.model.request.award.runtime.AwardPredictParam;
import com.alibaba.fliggy.award.client.model.request.award.runtime.BatchAwardPredictRequest;
import com.alibaba.fliggy.award.client.model.response.award.runtime.AwardApplyResponse;
import com.alibaba.fliggy.award.client.model.response.award.runtime.BatchAwardApplyResponse;
import com.alibaba.fliggy.marketing.play.client.enums.AppChannelEnum;
import com.alibaba.fliggy.marketing.play.client.enums.RequestModeEnum;
import com.alibaba.fliggy.marketing.play.client.enums.SendWayExtEnum;
import com.alibaba.fliggy.marketing.play.client.model.base.BaseResult;
import com.alibaba.fliggy.marketing.play.client.model.dto.ApplyRequestOption;
import com.alibaba.fliggy.marketing.play.client.model.dto.PredictRequestOption;
import com.alibaba.fliggy.marketing.play.client.model.dto.PredictReturnOption;
import com.alibaba.fliggy.marketing.play.client.model.dto.SecurityParam;
import com.alibaba.fliggy.marketing.play.client.model.dto.custom.CustomExtDTO;
import com.alibaba.fliggy.marketing.play.client.model.dto.custom.common.ChannelCustomExtDTO;
import com.alibaba.fliggy.marketing.play.client.model.dto.custom.common.MtopContextCustomExtDTO;
import com.alibaba.fliggy.marketing.play.client.model.dto.custom.common.SendWayExtDTO;
import com.alibaba.fliggy.marketing.play.client.model.dto.uncheck.ActUnCheckOption;
import com.taobao.ateye.annotation.Switch;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/5/18
 */
public class ApplyUtils {
    @Switch
    public static String MOCK_USER_WHITE_LIST = "";

    public static PredictRequestOption getPredictRequestOption(Long userId, boolean isMock) {
        if (isMock && userId != null && isUserInWhite(userId)) {
            PredictRequestOption predictRequestOption = new PredictRequestOption();
            ActUnCheckOption actUnCheckOption = new ActUnCheckOption();
            actUnCheckOption.setUnCheckSceneCode("OPERATORS_SELF_EXAMINE");
            actUnCheckOption.setUnCheckActValid(true);
            predictRequestOption.setActUnCheckOption(actUnCheckOption);
            return predictRequestOption;
        }
        return null;
    }


    public static ApplyRequestOption getApplyRequestOption(Long userId, boolean isMock) {
        if (isMock && userId != null && isUserInWhite(userId)) {
            ApplyRequestOption applyRequestOption = new ApplyRequestOption();
            ActUnCheckOption actUnCheckOption = new ActUnCheckOption();
            actUnCheckOption.setUnCheckSceneCode("OPERATORS_SELF_EXAMINE");
            actUnCheckOption.setUnCheckActValid(true);
            applyRequestOption.setActUnCheckOption(actUnCheckOption);
            return applyRequestOption;
        }
        return null;

    }

    public static String getSource(Long userId, boolean isMock) {
        if (isMock && userId != null && isUserInWhite(userId)) {
            return "FLIGGY_TESTING";
        }
        return "CRM";
    }

    public static String getAppName(Long userId, boolean isMock) {
        if (isMock && userId != null && isUserInWhite(userId)) {
            return "crmMock";
        }
        return "f-ai-ask";
    }

    public static JSONObject params(String json) {
        if (StringUtils.startsWith(json, "{")) {
            return JSON.parseObject(json);
        }
        return null;
    }

    public static boolean isMock(JSONObject params) {
        if (params != null) {
            return params.getBooleanValue("mock");
        }
        return false;
    }

    public static CustomExtDTO getChannel(JSONObject params, String ttid) {
        String client = MapUtils.getString(params, "client");
        ChannelCustomExtDTO channelCustomExtDTO = new ChannelCustomExtDTO();
        if (StringUtils.isNotBlank(ttid)) {
            channelCustomExtDTO.setTtid(ttid);
        }
        if ("taobao".equals(client)) {
            channelCustomExtDTO.setAppChannelV2(AppChannelEnum.TAOBAO_CHANNEL.getChannelCode());
        } else if ("alipay".equals(client)) {
            channelCustomExtDTO.setAppChannelV2(AppChannelEnum.ALIPAY_CHANNEL.getChannelCode());
        } else if ("fliggy".equals(client)) {
            channelCustomExtDTO.setAppChannelV2(AppChannelEnum.FLIGGY_CHANNEL.getChannelCode());
        } else if ("h5".equals(client)) {
            channelCustomExtDTO.setAppChannelV2(AppChannelEnum.H5_CHANNEL.getChannelCode());
        }
        return channelCustomExtDTO;
    }

    public static CustomExtDTO getChannelCustomExtDTO(JSONObject params) {
        if (MapUtils.isEmpty(params)) {
            return null;
        }
        ChannelCustomExtDTO channelCustomExtDTO = (ChannelCustomExtDTO) getChannel(params, null);
        JSONObject afterCoupon = params.getJSONObject("afterCoupon");
        if (afterCoupon != null) {
            channelCustomExtDTO.setAppChannelV2(afterCoupon.getString("appChannelV2"));
            channelCustomExtDTO.setTtid(afterCoupon.getString("ttid"));
            channelCustomExtDTO.setSendWayExtType(afterCoupon.getString("sendWayExtType"));
            channelCustomExtDTO.setSpm(afterCoupon.getString("spm"));
            channelCustomExtDTO.setModuleType(afterCoupon.getByte("moduleType"));
            channelCustomExtDTO.setModuleName(afterCoupon.getString("moduleName"));
        }
        return channelCustomExtDTO;
    }

    public static CustomExtDTO getSendWay(boolean isDirect) {
        SendWayExtDTO sendWayExtDTO = new SendWayExtDTO();
        if (isDirect) {
            sendWayExtDTO.setSendWay(SendWayExtEnum.DIRECT_SEND.getCode());
        } else {
            sendWayExtDTO.setSendWay(SendWayExtEnum.MANUAL_TAKE.getCode());
        }
        return sendWayExtDTO;
    }

    public static CustomExtDTO getMtopInfo(String utdId, String umid) {
        MtopContextCustomExtDTO dto = new MtopContextCustomExtDTO();
        dto.setUtdid(utdId);
        dto.setUmid(umid);
        return dto;
    }

    public static boolean isAwardSuccess(BaseResult<BatchAwardApplyResponse> awardResult) {
        BatchAwardApplyResponse model = awardResult.getModel();
        if (!awardResult.isSuccess() || null == model || !model.getActionResult()) {
            return false;
        }
        List<AwardApplyResponse> rsList = model.getRsList();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(rsList)) {
            return false;
        }
        //判断是否成功
        return rsList.stream().anyMatch(r -> BooleanUtils.isTrue(r.getActionResult()));
    }

    public static boolean isAllAwardSuccess(BaseResult<BatchAwardApplyResponse> awardResult) {
        BatchAwardApplyResponse model = awardResult.getModel();
        List<AwardApplyResponse> rsList = model.getRsList();
        //判断是否全部成功
        return rsList.stream().allMatch(r -> BooleanUtils.isTrue(r.getActionResult()) && BooleanUtils.isTrue(r.getAllAwardSuccess()));
    }

    public static BatchAwardPredictRequest buildBatchPredictRequest(Long userId, String asac, List<Map<String, Object>> awards, Map<String, Object> params) {
        return buildBatchPredictRequest(userId, asac, awards, params, false);
    }

    public static BatchAwardPredictRequest buildBatchPredictRequest(Long userId, String asac, List<Map<String, Object>> awards, Map<String, Object> params, boolean isDirect) {
        boolean isMock = MapUtils.getBooleanValue(params, "isMock");
        BatchAwardPredictRequest request = new BatchAwardPredictRequest();
        request.setAppName(ApplyUtils.getAppName(userId, isMock));
        request.setUserId(userId);
        request.setInterfaceRequestSource(getSource(userId, isMock));
        PredictReturnOption returnOption = new PredictReturnOption();
        returnOption.setReturnCouponTemplateInfo(false);
        returnOption.setReturnAlreadyAcceptPrize(true);
        request.setReturnOption(returnOption);
        List<AwardPredictParam> list = new ArrayList<>();
        Set<String> distinct = new HashSet<>();
        for (Map<String, Object> coupon : awards) {
            String activityId = MapUtils.getString(coupon, "subActId");
            String channel = MapUtils.getString(coupon, "prodChannelCode");
            if (StringUtils.isAnyBlank(activityId, channel)) {
                continue;
            }
            if (distinct.contains(activityId)) {
                continue;
            } else {
                distinct.add(activityId);
            }
            AwardPredictParam awardPredictParam = new AwardPredictParam();
            awardPredictParam.setActivityId(activityId);
            awardPredictParam.setProdChannelCode(channel);
            list.add(awardPredictParam);
        }
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        request.addCustomExtDTO(getSendWay(isDirect));
        request.setParamList(list);
        request.setRequestMode(RequestModeEnum.HSF.name());
        if (StringUtils.isNotBlank(asac)) {
            SecurityParam param = new SecurityParam();
            param.setAsac(asac);
            request.setSecurityParam(param);
        }
        request.setRequestOption(getPredictRequestOption(userId, isMock));
        return request;
    }

    private static boolean isUserInWhite(Long userId) {
        String uid = String.valueOf(userId);
        return StringUtils.contains(MOCK_USER_WHITE_LIST, uid);
    }
}
