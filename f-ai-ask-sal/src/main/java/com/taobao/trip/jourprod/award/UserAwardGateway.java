package com.taobao.trip.jourprod.award;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fliggy.award.client.facade.award.runtime.AwardRecordReadService;
import com.alibaba.fliggy.award.client.facade.award.runtime.AwardWriteService;
import com.alibaba.fliggy.award.client.model.dto.award.UserAwardRecordsQueryResp;
import com.alibaba.fliggy.award.client.model.request.award.runtime.AwardApplyRequest;
import com.alibaba.fliggy.award.client.model.request.award.runtime.UserAwardRecordsQueryReq;
import com.alibaba.fliggy.award.client.model.response.award.runtime.AwardApplyResponse;
import com.alibaba.fliggy.marketing.play.client.enums.RequestModeEnum;
import com.alibaba.fliggy.marketing.play.client.model.base.BaseResult;
import com.alibaba.fliggy.marketing.play.client.model.dto.ApplyReturnOption;
import com.alibaba.fliggy.marketing.play.client.model.dto.custom.CustomExtDTO;
import com.fliggy.fliggyplaycore.client.model.ServiceResult;
import com.fliggy.fliggyplaycore.client.redeem.RedeemCodeAdminService;
import com.fliggy.fliggyplaycore.client.redeem.RedeemCodeOperationService;
import com.fliggy.fliggyplaycore.client.redeem.model.*;
import com.taobao.hsf.exception.HSFTimeOutException;
import com.taobao.trip.jourprod.utils.GatewayLoggerUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/4/1
 */
@Component
public class UserAwardGateway {
    @Resource
    private AwardWriteService awardWriteService;
    @Resource
    private AwardRecordReadService awardRecordReadService;
    @Resource
    private RedeemCodeOperationService redeemCodeOperationService;
    @Resource
    private RedeemCodeAdminService redeemCodeAdminService;

    public AwardApplyResponse apply(SendCouponInput input) {
        try {
            AwardApplyRequest request = buildAwardApplyRequest(input);
            BaseResult<AwardApplyResponse> awardResult = awardWriteService.apply(request);
            AwardApplyResponse model = awardResult.getModel();
            if (awardResult.isSuccess() && null != model && model.getActionResult()) {
                return model;
            } else {
                GatewayLoggerUtils.error("发券失败:{}", JSON.toJSONString(awardResult));
            }
        } catch (HSFTimeOutException e) {
            GatewayLoggerUtils.error("发券超时");
        } catch (Throwable e) {
            GatewayLoggerUtils.error("发券异常", e);
        }
        return null;
    }

    public UserAwardRecordsQueryResp queryUserAwardRecords(SendCouponInput input) {
        try {
            UserAwardRecordsQueryReq request = new UserAwardRecordsQueryReq();
            request.setUserId(input.getUserId());
            request.setAppName(ApplyUtils.getAppName(input.getUserId(), false));
            request.setActivityIds(Collections.singletonList(input.getSubActId()));
            request.setOrderStatus(1);
            BaseResult<UserAwardRecordsQueryResp> response = awardRecordReadService.queryUserAwardRecords(request);
            if (response.isSuccess() && null != response.getModel()) {
                return response.getModel();
            } else {
                GatewayLoggerUtils.error("查发券记录失败:{}", JSON.toJSONString(response));
            }
        } catch (Throwable e) {
            GatewayLoggerUtils.error("查发券记录失败异常", e);
        }
        return null;
    }

    public ServiceResult<RedeemCodeRedeemRsp> redeem(Long userId, String code) {
        try {
            RedeemCodeRedeemReq request = new RedeemCodeRedeemReq();
            request.setUserId(userId);
            request.setRedeemCode(code);
            ServiceResult<RedeemCodeRedeemRsp> result = redeemCodeOperationService.redeem(request);
            GatewayLoggerUtils.info("核销结果:{}", JSON.toJSONString(result));
            return result;
        } catch (Throwable e) {
            GatewayLoggerUtils.error("邀请码核销异常", e);
        }
        return null;
    }

    public List<Map<String, String>> queryStatus(List<String> codes) {
        try {
            RedeemCodeRecordByCodeRequest request = new RedeemCodeRecordByCodeRequest();
            request.setCodes(codes);
            ServiceResult<RedeemCodeStatusRes> response = redeemCodeAdminService.batchQueryCodeStatus(request);
            if (!response.isSuccess() || response.getResultData() == null || CollectionUtils.isEmpty(response.getResultData().getRedeemCodeRecordDTOList())) {
                GatewayLoggerUtils.error("查询邀请码状态失败:{}", JSON.toJSONString(response));
                return Collections.emptyList();
            }
            List<RedeemCodeRecordDTO> list = response.getResultData().getRedeemCodeRecordDTOList();
            GatewayLoggerUtils.info("邀请码状态:{}", JSON.toJSONString(list));
            List<Map<String, String>> result = new ArrayList<>();
            for (RedeemCodeRecordDTO code : list) {
                Map<String, String> map = new HashMap<>();
                map.put("code", code.getCode());
                map.put("status", code.getStatus() == null ? "0" : "1");
                result.add(map);
            }
            return result;
        } catch (Throwable e) {
            GatewayLoggerUtils.error("查询邀请码状态异常", e);
            return Collections.emptyList();
        }
    }

    private AwardApplyRequest buildAwardApplyRequest(SendCouponInput input) {
        JSONObject params = ApplyUtils.params(input.getExtInfoJson());
        boolean isMock = ApplyUtils.isMock(params);
        AwardApplyRequest request = new AwardApplyRequest();
        request.setActivityId(input.getSubActId());
        request.setUserId(input.getUserId());
        request.setAppName(ApplyUtils.getAppName(input.getUserId(), isMock));
        request.setAwardPolicyType("SYNC_TURN_SYNC_AWARD");
        request.setFailedRollBack(true);
        request.setPartSuccessAsFail(false);
        Boolean couponStatus = input.getCouponStatus();
        ApplyReturnOption returnOption = new ApplyReturnOption();
        if (couponStatus != null && couponStatus) {
            returnOption.setReturnCouponTemplateInfo(true);
        }
        returnOption.setReturnRightInstanceCode(true);
        request.setReturnOption(returnOption);
        request.setInterfaceRequestSource(ApplyUtils.getSource(input.getUserId(), isMock));
        request.setExternalSourceCode(buildCrmIdAndScm(input.getExtInfoJson()));
        request.setProdChannelCode(input.getProdChannelCode());
        //换成异步发奖了，这个不传了
        //request.setIdempotentId(buildCrmId(input.getExtInfoJson()));
        request.setRequestMode(RequestModeEnum.HSF.name());
        request.setRequestOption(ApplyUtils.getApplyRequestOption(input.getUserId(), isMock));
        Long templateMetaId = input.getTemplateMetaId();
        if (null != templateMetaId) {
            request.setRequestId(String.valueOf(templateMetaId));
        }
        CustomExtDTO channel = ApplyUtils.getChannelCustomExtDTO(params);
        if (channel != null) {
            request.addCustomExtDTO(channel);
        }
        request.addCustomExtDTO(ApplyUtils.getSendWay(input.isDirect()));
        request.addCustomExtDTO(ApplyUtils.getMtopInfo(input.getUtdid(), input.getUmid()));
        return request;
    }

    private String buildCrmIdAndScm(String extInfoJson) {
        try {
            Map<String, Object> extInfoJsonMap = JSON.parseObject(extInfoJson, new TypeReference<>() {});
            String key = MapUtils.getString(extInfoJsonMap, "TRIPBP_STATISTIC");
            if (StringUtils.isBlank(key)) {
                return null;
            }
            return key;
        } catch (Exception e) {
            GatewayLoggerUtils.error("buildCrmIdAndScm：{}", JSON.toJSONString(extInfoJson));
            return null;
        }
    }
}
