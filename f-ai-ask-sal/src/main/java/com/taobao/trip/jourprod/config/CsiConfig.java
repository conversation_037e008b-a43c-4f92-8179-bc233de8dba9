package com.taobao.trip.jourprod.config;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.alibaba.security.tenant.common.service.RequestService;

import com.taobao.csi.common.client.checker.CsiObjectChecker;
import com.taobao.csi.common.client.service.CsiServiceImpl;
import com.taobao.csi.common.core.conf.CsiCommonProperties;
import com.taobao.csi.common.core.service.CsiService;
import com.taobao.csi.common.mtee3.hsf.checker.Mtee3HsfChecker;
import com.taobao.csi.common.mtee3.hsf.checker.Mtee3HsfRequestChecker;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * AIGC 绿网 配置
 * https://aliyuque.antfin.com/platform-content/help_doc/nglehh99a7cc1osw#l5JUr
 *
 * <AUTHOR>
 * @date 2024/2/6
 */
@Configuration
public class CsiConfig {

    public static final String MTEE3_EVENT_CODE = "trip_aixcgh_aigc_mtee_sns_unify_check";

    public static final String BUSINESS_NAME = "ali.china.trip_xingchen";

    public static final String FIRST_PRODUCT_NAME = "aixcgh";

    @Bean
    CsiCommonProperties csiCommonProperties(){
        CsiCommonProperties props = new CsiCommonProperties();
        List<CsiCommonProperties.BizConf> bizConfList = new ArrayList<>();
        //事件code、业务线名称、业务场景名称，是必填字段，不清楚可以咨询中台交付同学
        CsiCommonProperties.BizConf bizConf1 = CsiCommonProperties.BizConf.builder()
            .mtee3EventCode(MTEE3_EVENT_CODE)
            .businessName(BUSINESS_NAME)
            .firstProductName(FIRST_PRODUCT_NAME)
            .build();
        bizConfList.add(bizConf1);
        props.setBizConfList(bizConfList);
        return props;
    }

    @Bean
    public CsiObjectChecker csiObjectChecker() {
        return new CsiObjectChecker();
    }

    @Bean
    public Mtee3HsfChecker mtee3HsfChecker(RequestService requestService) {
        Mtee3HsfRequestChecker checker = new Mtee3HsfRequestChecker();
        checker.setRequestService(requestService);
        return checker;
    }

    @Bean
    public CsiService csiService(CsiObjectChecker csiObjectChecker, Mtee3HsfChecker mtee3HsfChecker) {
        CsiServiceImpl csiService = new CsiServiceImpl();
        csiService.setCsiCheckerList(Arrays.asList(
            csiObjectChecker,
            mtee3HsfChecker
        ));
        return csiService;
    }

}