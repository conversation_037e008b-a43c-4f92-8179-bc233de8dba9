package com.taobao.trip.jourprod.config;

import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.alibaba.fliggy.tuan.gateway.service.chanelpage.ChanelPageApi;
import com.alibaba.security.tenant.common.service.RequestService;
import com.alibaba.trip.tripdivision.client.TrdiDivisionReadService;
import com.alitrip.search.tripguide.ai.service.AiQuestionSuggestService;
import com.fliggy.fai.client.fsg.service.FspAISearchService;
import com.fliggy.ffa.client.interflow.FfaInterflowQueryMemberInfoService;
import com.taobao.trip.wireless.hotel.HotelWirelessQueryService;
import org.springframework.context.annotation.Configuration;


@Configuration
public class HsfConsumerConfig {
    /** 酒店搜索接口 **/
    @HSFConsumer(serviceVersion = "1.0.0.hwsearch", clientTimeoutStr = "${hwsearch.hsf.timeout}")
    private HotelWirelessQueryService hotelWirelessQueryService;

    @HSFConsumer(serviceVersion = "1.0.0", clientTimeout = 1000)
    private TrdiDivisionReadService trdiDivisionReadService;

    @HSFConsumer(serviceVersion = "1.0.0", clientTimeout = 6000)
    private FspAISearchService fspAISearchService;

    /** 酒店套餐接口 **/
    @HSFConsumer(serviceVersion = "1.0.0", clientTimeout = 3000)
    private ChanelPageApi chanelPageApi;

    @HSFConsumer(serviceVersion = "1.0.0_content_aigc", clientTimeout = 5000)
    private RequestService requestService;


    /** 主搜接口 **/
    @HSFConsumer(serviceVersion = "1.0.0", clientTimeout = 3000)
    private AiQuestionSuggestService aiQuestionSuggestService;

    @HSFConsumer(serviceVersion = "1.0.0", clientTimeout = 2000)
    private FfaInterflowQueryMemberInfoService ffaInterflowQueryMemberInfoService;
}
