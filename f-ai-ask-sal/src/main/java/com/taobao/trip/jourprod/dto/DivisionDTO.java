package com.taobao.trip.jourprod.dto;

import java.io.Serializable;

import lombok.Data;

@Data
public class DivisionDTO implements Serializable {
    private static final long serialVersionUID = 3189520355137242514L;
    private Long id;
    private String name;
    private String nameEn;
    private String nameAbbr;
    private String pinyin;
    private String py;
    private Integer statusEnum;
    private Integer levelEnum;
    private String treeId;
    private String treeName;
    private Long parentId;
    private Double longitude;
    private Double latitude;
    private String countryName;
    private String countryCode2;
    private String callingCode;
    private String timezoneid;
    private String airportCode3;
    private Boolean abroad;
    private String eanRegionId;
    private String lastModifier;
    private String travelDivisionId;
    private String extendId;
    private Boolean capitalFlag;
    private String logicAreaIds;
    private String cityCode3;
    private String relationIds;
    private String antAreaCode;
    private Integer hDivisionCode;
    private Long mergeId;
    /**
     * 扩展参数：com.alibaba.trip.tripdivision.domain.TrdiDivisionDO
     */
    private String extDta;
}