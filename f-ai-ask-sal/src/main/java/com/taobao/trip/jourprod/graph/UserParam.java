package com.taobao.trip.jourprod.graph;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.joda.time.DateTime;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/1
 */
@Getter
@Setter
@Builder
public class UserParam {
    private Long userId;

    private DateTime now;

    private Map<String, Object> params;

    private Map<String, Object> material;

    public UserParam copy(Map<String, Object> params) {
        if (params == null) {
            params = new HashMap<>();
        }
        params.putAll(this.params);
        return UserParam.builder().userId(userId).now(now).params(params).material(material).build();
    }
}
