package com.taobao.trip.jourprod.hotel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import com.taobao.mitem.util.DateUtil;
import com.taobao.mtop.common.Result;
import com.taobao.trip.wireless.common.util.DateTimeUtils;
import com.taobao.trip.wireless.hotel.HotelWirelessQueryService;
import com.taobao.trip.wireless.hotel.domain.SearchHotelListParam;
import com.taobao.trip.wireless.hotel.domain.SearchHotelListVO;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 酒店查询服务
 * <AUTHOR>
 */
@Service
public class HwSearchService {
    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(HwSearchService.class);
    @Resource
    private HotelWirelessQueryService hotelWirelessQueryService;

    public Result<SearchHotelListVO> batchQuerySearchHotel(SearchHotelListParam param) {
        long startTime = System.currentTimeMillis();
        try {
            // 检查并设置默认的入离时间
            Date now = new Date();
            String todayStr = DateTimeUtils.format(now, "yyyy-MM-dd");
            String tomorrowStr = DateTimeUtils.format(DateUtil.addDays(now, 1), "yyyy-MM-dd");
            String dayAfterTomorrowStr = DateTimeUtils.format(DateUtil.addDays(now, 2), "yyyy-MM-dd");

            String checkInStr = param.getCheckIn();
            String checkOutStr = param.getCheckOut();
            
            // 检查入离日期是否为空或者有效
            if (checkInStr == null || checkOutStr == null || 
                checkInStr.compareTo(todayStr) < 0 || 
                checkOutStr.compareTo(todayStr) < 0 || 
                checkOutStr.compareTo(checkInStr) <= 0) {
                
                // 设置为明天和后天
                param.setCheckIn(tomorrowStr);
                param.setCheckOut(dayAfterTomorrowStr);
                
                // 只记录一条日志，说明设置了默认日期
                if (checkInStr != null && checkOutStr != null) {
                    LOGGER.recordOutput(new LogModel("batchQuerySearchHotel")
                        .message(String.format("入离日期无效，已设置为默认值。原始入住日期:%s, 原始离店日期:%s, 现入住日期:%s, 现离店日期:%s", 
                            checkInStr, checkOutStr, tomorrowStr, dayAfterTomorrowStr)));
                } else {
                    LOGGER.recordOutput(new LogModel("batchQuerySearchHotel")
                        .message(String.format("入离日期为空，已设置为默认值。现入住日期:%s, 现离店日期:%s", 
                            tomorrowStr, dayAfterTomorrowStr)));
                }
            }

            Result<SearchHotelListVO> hotelListVOResult = hotelWirelessQueryService.searchHotelList4AI(param);

            LogModel logModel = new LogModel("batchQuerySearchHotel")
                    .request(JSON.toJSONString(param))
                    .response(JSON.toJSONString(hotelListVOResult))
                    .cost(System.currentTimeMillis() - startTime);
            if (hotelListVOResult == null || !hotelListVOResult.isSuccess()) {
                LOGGER.recordDangerException(logModel.success(false).message("batchQuerySearchHotel.fail"));
            } else {
                LOGGER.recordOutput(logModel.message("batchQuerySearchHotel.success"));
            }
            return hotelListVOResult;
        } catch (Exception ex) {
            LOGGER.recordDangerException(new LogModel("batchQuerySearchHotel")
                    .success(false)
                    .message("batchQuerySearchHotel.error")
                    .request(JSON.toJSONString(param))
                    .cost(System.currentTimeMillis() - startTime)
                    .e(ex));
        }
        return null;
    }


    @AteyeInvoker(description = "测试酒店查询服务", paraDesc = "jsonData")
    public void testBatchQuerySearchHotel(String jsonData) {
        SearchHotelListParam searchHotelListParam = JSONObject.parseObject(jsonData, SearchHotelListParam.class);
        Result<SearchHotelListVO> hotelListVOResult = batchQuerySearchHotel(searchHotelListParam);
        Ateye.out.println(hotelListVOResult);
    }
}
