package com.taobao.trip.jourprod.item;

import com.alibaba.fastjson.JSON;
import com.alibaba.fliggy.tuan.common.model.TuanResult;
import com.alibaba.fliggy.tuan.gateway.service.chanelpage.ChanelPageApi;
import com.alibaba.fliggy.tuan.gateway.service.chanelpage.param.QueryItemForItemCardParam;
import com.alibaba.fliggy.tuan.gateway.service.chanelpage.vo.item.ChannelItemVO;
import com.alibaba.fliggy.tuan.gateway.service.chanelpage.vo.menu.QueryItemsForItemCardVO;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 酒店套餐查询服务
 * <AUTHOR>
 */
@Service
public class HotelPackageService {
    private final static FliggyNewLogger logger = LogUtil.getFliggyNewLogger("AiSearchLlmProvider");

    @Resource
    private ChanelPageApi chanelPageApi;

    public List<ChannelItemVO> getHotelPackage(Long userId, List<Long> itemIdList) {
        if (userId == null || CollectionUtils.isEmpty(itemIdList)) {
            throw new IllegalArgumentException("userId or itemIdList is null");
        }
        QueryItemForItemCardParam param = new QueryItemForItemCardParam();
        param.setUserId(userId);
        param.setItemIdList(itemIdList);
        TuanResult<QueryItemsForItemCardVO> result =  chanelPageApi.queryItemsForItemCard(param);
        if (result == null || !result.isSuccess()) {
            logger.recordOutput(new LogModel("getHotelPackage").message("queryItemsForItemCard fail").request(JSON.toJSONString(param)).response(JSON.toJSONString(result)));
            return null;
        }
        QueryItemsForItemCardVO data = result.getData();
        logger.recordOutput(new LogModel("getHotelPackage").message("queryItemsForItemCard success").request(JSON.toJSONString(param)).response(JSON.toJSONString(data)));
        return data.getItemVOList();
    }

    @AteyeInvoker(description = "测试获取酒店套餐", paraDesc = "userId&itemIdList")
    public void testGetHotelPackage(String userId, String itemIdList) {
        List<ChannelItemVO> hotelPackage = getHotelPackage(Long.valueOf(userId), JSON.parseArray(itemIdList, Long.class));
        Ateye.out.println(JSON.toJSONString(hotelPackage));
    }
}
