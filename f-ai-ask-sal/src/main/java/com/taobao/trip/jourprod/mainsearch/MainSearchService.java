package com.taobao.trip.jourprod.mainsearch;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.alitrip.search.tripguide.ai.service.AiQuestionSuggestService;
import com.alitrip.search.tripguide.ai.service.domain.AiQuestionSuggestParam;
import com.alitrip.search.tripguide.ai.service.domain.AiQuestionSuggestResult;
import com.alitrip.search.tripguide.ai.service.domain.AiQuestionSuggestResult.QuestionSuggestVO;
import com.alitrip.search.tripguide.common.base.TgResult;
import com.taobao.trip.jourprod.dto.SugDTO;
import com.taobao.trip.jourprod.utils.JSONUtil;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.taobao.trip.response.sug.InputSugReq;

/**
 * <AUTHOR>
 * @Date 2025/05/06
 **/
@Service
public class MainSearchService {
    private final static FliggyNewLogger log = LogUtil.getFliggyNewLogger(MainSearchService.class);

    @Resource
    private AiQuestionSuggestService aiQuestionSuggestService;

    public List<SugDTO> getInputSug(InputSugReq req) {
        try {
            AiQuestionSuggestParam aiQuestionSuggestParam = new AiQuestionSuggestParam();
            aiQuestionSuggestParam.setKeyword(req.getKeyword());
            aiQuestionSuggestParam.setUserId(String.valueOf(req.getUserId()));
            aiQuestionSuggestParam.setUserCityName(req.getUserCityName());
            aiQuestionSuggestParam.setUserCityCode(req.getUserCityCode());
            TgResult<AiQuestionSuggestResult> aiQuestionSuggestResultTgResult = aiQuestionSuggestService.querySuggest(
                aiQuestionSuggestParam);
            log.recordOutput(new LogModel("getInputSug")
                .success(true)
                .message("getInputSug")
                .request(JSONUtil.toJSONString(aiQuestionSuggestParam)).response(
                    JSONUtil.toJSONString(aiQuestionSuggestResultTgResult)));
            if (aiQuestionSuggestResultTgResult == null || !aiQuestionSuggestResultTgResult.isSuccess()
                || aiQuestionSuggestResultTgResult.getModel() == null) {
                log.recordOutput(new LogModel("getInputSug")
                    .success(false)
                    .message("getInputSug.systemError")
                    .request(JSONUtil.toJSONString(req)).response(JSONUtil.toJSONString(aiQuestionSuggestResultTgResult)));
                return null;
            }
            AiQuestionSuggestResult model = aiQuestionSuggestResultTgResult.getModel();
            return covertRsp(model.getSuggestVOList());

        } catch (Exception e) {
            log.recordDangerException(new LogModel("getInputSug")
                .success(false)
                .message("getInputSug.error")
                .request(JSONUtil.toJSONString(req))
                .e(e));
            return new ArrayList<>();
        }

    }

    private List<SugDTO> covertRsp(List<QuestionSuggestVO> suggestVOList) {
        if (CollectionUtils.isEmpty(suggestVOList)) {
            return new ArrayList<>();
        }
        List<SugDTO> sugList = new ArrayList<>();
        for (QuestionSuggestVO questionSuggestVO : suggestVOList) {
            SugDTO sugDTO = new SugDTO();
            sugDTO.setSuggestName(questionSuggestVO.getSuggestName());
            sugDTO.setJumpUrl(questionSuggestVO.getJumpUrl());
            sugList.add(sugDTO);
        }
        return sugList;
    }
}
