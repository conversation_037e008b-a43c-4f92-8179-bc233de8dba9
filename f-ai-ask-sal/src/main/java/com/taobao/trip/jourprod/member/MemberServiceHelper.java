package com.taobao.trip.jourprod.member;

import com.alibaba.fastjson.JSONObject;
import com.fliggy.ffa.touch.prod.customize.client.common.MemberInfoService;
import com.fliggy.ffa.touch.prod.customize.client.common.param.MemberDTO;
import com.fliggy.ffa.touch.prod.customize.client.common.param.MemberInfoRequest;
import com.fliggy.ffa.touch.shared.model.common.result.FtResult;
import com.fliggy.ffa.touch.shared.model.enums.TripUserField;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 会员基本信息相关（头像）
 * 文档：https://aliyuque.antfin.com/pcbwhg/nmzxwa/ghfov5?singleDoc#hXspI
 *
 * <AUTHOR>
 * @date 2025/04/03
 */
@Component
public class MemberServiceHelper {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(MemberServiceHelper.class);

    @Resource
    private MemberInfoService memberInfoService;

    @AteyeInvoker
    public MemberDTO getByUserIdV2(Long userId) {
        FtResult<MemberDTO> result = null;
        try {
            MemberInfoRequest request = new MemberInfoRequest();
            request.setUserId(userId);
            request.setScene("default-scene");
            request.setFields(Lists.newArrayList(TripUserField.SNS.name(), TripUserField.LEVEL_PICTURE.name(), TripUserField.LEVEL_OR_F0.name()));
            result = memberInfoService.getByUserIdV2(request);
            if (Objects.nonNull(result) && result.isSuccess()) {
                return result.getData();
            }
        } catch (Exception e) {
            LOGGER.recordNormalException(new LogModel("getByUserIdV2").userId(userId).e(e));
        } finally {
            LOGGER.recordOutput(new LogModel("getByUserIdV2").userId(userId).response(JSONObject.toJSONString(result)));
        }
        return null;
    }


    /**
     * 获取用户头像
     *
     * @param userId
     * @return
     */
    public String getAvatarByUserIdV2(String userId) {
        try {
            MemberInfoRequest request = new MemberInfoRequest();
            request.setUserId(Long.valueOf(userId));
            request.setScene("default-scene");
            request.setFields(Lists.newArrayList(TripUserField.SNS.name(), TripUserField.LEVEL_PICTURE.name(), TripUserField.LEVEL_OR_F0.name()));
            FtResult<MemberDTO> result = memberInfoService.getByUserIdV2(request);
            if (Objects.nonNull(result) && result.isSuccess() && result.getData() != null) {
                return result.getData().getAvatar();
            }
            return null;
        } catch (Exception e) {
            LOGGER.recordNormalException(new LogModel("getAvatarByUserIdV2").userId(userId).e(e));
        }
        return null;
    }

}
