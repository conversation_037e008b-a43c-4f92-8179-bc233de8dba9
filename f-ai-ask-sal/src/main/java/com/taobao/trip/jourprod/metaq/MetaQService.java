package com.taobao.trip.jourprod.metaq;

import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.common.message.Message;
import com.taobao.metaq.client.MetaProducer;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/2/13
 */
@Component
public class MetaQService implements InitializingBean, DisposableBean {
    private final MetaProducer producer = new MetaProducer("f-ai-ask");

    public SendResult send(String topic, String tag, String key, byte[] bytes) throws Exception {
        Message msg = new Message(topic, tag, key, bytes);
        return this.producer.send(msg);
    }

    @Override
    public void destroy() throws Exception {
        producer.shutdown();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        producer.start();
    }
}
