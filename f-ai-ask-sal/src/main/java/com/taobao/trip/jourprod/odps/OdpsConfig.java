package com.taobao.trip.jourprod.odps;

import com.aliyun.odps.Instance;
import com.aliyun.odps.Odps;
import com.aliyun.odps.account.Account;
import com.aliyun.odps.account.AliyunAccount;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.task.SQLTask;
import com.aliyun.odps.tunnel.TableTunnel;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: odps配置类
 * @author: huiyi
 * @create: 2021-06-07 14:10
 **/
@Slf4j
public class OdpsConfig {
    private Account account;
    private Odps odps;
    private TableTunnel tableTunnel;
    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(OdpsConfig.class);
    /**
     * 云芃的id
     */
    private static final String ACCESS_ID = "LTAIMnEAZUxc3cOp";
    private static final String ACCESS_KEY = "Unz2918ZN5kWfJCOdQalNBZxgeIror";
    public static final String PROJECT_NAME = "trip_vacation";
    public static final String END_POINT = "http://service.odps.aliyun-inc.com/api";

    private static OdpsConfig instance;

    public static OdpsConfig getInstance() {
        if (instance == null) {
            synchronized (OdpsConfig.class) {
                if (instance == null) {
                    instance = new OdpsConfig();
                }
            }
        }
        return instance;
    }

    private OdpsConfig() {
        account = new AliyunAccount(ACCESS_ID, ACCESS_KEY);
        odps = new Odps(account);
        odps.setEndpoint(END_POINT);
        odps.setDefaultProject(PROJECT_NAME);
        Map<String, String> settings = odps.getGlobalSettings();
        settings.put("odps.sql.submit.mode", "script");
        odps.setGlobalSettings(settings);
        tableTunnel = new TableTunnel(odps);
    }

    public Account getAccount() {
        return account;
    }

    public Odps getOdps() {
        return odps;
    }

    public TableTunnel getTableTunnel() {
        return tableTunnel;
    }

    public static Odps getOdps(String projectName) {
        Account account = new AliyunAccount(ACCESS_ID, ACCESS_KEY);
        Odps odps = new Odps(account);
        odps.setEndpoint(END_POINT);
        Map<String, String> settings = odps.getGlobalSettings();
        settings.put("odps.sql.submit.mode", "script");
        odps.setGlobalSettings(settings);
        odps.setDefaultProject(projectName);

        return odps;
    }

    /**
     * 执行odps sql
     *
     * @param sql
     * @param params
     * @return
     */
    public static List<Map<String, Object>> executeSql(String sql, Map<String, Object> params) {
        List<Map<String, Object>> list = Lists.newArrayList();
        try {
            Odps odps = OdpsConfig.getOdps(PROJECT_NAME);

            //参数判空
            if (params != null && params.size() != 0) {
                for (String key : params.keySet()) {
                    sql = sql.replaceAll("#\\{" + key + "\\}", String.valueOf(params.get(key)));
                }
            }
            Instance instance = SQLTask.run(odps, sql);
            instance.waitForSuccess();
            List<Record> records = SQLTask.getResult(instance);
            for (Record r : records) {
                HashMap<String, Object> map = Maps.newHashMap();
                int columnCount = r.getColumnCount();
                for (int i = 0; i < columnCount; i++) {
                    map.put(r.getColumns()[i].getName(), r.get(i));
                }
                list.add(map);
            }
            return list;
        } catch (Exception e){
            LOGGER.recordDangerException(new LogModel("executeSql error").e(e).message("executeSql error"));
            return list;
        }

    }
}
