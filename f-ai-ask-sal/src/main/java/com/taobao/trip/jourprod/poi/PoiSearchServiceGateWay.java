package com.taobao.trip.jourprod.poi;

import com.alibaba.fastjson.JSON;
import com.alibaba.trip.tripdivision.client.TrdiDivisionReadService;
import com.alibaba.trip.tripdivision.domain.TrdiDivisionDO;
import com.alibaba.trip.tripdivision.domain.enumerate.TrdiDivisionLevel;
import com.google.common.collect.Lists;
import com.taobao.trip.jourprod.dto.DivisionDTO;
import com.taobao.trip.jourprod.utils.PoiUtils;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 酒店查询服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PoiSearchServiceGateWay {

    @Resource
    private TrdiDivisionReadService trdiDivisionReadService;


    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(PoiSearchServiceGateWay.class);

    /**
     * 根据经纬度查询
     *
     * @param lng 经度
     * @param lat 纬度
     * @return
     */
    public DivisionDTO getByLngAndLat(Double lng, Double lat) {
        try {
            List<DivisionDTO> byLngAndLat = getByLongAndLat(lng, lat);
            if (CollectionUtils.isEmpty(byLngAndLat)) {
                return null;
            }
            //找出城市
            return byLngAndLat.stream()
                    .filter(divisionDTO -> Objects.equals(TrdiDivisionLevel.CITY.getCode(), divisionDTO.getLevelEnum()))
                    .findFirst().orElse(null);
        } catch (Throwable e) {
            //这个存在超时为了不影响主业务直接返回null
            LOGGER.recordDangerException(new LogModel("PoiSearchServiceGateWay.getByLngAndLat").e(e).message("查询经纬度失败"));
        }
        return null;

    }

    public List<DivisionDTO> getByLongAndLat(Double lng, Double lat) {

        try {
            if (lng == null || lat == null) {
                return Lists.newArrayList();

            }
            if (lat == PoiUtils.ZERO
                    && lng == PoiUtils.ZERO) {
                return Lists.newArrayList();
            }

            List<TrdiDivisionDO> trdiDivisionDOS = trdiDivisionReadService.geoInverse(lat, lng);
            if (CollectionUtils.isEmpty(trdiDivisionDOS)) {
                return Lists.newArrayList();
            }
            return trdiDivisionDOS.stream().map(this::coverUserGuessDestDTO).collect(Collectors.toList());
        } catch (Throwable e) {
            LOGGER.recordDangerException(new LogModel("PoiSearchServiceGateWay.getByLngAndLat").e(e).message("查询经纬度失败"));
            return Lists.newArrayList();
        }
    }

    private DivisionDTO coverUserGuessDestDTO(TrdiDivisionDO trdiDivisionDO) {
        DivisionDTO divisionDTO = new DivisionDTO();
        divisionDTO.setId(trdiDivisionDO.getId());
        divisionDTO.setName(trdiDivisionDO.getName());
        divisionDTO.setNameEn(trdiDivisionDO.getNameEn());
        divisionDTO.setNameAbbr(trdiDivisionDO.getNameAbbr());
        divisionDTO.setPinyin(trdiDivisionDO.getPinyin());
        divisionDTO.setPy(trdiDivisionDO.getPy());
        divisionDTO.setStatusEnum(trdiDivisionDO.getStatus());
        divisionDTO.setLevelEnum(trdiDivisionDO.getLevelEnum().getCode());
        divisionDTO.setTreeId(trdiDivisionDO.getTreeId());
        divisionDTO.setTreeName(trdiDivisionDO.getTreeName());
        divisionDTO.setParentId(trdiDivisionDO.getParentId());
        divisionDTO.setLongitude(trdiDivisionDO.getLongitude());
        divisionDTO.setLatitude(trdiDivisionDO.getLatitude());
        divisionDTO.setCountryName(trdiDivisionDO.getCountryName());
        divisionDTO.setCountryCode2(trdiDivisionDO.getCountryCode2());
        divisionDTO.setCallingCode(trdiDivisionDO.getCallingCode());
        divisionDTO.setTimezoneid(trdiDivisionDO.getTimezoneid());
        divisionDTO.setAirportCode3(trdiDivisionDO.getAirportCode3());
        divisionDTO.setAbroad(trdiDivisionDO.getAbroad());
        divisionDTO.setEanRegionId(trdiDivisionDO.getEanRegionId());
        divisionDTO.setLastModifier(trdiDivisionDO.getLastModifier());
        divisionDTO.setTravelDivisionId(trdiDivisionDO.getTravelDivisionId());
        divisionDTO.setExtendId(trdiDivisionDO.getExtendId());
        divisionDTO.setCapitalFlag(trdiDivisionDO.getCapitalFlag());
        divisionDTO.setLogicAreaIds(trdiDivisionDO.getLogicAreaIds());
        divisionDTO.setCityCode3(trdiDivisionDO.getCityCode3());
        divisionDTO.setRelationIds(trdiDivisionDO.getRelationIds());
        divisionDTO.setAntAreaCode(trdiDivisionDO.getAntAreaCode());
        divisionDTO.setHDivisionCode(trdiDivisionDO.gethDivisionCode());
        divisionDTO.setMergeId(trdiDivisionDO.getMergeId());
        divisionDTO.setExtDta(JSON.toJSONString(trdiDivisionDO));
        return divisionDTO;
    }

    //@Resource
    //private HotelWirelessQueryService hotelWirelessQueryService;
    //
    //public Result<SearchHotelListVO> batchQuerySearchHotel(SearchHotelListParam param) {
    //    // return hotelWirelessQueryService.searchHotelList4AI(param);
    //    return null;
    //}
}
