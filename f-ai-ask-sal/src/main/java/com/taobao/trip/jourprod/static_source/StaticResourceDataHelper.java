package com.taobao.trip.jourprod.static_source;

import com.alibaba.fastjson.JSONObject;
import com.fliggy.fceadmin.resource.common.StaticResult;
import com.fliggy.fceadmin.resource.service.StaticResourceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 静态资源数据帮助类
 * <AUTHOR>
 */
@Slf4j
@Component
public class StaticResourceDataHelper {

    @Resource
    private StaticResourceService staticResourceService;

    public static final String ADD_RATE_CONFIG_ID = "15604";

    /**
     * 根据配置ID 获取配置，并序列化为指定类型
     * @param configId  配置ID
     * @param clazz     目标类型
     * @return
     * @param <T>
     */
    public <T>T getConfig(String configId, Class<T> clazz) {
        try {
            StaticResult<String> result = staticResourceService.getStaticData(configId);
            if (null == result || !result.isSuccess() || StringUtils.isBlank(result.getModule())) {
                return null;
            }
            String sourceStr = result.getModule();

            if (StringUtils.isBlank(sourceStr)) {
                log.error("getConfig is Empty");
                return null;
            }
            return JSONObject.parseObject(sourceStr, clazz);
        } catch (Exception e) {
            log.error("getConfig Error", e);
        }
        return null;
    }



}
