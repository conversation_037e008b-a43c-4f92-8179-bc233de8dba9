package com.taobao.trip.jourprod.utils;

import com.google.common.base.Joiner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 日志工具
 */
public final class AskMonitorLoggerUtil {

    private AskMonitorLoggerUtil() {
    }

    private static Logger infoLogger = LoggerFactory.getLogger("ASK_MONITOR_LOG");

    private static Logger errorLogger = LoggerFactory.getLogger("APPLICATION_LOG");

    /**
     * 打印正常会话日志
     *
     * @param messageId 消息id
     * @param firstByteCost 首字耗时
     * @param chainCompleteCost 思考链整体耗时
     * @param contentFinishCost 内容完整耗时
     */
    public static void logSuccessMessage(String messageId, long userId, String scene, long firstByteCost, long chainCompleteCost, long contentFinishCost, long chainContentGap) {
        doTraceLog(messageId, userId, scene, firstByteCost, chainCompleteCost, contentFinishCost, false, null, null, chainContentGap);
    }

    /**
     * 打印中断会话日志
     *
     * @param messageId 消息id
     * @param errorCode 错误码
     * @param interruptReason 中断原因
     */
    public static void logInterruptMessage(String messageId, long userId, String scene, String errorCode, String interruptReason, Long firstByteCost, Long chainCompleteCost, Long contentFinishCost, Long chainContentGap) {
        doTraceLog(messageId, userId, scene, firstByteCost, chainCompleteCost, contentFinishCost, true, errorCode, interruptReason, chainContentGap);
    }

    public static void doTraceLog(Object... obj) {
        if (YaceUtil.isOpen()) {
            return;
        }

        try {
            infoLogger.info(Joiner.on("|")
                    .join(Arrays.stream(obj).map(item -> item == null ? "-" : Objects.toString(item)).collect(Collectors.toList())));
        } catch (Exception e) {
            error("doTraceLog exception", e);
        }
    }

    public static void error(String message, Throwable t) {
        if (YaceUtil.isOpen()) {
            return;
        }
        Optional.ofNullable(errorLogger).ifPresent(p -> p.error(message, t));
    }

    public static void error(String message) {
        if (YaceUtil.isOpen()) {
            return;
        }
        Optional.ofNullable(errorLogger).ifPresent(p -> p.error(message));
    }

    public static void error(String message, Object... args) {
        if (YaceUtil.isOpen()) {
            return;
        }
        Optional.ofNullable(errorLogger).ifPresent(p -> p.error(message, args));
    }

    public static void error(String message, Throwable t, Object... args) {
        if (YaceUtil.isOpen()) {
            return;
        }
        Optional.ofNullable(errorLogger).ifPresent(p -> p.error(message, args, t));
    }

    public static void warn(String message, Throwable t) {
        if (YaceUtil.isOpen()) {
            return;
        }
        Optional.ofNullable(errorLogger).ifPresent(p -> p.warn(message, t));
    }

    public static void warn(String message) {
        if (YaceUtil.isOpen()) {
            return;
        }
        Optional.ofNullable(errorLogger).ifPresent(p -> p.warn(message));
    }

    public static void warn(String message, Throwable t, Object... args) {
        if (YaceUtil.isOpen()) {
            return;
        }
        Optional.ofNullable(errorLogger).ifPresent(p -> p.warn(message, args, t));
    }

    public static void info(String message) {
        if (YaceUtil.isOpen()) {
            return;
        }
        Optional.ofNullable(infoLogger).ifPresent(p -> p.error(message));
    }

    public static void info(String message, Object... args) {
        if (YaceUtil.isOpen()) {
            return;
        }
        Optional.ofNullable(infoLogger).ifPresent(p -> p.info(message, args));
    }
}
