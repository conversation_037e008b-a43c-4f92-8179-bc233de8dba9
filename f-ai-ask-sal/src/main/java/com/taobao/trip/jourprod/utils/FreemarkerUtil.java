package com.taobao.trip.jourprod.utils;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.io.StringWriter;
import java.util.Map;

/**
 * @Description Freemarker模板处理工具类
 * <AUTHOR>
 * @Date 2025/1/30
 **/
@Component
public class FreemarkerUtil {
    
    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(FreemarkerUtil.class);
    
    @Resource
    private Configuration freemarkerConfig;

    private static Configuration staticFreemarkerConfig;

    @PostConstruct
    public void init() {
        staticFreemarkerConfig = freemarkerConfig;
    }

    /**
     * 处理模板
     * 
     * @param templateName 模板名称
     * @param dataModel 数据模型
     * @return 渲染结果
     */
    public static String processTemplate(String templateName, Map<String, Object> dataModel) {
        try {
            Template template = staticFreemarkerConfig.getTemplate(templateName);
            StringWriter writer = new StringWriter();
            template.process(dataModel, writer);
            
            String result = writer.toString();
            
            LOGGER.recordOutput(new LogModel("processTemplate")
                .message("模板处理成功，模板: {0}, 结果长度: {1}", templateName, result.length()));
            
            return result;
            
        } catch (IOException e) {
            LOGGER.recordDangerException(new LogModel("processTemplate")
                .message("模板文件读取失败，模板: {0}", templateName)
                .e(e));
            throw new RuntimeException("模板文件读取失败: " + templateName, e);
            
        } catch (TemplateException e) {
            LOGGER.recordDangerException(new LogModel("processTemplate")
                .message("模板处理失败，模板: {0}", templateName)
                .request(JSONUtil.toJSONStringForLog(dataModel))
                .e(e));
            throw new RuntimeException("模板处理失败: " + templateName, e);
        }
    }
    
    /**
     * 处理模板字符串
     * 
     * @param templateContent 模板内容
     * @param dataModel 数据模型
     * @return 渲染结果
     */
    public static String processTemplateString(String templateContent, Map<String, Object> dataModel) {
        try {
            Template template = new Template("stringTemplate", templateContent, staticFreemarkerConfig);
            StringWriter writer = new StringWriter();
            template.process(dataModel, writer);
            
            return writer.toString();
            
        } catch (IOException | TemplateException e) {
            LOGGER.recordDangerException(new LogModel("processTemplateString")
                .message("模板字符串处理失败")
                .request("template: " + templateContent)
                .e(e));
            throw new RuntimeException("模板字符串处理失败", e);
        }
    }
    
    /**
     * 验证模板语法
     * 
     * @param templateName 模板名称
     * @return 是否有效
     */
    public static boolean validateTemplate(String templateName) {
        try {
            staticFreemarkerConfig.getTemplate(templateName);
            return true;
        } catch (IOException e) {
            LOGGER.recordNormalException(new LogModel("validateTemplate")
                .message("模板验证失败，模板: {0}", templateName)
                .e(e));
            return false;
        }
    }
    
}
