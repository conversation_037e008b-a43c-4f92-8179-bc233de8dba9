package com.taobao.trip.jourprod.utils;

import com.fliggy.graph.support.logger.LoggerUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

/**
 * <AUTHOR>
 * @date 2025/4/2
 */
@Slf4j
public class GatewayLoggerUtils {

    public static void info(String format, Object... args) {
        if(inFBird()) {
            LoggerUtils.info(format, args);
        } else {
            log.info(format, args);
        }
    }

    public static void error(String format, Object... args) {
        if(inFBird()) {
            LoggerUtils.error(format, args);
        } else {
            log.error(format, args);
        }
    }

    public static void error(String format, Throwable throwable, Object... args) {
        if(inFBird()) {
            LoggerUtils.error(format, throwable, args);
        } else {
            log.error(format, throwable, args);
        }
    }

    private static boolean inFBird() {
        String graphInstanceId = MDC.get("GRAPH_INSTANCE_ID");
        return graphInstanceId != null && !graphInstanceId.isEmpty();
    }
}
