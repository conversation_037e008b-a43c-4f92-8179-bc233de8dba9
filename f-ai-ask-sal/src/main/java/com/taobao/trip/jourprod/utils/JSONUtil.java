package com.taobao.trip.jourprod.utils;

import java.util.List;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;

import com.google.common.collect.Lists;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

/**
 * json相关工具类
 *
 * <AUTHOR>
 * @date 2123-04-11
 */
public class JSONUtil {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JSONUtil.class);

    private static String toStr(Object obj) {
        if (null == obj) {
            return "null";
        }
        return obj.toString();
    }

    public static String toJSONString(Object object) {
        try {
            return JSON.toJSONString(object);
        } catch (Throwable e) {
            LOGGER.recordNormalException(new LogModel("toJSONString")
                .e(e)
                .request(toStr(object)));
            return "toJSONString error";
        }
    }

    /**
     * object转jsonobject
     * @param object
     * @return 报错直接返回空jsonobject
     */
    public static JSONObject genJSONObjectFromObject(Object object) {
        try {
            return JSON.parseObject(JSON.toJSONString(object));
        } catch (Exception e) {
            LOGGER.recordNormalException(new LogModel("genJSONObjectFromObject")
                .e(e)
                .request(toStr(object)));
            return new JSONObject();
        }
    }

    /**
     * 字符串转json
     * @param jsonString
     * @return
     */
    public static JSONObject genJSONObjectFromString(String jsonString) {
        try {
            return JSON.parseObject(jsonString);
        } catch (Exception e) {
            LOGGER.recordNormalException(new LogModel("genJSONObjectFromString")
                .e(e)
                .request(toStr(jsonString)));
            return new JSONObject();
        }
    }

    /**
     * 字符串转jsonarray
     * @param jsonString
     * @return
     */
    public static JSONArray genJSONArrayFromString(String jsonString) {
        try {
            return JSON.parseArray(jsonString);
        } catch (Exception e) {
            LOGGER.recordNormalException(new LogModel("genJSONArrayFromString")
                .e(e)
                .request(toStr(jsonString)));
            return new JSONArray();
        }
    }

    /**
     * 基于jsonPath从jsonString中解析内容
     * @param jsonObject
     * @param jsonPath
     * @return
     */
    public static String extractContentFromJSONPath(Object jsonObject, String jsonPath) {
        try {
            String json = JSON.toJSONString(jsonObject);
            if (jsonObject instanceof String) {
                json = (String) jsonObject;
            }
            Object extract = JSONPath.extract(json, jsonPath);
            return String.valueOf(extract);
        } catch (Exception e) {
            LOGGER.recordNormalException(new LogModel("extractContentFromJSONPath")
                .e(e)
                .request(toStr(jsonObject)+"_"+toStr(jsonPath)));
            return StringUtils.EMPTY;
        }
    }

    /**
     * 基于json拷贝对象
     */
    public static <T> List<T> copyList(List<T> obj, Class<T> clazz) {
        try {
            String jsonString = JSON.toJSONString(obj);
            return JSON.parseArray(jsonString, clazz);
        } catch (Exception e) {
            LOGGER.recordNormalException(new LogModel("copyList")
                .e(e)
                .request(toStr(obj)+"_"+toStr(clazz)));
            return Lists.newArrayList();
        }
    }

    /**
     * 基于json拷贝对象
     */
    public static <T> T parseObject(Object obj, Class<T> clazz) {
        try {
            String jsonString;
            if (obj instanceof String) {
                jsonString = (String) obj;
            } else {
                jsonString = JSON.toJSONString(obj);
            }
            return JSON.parseObject(jsonString, clazz);
        } catch (Exception e) {
            LOGGER.recordNormalException(new LogModel("parseObject")
                .e(e)
                .request(toStr(obj)+"_"+toStr(clazz)));
            return null;
        }
    }

    /**
     * 解析对象
     */
    public static <T> T parseObject(String jsonString, TypeReference<T> typeReference) {
        try {
            return JSON.parseObject(jsonString, typeReference);
        } catch (Throwable e) {
            LOGGER.recordNormalException(new LogModel("parseObject")
                .e(e)
                .request(toStr(jsonString)+"_"+toStr(typeReference)));
            return null;
        }
    }

    public static void main(String[] args) {

    }

    /**
     * 解析数组
     */
    public static <T> List<T> parseArray(String value, Class<T> clazz) {
        try {
            return JSON.parseArray(value, clazz);
        } catch (Throwable e) {
            LOGGER.recordNormalException(new LogModel("parseArray")
                .e(e)
                .request(toStr(value)+"_"+toStr(clazz)));
            return Lists.newArrayList();
        }
    }

    public static String toJSONString(Object ...object) {
        try {
            return JSON.toJSONString(object);
        } catch (Throwable e) {
            LOGGER.recordNormalException(new LogModel("toJSONString")
                .e(e)
                .request(toStr(object)));
            return "toJSONString error";
        }
    }

    public static String toJSONStringForLog(Object object) {
        try {
            String str = JSON.toJSONString(object, SerializerFeature.DisableCircularReferenceDetect);
            String newStr = removeVerticalBar(str);
            return newStr;
        } catch (Throwable e) {
            LOGGER.recordNormalException(new LogModel("toJSONStringForLog")
                .e(e)
                .request(toStr(object)));
            return "toJSONStringForLog error";
        }
    }

    public static String toJSONStringForLog(Object ...object) {
        try {
            String str = JSON.toJSONString(object);
            String newStr = removeVerticalBar(str);
            return newStr;
        } catch (Throwable e) {
            LOGGER.recordNormalException(new LogModel("toJSONStringForLog")
                .e(e)
                .request(toStr(object)));
            return "toJSONStringForLog error";
        }
    }

    @NotNull
    public static String removeVerticalBar(String str) {
        if(null == str){
            return null;
        }
        StringBuilder sb = new StringBuilder(str.length());
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (c == '|') {
                sb.append("¦");
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }

    /**
     * 获取jsonObject中的值
     * @param object jsonObject
     * @param key 值路径，如 data.collectData.bizId
     * @return 可以提取到返回对应的objct，否则返回空
     */
    public static Object getObject(JSONObject object, String key) {
        String[] keys;
        // key中包含.则需要递归获取
        if (key.contains(".")) {
            keys = key.split("\\.");
        } else {
            keys = new String[]{key};
        }
        Object result = object;
        // 递归获取，遇到空直接返回
        for (String k : keys) {
            if (result == null) {
                return null;
            }
            if (!(result instanceof JSONObject)) {
                return null;
            }
            result = ((JSONObject) result).get(k);
        }
        return result;
    }

}
