package com.taobao.trip.jourprod.utils;


import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

/**
 * poi工具类
 * <AUTHOR>
 */
public class PoiUtils {

    public static final double ZERO = 0.0d;

    /**
     * 地球半径
     */
    private static double EARTH_RADIUS = 6371.393;

    /**
     * 计算两个poi之间的距离, 结果单位:米
     * @param lat1
     * @param lng1
     * @param lat2
     * @param lng2
     * @return
     */
    public static double getDistance(double lat1, double lng1, double lat2, double lng2) {
        double radLat1 = Math.toRadians(lat1);
        double radLat2 = Math.toRadians(lat2);
        double a = radLat1 - radLat2;
        double b = Math.toRadians(lng1) - Math.toRadians(lng2);
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2)
                + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
        return Math.round(s * EARTH_RADIUS * 1000);
    }

    public static boolean validLngLat(String longitude, String latitude) {
        if (StringUtils.isAnyBlank(longitude, latitude)) {
            return false;
        }
        double lng = NumberUtils.toDouble(longitude);
        double lat = NumberUtils.toDouble(latitude);
        if (lng == ZERO && lat == ZERO) {
            return false;
        }
        return lng >= -180.0D && lng <= 180.0D && lat >= -90.0D && lat <= 90.0D;
    }

}
