<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.taobao.trip.jourprod</groupId>
		<artifactId>f-ai-ask</artifactId>
		<version>1.0.0</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>f-ai-ask-service</artifactId>
	<packaging>jar</packaging>
	<name>f-ai-ask-service</name>

	<dependencies>
		<dependency>
			<groupId>com.jayway.jsonpath</groupId>
			<artifactId>json-path</artifactId>
			<version>2.9.0</version> <!-- 建议使用最新版本 -->
		</dependency>

		<!-- JsonPath 需要一个 JSON 处理库，例如 json-smart (默认) -->
		<dependency>
			<groupId>net.minidev</groupId>
			<artifactId>json-smart</artifactId>
			<version>2.5.0</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba.boot</groupId>
			<artifactId>pandora-hsf-spring-boot-starter</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.taobao.config.client.helper</groupId>
					<artifactId>config-client-helper</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- 明确指定 config-client-helper 版本 -->
		<dependency>
			<groupId>com.taobao.config.client.helper</groupId>
			<artifactId>config-client-helper</artifactId>
			<version>1.4</version>
		</dependency>
		<dependency>
			<groupId>com.taobao.csi</groupId>
			<artifactId>csi-common2</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.trip.jourprod</groupId>
			<artifactId>f-ai-ask-dal</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.trip.jourprod</groupId>
			<artifactId>f-ai-ask-sal</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.trip.jourprod</groupId>
			<artifactId>f-ai-ask-chat</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.trip.jourprod</groupId>
			<artifactId>f-ai-ask-core</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>f-ai-ask-sal</artifactId>
					<groupId>com.taobao.trip.jourprod</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.boot</groupId>
			<artifactId>pandora-tair-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.boot</groupId>
			<artifactId>pandora-tddl-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>org.mybatis.spring.boot</groupId>
			<artifactId>mybatis-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.trip.wireless</groupId>
			<artifactId>tripw-common</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>commons-lang3</artifactId>
					<groupId>org.apache.commons</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.taobao.trip</groupId>
			<artifactId>tripjourneyop-service-facade</artifactId>
			<version>${tripjourneyop.service.facade.version}</version>
		</dependency>
		<dependency>
			<groupId>com.taobao.trip.wireless</groupId>
			<artifactId>ssif-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.trippedia.tripplan</groupId>
			<artifactId>planengine-domain</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alitrip.recommend</groupId>
			<artifactId>fliggy-recommend-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.boot</groupId>
			<artifactId>pandora-switchcenter-spring-boot-starter</artifactId>
		</dependency>
		<!-- 日志工具 -->
		<dependency>
			<groupId>fliggy.content</groupId>
			<artifactId>utils</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.trip.trippoi</groupId>
			<artifactId>fliggypoi-client</artifactId>
		</dependency>
		<!--新行政区划-->
		<dependency>
			<groupId>com.alibaba.trip.tripdivision</groupId>
			<artifactId>tripdivision-client</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>mtop-hsf-agent</artifactId>
					<groupId>com.taobao.wireless</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.alibaba.trippoi</groupId>
			<artifactId>trippoiadmin-facade</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.trippoi</groupId>
			<artifactId>trippoiadmin-domain</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>dashscope-sdk-java</artifactId>
		</dependency>
		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>4.12.0</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba.fliggypoi</groupId>
			<artifactId>poi-dal</artifactId>
		</dependency>
		<!-- 圈人平台-->
		<dependency>
			<groupId>com.fliggy</groupId>
			<artifactId>crowd-client</artifactId>
		</dependency>
		<!--会员相关-->
		<dependency>
			<groupId>com.fliggy.ffalevel</groupId>
			<artifactId>ffalevel-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fliggy.ffalevel</groupId>
			<artifactId>ffalevel-model</artifactId>
		</dependency>
		<!--青鸟编排-->
		<dependency>
			<groupId>com.fliggy.graph</groupId>
			<artifactId>fliggy-graph-springboot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
		</dependency>
		<!-- 抖音deepseek模型sdk -->
		<dependency>
			<groupId>com.volcengine</groupId>
			<artifactId>volcengine-java-sdk-ark-runtime</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.whale</groupId>
			<artifactId>whale-sdk</artifactId>
		</dependency>
		<!-- FAI sdk -->
		<dependency>
			<groupId>com.fliggy.fai</groupId>
			<artifactId>fai-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.tripca</groupId>
			<artifactId>tripca-facade</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fliggy.forge</groupId>
			<artifactId>forge-client</artifactId>
		</dependency>
		<!-- 火车票时刻表查询 -->
		<dependency>
			<groupId>com.taobao.trip.wireless</groupId>
			<artifactId>wtrs-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.trip.wireless</groupId>
			<artifactId>hwsearch-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.ateye</groupId>
			<artifactId>ateye-client</artifactId>
		</dependency>
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-engine</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-api</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.csp</groupId>
			<artifactId>sentinel-resource-hook</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.trip.wireless</groupId>
			<artifactId>tripw-sensible-if-client</artifactId>
		</dependency>
		<!-- FAI网关服务 -->
		<dependency>
			<groupId>com.alitrip.ai</groupId>
			<artifactId>tripalgo-llm-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.fliggy.magnet</groupId>
			<artifactId>magnet-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fliggy.magnet</groupId>
			<artifactId>magnet-domain</artifactId>
		</dependency>

		<dependency>
			<groupId>com.fliggy.flyway</groupId>
			<artifactId>flyway-model</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
		</dependency>

		<dependency>
			<groupId>com.fliggy.flyway</groupId>
			<artifactId>flyway-client</artifactId>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

        <dependency>
            <groupId>com.alitrip.tpp</groupId>
            <artifactId>ai-xsearch-model</artifactId>
        </dependency>

		<dependency>
			<groupId>com.taobao.travel</groupId>
			<artifactId>travelvc-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.taobao.travel</groupId>
			<artifactId>travelvc-common</artifactId>
		</dependency>

		<dependency>
			<groupId>com.fliggy.aiworks</groupId>
			<artifactId>aiworks-client</artifactId>
		</dependency>

        <dependency>
            <groupId>com.taobao.tripwf</groupId>
            <artifactId>tripwf-client</artifactId>
        </dependency>
		<dependency>
			<groupId>com.taobao.trip.hotelsearch</groupId>
			<artifactId>platform-model</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.tripwf</groupId>
			<artifactId>tripwf-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.trip.jourprod</groupId>
			<artifactId>f-ai-ask-facade</artifactId>
		</dependency>

		<dependency>
			<groupId>com.taobao.tripmdd</groupId>
			<artifactId>tripmdd-facade</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.boot</groupId>
			<artifactId>pandora-diamond-spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct</artifactId>
			<version>1.5.5.Final</version>
		</dependency>

		<!-- 会员基础信息 -->
		<dependency>
			<groupId>com.fliggy.ffa.touch</groupId>
			<artifactId>ffa-customize-touch-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.taobao.trip.jourprod</groupId>
			<artifactId>tripwjourprod-global-facade</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.tripc.logicengine</groupId>
			<artifactId>logicengine-facade</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.tripc.logicengine</groupId>
			<artifactId>logicengine-facade</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.pandora</groupId>
			<artifactId>pandora-boot-test</artifactId>
			<scope>test</scope>
		</dependency>
		
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>3.2.2</version>
				<configuration>
					<systemPropertyVariables>
						<!-- 禁用 EagleEye 在测试环境中的加载 -->
						<eagleeye.disable>true</eagleeye.disable>
						<pandora.location></pandora.location>
						<JM.LOG.PATH>target/logs</JM.LOG.PATH>
						<!-- 禁用其他可能冲突的中间件组件 -->
						<hsf.server.port>-1</hsf.server.port>
						<hsf.publish.delayed>true</hsf.publish.delayed>
					</systemPropertyVariables>
					<argLine>
						-Deagleeye.disable=true
						-Dpandora.location=
						-Dhsf.server.port=-1
						-Dhsf.publish.delayed=true
						-Djava.awt.headless=true
						-Dfile.encoding=UTF-8
						-Dmaven.test.skip.exec=false
					</argLine>
					<!-- 使用自定义类加载器避免依赖冲突 -->
					<useSystemClassLoader>false</useSystemClassLoader>
					<useManifestOnlyJar>false</useManifestOnlyJar>
					<!-- 排除一些可能导致问题的测试类 -->
					<excludes>
						<exclude>**/*IntegrationTest.java</exclude>
					</excludes>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
