package com.taobao.trip.jourdprod.core.model.common;

import com.google.common.collect.Maps;
import java.util.Map;
import org.springframework.stereotype.Service;

/**
 * Created by mojin on 2018/9/7.
 */
@Service
public class ConstantElement {

    public static final String CHINA_COUNTRY_NAME = "中国";
    public static final Long CHINA_COUNTRY_ID = 100000L;

    /** APP名称 **/
    public static final String APP_NAME = "tripwjourprod";

    /**
     * 场景id
     */
    public static final class PLATFORM_SCENE_ID {
        public static final String FLIGGY_HOME = "21001";
        public static final String JOURNEY_ASSISTANT = "36001";
        public static final String JOURNEY_HOME_NEW = "39001";
        }

    public static final Map<String, Long> JOURNEY_MILE_ACTIVITY_ID_MAP = Maps.newHashMap();
    static {
        JOURNEY_MILE_ACTIVITY_ID_MAP.put(PLATFORM_SCENE_ID.JOURNEY_ASSISTANT, 10156L);
    }
    public static final Map<String, Map<String, Long>> JOURNEY_MILE_TASK_ID_MAP = Maps.newHashMap();
    static {
        Map<String, Long> HOME_TASK_ID_MAP = Maps.newHashMap();
        // 返乡政策
        HOME_TASK_ID_MAP.put("ITEM161856277265084", 8002L);
        // 景点查询
        HOME_TASK_ID_MAP.put("ITEM162398307487445", 8003L);
//        // 航班动态
//        HOME_TASK_ID_MAP.put("ITEM162398325928786", 8004L);
        // 旅行贴士
        HOME_TASK_ID_MAP.put("ITEM162446353113117", 8004L);
        // 里程中心
        HOME_TASK_ID_MAP.put("ITEM162398356108102", 8005L);
        JOURNEY_MILE_TASK_ID_MAP.put(PLATFORM_SCENE_ID.JOURNEY_ASSISTANT, HOME_TASK_ID_MAP);
    }

    /**
     * 高德请求常量key管理
     */
    public static String AMAP_REQUEST_AUTH_TAIR_KEY = "AMAP_REQUEST_AUTH_TAIR_KEY_";
    // 交通
    public static String AMAP_TRANSIT = "TRANSIT";
    // 步行
    public static String AMAP_WALKING = "WALKING";
    // 开车
    public static String AMAP_DRIVING = "DRIVING";
    //搜索
    public static String AMAP_SEARCH = "SEARCH";

}