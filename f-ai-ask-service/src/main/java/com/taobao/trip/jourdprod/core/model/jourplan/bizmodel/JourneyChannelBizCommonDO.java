package com.taobao.trip.jourdprod.core.model.jourplan.bizmodel;

import com.taobao.trip.jourprod.service.facade.model.response.JourPlanError;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class JourneyChannelBizCommonDO implements Serializable {

    private static final long serialVersionUID = 6790267896840311184L;

    private boolean success;

    private JourPlanError planError;

}
