package com.taobao.trip.jourdprod.core.model.jourplan.bizmodel;

import com.alibaba.fliggypoi.query.PoiPhotographQuery;
import com.alibaba.trip.tripdivision.client.vo.TrdiDivisionExtendVO;
import com.alibaba.trip.tripdivision.domain.TrdiDivisionDO;
import com.alibaba.trip.trippoi.domain.TrpoPoiDO;

import com.taobao.pushcenter.domain.weather.WeatherForecastInfo;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplan.JourneyChannelQueryV3Request;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplan.newChannel.JourneySubAuth;
import com.taobao.trip.tripjourneyop.domain.JourneyIndexInfo;
import com.taobao.trip.tripjourneyop.journeyplatform.domain.CarrierServiceDetailModel;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JourneyChannelBizV3DO extends JourneyChannelBizCommonDO {

    private static final long serialVersionUID = 8084888562470226331L;

    /**
     * 频道页请求参数
     */
    private JourneyChannelQueryV3Request requestParam;

    /**
     * 授权信息
     */
    private JourneySubAuth journeySubAuth;

    /**
     * 最后一张卡片的数据
     */
    private String lastCardId;

    /**
     * 是否还有数据可继续查询
     */
    private boolean hasNext;

    /**
     * 一页行程包数据
     */
    private List<JourneyIndexInfo> indexInfoList;

    /**
     * 一页行程包数据，历史行程
     */
    private List<JourneyIndexInfo> hisIndexInfoList;

    /**
     * 订单详情
     */
    private Map<String, CarrierServiceDetailModel> detailModelMap;

    /**
     * 订单详情
     */
    private Map<String, CarrierServiceDetailModel> hisDetailModelMap;

    /**
     * KEY：cityCode
     * value:天气信息
     */
    private Map<String, WeatherForecastInfo> cityCodeWeatherInfoMap;
    /**
     * poi详情
     */
    private Map<String, TrpoPoiDO> poiDOMap;

    /**
     * 用户当前所在地
     */
    private TrdiDivisionDO currentLocation;

    /**
     * 已经删除的服务卡
     */
    private Map<String, List<String>> deletedServiceCardMap;

    /**
     * 拍照攻略
     */
    private Map<Long, PoiPhotographQuery> poiPhotographQueryMap;

    /**
     * poi是否命中地理围栏
     */
    private Map<Long, Boolean> poiHitFenceMap;



    /**
     * 套餐的下一页 是第几页
     * -只针对酒店套餐
     */
    private Boolean hasNextTaoCanPage;

    /**
     * 套餐的下一页 是第几页
     * -只针对酒店套餐
     */
    private Integer nextTaoCanPage;

    /**
     * 代订订单数量
     */
    private Integer subOrderNum;

    /**
     * 卡片id -> 行程管家服务卡
     */
    private Map<String, String> cardIdToJourneyManagerUrlMap;

    /**
     * lbs对应行政区划信息
     */
    private TrdiDivisionExtendVO lbsDivisionInfo;

    /**
     * 第一段境外行程对应国家
     */
    private Long firstAbroadCountry;

    /**
     * 第一个国外行程
     */
    private JourneyIndexInfo firstAbroadIndex;

    /**
     * 群聊目的地id
     */
    private String chatDestinationId;

    /**
     * 群聊目的地
     */
    private TrdiDivisionExtendVO chatDestination;

    /**
     * 群聊出发时间
     */
    private Date chatDestinationTime;

    /**
     * 群聊关联的行程
     */
    private JourneyIndexInfo chatJourney;

    /**
     * 判断是不是v4版本
     */
    private Boolean isV4Version;
}
