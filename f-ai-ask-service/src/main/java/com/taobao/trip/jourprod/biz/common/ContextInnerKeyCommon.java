package com.taobao.trip.jourprod.biz.common;

/**
 * <AUTHOR>
 * @Date 2025/03/27
 **/
public class ContextInnerKeyCommon {

    // 形成卡片，每日poi卡片
    public static final String DAILY_POI_LIST = "dailyPoiList";

    /**
     * 标题
     */
    public static final String TITLE = "journeyTitle";

    /**
     * 游玩卡
     */
    public static final String ROUTE_MAP_IMG = "routeMapImg";

    /**
     * 酒店
     */
    public static final String JOURNEY_HOTEL = "journeyHotel";

    /**
     * 交通
     */
    public static final String JOURNEY_TRAFFIC = "journeyTraffic";

    /**
     * 宝贝类型
     */
    public static final String JOURNEY_ITEM_TYPE = "journeyItemType";

    /**
     * 地图跳转链接
     */
    public static final String MAP_JUMP_URL = "mapJumpUrl";

    /**
     * 市内公交费用
     */
    public static final String JOURNEY_BUS_COST = "journeyBusCost";

    /**
     * 路线地图卡片ID
     */
    public static final String JOURNEY_ROUTE_MAP_CARD_ID = "journeyRouteMapCardId";

    /**
     * 快捷预订标
     */
    public static final String JOURNEY_FAST_RESERVE_TAG = "journeyFastReserveTag";

    /**
     * 快捷预订卡片ID
     */
    public static final String JOURNEY_FAST_RESERVE_CARD_ID = "journeyFastReserveCardId";

    /**
     * 预算总计费用
     */
    public static final String JOURNEY_BUDGET_TOTAL_COST = "journeyBudgetTotalCost";
}
