package com.taobao.trip.jourprod.biz.common;

import java.util.Objects;

import com.taobao.trip.jourprod.common.sal.hsf.config.Switcher;
import org.apache.commons.lang.BooleanUtils;

public enum DrawMapSceneType {

    /**
     * 单城单天
     */
    SINGLE_CITY_ONE_DAY("SINGLE_CITY_ONE_DAY", "单城单天"),

    /**
     * 单城两天
     */
    SINGLE_CITY_TWO_DAY("SINGLE_CITY_TWO_DAY", "单城两天"),

    /**
     * 单城多天
     */
    SINGLE_CITY_MULTI_DAY("SINGLE_CITY_MULTI_DAY", "单城多天"),

    /**
     * 多城多天（多城市、多航段）
     */
    MULTI_CITY_MULTI_DAY("MULTI_CITY_MULTI_DAY", "多城多天"),

    /**
     * 多城单天
     */
    MULTI_CITY_ONE_DAY("MULTI_CITY_ONE_DAY", "多城单天"),

    DEFAULT("DEFAULT", "默认走兜底地图。");

    private final String code;
    private final String desc;

    DrawMapSceneType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DrawMapSceneType buildType(int citySize, int day) {

        if (Objects.equals(2, day) && Objects.equals(citySize, 1)) {
            return SINGLE_CITY_TWO_DAY;
        }

        if (Objects.equals(1, day) && Objects.equals(citySize, 1)) {
            return SINGLE_CITY_ONE_DAY;
        }
        if (citySize > 1 && day < Switcher.AI_ASK_DAY_MORE) {
            return MULTI_CITY_MULTI_DAY;
        }
        if (Objects.equals(citySize,1) && (Switcher.SINGLE_CITY_MULTI_DAY_DAY_NUM_MIN <= day && day <= Switcher.SINGLE_CITY_MULTI_DAY_DAY_NUM_MAX)) {
            return SINGLE_CITY_MULTI_DAY;
        }
        return DEFAULT;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据 code 获取对应的枚举值
     *
     * @param code 英文标识符
     * @return 对应的枚举项
     * @throws IllegalArgumentException 如果 code 无效
     */
    public static DrawMapSceneType of(String code) {
        for (DrawMapSceneType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 覆盖 toString 方法，返回中文描述
     */
    @Override
    public String toString() {
        return "code: " + this.code + " desc: " + this.desc;
    }
}
