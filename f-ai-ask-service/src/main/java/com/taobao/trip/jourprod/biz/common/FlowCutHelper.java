package com.taobao.trip.jourprod.biz.common;

import javax.annotation.Resource;

import com.taobao.trip.jourprod.common.sal.hsf.crowds.TripCrowdService;
import fliggy.content.model.FliggyLogger;
import fliggy.content.utils.LogUtil;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/6/7
 */
@Component
public class FlowCutHelper {

    private static final FliggyLogger logger = LogUtil.getFliggyLogger(FlowCutHelper.class);

    @Resource
    private TripCrowdService tripCrowdService;


    /**
     * 是否没有命中切流比例
     * @param userId
     * @return
     */
    public static boolean notHitFlowCut(String userId, List<String> whiteUserList, Integer cut) {
        return !isHitFlowCut(userId, whiteUserList, cut);
    }

    /**
     * 是否是命中切流比例
     */
    public boolean isHitFlowCut(String userId, List<String> whiteUserList, Integer cut, Long crowdId) {
        if (StringUtils.isBlank(userId)) {
            return false;
        }

        if (BooleanUtils.isTrue(tripCrowdService.isUserInCrowd(NumberUtils.toLong(userId), crowdId))) {
            return true;
        }

        return isHitFlowCut(userId, whiteUserList, cut);
    }

    /**
     * 是否是命中切流比例
     * @param userId
     * @return
     */
    public static boolean isHitFlowCut(String userId, List<String> whiteUserList, Integer cut) {
        try {
            if (whiteUserList.contains(userId)) {
                return true;
            }
            long userIdNum = NumberUtils.toLong(userId);
            long rand = userIdNum % 100;
            if (rand < cut) {
                return true;
            }
        } catch (Exception e) {
            logger.recordDangerException("isHitFlowCut", e, "flow cut is error, userId={}", userId);
        }
        return false;
    }

    /**
     * 结合source userid 判断是否命中切流比例
     * 如果没有传source，默认做切流
     * @param userId        当前userid
     * @param whiteUserList 灰度名单
     * @param cut           切流比例
     * @param source        请求来源
     * @param sourceList    需要切流的source
     * @return
     */
    public static boolean isHitFlowCutBySource(String userId, List<String> whiteUserList, Integer cut, String source, String sourceList) {
        try {
            if (StringUtils.isBlank(source)) {
                return isHitFlowCut(userId, whiteUserList, cut);
            }
            List<String> sources = Arrays.stream(sourceList.split(",")).collect(Collectors.toList());
            if (!sources.contains(source)) {
                return false;
            }
            return isHitFlowCut(userId, whiteUserList, cut);
        } catch (Exception e) {
            logger.recordDangerException("isHitFlowCutBySource", e, "flow cut is error, userId={}", userId);
        }
        return false;
    }

}
