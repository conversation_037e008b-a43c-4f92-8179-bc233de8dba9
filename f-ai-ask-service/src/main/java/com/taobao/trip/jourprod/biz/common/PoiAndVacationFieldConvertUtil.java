package com.taobao.trip.jourprod.biz.common;

import com.ali.com.google.common.collect.Lists;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl.JourneyPlanNodeProcessor;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.TagCardVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl.PoiNodeProcessor.BUSINESS_TAG_COLOR_MAP;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl.PoiNodeProcessor.POI_LIST_RANK_ICON;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl.PoiNodeProcessor.SHOP_HONOR_ENABLE_ICON;

public class PoiAndVacationFieldConvertUtil {

    public static String buildFuzzyUv(String uvStr) {
        if (!StringUtils.isNumeric(uvStr)) {
            return null;
        }
        int uv = Integer.parseInt(uvStr);
        if (uv < 1000) {
            return null;
        }
        if (uv < 10000) {
            return String.format("%d人感兴趣", uv);
        }
        return String.format("%.1f万+人感兴趣", uv / 10000.0);
    }

    public static String buildFuzzySold(Integer sold) {
        if (sold == null) {
            return null;
        }
        if (sold > 1000000) {
            return String.format("已售%d00万+", sold / 1000000);
        }
        if (sold > 10000) {
            return String.format("已售%d万+", sold / 10000);
        }
        if (sold > 1000) {
            return String.format("已售%d000+", sold / 1000);
        }
        if (sold > 100) {
            return String.format("已售%d00+", sold / 100);
        }
        return String.format("已售%d", sold);
    }

    public static List<TagCardVO> buildTags(JSONArray tags) {
        if (CollectionUtils.isEmpty(tags)) {
            return null;
        }
        List<TagCardVO> tagCardVOS = Lists.newArrayList();
        for (int i = 0; i < tags.size(); i++) {
            JSONObject tag = tags.getJSONObject(i);
            String text = tag.getString("text");
            String color = BUSINESS_TAG_COLOR_MAP.get(text);
            if (StringUtils.isNotBlank(color)) {
                tagCardVOS.add(TagCardVO.builder().text(text).color(color).build());
            }
        }
        return tagCardVOS;
    }


    public static JourneyPlanNodeProcessor.ListRank buildListRank(JSONObject listRank) {
        if (listRank == null || listRank.getString("poi_list_name") == null || listRank.getString("rank") == null) {
            return null;
        }
        String poiListName = listRank.getString("poi_list_name");
        String rank = listRank.getString("rank");
        return JourneyPlanNodeProcessor.ListRank.builder().text(poiListName + "第" + rank + "名").icon(POI_LIST_RANK_ICON).build();
    }


    public static List<TagCardVO> buildShopHonorInfo(Object shopHonorInfosObj) {
        if (shopHonorInfosObj == null) {
            return null;
        }
        JSONArray shopHonorInfos = JSONArray.parseArray(shopHonorInfosObj.toString());
        if (CollectionUtils.isEmpty(shopHonorInfos)) {
            return null;
        }
        List<TagCardVO> tagCardVOS = Lists.newArrayList();
        for (int i = 0; i < shopHonorInfos.size(); i++) {
            JSONObject shopHonorInfo = shopHonorInfos.getJSONObject(i);
            String icon = shopHonorInfo.getString("icon");
            if (StringUtils.isBlank(icon) || !SHOP_HONOR_ENABLE_ICON.contains(icon)) {
                continue;
            }
            tagCardVOS.add(TagCardVO.builder().icon(icon).build());
        }
        return tagCardVOS;
    }

    public static TagCardVO buildShopInfo(Object shopInfosObj) {
        if (shopInfosObj == null) {
            return null;
        }
        JSONArray shopInfos = JSONArray.parseArray(shopInfosObj.toString());
        if (CollectionUtils.isEmpty(shopInfos)) {
            return null;
        }
        JSONObject shopInfo = shopInfos.getJSONObject(0);
        String text = shopInfo.getString("text");
        if (StringUtils.isBlank(text)) {
            return null;
        }
        return TagCardVO.builder().text(text).build();
    }
}
