package com.taobao.trip.jourprod.biz.common;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2025/05/07
 **/
public class StringCommonUtils {

    private static final Pattern CHINESE_PATTERN = Pattern.compile("[\\u4e00-\\u9fa5]");

    /**
     * 判断字符串是否包含至少一个中文汉字
     *
     * @param word 待检测的字符串
     * @return 如果包含中文汉字，返回 true；否则返回 false
     */
    public static boolean checkContainChineseChar(String word) {
        if (word == null || word.isEmpty()) {
            return false;
        }
        Matcher m = CHINESE_PATTERN.matcher(word);
        return m.find();
    }

}
