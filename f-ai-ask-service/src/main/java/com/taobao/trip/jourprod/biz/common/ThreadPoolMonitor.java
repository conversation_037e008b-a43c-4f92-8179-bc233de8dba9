package com.taobao.trip.jourprod.biz.common;

import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import java.util.concurrent.ThreadPoolExecutor;

import static com.taobao.trip.jourprod.common.sal.hsf.config.Switcher.IS_STOP_MONITOR;
import static com.taobao.trip.jourprod.common.sal.hsf.config.Switcher.MONITOR_SLEEP_TIME;

/**
 * @DECLARATION: THE AUTHORITY OF CODE BELONGS TO ENTERPRISE TECHNOLOGY GROUP OF FEIZHU
 * @NOTE: 线程池工具类，监视ThreadPoolExecutor执行情况
 * @AUTHOR: benjamin.zhm
 * @DATE: 2023/3/21
 **/
public class ThreadPoolMonitor implements Runnable{

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(ThreadPoolMonitor.class);
    /**
     * 线程池
     */
    private final ThreadPoolExecutor executor;

    /**
     * 线程池名字
     */
    private String name = "";

    public ThreadPoolMonitor(ThreadPoolExecutor executor, String name){
        this.executor = executor;
        this.name = name;
    }

    public void run(){
        while(!IS_STOP_MONITOR){
            LOGGER.recordKeyPath(new LogModel(name)
                    .message(String.format("[monitor] [%d/%d] Active: %d, Completed: %d, queueSize: %d, Task: %d, isShutdown: %s, isTerminated: %s",
                            this.executor.getPoolSize(), this.executor.getCorePoolSize(), this.executor.getActiveCount(),
                            this.executor.getCompletedTaskCount(), this.executor.getQueue().size(), this.executor.getTaskCount(),
                            this.executor.isShutdown(), this.executor.isTerminated())));
            try {
                Thread.sleep(MONITOR_SLEEP_TIME);
            } catch (InterruptedException e) {
                // ignore
            }
        }
    }
}