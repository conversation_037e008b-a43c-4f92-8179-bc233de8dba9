package com.taobao.trip.jourprod.biz.frt;

import com.taobao.tripmdd.facade.client.TripmddDestinationListService;
import com.taobao.tripmdd.facade.model.destination.DestinationInfo;
import com.taobao.tripmdd.facade.result.BaseResult;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class MddService {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(MddService.class);


    @Resource
    private TripmddDestinationListService tripmddDestinationListService;

    public boolean isInWhiteList(Long code) {
        try {
            if (Objects.isNull(code)) {
                return false;
            }
            BaseResult<List<DestinationInfo>> listBaseResult = tripmddDestinationListService.queryWhiteDestinationList();
            if (Objects.nonNull(listBaseResult) && CollectionUtils.isNotEmpty(listBaseResult.getData())) {
                List<DestinationInfo> data = listBaseResult.getData();
                Set<Long> whiteSet = data.stream().map(DestinationInfo::getDestinationCode).collect(Collectors.toSet());
                return whiteSet.contains(code);
            }
        } catch (Exception e) {
            LOGGER.recordNormalException(new LogModel("isInWhiteList")
                    .request(String.valueOf(code))
                    .e(e));
        }
        return false;
    }
}
