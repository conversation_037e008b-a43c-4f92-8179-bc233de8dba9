package com.taobao.trip.jourprod.biz.frt.eas;

import com.taobao.trip.jourprod.biz.frt.eas.model.PredictAnalysisData;
import com.taobao.trip.jourprod.biz.frt.eas.model.PredictAnalysisForDivisionData;
import java.util.List;

/**
 * eas查询
 */
public interface PredictQueryClient {

    /**
     * 调取算法获取结果
     * @param text
     * @return
     */
    List<PredictAnalysisData> getAnalysisInfo(String text, Integer type);

    /**
     * 调取算法获取结果，行政区划列表
     * @param text
     * @return
     */
    List<PredictAnalysisForDivisionData> getAnalysisInfoForDivisionList(String text, Integer divLevel);

}
