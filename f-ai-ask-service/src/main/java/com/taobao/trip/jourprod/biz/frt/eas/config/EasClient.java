package com.taobao.trip.jourprod.biz.frt.eas.config;

import javax.annotation.PostConstruct;

import com.aliyun.openservices.eas.predict.http.HttpConfig;
import com.taobao.trip.jourprod.biz.frt.eas.impl.JourprodHttpAsyncClients;
import org.springframework.stereotype.Component;

@Component
public class EasClient {

    private JourprodHttpAsyncClients predictClient;

    private JourprodHttpAsyncClients predictDivisionClient;

    public static final String ACCESS_TOKEN = "yDmp1mLyf2UveOOUzJL3gKjialin111";
    public static final String ENDPOINT = "eas-zhangbei-b-intranet.alibaba-inc.com";
    public static final String MODEL_NAME = "text_poi_extract";

    public static final String MODEL_DIVISION_NAME = "text_division_extract";
    public static final String ACCESS_TOKEN_DIVISION = "yDmp1mLyf2UveOOUzJL3gKjialin111";


    @PostConstruct
    public void init() {
        predictClient = new JourprodHttpAsyncClients(new HttpConfig());
        predictClient.setToken(ACCESS_TOKEN);
        predictClient.setEndpoint(ENDPOINT);
        predictClient.setModelName(MODEL_NAME);

        predictDivisionClient = new JourprodHttpAsyncClients(new HttpConfig());
        predictDivisionClient.setToken(ACCESS_TOKEN_DIVISION);
        predictDivisionClient.setEndpoint(ENDPOINT);
        predictDivisionClient.setModelName(MODEL_DIVISION_NAME);
    }


    /**
     * 获取eas客户端，获取poi点
     * @return
     */
    public JourprodHttpAsyncClients getClient() {
        return this.predictClient;
    }

    /**
     * 获取eas客户端，获取行政区划列表
     * @return
     */
    public JourprodHttpAsyncClients getClientForDivision() {
        return this.predictDivisionClient;
    }
}
