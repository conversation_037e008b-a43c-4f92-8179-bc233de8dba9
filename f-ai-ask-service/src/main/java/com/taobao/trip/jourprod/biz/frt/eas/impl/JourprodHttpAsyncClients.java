package com.taobao.trip.jourprod.biz.frt.eas.impl;

import com.aliyun.openservices.eas.discovery.core.DiscoveryClient;
import com.aliyun.openservices.eas.predict.auth.HmacSha1Signature;
import com.aliyun.openservices.eas.predict.http.HttpConfig;
import com.aliyun.openservices.eas.predict.http.HttpException;
import com.aliyun.openservices.eas.predict.http.HttpHeaders;
import com.aliyun.openservices.eas.predict.http.PredictClient;
import com.taobao.vipserver.client.core.VIPClient;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.TimeZone;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import org.apache.commons.io.IOUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.concurrent.FutureCallback;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClients;
import org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager;
import org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.apache.http.nio.entity.NByteArrayEntity;
import org.apache.http.nio.reactor.ConnectingIOReactor;
import org.xerial.snappy.Snappy;

public class JourprodHttpAsyncClients extends PredictClient {

    private static Log log = LogFactory.getLog(PredictClient.class);

    final private int endpointRetryCount = 10;
    private HashMap<String, String> mapHeader = null;
    private CloseableHttpAsyncClient httpclient = null;
    private String token = null;
    private String modelName = null;
    private String requestPath = "";
    private String endpoint = null;
    private boolean isCompressed = false;
    private int retryCount = 3;
    private String contentType = "application/octet-stream";
    private int errorCode = 400;
    private String errorMessage;
    private String vipSrvEndPoint = null;
    private String directEndPoint = null;
    private int requestTimeout = 0;

    public JourprodHttpAsyncClients(HttpConfig httpConfig) {
        try {
            ConnectingIOReactor ioReactor = new DefaultConnectingIOReactor();
            PoolingNHttpClientConnectionManager cm = new PoolingNHttpClientConnectionManager(
                    ioReactor);
            cm.setMaxTotal(httpConfig.getMaxConnectionCount());
            cm.setDefaultMaxPerRoute(httpConfig.getMaxConnectionPerRoute());
            requestTimeout = httpConfig.getRequestTimeout();
            IOReactorConfig config = IOReactorConfig.custom()
                    .setTcpNoDelay(true)
                    .setSoTimeout(httpConfig.getReadTimeout())
                    .setSoReuseAddress(true)
                    .setConnectTimeout(httpConfig.getConnectTimeout())
                    .setIoThreadCount(httpConfig.getIoThreadNum())
                    .setSoKeepAlive(httpConfig.isKeepAlive()).build();
            final RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(httpConfig.getConnectTimeout())
                    .setSocketTimeout(httpConfig.getReadTimeout()).build();
            httpclient = HttpAsyncClients.custom().setConnectionManager(cm)
                    .setDefaultIOReactorConfig(config)
                    .setDefaultRequestConfig(requestConfig).build();
            httpclient.start();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private JourprodHttpAsyncClients setHttp(CloseableHttpAsyncClient httpclient) {
        this.httpclient = httpclient;
        return this;
    }

    public JourprodHttpAsyncClients setToken(String token) {
        if (token == null || token.length() > 0) {
            this.token = token;
        }
        return this;
    }

    public JourprodHttpAsyncClients setRequestTimeout(int requestTimeout) {
        this.requestTimeout = requestTimeout;
        return this;
    }

    public JourprodHttpAsyncClients setModelName(String modelName) {
        this.modelName = modelName;
        return this;
    }

    public JourprodHttpAsyncClients setEndpoint(String endpoint) {
        this.endpoint = endpoint;
        return this;
    }

    public JourprodHttpAsyncClients setVIPServer(String vipSrvEndPoint) {
        if (vipSrvEndPoint == null || vipSrvEndPoint.length() > 0) {
            this.vipSrvEndPoint = vipSrvEndPoint;
        }
        return this;
    }

    public JourprodHttpAsyncClients setDirectEndpoint(String directEndpoint) {
        if (directEndPoint == null || directEndPoint.length() > 0) {
            this.directEndPoint = directEndpoint;
            System.setProperty("com.aliyun.eas.discovery", directEndpoint);
        }
        return this;
    }

    public JourprodHttpAsyncClients setIsCompressed(boolean isCompressed) {
        this.isCompressed = isCompressed;
        return this;
    }

    public JourprodHttpAsyncClients setRetryCount(int retryCount) {
        this.retryCount = retryCount;
        return this;
    }

    public JourprodHttpAsyncClients setTracing(HashMap<String, String> mapHeader) {
        this.mapHeader = mapHeader;
        return this;
    }

    public JourprodHttpAsyncClients setContentType(String contentType) {
        this.contentType = contentType;
        return this;
    }

    public String getRequestPath() {
        return requestPath;
    }

    public void setRequestPath(String requestPath) {
        if (requestPath == null) {
            return;
        }
        if (requestPath.length() > 0 && requestPath.charAt(0) != '/') {
            requestPath = "/" + requestPath;
        }
        this.requestPath = requestPath;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public byte[] predict(byte[] requestContent) throws Exception {
        byte[] content = null;
        String lastUrl = "";
        try {
            HttpPost request = generateSignature(requestContent, lastUrl);
            content = getContent(request);
        } catch (Exception e) {
            throw e;
        }
        return content;
    }

    private byte[] getContent(HttpPost request) throws IOException,
            InterruptedException, ExecutionException, TimeoutException {
        byte[] content = null;
        HttpResponse response = null;

        Future<HttpResponse> future = httpclient.execute(request, new FutureCallback<HttpResponse>() {

            @Override
            public void completed(HttpResponse httpResponse) {

            }

            @Override
            public void failed(Exception e) {

            }

            @Override
            public void cancelled() {

            }
        });
        if (requestTimeout > 0) {
            response = future.get(requestTimeout, TimeUnit.MILLISECONDS);
        } else {
            response = future.get();
        }

        if (future.isDone()) {
            try {
                errorCode = response.getStatusLine().getStatusCode();
                errorMessage = "";

                if (errorCode == 200) {
                    content = IOUtils.toByteArray(response.getEntity()
                            .getContent());
                    if (isCompressed) {
                        content = Snappy.uncompress(content);
                    }
                } else {
                    errorMessage = IOUtils.toString(response.getEntity()
                            .getContent(), "UTF-8");
                    throw new HttpException(errorCode, errorMessage);
                }
            } catch (IllegalStateException e) {
            }
        } else if (future.isCancelled()) {
        } else {
            throw new HttpException(-1, "request failed!");
        }
        return content;
    }

    private HttpPost generateSignature(byte[] requestContent, String lastUrl) throws Exception {
        HttpPost request = new HttpPost(getUrl(lastUrl));
        request.setEntity(new NByteArrayEntity(requestContent));
        if (isCompressed) {
            try {
                requestContent = Snappy.compress(requestContent);
            } catch (IOException e) {
                log.error("Compress Error", e);
            }
        }
        HmacSha1Signature signature = new HmacSha1Signature();
        String md5Content = signature.getMD5(requestContent);
        request.addHeader(HttpHeaders.CONTENT_MD5, md5Content);
        Date now = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat(
                "EEE, dd MMM yyyy HH:mm:ss", Locale.ENGLISH);
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        String currentTime = dateFormat.format(now) + " GMT";
        request.addHeader(HttpHeaders.DATE, currentTime);
        request.addHeader(HttpHeaders.CONTENT_TYPE, contentType);

        if (mapHeader != null) {
            request.addHeader("Client-Timestamp",
                    String.valueOf(System.currentTimeMillis()));
        }

        if (token != null) {
            String auth = "POST" + "\n" + md5Content + "\n"
                    + contentType + "\n" + currentTime + "\n"
                    + "/api/predict/" + modelName + requestPath;
            request.addHeader(HttpHeaders.AUTHORIZATION,
                    "EAS " + signature.computeSignature(token, auth));
        }
        return request;
    }

    private String getUrl(String lastUrl) throws Exception {
        if (this.endpoint != null && !this.endpoint.startsWith("http://") && !this.endpoint.startsWith("https://")) {
            this.endpoint = "http://" + endpoint;
        }
        String endpoint = this.endpoint;
        String url = "";
        for (int i = 0; i < endpointRetryCount; i++) {
            if (vipSrvEndPoint != null) {
                endpoint = VIPClient.srvHost(vipSrvEndPoint).toInetAddr();
                url = "http://" + endpoint + "/api/predict/" + modelName + requestPath;
                if (VIPClient.srvHosts(vipSrvEndPoint).size() < 2) {
                    return url;
                }
                // System.out.println("URL: " + url + " LastURL: " + lastUrl);
                if (!url.equals(lastUrl)) {
                    return url;
                }
            } else if (directEndPoint != null) {
                endpoint = DiscoveryClient.srvHost(this.modelName).toInetAddr();
                url = "http://" + endpoint + "/api/predict/" + modelName + requestPath;
                // System.out.println("URL: " + url + " LastURL: " + lastUrl);
                if (DiscoveryClient.getHosts(this.modelName).size() < 2) {
                    return url;
                }
                if (!url.equals(lastUrl)) {
                    return url;
                }
            } else {
                url = endpoint + "/api/predict/" + modelName + requestPath;
                break;
            }
        }
        return url;
    }
}
