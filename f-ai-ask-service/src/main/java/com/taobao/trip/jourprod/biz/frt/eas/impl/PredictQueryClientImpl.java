package com.taobao.trip.jourprod.biz.frt.eas.impl;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;

import com.aliyun.openservices.eas.predict.http.PredictClient;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.trip.jourprod.biz.common.ThreadPoolMonitor;
import com.taobao.trip.jourprod.biz.frt.eas.PredictQueryClient;
import com.taobao.trip.jourprod.biz.frt.eas.config.EasClient;
import com.taobao.trip.jourprod.biz.frt.eas.config.EasExtractEnum;
import com.taobao.trip.jourprod.biz.frt.eas.model.PredictAnalysisData;
import com.taobao.trip.jourprod.biz.frt.eas.model.PredictAnalysisForDivisionData;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.jourprod.common.lang.utils.MD5Util;
import com.taobao.trip.jourprod.common.sal.tair.impl.LdbTairManager;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;

@Service
public class PredictQueryClientImpl implements PredictQueryClient {

    private final FliggyNewLogger newLogger = LogUtil.getFliggyNewLogger(PredictQueryClientImpl.class);

    @Resource
    private EasClient easClient;
    @Resource
    private LdbTairManager ldbTairManager;


    @PostConstruct
    public void init() {
        SwitchManager.init(PredictQueryClientImpl.class);
    }

    @AppSwitch(des = "新行程规划-算法结果mock", level = Switch.Level.p3)
    public static List<PredictAnalysisData> EAS_DATA_MOCK = Lists.newArrayList();

    @AppSwitch(des = "新行程规划-算法结果mock开关", level = Switch.Level.p3)
    public static Boolean EAS_MOCK_OPEN = false;

    @AppSwitch(des = "新行程规划-简单的-获取超时时间", level = Switch.Level.p4)
    public static int SIMPLE_GET_TIMEOUT = 300;

    @AppSwitch(des = "新行程规划-复杂的-获取超时时间", level = Switch.Level.p4)
    public static int COMPLEX_GET_TIMEOUT = 5000;

    @AppSwitch(des = "新行程规划-行政区划列表-获取超时时间", level = Switch.Level.p4)
    public static int DIVISION_GET_TIMEOUT = 1000;

    @AppSwitch(des = "新行程规划-行政区划列表-缓存时间", level = Switch.Level.p4)
    public static Integer DIVISION_GET_TTL = 24 * 60 * 60;



    public static final String TYPE = "model_type";
    public static final String TEXT = "text";
    public static final String DIV_LEVEL = "div_level";
    //行政区划 算法识别缓存key
    public static final String PREDICT_QUERY_DIVISION_CACHE_KEY = "tripOd:planNew:predict:division:";
    //poi 算法识别缓存key
    public static final String PREDICT_QUERY_POI_CACHE_KEY = "tripOd:planNew:predict:poi:";

    /**
     * 线程池
     */
    private static final ThreadPoolExecutor executor = new ThreadPoolExecutor(60, 60, 0, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(300),
            new CustomizableThreadFactory("PredictQueryClientImpl"));

    static {
        new Thread(new ThreadPoolMonitor(executor, "PredictQueryClientImpl")).start();
    }

    @Override
    public List<PredictAnalysisData> getAnalysisInfo(String text, Integer type) {

        if (EAS_MOCK_OPEN && !text.startsWith("测试")) {
            if (CollectionUtils.isNotEmpty(EAS_DATA_MOCK)) {
                return EAS_DATA_MOCK;
            }
        }
        //取缓存
        String key = PREDICT_QUERY_POI_CACHE_KEY + MD5Util.md5(text);
        Object value = ldbTairManager.get(key);
        if (Objects.nonNull(value)) {
            List<PredictAnalysisData> dataList = JSON.parseObject(value.toString(), new TypeReference<List<PredictAnalysisData>>() {
            });
            newLogger.recordOutput(new LogModel("getAnalysisInfo_executor_cache")
                    .request(text)
                    .code(type + "")
                    .response(JSONUtil.toJSONStringForLog(dataList)));
            return dataList;
        }

        long t1 = System.currentTimeMillis();
        try {
            //异步 发起调用
            Future<String> submit = executor.submit(() -> {
                PredictClient client = easClient.getClient();
                String res = null;
                try {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put(TYPE, type);
                    jsonObject.put(TEXT, text);
                    String request = jsonObject.toJSONString();
                    res = new String(client.predict(request.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
                    newLogger.recordOutput(new LogModel("getAnalysisInfo_executor")
                            .request(text)
                            .code(type + "")
                            .cost(System.currentTimeMillis() - t1)
                            .response(res));
                } catch (Exception e) {
                    newLogger.recordDangerException(new LogModel("getAnalysisInfo_executor")
                            .e(e)
                            .code(type + "")
                            .request(text));
                }
                return res;
            });
            String response = submit.get(Objects.equals(EasExtractEnum.SAMPLE.getType(), type) ? SIMPLE_GET_TIMEOUT : COMPLEX_GET_TIMEOUT, TimeUnit.MILLISECONDS);
            if (StringUtils.isBlank(response)) {
                return Lists.newArrayList();
            }
            List<List> resList = JSONArray.parseArray(response, List.class);
            if (CollectionUtils.isEmpty(resList)) {
                return Lists.newArrayList();
            }
            List<PredictAnalysisData> lists = resList.stream()
                    .map(list -> PredictAnalysisData.builder()
                            .sourceText(list.get(0).toString())
                            .poiId(list.get(1).toString())
                            .targetText(list.get(2).toString())
                            .build())
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(lists)) {
                return Lists.newArrayList();
            }
            ldbTairManager.put(key, JSON.toJSONString(lists), DIVISION_GET_TTL);
            return lists;
        } catch (Exception e) {
            newLogger.recordDangerException(new LogModel("getAnalysisInfo")
                    .e(e)
                    .code(type + "")
                    .request(text));
        }
        return Lists.newArrayList();
    }

    @Override
    public List<PredictAnalysisForDivisionData> getAnalysisInfoForDivisionList(String text, Integer divLevel) {
        long t1 = System.currentTimeMillis();
        try {
            //取缓存
            String key = PREDICT_QUERY_DIVISION_CACHE_KEY + MD5Util.md5(text);
            Object value = ldbTairManager.get(key);
            if (Objects.nonNull(value)) {
                List<PredictAnalysisForDivisionData> dataList = JSON.parseObject(value.toString(), new TypeReference<List<PredictAnalysisForDivisionData>>() {
                });
                newLogger.recordOutput(new LogModel("getAnalysisInfoForDivisionList_executor_cache")
                        .request(text)
                        .code(String.valueOf(divLevel))
                        .response(JSONUtil.toJSONStringForLog(dataList)));
                return dataList;
            }

            //异步 发起调用
            Future<String> submit = executor.submit(() -> {
                PredictClient client = easClient.getClientForDivision();
                String res = null;
                try {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put(DIV_LEVEL, divLevel);
                    jsonObject.put(TEXT, text);
                    String request = jsonObject.toJSONString();
                    res = new String(client.predict(request.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
                    newLogger.recordOutput(new LogModel("getAnalysisInfoForDivisionList_executor")
                            .request(text)
                            .cost(System.currentTimeMillis() - t1)
                            .response(res));
                } catch (Exception e) {
                    newLogger.recordDangerException(new LogModel("getAnalysisInfoForDivisionList_executor")
                            .e(e)
                            .request(text));
                }
                return res;
            });
            String response = submit.get(DIVISION_GET_TIMEOUT, TimeUnit.MILLISECONDS);
            if (StringUtils.isBlank(response)) {
                return Lists.newArrayList();
            }
            List<PredictAnalysisForDivisionData> resList = JSONArray.parseArray(response, PredictAnalysisForDivisionData.class);
            if (CollectionUtils.isEmpty(resList)) {
                return Lists.newArrayList();
            }
            ldbTairManager.put(key, JSON.toJSONString(resList), DIVISION_GET_TTL);
            return resList;
        } catch (Exception e) {
            newLogger.recordDangerException(new LogModel("getAnalysisInfoForDivisionList")
                    .e(e)
                    .request(text));
        }
        return Lists.newArrayList();
    }

    @AteyeInvoker(description = "测试算法解析", paraDesc = "text&type")
    public List<PredictAnalysisData> testAnalysisInfo(String text, Integer type) {
        return getAnalysisInfo(text, type);
    }

    @AteyeInvoker(description = "测试算法解析行政区划列表", paraDesc = "text&divLevel")
    public List<PredictAnalysisForDivisionData> testAnalysisInfoForDivisionList(String text, Integer divLevel) {
        return getAnalysisInfoForDivisionList(text, divLevel);
    }


}
