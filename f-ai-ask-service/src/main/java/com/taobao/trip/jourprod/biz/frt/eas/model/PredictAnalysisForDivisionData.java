package com.taobao.trip.jourprod.biz.frt.eas.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PredictAnalysisForDivisionData {

    /**
     * 行政区划id
     */
    private String id;

    /**
     * 行政区划名称
     */
    private String name;

    /**
     * 行政区划等级
     */
    private Integer level;

    /**
     * 是否国外
     */
    @JsonProperty("is_abroad")
    private Integer isAbroad;

    /**
     * 行政区划id tree，英文逗号分割
     */
    @JsonProperty("is_tree")
    private String idTree;

    /**
     * 行政区划名称 tree，英文逗号分割
     */
    @JsonProperty("name_tree")
    private String nameTree;

    /**
     * 来源
     */
    private String source;

    /**
     * 权重
     */
    private Double confidence;

}
