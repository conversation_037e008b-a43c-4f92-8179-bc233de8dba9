package com.taobao.trip.jourprod.biz.frt.impl.switchconfig;

import com.taobao.ateye.annotation.Switch;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by mojin on 2018/3/9.
 */
@Service
public class BizSwitch {

    @Switch(description = "是否分享卡片, 默认为true")
    public static volatile boolean isShareCard = true;

    @Switch(description = "卡片添加日历开关, 默认为true")
    public static volatile boolean addCalendarCard = true;

    @Switch(name = "cardBackDetail", description = "卡片背面详情页开关, 默认为true")
    public static volatile boolean cardBackDetail = true;

    @Switch(description = "汽车票卡片背面详情页开关, 默认为true")
    public static volatile boolean BUS_CARD_BACK_DETAIL = true;

    @Switch(description = "机票卡片背面详情页开关, 默认为true")
    public static volatile boolean FLIGHT_CARD_BACK_DETAIL = true;

    @Switch(description = "火车票卡片背面详情页开关, 默认为true")
    public static volatile boolean TRAIN_CARD_BACK_DETAIL = true;

    @Switch(description = "酒店卡片背面详情页开关, 默认为true")
    public static volatile boolean HOTEL_CARD_BACK_DETAIL = true;

    @Switch(name = "行程2.0版本号控制", description = "行程2.0版本号控制")
    public static volatile String appVersion2 = "9.3.3";

    @Switch(name = "是否对行程2.0进行版本号控制", description = "是否对行程2.0进行版本号控制")
    public static volatile boolean isControlVersion2 = false;

    @Switch(name = "native页面展示目的地按钮个数", description = "native页面展示目的地按钮个数")
    public static volatile Integer BUTTON_DESTINATION = 3;

    @Switch(name = "对外投放的行程包类型对应的用户id", description = "对外投放的行程包类型对应的用户id")
    public static volatile Long journeyPutUserId = 4163150259L;

    @Switch(name = "DEFAULT_PLAN_NAME", description = "默认行程包名称")
    public static String DEFAULT_PLAN_NAME = "新的行程";

    @Switch(name = "DEFAULT_PLAN_NAME_LENGTH", description = "默认行程包名称，长度限制")
    public static Integer DEFAULT_PLAN_NAME_LENGTH = 14;

    @Switch(name = "SUPPORT_ORDER_TO_PLAN", description = "是否支持订单引导推荐规划")
    public static boolean SUPPORT_ORDER_TO_PLAN = true;

    @Switch(name = "ACTIVITY_MODULE_SHOW", description = "近期活动模块是否展示,默认打开")
    public static volatile boolean ACTIVITY_MODULE_SHOW = true;

    @Switch(name = "INTEREST_DEST_MODULE_SHOW", description = "最嗨玩法模块是否展示,默认打开")
    public static volatile boolean INTEREST_DEST_MODULE_SHOW = true;

    @Switch(name = "SCENE_MODULE_SHOW", description = "时下热门模块是否展示,默认打开")
    public static volatile boolean SCENE_MODULE_SHOW = true;

    @Switch(name = "PERIPHERY_RECOMMEND_MODULE_SHOW", description = "评价列表推荐模块是否展示,默认打开")
    public static volatile Boolean PERIPHERY_RECOMMEND_MODULE_SHOW = true;

    @Switch(name = "defultTransferAirUrl", description = "接送机url pattern")
    public static String defultTransferAirUrl = "https://h5.m.taobao.com/trip/vehicle/home/<USER>";

    @Switch(name = "DEFAULT_TRANSFER_STATION_URL", description = "接送站url pattern")
    public static final String DEFAULT_TRANSFER_STATION_URL = "https://h5.m.taobao.com/trip/vehicle/home/<USER>";

    @Switch(name = "NATIVE_MAP_TEXT", description = "低版本点击地图文案")
    public static String NATIVE_MAP_TEXT =  "请设置出发日期后，再使用地图功能";

    @Switch(name = "NATIVE_EDIT_TEXT", description = "低版本点击编辑器文案")
    public static String NATIVE_EDIT_TEXT = "请设置出发日期后，再编辑行程";

    @Switch(name = "UPDATE_NICK_URL", description = "修改淘宝昵称链接")
    public static String UPDATE_NICK_URL = "https://h5.m.taobao.com/trip/wx-member-v2/editor/index.html?_wx_tpl=https://h5.m.taobao.com/trip/wx-member-v2/editor/index.weex.js&type=%s";

    @Switch(description = "行程包列表页过滤子包开关, 默认为false")
    public static volatile boolean PLAN_HOME_FILTER_SUB_PLAN = false;

    @Switch(name = "JOURENY_CHANNEL_SUPPORT_TIPS", description = "行程频道红点提示开关，默认为false")
    public static volatile boolean JOURENY_CHANNEL_SUPPORT_TIPS = false;

    @Switch(description = "行程包支持模版化查询卡片类型")
    public static volatile String SUPPORT_TEMPLATE_CARD_TYPE = "FLIGHT";

    @Switch(name = "EXTERNAL_HAVE_TIME_CARD_TYPE", description = "外部添加有日期元素类型")
    public static String EXTERNAL_HAVE_TIME_CARD_TYPE = "INTER_FLIGHT";//INTER_FLIGHT

    @Switch(name = "EXTERNAL_NO_TIME_CARD_TYPE", description = "外部添加无日期元素类型")
    public static String EXTERNAL_NO_TIME_CARD_TYPE = "POI,HOTEL";

    @Switch(name = "EXTERNAL_GOODS_CARD_TYPE", description = "外部添加商品元素类型")
    public static String EXTERNAL_GOODS_CARD_TYPE = "GOODS";

    @Switch(name = "EXTERNAL_JOIN_PLAN_URL", description = "添加行业元素进行程，组件跳转链接")
    public static String EXTERNAL_JOIN_PLAN_URL = "https://market.m.taobao.com/app/trip/rx-journey/pages/ext-page-poi?wh_weex=true&bizAttributes=%s&joinSource=%s&_fli_background_transparent=true&titleBarHidden=1";

    @Switch(name = "CIVIL_FLIGHT_ORDER_H5_URL", description = "国内机票订单URL")
    public static String CIVIL_FLIGHT_ORDER_H5_URL = "https://market.m.taobao.com/app/trip/h5-flight-refactor/pages/order-detail/index.html?orderId=%s&pagePopFlag=1&_fli_webview=true";

    @Switch(name = "INTER_FLIGHT_ORDER_H5_URL", description = "国际机票订单URL")
    public static String INTER_FLIGHT_ORDER_H5_URL = "https://market.m.taobao.com/app/trip/h5-iflight-new/pages/detail/index.html?simpleBack=true&orderId=%s&pagePopFlag=1&_fli_webview=true";

    @Switch(name = "TRAIN_ORDER_H5_URL_NEW", description = "新版火车票订单URL，9.6.7及以上")
    public static String TRAIN_ORDER_H5_URL_NEW = "https://market.%s.taobao.com/app/trip/h5-train-react/pages/order-detail/index.html?orderId=%s&orderType=4&pagePopFlag=1";

    @Switch(name = "TRAIN_ORDER_H5_URL", description = "火车票订单URL")
    public static String TRAIN_ORDER_H5_URL = "https://h5.m.taobao.com/trip/train-react/order-detail/index.html?orderId=%s&orderType=4&pagePopFlag=1";

    @Switch(name = "TEMPLATE_CARD_QUERY_TIME_LIMIT", description = "行程单中模版卡片并发填充数据超时时间，默认800ms")
    public static int TEMPLATE_CARD_QUERY_TIME_LIMIT = 800;

    @Switch(name = "PLAN_DETAIL_QUERY_TIME_OUT", description = "模版化行程单详情页并发查询超时")
    public static volatile Long PLAN_DETAIL_QUERY_TIME_OUT = 1500L;

    @Switch(name = "FILL_RECOMMEND_TIME_OUT", description = "行程包详情页补充推荐数据超时时间")
    public static volatile Integer FILL_RECOMMEND_TIME_OUT = 500;

    @Switch(name = "NOT_TEMPLATE_ORDER_CARD_QUERY_TIME_LIMIT", description = "行程单中非模版卡片并发填充数据超时时间，默认800ms")
    public static Integer NOT_TEMPLATE_ORDER_CARD_QUERY_TIME_LIMIT = 800;

    @Switch(name = "CREATE_PLAN_BY_JOURNEY_CARD", description = "根据行程卡片创建行程包，走打底创包逻辑，默认为false")
    public static volatile boolean CREATE_PLAN_BY_JOURNEY_CARD = false;

    @Switch(name = "TICKET_GOODS_IDS", description = "门票添加到行程的类目ID集合")
    public static String TICKET_GOODS_IDS = "124866006,50462016";

    @Switch(name = "SUPPORT_GOODS_CATEGORY_ID", description = "支持非门票商品添加到行程的类目ID集合")
    public static String SUPPORT_GOODS_CATEGORY_ID = "124770001";

    @Switch(name = "COLSE_WEAK_CORRELATION", description = "是否关闭弱依赖页面展示，默认为false")
    public static volatile Boolean COLSE_WEAK_CORRELATION = false;

    @Switch(name = "CREATE_PLAN_BY_EXTERNAL_ELEMENT", description = "根据外部元素创建行程包，走打底创包逻辑，默认为false")
    public static volatile boolean CREATE_PLAN_BY_EXTERNAL_ELEMENT = false;

    @Switch(name = "CARD_SUMMARY_QUERY_TIME_LIMIT", description = "卡片summary并发填充超时时间，默认800ms")
    public static int CARD_SUMMARY_QUERY_TIME_LIMIT = 800;

    @Switch(name = "PLAN_SUMMARY_QUERY_TIME_LIMIT", description = "行程包summary并发填充超时时间，默认1500ms")
    public static int PLAN_SUMMARY_QUERY_TIME_LIMIT = 1500;

    @Switch(name = "PLAN_REFRESH_SUMMARY_QUERY_TIME_LIMIT", description = "行程包局部刷新summary并发填充超时时间，默认2000ms")
    public static int PLAN_REFRESH_SUMMARY_QUERY_TIME_LIMIT = 2000;

    @Switch(name = "CLOSE_JOURNEY_HOME_LOCATION_TIPS", description = "行程频道定位消息红点提示关闭，默认为false")
    public static volatile boolean CLOSE_JOURNEY_HOME_LOCATION_TIPS = false;

    @Switch(name = "CLOSE_JOURNEY_HOME_PLAN_TIPS", description = "行程频道行程包提示消息红点关闭，默认为false")
    public static volatile boolean CLOSE_JOURNEY_HOME_PLAN_TIPS = false;

    @Switch(name = "HOME_PLAN_BIZ_QUERY_TIME_OUT", description = "630行程首页并发查询超时")
    public static volatile Long HOME_PLAN_BIZ_QUERY_TIME_OUT = 2000L;

    @Switch(name = "JOURNEY_DETAIL_BIZ_QUERY_TIME_OUT", description = "行程详情卡片并发查询超时")
    public static volatile Long JOURNEY_DETAIL_BIZ_QUERY_TIME_OUT = 2000L;

    @Switch(name = "GET_BASE_CARD", description = "获取打底卡片，默认不为打底数据")
    public static boolean GET_BASE_CARD = false;
    
    @Switch(name = "使用新目的地库获取目的地主图", description = "使用新目的地库获取目的地主图")
    public static boolean USE_NEW_DEST_LIB_PIC = true;

    @Switch(name = "WISH_QUERY_TIMEOUT", description = "心愿单查询超时时间，默认300ms")
    public static volatile int WISH_QUERY_TIMEOUT = 300;

    @Switch(name = "SUPPORT_USER_CREATE_PLAN", description = "是否支持C端用户主动创包")
    public static boolean SUPPORT_USER_CREATE_PLAN = true;

    @Switch(name = "USE_NEW_POI_QUERY_SERVICE", description = "是否使用POI查询目的地新接口")
    public static boolean USE_NEW_POI_QUERY_SERVICE = true;

    @Switch(name = "JOURNEY_NEW_HOME_USE_DEFAULT", description = "新版行程使用打底数据")
    public static boolean JOURNEY_NEW_HOME_USE_DEFAULT = false;

    @Switch(name = "JOURNEY_NEW_HOME_USE_DEFAULT", description = "新版历史行程是否过滤打底数据")
    public static boolean JOURNEY_NEW_HOME_FILTER_DEFAULT = true;

    @Switch(name = "FOOTMARK_TITLE_USE_DEFAULT", description = "新版行程足迹是否使用打底数据")
    public static boolean FOOTMARK_TITLE_USE_DEFAULT = false;

    @Switch(name = "JOURNEY_HOME_H5_URL", description = "跳转到行程频道首页")
    public static String JOURNEY_HOME_H5_URL = "https://market.m.taobao.com/app/trip/rx-journey1/pages/home?titleBarHidden=2&mtopVer=2.6&_projVer=1.7.2&cardId=%s";

    @Switch(name = "POI_LIST_JUMP_RAX", description = "热门POI二级页面")
    public static String POI_LIST_JUMP_RAX = "https://h5.{0}.taobao.com/trip/rx-poi/poi-list/index.html?_wx_tpl=https%3A%2F%2Fh5.{0}.taobao.com%2Ftrip%2Frx-poi%2Fpoi-list%2Findex.weex.js&destId={1}&destName={2}&sortType={3}&category={4}&filter={5}";

    @Switch(name = "POI_MAP_URL", description = "poi地图链接")
    public static String POI_MAP_URL = "https://h5.m.taobao.com/trip/city-map/poi/index.html?poiId={0}&poiName={1}&poiAddress={2}&longitude={3}&latitude={4}";

    @Switch(name = "CIVIL_FLIGHT_CUSTOMER_URL", description = "国内机票阿里小蜜")
    public static String CIVIL_FLIGHT_CUSTOMER_URL = "https://ai.alimebot.taobao.com/intl/index.htm?from=NybUJWE5Iu&orderId=%s";

    @Switch(name = "INTER_FLIGHT_CUSTOMER_URL", description = "国际机票阿里小蜜")
    public static String INTER_FLIGHT_CUSTOMER_URL = "https://ai.alimebot.taobao.com/intl/index.htm?from=alitrip_super_intplane&orderId=%s";

    @Switch(name = "FLIGHT_DETAIL_CARD_URL", description = "机票单点详情页跳转链接")
    public static String FLIGHT_DETAIL_CARD_URL = "https://market.%s.taobao.com/app/trip/rx-journey-other/pages/flight?_fli_wk=true&titleBarHidden=2&disableNav=YES&cardId=%s";

    @Switch(name = "HOTEL_DETAIL_CARD_URL", description = "酒店单点详情页跳转链接")
    public static String HOTEL_DETAIL_CARD_URL =  "https://market.%s.taobao.com/app/trip/rx-journey-other/pages/hotel?_fli_wk=true&titleBarHidden=2&disableNav=YES&cardId=%s";

    @Switch(name = "TRAIN_DETAIL_CARD_URL", description = "火车票单点详情页跳转链接")
    public static String TRAIN_DETAIL_CARD_URL = "https://market.%s.taobao.com/app/trip/rx-journey-other/pages/train?_fli_wk=true&titleBarHidden=2&disableNav=YES&cardId=%s";

    @Switch(name = "JOURNEY_HOME_CARRIER_TYPE", description = "新版行程接入结构化数据的类型")
    public static String JOURNEY_HOME_CARRIER_TYPE = "AIRPORT_TRANSFER,ENTRANCE,TICKET,FLIGHT,FLIGHT_SUB,TRAIN,HOTEL,HOTEL_SUB";

    @Switch(name = "JOURNEY_HOME_HIS_CARRIER_TYPE", description = "新版历史行程接入结构化数据的类型")
    public static String JOURNEY_HOME_HIS_CARRIER_TYPE = "AIRPORT_TRANSFER,ENTRANCE,TICKET,TRAIN,HOTEL,HOTEL_SUB";

    @Switch(name = "JOURNEY_SUBSCRIBE_CARRIER_TYPE", description = "代订订单类型")
    public static String JOURNEY_SUBSCRIBE_CARRIER_TYPE = "FLIGHT_SUB,HOTEL_SUB,TRAIN_SUB,BUS_SUB,ENTRANCE_SUB,TICKET_SUB";

    @Switch(name = "JOURNEY_SUBSCRIBE_FLIGHT_FILTER_BTN", description = "机票代订订单需要过滤的行动点")
    public static String JOURNEY_SUBSCRIBE_FLIGHT_FILTER_BTN = "FlightCheckIn,FlightOrderDetail";

    @Switch(name = "JOURNEY_SUBSCRIBE_HOTEL_FILTER_BTN", description = "酒店代订订单需要过滤的行动点")
    public static String JOURNEY_SUBSCRIBE_HOTEL_FILTER_BTN = "ITEM161587744040840";

    @Switch(name = "JOURNEY_SUBSCRIBE_TRAIN_FILTER_BTN", description = "火车票代订订单需要过滤的行动点")
    public static String JOURNEY_SUBSCRIBE_TRAIN_FILTER_BTN = "";

    @Switch(name = "JOURNEY_SUBSCRIBE_FLIGHT_DESC", description = "代订机票描述")
    public static String JOURNEY_SUBSCRIBE_FLIGHT_DESC = "该行程为飞猪通过您支付宝绑定的证件号同步的机票出行信息，您不是实际下单人，无法由您本人退改签，如有需求请联系实际下单人。";

    @Switch(name = "JOURNEY_SUBSCRIBE_HOTEL_DESC", description = "代订酒店描述")
    public static String JOURNEY_SUBSCRIBE_HOTEL_DESC = "该行程为飞猪通过您支付宝绑定的证件号同步的酒店出行信息，您不是实际下单人，无法由您本人进行退订，如有需求请联系实际下单人。";

    /** 首页行程卡mock配置开关 */
    @Switch(name = "homeCardOpenMock", description = "整体mock开关")
    public static Boolean homeCardOpenMock = false;

    @Switch(name = "oldHomeCardOpen", description = "旧首页接口是否打开")
    public static Boolean oldHomeCardOpen = false;

    @Switch(name = "homeCardMockData", description = "mock卡片数据")
    public static Boolean homeCardMockData = false;

    @Switch(name = "homeCardReturnMockDataUserId", description = "强制mock的用户id")
    public static String homeCardReturnMockDataUserId = "2211396956587";

    @Switch(name = "closeFilterOrderStatus", description = "不过滤订单状态")
    public static Boolean closeFilterOrderStatus = false;

    @Switch(name = "flightVisibleStatusOpen", description = "机票每一程状态开关")
    public static Boolean flightVisibleStatusOpen = false;

    @Switch(name = "homeCardQueryHistory", description = "查询mock用户的历史行程")
    public static Boolean homeCardQueryHistory = false;

    @Switch(name = "homeCardMockUserId", description = "首页服务卡用户id mock")
    public static String homeCardMockUserId = "";

    @Switch(name = "homeCardMockUserIdWhiteList", description = "首页服务卡用户id mock白名单")
    public static String homeCardMockUserIdWhiteList = "";

    @Switch(name = "homeCardMockCurrentDateTime", description = "首页服务卡mock当前查看的时间，单位：毫秒！！！")
    public static Long homeCardMockCurrentDateTime = 0L;

    @Switch(name = "homeCardMockNowTime", description = "首页服务卡mock当前查看的时间，格式yyyy-MM-dd HH:mm:ss")
    public static String homeCardMockNowTime = "";

    @Switch(name = "selectCrossServiceId", description = "需要透的交叉服务id")
    public static String selectCrossServiceId = null;

    @Switch(name = "ALIPAY_FLIGHT_HOTEL_WHITE_LIST", description = "机票酒店支付宝新模板卡包切流用户白名单")
    public static String ALIPAY_FLIGHT_HOTEL_WHITE_LIST = "2088502912268553,2088002432488436";

    @Switch(name = "ALIPAY_FLIGHT_HOTEL_FLOW_CUT", description = "机票酒店支付宝新模板卡包切流比例（1-100）")
    public static int ALIPAY_FLIGHT_HOTEL_FLOW_CUT = 100;

    @Switch(name = "ALIPAY_TICKET_SYNC_USER_ID", description = "门票同步支付宝切流用户白名单")
    public static String ALIPAY_TICKET_SYNC_USER_ID = "";
    @Switch(name = "ALIPAY_TICKET_SYNC_FLOW_CUT", description = "门票同步支付宝切流比例（1-100）")
    public static int ALIPAY_TICKET_SYNC_FLOW_CUT = 0;

    @Switch(name = "FLIGHT_DEFAULT_IMAGE", description = "飞机默认图片")
    public static String FLIGHT_DEFAULT_IMAGE = "";

    @Switch(name = "CHECK_IN_COUNTER_TIP", description = "CHECK_IN_COUNTER_TIP")
    public static String FLIGHT_CHECK_IN_COUNTER_TIP = "离柜台较近";

    @Switch(name = "BOARD_GATE_TIP", description = "BOARD_GATE_TIP")
    public static String FLIGHT_BOARD_GATE_TIP = "安检后步行";

    @Switch(name = "FLIGHT_REACH_EXIT_TIP", description = "FLIGHT_REACH_EXIT_TIP")
    public static String FLIGHT_REACH_EXIT_TIP = "无行李出机场";

    @Switch(name = "FLIGHT_BAGGAGE_ID_TIP", description = "FLIGHT_BAGGAGE_ID_TIP")
    public static String FLIGHT_BAGGAGE_ID_TIP = "有行李出机场";

    @Switch(name = "HOTEL_CROSS_SCENE_ID", description = "机酒交叉推荐场景id")
    public static String HOTEL_CROSS_SCENE_ID = "0";

    @Switch(name = "JOURNEY_MEMBER_DESCRIPTION", description = "行程中会员服务描述")
    public static String JOURNEY_MEMBER_DESCRIPTION = "本行程可享飞猪会员权益";

    @Switch(name = "JOURNEY_MEMBER_BOTTOM_ITEM_ID", description = "行程中底部会员服务ID")
    public static String JOURNEY_MEMBER_BOTTOM_ITEM_ID = "ITEM162202853952071";

    @Switch(name = "JOURNEY_MEMBER_BOTTOM_USE_DESCRIPTION", description = "行程中会员服务使用描述")
    public static String JOURNEY_MEMBER_BOTTOM_USE_DESCRIPTION = "机场贵宾厅";

    @Switch(name = "JOURNEY_MEMBER_BOTTOM_EXCHANGE_DESCRIPTION", description = "行程中会员服务兑换描述")
    public static String JOURNEY_MEMBER_BOTTOM_EXCHANGE_DESCRIPTION = "兑换可享机场贵宾厅";

    @Switch(name = "JOURNEY_CARD_DETAIL_URL", description = "行程单点详情投放链接")
    public static String JOURNEY_CARD_DETAIL_URL = "https://market.m.taobao.com/app/trip/rx-journey1/pages/home?titleBarHidden=2&disableNav=YES&hasBack=true";

    @Switch(name = "JOURNEY_SUB_ORDER_SHOW_MEMBER", description = "代订订单展示会员模块")
    public static Boolean JOURNEY_SUB_ORDER_SHOW_MEMBER = true;

    @Switch(name = "MEMBER_RIGHT_SERVICE_OPEN", description = "开启会员服务")
    public static Boolean MEMBER_RIGHT_SERVICE_OPEN = true;

    @Switch(name = "MEMBER_RIGHT_PAGE_URL", description = "会员权益模块页面")
    public static String MEMBER_RIGHT_PAGE_URL = "https://market.m.taobao.com/app/trip/rx-member/pages/index-new?wh_weex=true";

    @Switch(name = "MEMBER_RIGHT_WATER_PARK_SHOW_CITIES", description = "会员权益水上乐园透出城市id")
    public static String MEMBER_RIGHT_WATER_PARK_SHOW_CITIES = "330100";

    @Switch(name = "MEMBER_BLOCK_BG_IMAGE_F1", description = "F1会员背景图片")
    public static String MEMBER_BLOCK_BG_IMAGE_F1 = "https://img.alicdn.com/imgextra/i3/O1CN01QlcSwE1dLo2jvkvbp_!!6000000003720-2-tps-1404-276.png";

    @Switch(name = "MEMBER_BLOCK_BG_IMAGE_F2", description = "F2会员背景图片")
    public static String MEMBER_BLOCK_BG_IMAGE_F2 = "https://img.alicdn.com/imgextra/i1/O1CN01FHa7a327Gi74rWEGp_!!6000000007770-2-tps-1404-276.png";

    @Switch(name = "MEMBER_BLOCK_BG_IMAGE_F3", description = "F3会员背景图片")
    public static String MEMBER_BLOCK_BG_IMAGE_F3 = "https://img.alicdn.com/imgextra/i3/O1CN01jeaUPF25cZn29bVcP_!!6000000007547-2-tps-1404-396.png";

    @Switch(name = "MEMBER_BLOCK_BG_IMAGE_F4", description = "F4会员背景图片")
    public static String MEMBER_BLOCK_BG_IMAGE_F4 = "https://img.alicdn.com/imgextra/i2/O1CN01QTpNrB1vGuu2FMdRI_!!6000000006146-2-tps-1404-396.png";

    // 出行助手
    @Switch(name = "ASSISTANT_QUERY_SERVICE_TIMEOUT", description = "行程助手查询超时时间， 默认2s")
    public static Integer ASSISTANT_QUERY_SERVICE_TIMEOUT = 2;

    @Switch(name = "ASSISTANT_WEATHER_QUERY_DAYS", description = "行程助手查询未来x天天气")
    public static Integer ASSISTANT_WEATHER_QUERY_DAYS = 4;

    @Switch(name = "ASSISTANT_POI_TOOL_BRFORE_HOUR", description = "行程助手POI工具，提前x小时透出，默认2小时")
    public static Integer ASSISTANT_POI_TOOL_BRFORE_HOUR = -2;

    @Switch(name = "ASSISTANT_POI_TOOL_DISTANCE", description = "行程助手POI工具，x米范围内透出，默认500米")
    public static Integer ASSISTANT_POI_TOOL_DISTANCE = 500;

    @Switch(name = "ASSISTANT_FLIGGY_IMAGE", description = "行程助手飞猪图片")
    public static String ASSISTANT_FLIGGY_IMAGE = "https://img.alicdn.com/imgextra/i4/O1CN01BFeKwS1ZIp0HG5qQe_!!6000000003172-2-tps-252-260.png";

    @Switch(name = "ASSISTANT_MEMBER_TAG", description = "行程助手会员标志")
    public static String ASSISTANT_MEMBER_TAG = "https://img.alicdn.com/imgextra/i2/O1CN01FCGvEJ1op58epaoqP_!!6000000005273-2-tps-264-56.png";

    @Switch(name = "ASSISTANT_WEATHER_MESSAGE", description = "行程助手天气工具话术")
    public static String ASSISTANT_TOOL_TITLE = "基于你%s的行程推荐";

    @Switch(name = "ASSISTANT_WEATHER_MESSAGE", description = "行程助手天气工具话术")
    public static String ASSISTANT_WEATHER_MESSAGE = "近期出行，记得关注天气变化哦～";

    @Switch(name = "ASSISTANT_AIRPORT_VIP_LOUNGE_MESSAGE", description = "行程助手贵宾厅工具话术")
    public static String ASSISTANT_AIRPORT_VIP_LOUNGE_MESSAGE = "想要更舒适的候机体验？试试贵宾厅吧";

    @Switch(name = "ASSISTANT_STARBUCKS_CUP_MESSAGE", description = "行程助手星巴克工具话术")
    public static String ASSISTANT_STARBUCKS_CUP_MESSAGE = "旅途路上，美味咖啡来助力～";

    @Switch(name = "ASSISTANT_TRAFFIC_PLAN_MESSAGE", description = "行程助手交通工具话术")
    public static String ASSISTANT_TRAFFIC_PLAN_MESSAGE = "看看当前位置前往%s的交通情况吧～";

    @Switch(name = "ASSISTANT_AIRPORT_POI_MESSAGE", description = "行程助手机场poi工具话术")
    public static String ASSISTANT_AIRPORT_POI_MESSAGE = "机场里有以下服务喔～";

    @Switch(name = "ASSISTANT_RETURN_POLICY_PAGE", description = "返乡政策url")
    public static String ASSISTANT_RETURN_POLICY_PAGE = "https://vt.sm.cn/api/QuarkGo/home?fromCityCode=%s&toCityCode=%s&fromsource=tripwjourprod";

    @Switch(name = "ASSISTANT_RETURN_POLICY_TEXT", description = "返乡政策按钮名称")
    public static String ASSISTANT_RETURN_POLICY_TEXT = "防疫助手";

    @Switch(name = "ASSISTANT_RETURN_POLICY_ICON", description = "返乡政策icon")
    public static String ASSISTANT_RETURN_POLICY_ICON = "https://img.alicdn.com/imgextra/i1/O1CN01aBDXLa2ADS9DvH3cp_!!6000000008169-2-tps-60-60.png";

    @Switch(name = "ASSISTANT_SCENE_QUERY_PAGE", description = "景点poi查询列表")
    public static String ASSISTANT_SCENE_QUERY_PAGE = "https://h5.m.taobao.com/trip/rx-poi/poi-list/index.html?_wx_tpl=https%3A%2F%2Fh5.m.taobao.com%2Ftrip%2Frx-poi%2Fpoi-list%2Findex.weex.js&destId={0}&sortType=hot&category=5&type=%E6%99%AF%E7%82%B9&destName={1}&spm=181.9406239.6502486.d0_0&_projVer=0.12.19";

    @Switch(name = "ASSISTANT_SCENE_QUERY_TEXT", description = "景点poi查询列表按钮名称")
    public static String ASSISTANT_SCENE_QUERY_TEXT = "景点查询";

    @Switch(name = "ASSISTANT_SCENE_QUERY_ICON", description = "景点poi查询列表icon")
    public static String ASSISTANT_SCENE_QUERY_ICON = "https://img.alicdn.com/imgextra/i4/O1CN01IwYQwl1ytnNRad0LR_!!6000000006637-2-tps-60-60.png";

    @Switch(name = "ASSISTANT_FLIGHT_DYNAMIC_PAGE", description = "航班动态查询")
    public static String ASSISTANT_FLIGHT_DYNAMIC_PAGE = "page://flight_dynamic_home?params={\"flutter_path\":\"\\/flight_dynamic_home\",\"un_flutter\":true}";

    @Switch(name = "ASSISTANT_FLIGHT_DYNAMIC_TEXT", description = "航班动态查询按钮名称")
    public static String ASSISTANT_FLIGHT_DYNAMIC_TEXT = "航班动态";

    @Switch(name = "ASSISTANT_FLIGHT_DYNAMIC_ICON", description = "航班动态查询icon")
    public static String ASSISTANT_FLIGHT_DYNAMIC_ICON = "https://img.alicdn.com/imgextra/i1/O1CN01Z1awSh1VKsJwX0lUQ_!!6000000002635-2-tps-64-64.png";

    @Switch(name = "ASSISTANT_TRAVEL_TIPS_PAGE", description = "旅行贴士")
    public static String ASSISTANT_TRAVEL_TIPS_PAGE = "https://market.m.taobao.com/app/trip/rx-service-center/pages/travel-trip?source=2&_projVer=2.0.5";

    @Switch(name = "ASSISTANT_TRAVEL_TIPS_TEXT", description = "旅行贴士按钮名称")
    public static String ASSISTANT_TRAVEL_TIPS_TEXT = "旅行贴士";

    @Switch(name = "ASSISTANT_TRAVEL_TIPS_ICON", description = "旅行贴士查询icon")
    public static String ASSISTANT_TRAVEL_TIPS_ICON = "https://img.alicdn.com/imgextra/i1/O1CN01Dn1kzE1D0ZgILwXKy_!!6000000000154-2-tps-60-60.png";


    @Switch(name = "ASSISTANT_MILE_QUERY_PAGE", description = "里程中心页面")
    public static String ASSISTANT_MILE_QUERY_PAGE = "https://market.m.taobao.com/app/trip/h5-mileage-play/pages/home/<USER>";

    @Switch(name = "ASSISTANT_MILE_QUERY_TEXT", description = "里程中心按钮名称")
    public static String ASSISTANT_MILE_QUERY_TEXT = "里程中心";

    @Switch(name = "ASSISTANT_MILE_QUERY_ICON", description = "里程中心查询icon")
    public static String ASSISTANT_MILE_QUERY_ICON = "https://img.alicdn.com/imgextra/i3/O1CN01JISabM1yiLTV72GwQ_!!6000000006612-2-tps-60-60.png";

    @Switch(name = "ASSISTANT_DEFAULT_BACK_IMAGE", description = "行程助手默认背景图")
    public static String ASSISTANT_DEFAULT_BACK_IMAGE = "https://img.alicdn.com/imgextra/i1/O1CN01vl7Fhw24EvjPafKza_!!6000000007360-0-tps-1072-718.jpg";

    @Switch(name = "ASSISTANT_MAX_NUM", description = "行程助手最多透出几个，默认5个")
    public static Integer ASSISTANT_MAX_NUM = 5;

    @Switch(name = "AIRPORT_TOOL_MAX_NUM", description = "机场POI工具最多透出几个，默认4个")
    public static Integer AIRPORT_TOOL_MAX_NUM = 4;

    @Switch(name = "ASSISTANT_TRAFFIC_PLAN_MIN_DISTANCE", description = "交通规划透出最小距离")
    public static Integer ASSISTANT_TRAFFIC_PLAN_MIN_DISTANCE = 300;

    @Switch(name = "ASSISTANT_TRAFFIC_PLAN_MAX_DISTANCE", description = "交通规划透出最大距离")
    public static Integer ASSISTANT_TRAFFIC_PLAN_MAX_DISTANCE = 200000;

    @Switch(name = "assistantMockCurrentDateTime", description = "出行助手mock当前查看的时间，单位：毫秒！！！")
    public static Long assistantMockCurrentDateTime = 0L;

    @Switch(name = "GAODE_ANDROID_URL", description = "高德安卓链接")
    public static String GAODE_ANDROID_URL = "amapuri://route/plan/?";

    @Switch(name = "GAODE_IOS_URL", description = "高德IOS链接")
    public static String GAODE_IOS_URL = "iosamap://path?sourceApplication=amap&dev=0&";

    @Switch(name = "GAODE_PARAMS_URL", description = "高德参数链接")
    public static String GAODE_PARAMS_URL = "sid=%s&slat=%s&slon=%s&did=%s&dlat=%s&dlon=%s&t=%s&dev=0";

    @Switch(name = "GAODE_H5_URL", description = "高德h5链接")
    public static String GAODE_H5_URL = "https://uri.amap.com/navigation?from=%s,%s,%s&to=%s,%s,%s&via=&mode=%s&policy=0&src=tripwjourprod&coordinate=gaode&callnative=0";

    @Switch(name = "POI_INSTALLATION_URL", description = "poi设施二级承接页")
    public static String POI_INSTALLATION_URL = "https://market.m.taobao.com/app/trip/rx-poi-related/pages/poi-flight-service?poiId=%s&poiName=%s&moduleId=%s";

    @Switch(name = "POI_DETAIL_SCROLL_URL", description = "poi详情页动态锚点")
    public static String POI_DETAIL_SCROLL_URL = "https://market.m.taobao.com/app/trip/rx-trip-ticket/pages/detail?_fli_newpage=1&un_flutter=true&poi_detail_page&_fli_unify=false&titleBarHidden=2&disableNav=YES&poiId=%s&scrollTo=%s";

    @Switch(name = "HOME_HOTEL_JUMP_TO_DETAIL", description = "首页酒店卡跳转到单点详情页")
    public static Boolean HOME_HOTEL_JUMP_TO_DETAIL = false;

    @Switch(name = "NEW_HOME_CARD_WHITE_LIST", description = "新版首页卡白名单，为空则全体生效")
    public static String NEW_HOME_CARD_WHITE_LIST = "";

    @Switch(name = "SORT_INDEX_BY_TYPE", description = "场景端对订单索引按照类目排序")
    public static Boolean SORT_INDEX_BY_TYPE = false;

    /** 20210831新版新版行程频道 */
    @Switch(name = "JOURNEY_CHANNEL_QUERY_PAGE_SIZE", description = "新版有效行程每页查询数量")
    public static Integer JOURNEY_CHANNEL_QUERY_PAGE_SIZE = 20;

    @Switch(name = "JOURNEY_HOME_QUERY_PAGE_SIZE" ,description = "V3版本行程每页查询数量 ")
    public static Integer JOURNEY_HOME_QUERY_PAGE_SIZE = 10;

    @Switch(name = "JOURNEY_CHANNEL_HISTORY_QUERY_PAGE_SIZE", description = "新版历史行程每页查询数量")
    public static Integer JOURNEY_CHANNEL_HISTORY_QUERY_PAGE_SIZE = 20;

    @Switch(name = "JOURNEY_CHANNEL_WARN_TIP_ID", description = "行程频道告警提示泰坦id")
    public static String JOURNEY_CHANNEL_WARN_TIP_ID = "497547";

    @Switch(name = "JOURNEY_RECOMMEND_ICON", description = "行程推荐icon")
    public static String JOURNEY_RECOMMEND_ICON = "https://img.alicdn.com/tfs/TB15iHfG5_1gK0jSZFqXXcpaXXa-82-60.png";

    @Switch(name = "PLAY_RECOMMEND_DETAIL_URL", description = "推荐玩法详情url")
    public static String PLAY_RECOMMEND_DETAIL_URL = "https://market.m.taobao.com/app/trip/rx-journey1/pages/recommend-detail?playId={0}&mainTitle={1}&subTitle={2}";

    @Switch(name = "JOURNEY_TRANSIT_CARD_ICON", description = "中转卡片icon")
    public static String JOURNEY_TRANSIT_CARD_ICON = "https://gw.alicdn.com/imgextra/i2/O1CN01CK0YWk26SmxYRwBYd_!!6000000007661-2-tps-72-72.png";
    @Switch(name = "JOURNEY_TRANSIT_CARD_ICON_v4", description = "中转卡片icon_v4")
    public static String JOURNEY_TRANSIT_CARD_ICON_v4 = "https://gw.alicdn.com/imgextra/i4/O1CN01OX6lPN1TBbPN2012E_!!6000000002344-2-tps-64-64.png";

    @Switch(name = "JOURNEY_PACKAGE_CARD_ICON", description = "行程包卡片icon")
    public static String JOURNEY_PACKAGE_CARD_ICON = "https://gw.alicdn.com/imgextra/i3/O1CN018ZuD401HJabNMGGaG_!!6000000000737-2-tps-72-72.png";

    @Switch(name = "FLIGHT_JOURNEY_ICON", description = "机票卡中间icon")
    public static String FLIGHT_JOURNEY_ICON = "https://gw.alicdn.com/imgextra/i4/O1CN01FzdFSo1WdU2OBFfR2_!!6000000002811-2-tps-274-62.png";

    @Switch(name = "FLIGHT_CANCEL_JOURNEY_ICON", description = "机票卡取消状态中间icon")
    public static String FLIGHT_CANCEL_JOURNEY_ICON = "https://gw.alicdn.com/imgextra/i3/O1CN01vEidAU1pPGRcE2Itd_!!6000000005352-2-tps-200-200.png";

    @Switch(name = "TRAIN_JOURNEY_ICON", description = "火车卡中间icon")
    public static String TRAIN_JOURNEY_ICON = "https://gw.alicdn.com/imgextra/i1/O1CN01TgPrGh1tlU7gKrRJo_!!6000000005942-2-tps-274-44.png";

    @Switch(name = "TRAIN_STOP_JOURNEY_ICON", description = "火车卡停运中间icon")
    public static String TRAIN_STOP_JOURNEY_ICON = "https://gw.alicdn.com/imgextra/i3/O1CN013fdAAs1sNq3jNYYFv_!!6000000005755-2-tps-200-200.png";

    @Switch(name = "TRAIN_STOP_JOURNEY_TIP", description = "火车卡停运提示")
    public static String TRAIN_STOP_JOURNEY_TIP = "很抱歉，次列车已停运";

    @Switch(name = "JOURNEY_TOP_WELCOME_MESSAGE", description = "行程顶部欢迎语")
    public static String JOURNEY_TOP_WELCOME_MESSAGE = "即将开启%s的旅程~";

    @Switch(name = "JOURNEY_TOP_DEFAULT_WELCOME_MESSAGE", description = "行程顶部默认欢迎语")
    public static String JOURNEY_TOP_DEFAULT_WELCOME_MESSAGE = "您有一段行程~";

    @Switch(name = "JOURNEY_TOP_EMPTY_WELCOME_IMAGE", description = "行程顶部（空行程）欢迎图")
    public static String JOURNEY_TOP_EMPTY_WELCOME_IMAGE = "https://gw.alicdn.com/imgextra/i2/O1CN01o5ThA41ML1vL5I46A_!!6000000001417-2-tps-280-290.png";

    @Switch(name = "JOURNEY_TOP_WELCOME_IMAGE", description = "行程顶部欢迎图")
    public static String JOURNEY_TOP_WELCOME_IMAGE = "https://gw.alicdn.com/imgextra/i2/O1CN01o5ThA41ML1vL5I46A_!!6000000001417-2-tps-280-290.png";

    @Switch(name = "JOURNEY_TOP_EPIDEMIC_WELCOME_IMAGE", description = "行程顶部疫情欢迎图")
    public static String JOURNEY_TOP_EPIDEMIC_WELCOME_IMAGE = "https://gw.alicdn.com/imgextra/i3/O1CN01UHD7UZ1gdcJgRmcle_!!6000000004165-2-tps-280-290.png";

    @Switch(name = "JOURNEY_DEFAULT_BACKGROUND_IMAGE", description = "行程频道默认背景图")
    public static String JOURNEY_DEFAULT_BACKGROUND_IMAGE = "https://gw.alicdn.com/imgextra/i2/O1CN01AZRyx71iWPJQXmXnK_!!6000000004420-2-tps-1500-744.png";

    @Switch(name = "JOURNEY_PACKAGE_DEFAULT_IMAGE", description = "行程频道行程包打底图")
    public static String JOURNEY_PACKAGE_DEFAULT_IMAGE = "https://gw.alicdn.com/imgextra/i2/O1CN01AZRyx71iWPJQXmXnK_!!6000000004420-2-tps-1500-744.png";

    @Switch(name = "ENTRANCE_USED_IMAGE", description = "门票已使用图片")
    public static String ENTRANCE_USED_IMAGE = "https://gw.alicdn.com/imgextra/i4/O1CN01PclGif1lCf57OFgKB_!!6000000004783-2-tps-230-232.png";

    @Switch(name = "JOURNEY_HOME_CARRIER_TYPE_NEW", description = "新版行程接入结构化数据的类型")
    public static String JOURNEY_HOME_CARRIER_TYPE_NEW = "AIRPORT_TRANSFER,ENTRANCE,TICKET,FLIGHT,FLIGHT_SUB,TRAIN,HOTEL,HOTEL_SUB,BUS";

    @Switch(name = "JOURNEY_HOME_HIS_CARRIER_TYPE_NEW", description = "新版历史行程接入结构化数据的类型")
    public static String JOURNEY_HOME_HIS_CARRIER_TYPE_NEW = "AIRPORT_TRANSFER,ENTRANCE,TICKET,FLIGHT,FLIGHT_SUB,TRAIN,HOTEL,HOTEL_SUB,BUS";

    @Switch(name = "JOURNEY_ORDER_BTN_TEXT", description = "右上角订单按钮话术")
    public static String JOURNEY_ORDER_BTN_TEXT = "订单管理";

    @Switch(name = "JOURNEY_DANGER_DELAY_MINUTE", description = "行程严重延误时间，默认60分钟")
    public static Integer JOURNEY_DANGER_DELAY_MINUTE = 60;

    @Switch(name = "JOURNEY_CHANNEL_USE_DEFAULT_DATA", description = "行程频道强制兜底")
    public static Boolean JOURNEY_CHANNEL_USE_DEFAULT_DATA = false;

    @Switch(name = "JOURNEY_VIP_ROOM_DESC", description = "机场贵宾厅引导文案")
    public static String JOURNEY_VIP_ROOM_DESC = "500里程可兑换>";

    @Switch(name = "JOURNEY_TRAFFIC_PLAN_MIN_DISTANCE", description = "导航透出最小距离")
    public static Integer JOURNEY_TRAFFIC_PLAN_MIN_DISTANCE = 1;

    @Switch(name = "JOURNEY_WALK_PLAN_DISTANCE", description = "步行导航透出距离")
    public static Integer JOURNEY_WALK_PLAN_DISTANCE = 2000;

    @Switch(name = "JOURNEY_DRIVING_PLAN_MAX_DISTANCE", description = "驾车导航透出最大距离")
    public static Integer JOURNEY_DRIVING_PLAN_MAX_DISTANCE = 100000;

    @Switch(name = "ORDER_LIST_BTN_TEXT", description = "订单列表按钮文案")
    public static String ORDER_LIST_BTN_TEXT = "查看全部订单>";

    @Switch(name = "ORDER_LIST_JUMP_URL", description = "订单列表链接")
    public static String ORDER_LIST_JUMP_URL = "https://market.m.taobao.com/app/trip/h5-olist/pages/order/index.html";

    @Switch(name = "JOURNEY_RECOMMEND_USE_DEFAULT", description = "使用兜底数据")
    public static Boolean JOURNEY_RECOMMEND_USE_DEFAULT = true;

    @Switch(name = "POLICY_DETAIL_PAGE_URL", description = "疫情政策详情页")
    public static String POLICY_DETAIL_PAGE_URL = "https://market.m.taobao.com/app/trip/rx-journey-other/pages/epidemic-policy?cardId=%s&titleBarHidden=2&disableNav=YES&_projVer=1.6.0";

    @Switch(name = "POLICY_DETAIL_PAGE_URL_NEW", description = "新版疫情政策详情页")
    public static String POLICY_DETAIL_PAGE_URL_NEW = "https://market.%s.taobao.com/app/trip/rx-service-toolkit/pages/epidemic?fromCityCode=%s&toCityCode=%s&fromCityName=%s&toCityName=%s&poiId=%s&source=JOURNEY_TOOL";

    @Switch(name = "RISK_LEVEL_TEXT", description = "风险等级文案")
    public static String RISK_LEVEL_TEXT = "部分地点%s";

    @Switch(name = "POLICY_PAGE_WELCOME_MESSAGE", description = "疫情政策页面欢迎语")
    public static String POLICY_PAGE_WELCOME_MESSAGE = "出门要记得戴好口罩哦";

    @Switch(name = "POLICY_PAGE_BOTTOM_TIP", description = "疫情政策页面底部提示语")
    public static String POLICY_PAGE_BOTTOM_TIP = "飞猪旅行温馨提示，出行前请您务必及时了解当地最新防疫政策，做好充分准备，合理安排出行。";

    @Switch(name = "POLICY_PAGE_QUERY_SIZE", description = "疫情政策页面最大输入id个数，默认6个")
    public static Integer POLICY_PAGE_QUERY_SIZE = 6;

    @Switch(name = "POLICY_NORMAL_TEXT", description = "没有疫情信息提示语")
    public static String POLICY_NORMAL_TEXT = "暂无疫情信息，请持续关注";

    @Switch(name = "SUB_FLIGHT_SUPPORT_CHECKIN", description = "代订支持值机")
    public static Boolean SUB_FLIGHT_SUPPORT_CHECKIN = false;

    @Switch(name = "OPEN_INNER_TEST_REQUEST_CHECK", description = "开启压测流量检查")
    public static Boolean OPEN_INNER_TEST_REQUEST_CHECK = true;

    @Switch(name = "HISTORY_CARD_NOT_USE_CARRIER", description = "历史卡片不使用详情数据")
    public static Boolean HISTORY_CARD_NOT_USE_CARRIER = true;

    @Switch(name = "HISTORY_CARD_USE_CARRIER_TYPES", description = "历史卡片使用详情数据的类型")
    public static String HISTORY_CARD_USE_CARRIER_TYPES = "RENT_CAR";

    @Switch(name = "AIRPORT_TRANSFER_WAIT_SEND_DRIVER_TEXT", description = "接送机未派司机时展示文案")
    public static String AIRPORT_TRANSFER_WAIT_SEND_DRIVER_TEXT = "已接单，待安排司机";

    @Switch(name = "AIRPORT_TRANSFER_BAD_CAR_NO", description = "接送机车牌号badcase")
    public static String AIRPORT_TRANSFER_BAD_CAR_NO = "UU0001,0000,00000,AAAA";

    @Switch(name = "BOOK_ORDER_LIST_URL", description = "待预约订单列表url")
    public static String BOOK_ORDER_LIST_URL = "https://market.m.taobao.com/app/trip/h5-olist/pages/specificOrder/index.html?orderStatus=BOOK&bookType=all&ttid=xingcheng";

    @Switch(name = "BOOK_ORDER_MODULE_IMAGE_URL", description = "待预约订单模块背景图url")
    public static String BOOK_ORDER_MODULE_IMAGE_URL = "https://gw.alicdn.com/imgextra/i1/O1CN01PNPzVj1RkkFsFUrQR_!!6000000002150-2-tps-1404-296.png";

    @Switch(name = "TAXI_SUBSCRIBE_CARD_URL", description = "接送机服务卡-预约送机-跳转链接")
    public static String TAXI_SUBSCRIBE_CARD_URL = "https://market.wapa.taobao.com/app/trip/rx-vehicle-search/pages/home?_projver=2.4.20&projectVersion=2.4.20&";

    @Switch(name = "TRANSFER_SUB_SCRIBE_JUMPURL_PARAMS", description = "接送机服务卡-预约送机-跳转链接拼接参数")
    public static final String TRANSFER_SUB_SCRIBE_JUMPURL_PARAMS = "bizType=%s&subBizType=%s&showTitle=%s&__webview_options__=fullscreen%s&locationParams=%s&_fli_webview=true&source=journeyCard";


    @Switch(name = "MAX_HOTEL_PACKAGE_ELEMENT", description = "展示的x元素最大数量")
    public static Integer MAX_HOTEL_PACKAGE_ELEMENT = 2;

    @Switch(name = "IGNORE_INTENT_NODE", description = "闲聊场景需要忽略的中间node节点ID")
    public static String IGNORE_INTENT_NODE = "Start_bYxoRU,LLM_XKdu,Judge_5xvz,End_DrQn7F,Script_QTZX";
}
