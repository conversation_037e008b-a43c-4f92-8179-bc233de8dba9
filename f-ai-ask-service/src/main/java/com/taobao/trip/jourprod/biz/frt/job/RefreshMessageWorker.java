package com.taobao.trip.jourprod.biz.frt.job;

import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.jourprod.domain.JourneyPlanAiChatMessageDO;
import com.taobao.trip.jourprod.domain.JourneyPlanAiChatMessageParam;
import com.taobao.trip.jourprod.domain.JourneyPlanAiChatSessionDO;
import com.taobao.trip.jourprod.domain.JourneyPlanAiChatSessionParam;
import com.taobao.trip.jourprod.mapper.JourneyPlanAiChatMessageDAO;
import com.taobao.trip.jourprod.mapper.JourneyPlanAiChatSessionDAO;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @Description 迁移聊天数据任务
 * <AUTHOR>
 * @Date 2025/4/22
 **/
@Component
public class RefreshMessageWorker {

    private FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(RefreshMessageWorker.class);

    Map<String, String> lruCache = new LinkedHashMap<String, String>(500, 0.75f, true) {
        @Override
        protected boolean removeEldestEntry(Map.Entry<String, String> eldest) {
            return size() > 1000;
        }
    };

    @Resource
    private JourneyPlanAiChatMessageDAO journeyPlanAiChatMessageDAO;

    @Resource
    private JourneyPlanAiChatSessionDAO journeyPlanAiChatSessionDAO;

    @AteyeInvoker(description = "刷新聊天数据", paraDesc = "startId&endId&step&mock&resetCache")
    public void refreshMessage(long startId, long endId, long step, boolean mock, boolean resetCache) throws InterruptedException {
        if (resetCache) {
            lruCache.clear();
        }
        // 从startid到endid按照一定步长循环操作
        for (long i = startId; i <= endId; i += step) {
            JourneyPlanAiChatMessageParam journeyPlanAiChatMessageParam = new JourneyPlanAiChatMessageParam();
            journeyPlanAiChatMessageParam.createCriteria()
                    .andIdGreaterThanOrEqualTo(i)
                    .andIdLessThan(i + step)
                    .andRoleEqualTo("user");
            List<JourneyPlanAiChatMessageDO> journeyPlanAiChatMessageDOS = journeyPlanAiChatMessageDAO.selectByParam(journeyPlanAiChatMessageParam);
            // 如果sessionid没有出现过，就存放到会话库中
            for (JourneyPlanAiChatMessageDO journeyPlanAiChatMessageDO : journeyPlanAiChatMessageDOS) {
                String sessionId = journeyPlanAiChatMessageDO.getSessionId();
                if (StringUtils.isBlank(sessionId)) {
                    continue;
                }
                try {
                    if (!lruCache.containsKey(sessionId)) {
                        // 在新库中再查一次
                        JourneyPlanAiChatSessionParam journeyPlanAiChatSessionParam = new JourneyPlanAiChatSessionParam();
                        journeyPlanAiChatSessionParam.createCriteria().andSessionIdEqualTo(sessionId);
                        List<JourneyPlanAiChatSessionDO> journeyPlanAiChatSessionDOS = journeyPlanAiChatSessionDAO.selectByParam(journeyPlanAiChatSessionParam);
                        if (CollectionUtils.isEmpty(journeyPlanAiChatSessionDOS)) {
                            JourneyPlanAiChatSessionDO journeyPlanAiChatSessionDO = new JourneyPlanAiChatSessionDO();
                            journeyPlanAiChatSessionDO.setSessionId(sessionId);
                            String content = journeyPlanAiChatMessageDO.getContent();
                            journeyPlanAiChatSessionDO.setSessionName(JSONUtil.extractContentFromJSONPath(content, "$.content"));
                            journeyPlanAiChatSessionDO.setUserId(journeyPlanAiChatMessageDO.getUserId());
                            journeyPlanAiChatSessionDO.setGmtCreate(journeyPlanAiChatMessageDO.getGmtCreate());
                            journeyPlanAiChatSessionDO.setGmtModified(journeyPlanAiChatMessageDO.getGmtModified());
                            journeyPlanAiChatSessionDO.setDeleteFlag("0");
                            if (!mock) {
                                journeyPlanAiChatSessionDAO.insert(journeyPlanAiChatSessionDO);
                            }
                            lruCache.put(sessionId, sessionId);
                            LOGGER.recordOutput(new LogModel("refreshMessage").message("插入会话").request(sessionId));
                        } else {
                            // 更新 更新时间
                            JourneyPlanAiChatSessionDO journeyPlanAiChatSessionDO = new JourneyPlanAiChatSessionDO();
                            journeyPlanAiChatSessionDO.setGmtModified(journeyPlanAiChatMessageDO.getGmtCreate());
                            journeyPlanAiChatSessionDAO.updateByParamSelective(journeyPlanAiChatSessionDO, journeyPlanAiChatSessionParam);
                        }
                    }
                } catch (Exception e) {
                    LOGGER.recordDangerException(new LogModel("refreshMessage").message("刷新聊天数据异常").request(sessionId).e(e));
                }
            }
            LOGGER.recordOutput(new LogModel("refreshMessage").message("记录进度").request(String.valueOf(i)));
            Thread.sleep(100);
        }
        
    }

}
