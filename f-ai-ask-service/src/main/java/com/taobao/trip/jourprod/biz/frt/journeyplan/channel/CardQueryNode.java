package com.taobao.trip.jourprod.biz.frt.journeyplan.channel;

import com.taobao.trip.jourdprod.core.model.jourplan.bizmodel.JourneyChannelBizV3DO;
import com.taobao.trip.jourprod.service.facade.model.request.journey.channel.CardQueryParam;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplan.JourneyChannelQueryV3Request;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplan.newChannel.CardQueryResult;

public interface CardQueryNode {

    /**
     * 支持查询的模块类型，用来确认当前需要查询的模块
     */
    String getModuleType();

    CardQueryResult query(CardQueryParam param, JourneyChannelBizV3DO bizDO, JourneyChannelQueryV3Request request);
}
