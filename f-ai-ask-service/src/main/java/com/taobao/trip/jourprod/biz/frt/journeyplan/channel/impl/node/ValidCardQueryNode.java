package com.taobao.trip.jourprod.biz.frt.journeyplan.channel.impl.node;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;

import com.google.common.collect.Maps;
import com.taobao.trip.jourdprod.core.model.common.ConstantElement;
import com.taobao.trip.jourdprod.core.model.jourplan.bizmodel.JourneyChannelBizV3DO;
import com.taobao.trip.jourprod.biz.frt.journeyplan.channel.CardQueryNode;
import com.taobao.trip.jourprod.common.sal.hsf.common.EnvSwitch;
import com.taobao.trip.jourprod.service.facade.model.request.journey.channel.CardQueryParam;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplan.JourneyChannelQueryV3Request;
import com.taobao.trip.jourprod.service.facade.model.response.JourPlanError;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplan.newChannel.CardQueryResult;
import com.taobao.trip.tripjourneyop.domain.Result;
import com.taobao.trip.tripjourneyop.domain.request.journyplatform.JourneyIndexSceneRequest;
import com.taobao.trip.tripjourneyop.journeyplatform.api.JourneyIndexQueryFacade;
import com.taobao.tripjourneyop.domain.result.JourneyIndexQueryResult;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;


@Service
public class ValidCardQueryNode implements CardQueryNode {

    public final static String VALID_NODE_TYPE = "valid";

    private final static FliggyNewLogger slsLogger = LogUtil.getFliggyNewLogger(ValidCardQueryNode.class);

    @Resource
    private JourneyIndexQueryFacade journeyIndexQueryFacade;

    @Override
    public String getModuleType() {
        return VALID_NODE_TYPE;
    }

    @Override
    public CardQueryResult query(CardQueryParam param, JourneyChannelBizV3DO bizDO, JourneyChannelQueryV3Request request) {
        String queryKey = StringUtils.isNotEmpty(param.getLastId()) ? "materielId_" + param.getLastId() : null;
        JourneyIndexSceneRequest validRequest = buildQueryRequest(String.valueOf(param.getUserId()),
                ConstantElement.PLATFORM_SCENE_ID.JOURNEY_HOME_NEW,
                queryKey, param.getRows());

        Result<JourneyIndexQueryResult> result = journeyIndexQueryFacade.getJourneyIndexByScene(validRequest);

        CardQueryResult resultModel = new CardQueryResult();
        if (Objects.isNull(result) || !result.isSuccess() || Objects.isNull(result.getModel())) {
            resultModel.setSuccess(false);
            resultModel.setPlanError(JourPlanError.PLAN_INDEX_QUERY_ERROR);
            slsLogger.recordKeyPath(new LogModel("queryUserIndexByPage").path("query_valid_index")
                    .userId(param.getUserId()).request(JSON.toJSONString(validRequest)).response(JSON.toJSONString(result)));
            return resultModel;
        }
        JourneyIndexQueryResult model = result.getModel();

        bizDO.setIndexInfoList(model.getIndexList());
        resultModel.setSuccess(true);
        if (CollectionUtils.isNotEmpty(bizDO.getIndexInfoList())) {
            resultModel.setRows(bizDO.getIndexInfoList().size());
        }

        String queryKey1 = model.getQueryKey();
        if (StringUtils.isNotBlank(queryKey1)) {
            String[] split = queryKey1.split("_");
            if (split.length == 2) {
                resultModel.setLastCardId(split[1]);
            }
        }
        resultModel.setHasNextPage(model.isHasNextPage());
        return resultModel;
    }

    /**
     * 构造索引查询请求
     *
     * @param userId
     * @param sceneId
     * @param queryKey
     * @param pageSize
     * @return
     */
    private JourneyIndexSceneRequest buildQueryRequest(String userId, String sceneId, String queryKey,
                                                       Integer pageSize) {
        JourneyIndexSceneRequest queryRequest = new JourneyIndexSceneRequest();
        queryRequest.setUserId(userId);
        queryRequest.setPageSize(pageSize);
        queryRequest.setSceneId(sceneId);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(queryKey)) {
            Map<String, Object> bizParams = Maps.newHashMap();
            // 下一页条件
            bizParams.put("queryKey", queryKey);
            queryRequest.setBizParams(bizParams);
        }

        if (EnvSwitch.isPre()) {
            queryRequest.setEnv(1);
        } else if (EnvSwitch.isOnline()) {
            queryRequest.setEnv(2);
        }
        return queryRequest;
    }
}
