package com.taobao.trip.jourprod.biz.frt.journeyplan.helper;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;

import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.trip.jourdprod.core.model.jourplan.bizmodel.JourneyChannelBizV3DO;
import com.taobao.trip.jourprod.biz.common.checkin.JourneyVersion3Constant;
import com.taobao.trip.jourprod.biz.frt.journeyplan.channel.CardQueryNode;
import com.taobao.trip.jourprod.service.facade.model.request.journey.channel.CardQueryParam;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplan.JourneyChannelQueryV3Request;
import com.taobao.trip.jourprod.service.facade.model.response.JourPlanError;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplan.newChannel.CardQueryResult;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import java.util.Objects;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import static com.taobao.trip.jourprod.biz.frt.impl.switchconfig.BizSwitch.JOURNEY_HOME_QUERY_PAGE_SIZE;

@Component
public class CardQueryExecutor {

    private final static FliggyNewLogger slsLogger = LogUtil.getFliggyNewLogger(CardQueryExecutor.class);

    public final static String RECOMMEND_NODE_TYPE = "recommend";

    @AteyeInvoker(description = "debugLogger")
    public void debugLogger() {
        slsLogger.recordDangerException(new LogModel("CardQueryExecutor")
            .path(JourneyVersion3Constant.VERSION_3_KEY_PATH)
            .e(new RuntimeException())
            .request(JSON.toJSONString(this)));
    }

    @Resource
    private CardQueryNode cardQueryNode;


    public JourneyChannelBizV3DO execute(Integer defaultQueryCount, JourneyChannelQueryV3Request request) {
        JourneyChannelBizV3DO bizDO = new JourneyChannelBizV3DO();
        try {
            bizDO.setSuccess(true);
            if (Objects.isNull(request)) {
                return bizDO;
            }

            //第一页默认都会查询套餐订单的，在第二页之后会显式的传nextTaoCanPage从2开始后为3、4
            //套餐只传递nextTaoCanPage，不给lastcardId 此时不需要做任何卡片的查询
            if (request.getNextTaoCanPage() != null && request.getNextTaoCanPage() != 0) {
                //以上一次分页结果为主
                bizDO.setLastCardId(request.getLastCardId());
                bizDO.setSuccess(true);
                bizDO.setHasNext(StringUtils.isNotBlank(request.getLastCardId()));
                return bizDO;
            }

            //最后一次成功读取的Result
            String lastCardId = null;
            CardQueryParam param = new CardQueryParam(request.getUserId(),
                    Objects.nonNull(defaultQueryCount)
                            ? defaultQueryCount
                            : JOURNEY_HOME_QUERY_PAGE_SIZE,
                    lastModuleId(request));

            CardQueryResult cardQueryResult = cardQueryNode.query(param, bizDO, request);
            if (!cardQueryResult.isSuccess()) {
                bizDO.setSuccess(false);
                if (cardQueryResult.getPlanError() == null) {
                    bizDO.setPlanError(JourPlanError.SYSTEM_ERROR);
                } else {
                    bizDO.setPlanError(cardQueryResult.getPlanError());
                }
                return bizDO;
            }
            //未来是否还有数据
            boolean hasNext = cardQueryResult.isHasNextPage();
            //只有 有下一页，并且不是推荐页，不管有没有数据（有可能是无效数据），都设置lastCardId，用来翻页
            if (hasNext && !cardQueryResult.getLastCardId().contains(RECOMMEND_NODE_TYPE)) {
                lastCardId = cardQueryNode.getModuleType() + "-" + cardQueryResult.getLastCardId();
            }

            bizDO.setHasNext(hasNext);
            bizDO.setLastCardId(lastCardId);
            return bizDO;
        } catch (Exception e) {
            slsLogger.recordDangerException(new LogModel("CardQueryExecutor")
                    .path(JourneyVersion3Constant.VERSION_3_KEY_PATH)
                    .e(e)
                    .request(JSON.toJSONString(this)));
            return bizDO;
        }
    }

    /**
     * 获取上次查询的模块类型
     */
    private String lastModuleType(JourneyChannelQueryV3Request request) {
        if (StringUtils.isNotEmpty(request.getLastCardId())) {
            String[] array = request.getLastCardId().split("-");
            if (array.length == 2) {
                return array[0];
            }
        }
        return null;
    }

    /**
     * 获取上次查询的模块 ID
     */
    private String lastModuleId(JourneyChannelQueryV3Request request) {
        if (StringUtils.isNotEmpty(request.getLastCardId())) {
            String[] array = request.getLastCardId().split("-");
            if (array.length == 2) {
                return array[1];
            }
        }
        return null;
    }

}
