package com.taobao.trip.jourprod.biz.frt.journeyplan.helper;

import com.alibaba.trip.tripdivision.client.TrdiDivisionReadService;
import com.alibaba.trip.tripdivision.client.vo.TrdiDivisionExtendVO;

import com.google.common.collect.Lists;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.trip.jourprod.common.sal.hsf.tripdivision.TrdiDivisionReadServiceClient;
import com.taobao.trip.tripjourneyop.enums.JourneyTypeEnum;
import fliggy.content.model.FliggyLogger;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.utils.LogUtil;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.taobao.trip.jourdprod.core.model.common.ConstantElement.CHINA_COUNTRY_ID;

/**
 * <AUTHOR>
 * @date 2021/5/31
 */
@Service
public class JourneyCommonHelper implements InitializingBean {

    private static final FliggyLogger logger = LogUtil.getFliggyLogger(JourneyCommonHelper.class);
    private static final FliggyNewLogger newLogger = LogUtil.getFliggyNewLogger(JourneyCommonHelper.class);

    @Autowired
    private TrdiDivisionReadService trdiDivisionReadService;

    @Autowired
    private TrdiDivisionReadServiceClient trdiDivisionReadServiceClient;

    @AppSwitch(des = "不能有效的唯一确认一个目的地的类目", level = Switch.Level.p4)
    public static List<String> MULITY_DESTINATION_JOURNEY_TYPE = Lists.newArrayList();

    static {
        MULITY_DESTINATION_JOURNEY_TYPE.add(JourneyTypeEnum.HOTEL.getName());
        MULITY_DESTINATION_JOURNEY_TYPE.add(JourneyTypeEnum.HOTEL_SHARE.getName());
        MULITY_DESTINATION_JOURNEY_TYPE.add(JourneyTypeEnum.HOTEL_SUB.getName());
        MULITY_DESTINATION_JOURNEY_TYPE.add(JourneyTypeEnum.ENTRANCE.getName());
        MULITY_DESTINATION_JOURNEY_TYPE.add(JourneyTypeEnum.ENTRANCE_SHARE.getName());
        MULITY_DESTINATION_JOURNEY_TYPE.add(JourneyTypeEnum.ENTRANCE_SUB.getName());
        MULITY_DESTINATION_JOURNEY_TYPE.add(JourneyTypeEnum.TICKET.getName());
        MULITY_DESTINATION_JOURNEY_TYPE.add(JourneyTypeEnum.TICKET_SUB.getName());
        MULITY_DESTINATION_JOURNEY_TYPE.add(JourneyTypeEnum.TICKET_SHARE.getName());
        MULITY_DESTINATION_JOURNEY_TYPE.add(JourneyTypeEnum.RENT_CAR.getName());
        MULITY_DESTINATION_JOURNEY_TYPE.add(JourneyTypeEnum.RENT_CAR_SHARE.getName());
        MULITY_DESTINATION_JOURNEY_TYPE.add(JourneyTypeEnum.TRIP_PLAN_NEW.getName());
        MULITY_DESTINATION_JOURNEY_TYPE.add(JourneyTypeEnum.TRIP_PLAN_NEW_SHARE.getName());
        MULITY_DESTINATION_JOURNEY_TYPE.add(JourneyTypeEnum.GROUP_TOUR_NEW.getName());
        MULITY_DESTINATION_JOURNEY_TYPE.add(JourneyTypeEnum.GROUP_TOUR_NEW_SHARE.getName());
    }

    @AppSwitch(des = "港澳台省份id", level = Switch.Level.p4)
    public static List<Long> OUT_PROVIENCE_ID_LIST = Lists.newArrayList(810000L, 820000L, 710000L);

    /**
     * 是否是境外城市 报错港澳台
     */
    public boolean isOutCity(Long cityId) {
        try {
            TrdiDivisionExtendVO divisionExtendById = trdiDivisionReadServiceClient.getDivisionExtendById(cityId);
            if (Objects.isNull(divisionExtendById)) {
                return false;
            }
            Long countryId = divisionExtendById.getCountryId();
            Long provinceId = divisionExtendById.getProvinceId();
            return isAbroadCountry(countryId) || OUT_PROVIENCE_ID_LIST.contains(provinceId);
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean isAbroadCountry(Long countryId) {
        return !CHINA_COUNTRY_ID.equals(countryId);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        SwitchManager.init(JourneyCommonHelper.class);
    }
}
