package com.taobao.trip.jourprod.biz.frt.journeyplan.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.trip.jourprod.biz.frt.eas.model.PredictAnalysisForDivisionData;
import com.taobao.tripca.facade.request.route.JourneyPlanVO;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import java.util.AbstractMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import static com.taobao.trip.jourprod.common.sal.hsf.config.JourneyChannelPlanNewSwitch.PLAN_NEW_WARN_PROVINCE_LIST_CONFIDENCE;

@Service
public class JourneyPlanHelper {

    private final FliggyNewLogger newLogger = LogUtil.getFliggyNewLogger(JourneyPlanHelper.class);

    //短链转长链缓存key
    private static final String LINK_SHORT_TO_LANG_KEY = "tripOd:planNew:shortToLong:";
    //高德poi信息缓存key
    private static final String GAO_DE_POI_INFO_CACHE_KEY = "tripOd:planNew:gaode:";

    //城市名称key
    private static final String PLAN_CITY_NAME_KEY = "name";
    private static final String PLAN_CITY_ID_KEY = "divisionId";

    private static final String GAO_DE_POI_REMARKS = "remarks";
    private static final String GAO_DE_POI_ORIGINAL_URL = "originalUrl";

    //城市的行政区划 级别
    private static final Integer DIVISION_CITY_LEVEL = 4;
    private static final Integer DIVISION_PROVINCE_LEVEL = 3;
    //poi建议游玩时间key
    private static final Long POI_SUGGEST_PLAY_TIME_PROPER_ID = 8L;
    //开放时间
    private static final Long POI_BUSINESS_TIME_PROPER_ID = 2L;
    //是否免费
    private static final Long POI_FREE_PROPER_ID = 42L;
    private static final String POI_FREE_KEY = "pay";
    private static final String POI_NO_FREE_VALUE = "不收费";
    private static final String POI_FREE_VALUE_SHOW = "免费";
    private static final String POI_NO_FREE = "收费";
    //是否预约
    private static final Long POI_BOOKING_PROPER_ID = 300015L;
    private static final String POI_BOOKING_TYPE_KEY = "type";
    //景点等级
    private static final Long POI_LEVEL_PROPER_ID = 9L;

    private static final Long CHINA_COUNTRY_ID = 100000L;

    //链接正则
    private static final Pattern LINK_PATTERN = Pattern.compile("\\b(?:https?|ftp):\\/\\/[^\\s<>\"'，,]+");

    // 热度标签正则
    private static final Pattern HOT_TAG_PATTERN = Pattern.compile("(\\d+)%");

    // 热度标签展示阈值
    private static final int HOT_TAG_THRESHOLD = 50;

    public static final Map<String, String> CITY_NAME_MAP = Maps.newHashMap();
    public static final Map<String, String> CITY_NAME_MAP_REVERSE = Maps.newHashMap();

    static {
        CITY_NAME_MAP.put("香港", "中国香港");
        CITY_NAME_MAP.put("澳门", "中国澳门");
        CITY_NAME_MAP.put("台湾", "中国台湾");
        CITY_NAME_MAP_REVERSE.put("中国香港", "香港");
        CITY_NAME_MAP_REVERSE.put("中国澳门", "澳门");
        CITY_NAME_MAP_REVERSE.put("中国台湾", "台湾");
    }

    public static final String SPLIT_FLAG = ",";

    public static final Long HOTEL_FIRST_CATEGORY = 4L;

    /**
     * 获取 提醒 省列表
     *
     * @param poiProvIdList
     * @param divisionList
     * @return
     */
    public List<String> getProvinceIdListByDivision(List<String> poiProvIdList, List<PredictAnalysisForDivisionData> divisionList) {
        List<String> result = Lists.newArrayList();
        try {
            //行政区划列表
            if (CollectionUtils.isNotEmpty(divisionList)) {
                Map<String, List<PredictAnalysisForDivisionData>> provinceIdListMap = divisionList.stream()
                    .filter(division -> Objects.nonNull(division.getLevel()) && !(division.getLevel().compareTo(DIVISION_PROVINCE_LEVEL) < 0))
                    .filter(division -> Objects.nonNull(division.getConfidence()))
                    .collect(Collectors.groupingBy(division -> division.getIdTree().split(SPLIT_FLAG)[2]));
                // 求和并排序
                List<Map.Entry<String, Double>> sortedKeys = provinceIdListMap.entrySet().stream()
                    .map(entry -> new AbstractMap.SimpleEntry<>(
                        entry.getKey(),
                        entry.getValue().stream().mapToDouble(PredictAnalysisForDivisionData::getConfidence).sum()
                    ))
                    .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
                    .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(sortedKeys)) {
                    result = sortedKeys.stream()
                        .filter(entry -> entry.getValue().compareTo(PLAN_NEW_WARN_PROVINCE_LIST_CONFIDENCE) > 0)
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toList());
                }
            }
            //查询所在省最多poi的 省
            if (CollectionUtils.isEmpty(result)) {
                result = getProvinceNameByPoiCount(poiProvIdList);
            }
        } catch (Exception e) {
            newLogger.recordDangerException(new LogModel("getPlanWarnProvinceListByDivision").e(e).request(JSON.toJSONString(divisionList)));
        }
        return result;
    }



    /**
     * 从规划路线中获取 城市名
     * @param journeyPlanVO
     * @return
     */
    public String dealGetCityNameByPlan(JourneyPlanVO journeyPlanVO) {
        if (Objects.isNull(journeyPlanVO)) {
            return "";
        }
        if (StringUtils.isBlank(journeyPlanVO.getCityList())) {
            return "";
        }
        JSONArray jsonArray = JSONArray.parseArray(journeyPlanVO.getCityList());
        if (CollectionUtils.isEmpty(jsonArray)) {
            return "";
        }
        JSONObject jsonObject = JSONObject.parseObject(jsonArray.get(0).toString());
        String name = jsonObject.getString(PLAN_CITY_NAME_KEY);
        if (StringUtils.isBlank(name)) {
            return "";
        }
        return name;
    }

    /**
     * 从规划路线中获取 城市id
     * @param journeyPlanVO
     * @return
     */
    public Long dealGetCityIdByPlan(JourneyPlanVO journeyPlanVO) {
        if (Objects.isNull(journeyPlanVO)) {
            return null;
        }
        if (StringUtils.isBlank(journeyPlanVO.getCityList())) {
            return null;
        }
        JSONArray jsonArray = JSONArray.parseArray(journeyPlanVO.getCityList());
        if (CollectionUtils.isEmpty(jsonArray)) {
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(jsonArray.get(0).toString());
        String id = jsonObject.getString(PLAN_CITY_ID_KEY);
        if (StringUtils.isBlank(id)) {
            return null;
        }
        return Long.parseLong(id);
    }

    /**
     * 查询所在省最多poi的 省
     * @param allProvinceList
     * @return
     */
    public List<String> getProvinceNameByPoiCount(List<String> allProvinceList) {
        if (CollectionUtils.isEmpty(allProvinceList)) {
            return Lists.newArrayList();
        }
        //poi城市最多的城市
        Map<String, Long> provinceNameCountMap = allProvinceList.stream()
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.groupingBy(info -> info, Collectors.counting()));
        if (MapUtils.isNotEmpty(provinceNameCountMap)) {
            Optional<Entry<String, Long>> optional = provinceNameCountMap.entrySet().stream().max(Map.Entry.comparingByValue());
            if (optional.isPresent()) {
                return Lists.newArrayList(optional.get().getKey());
            }
        }
        return Lists.newArrayList();
    }
}
