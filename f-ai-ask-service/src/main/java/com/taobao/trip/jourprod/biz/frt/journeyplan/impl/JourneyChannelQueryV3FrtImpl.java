package com.taobao.trip.jourprod.biz.frt.journeyplan.impl;

import javax.annotation.Resource;

import com.taobao.trip.jourdprod.core.model.jourplan.bizmodel.JourneyChannelBizV3DO;
import com.taobao.trip.jourprod.biz.frt.journeyplan.JourneyChannelQueryV3Frt;
import com.taobao.trip.jourprod.biz.frt.journeyplan.helper.*;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplan.JourneyChannelQueryV3Request;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.utils.LogUtil;
import java.util.Objects;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
public class JourneyChannelQueryV3FrtImpl implements JourneyChannelQueryV3Frt {

    private final static FliggyNewLogger slsLogger = LogUtil.getFliggyNewLogger(JourneyChannelQueryV3FrtImpl.class);

    @Resource
    private CardQueryExecutor cardQueryExecutor;

    /**
     * 获取用户行程频道索引数据
     * @param userId    userId
     * @param defaultQueryCount
     * @return  行程频道索引数据 如果userid为空，会返回null
     */
    public JourneyChannelBizV3DO fetchUserIndex(Long userId, Integer defaultQueryCount) {
        if (Objects.isNull(userId)) {
            return null;
        }
        JourneyChannelQueryV3Request request = new JourneyChannelQueryV3Request();
        request.setUserId(userId);
        return cardQueryExecutor.execute(defaultQueryCount, request);
    }

}
