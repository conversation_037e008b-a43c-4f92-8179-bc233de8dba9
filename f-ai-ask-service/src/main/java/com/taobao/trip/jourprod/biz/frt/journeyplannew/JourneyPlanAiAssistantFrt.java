package com.taobao.trip.jourprod.biz.frt.journeyplannew;

import java.util.List;

import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatRequest;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatShareRequest;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatViewShareRequest;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiHistoryRequest;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiInitRequest;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiStructRouteRequest;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanQueryPoiInfoByFloatRequest;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanAiAssistantHistoryResult;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanAiAssistantHistoryResult.MessageInfo;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanAiAssistantHistoryWithCurrentChatResult;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanAiInitResult;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanAiStructRouteResult;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanMessageSummaryResult;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanQueryPoiInfoByFloatResult;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanShareMessageResult;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult;
import com.taobao.trip.request.JourneyPlanAiAssistantWhiteUserRequest;
import com.taobao.trip.request.JourneyPlanAiUrlCallbackRequest;
import com.taobao.trip.request.QueryRecentMessageRequest;

/**
 * ai问一问具体逻辑实现接口
 */
public interface JourneyPlanAiAssistantFrt {

    /**
     * 初始化接口，返回提示语
     */
    JourneyPlanAiInitResult init(JourneyPlanAiInitRequest request);

    /**
     * 对话接口
     */
    StreamMessageResult streamChat(JourneyPlanAiChatRequest request);

    /**
     * 对话续接接口
     */
    StreamMessageResult continueChat(JourneyPlanAiChatRequest request);

    /**
     * 停止回答
     */
    Boolean stopChat(JourneyPlanAiChatRequest request);

    /**
     * 返回总结内容
     */
    JourneyPlanMessageSummaryResult chatSummary(JourneyPlanAiChatRequest request);

    /**
     * 删除会话id，同时清空历史记录
     */
    Boolean resetChat(JourneyPlanAiChatRequest request);

    /**
     * 返回历史记录
     */
    JourneyPlanAiAssistantHistoryWithCurrentChatResult history(JourneyPlanAiHistoryRequest request);

    /**
     * 评价消息
     */
    Boolean wrateMessage(JourneyPlanAiChatRequest request);

    /**
     * 查看分享消息
     */
    JourneyPlanAiAssistantHistoryResult viewShareMessage(JourneyPlanAiChatViewShareRequest request);

    /**
     * 分享消息
     */
    JourneyPlanShareMessageResult shareMessage(JourneyPlanAiChatShareRequest request);

    /**
     * 根据消息id查看结构化线路
     */
    JourneyPlanAiStructRouteResult structRoute(JourneyPlanAiStructRouteRequest request);

    /**
     * 查询poi详情用浮层展示
     */
    JourneyPlanQueryPoiInfoByFloatResult queryPoiInfoByFloat(JourneyPlanQueryPoiInfoByFloatRequest request);

    /**
     * 是否有历史消息
     */
    Boolean hasHistory(JourneyPlanAiChatRequest request);

    /**
     * 是否在白名单
     */
    Boolean inWhiteList(JourneyPlanAiAssistantWhiteUserRequest request);

    /**
     * 添加白名单
     */
    Boolean addWhiteList(JourneyPlanAiAssistantWhiteUserRequest request);

    
    Boolean mapUrlCallBack(JourneyPlanAiUrlCallbackRequest request);

    List<MessageInfo> getUserRecentMessageBySource(QueryRecentMessageRequest request);
}
