package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat;

import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneySceneModel;
import com.taobao.trip.jourprod.core.service.converter.AiJourneySceneModelConvert;

import java.util.Objects;
import java.util.Set;

/**
 * @Description 获取支持场景的接口
 * <AUTHOR>
 * @Date 2025/2/7
 **/
public abstract class AbstractSceneSupport {

    /**
     * 判断是否支持某个场景
     */
    public boolean support(AiJourneySceneModel scene) {
        AiJourneyPlanSceneEnum aiJourneyPlanScene = AiJourneySceneModelConvert.extractAiJourneyScene(scene);
        if (Objects.isNull(aiJourneyPlanScene)) {
            return false;
        }
        return getSupportScene().contains(aiJourneyPlanScene);
    }

    /**
     * 支持的场景
     */
    protected abstract Set<AiJourneyPlanSceneEnum> getSupportScene();

}
