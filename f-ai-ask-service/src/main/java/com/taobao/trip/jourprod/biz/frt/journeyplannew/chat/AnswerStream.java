package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat;

import com.alibaba.mtop3.invocation.MtopStream;

import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 先直接输出，后续可支持多意图处理
 *
 * <AUTHOR>
 * @date 2025/3/16
 */
public class AnswerStream {
    private final MtopStream mtopStream;
    /**
     * 可应用于记录历史消息
     */
    @Getter
    private final List<Object> contents = new ArrayList<>();
    @Getter
    private Throwable throwable;


    public AnswerStream(MtopStream mtopStream) {
        this.mtopStream = mtopStream;
    }

    public AnswerStream write(Object obj, AiJourneyPlanSceneEnum agentType) {
        contents.add(obj);
        mtopStream.write(obj);
        return this;
    }

    public void writeAndEnd(Object obj, AiJourneyPlanSceneEnum agentType) {
        contents.add(obj);
        mtopStream.writeAndEnd(obj);
    }

    public void end() {
        mtopStream.end();
    }

    public void endExceptionally(Throwable e) {
        throwable = e;
        mtopStream.endExceptionally(e);
    }

}
