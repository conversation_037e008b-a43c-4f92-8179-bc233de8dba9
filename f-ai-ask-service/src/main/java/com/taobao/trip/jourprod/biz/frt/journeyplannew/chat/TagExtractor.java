package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class TagExtractor {

    /**
     * 移除字符串中的 <f_more> 和 <f_inspiration> 标签及其内容
     *
     * @param message 输入字符串
     * @return 移除标签后的字符串
     */
    public static String removeTags(String message) {
        if (StringUtils.isBlank(message)) {
            return StringUtils.EMPTY;
        }
        // 使用正则表达式移除 <f_more> 和 <f_inspiration> 标签及其内容
        String result = message.replaceAll("(?s)<f_more>.*?</f_more>", "");
        result = result.replaceAll("(?s)<f_inspiration>.*?</f_inspiration>", "");

        return result;
    }

    /**
     * 提取字符串中的 <f_more> 标签内容，并按照 ; 进行分割
     *
     * @param message 输入字符串
     * @return 分割后的字符串列表
     */
    public static List<String> extractFMoreTags(String message) {
        String s = extractTags(message, "<f_more>(.*?)</f_more>");
        if (StringUtils.isBlank(s)) {
            return null;
        }
        String[] split = s.split("例如：");
        if (split.length == 2) {
            String s1 = split[1];
            if (s1.endsWith("）")) {
                s1 = s1.substring(0, s1.length() - 1);
            }
            return Arrays.stream(s1.split("、")).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 提取字符串中的 <f_more> 标签内容
     *
     * @param message 输入字符串
     * @return 提取后的字符串
     */
    public static String extractFMoreString(String message) {
        return extractTags(message, "<f_more>(.*?)</f_more>");
    }

    /**
     * 提取字符串中的 <f_inspiration> 标签内容，并按照 ; 进行分割
     *
     * @param message 输入字符串
     * @return 分割后的字符串列表
     */
    public static List<String> extractFInspirationTags(String message) {
        return extractTagsWithSplit(message, "<f_inspiration>(.*?)</f_inspiration>",";");
    }

    /**
     * 使用正则表达式提取标签内容，并按照指定分割符进行分割
     *
     * @param message 输入字符串
     * @param regex 正则表达式
     * @return 分割后的字符串列表
     */
    private static List<String> extractTagsWithSplit(String message, String regex, String split) {
        List<String> results = new ArrayList<>();
        if (StringUtils.isBlank(message)) {
            return results;
        }

        String content = extractTags(message, regex);
        if (StringUtils.isNotBlank(content)) {
            String[] splitContent = content.split(split);
            for (String part : splitContent) {
                results.add(part.trim());
            }
        }

        return results;
    }

    /**
     * 使用正则表达式提取标签内容，并按照指定分割符进行分割
     *
     * @param message 输入字符串
     * @param regex 正则表达式
     * @return 分割后的字符串列表
     */
    private static String extractTags(String message, String regex) {
        if (StringUtils.isBlank(message)) {
            return StringUtils.EMPTY;
        }

        // 处理多种分隔符
        message = message.replace('；', ';');

        Pattern pattern = Pattern.compile("(?s)" + regex);
        Matcher matcher = pattern.matcher(message);

        // 检查是否存在匹配项
        if (matcher.find()) {
            // 提取第一个捕获组并去除多余空格
            return matcher.group(1).trim();
        } else {
            // 如果未找到匹配项，返回空字符串或其他默认值
            return StringUtils.EMPTY;
        }
    }

    public static void main(String[] args) {
        String input = "制定方案时综合考虑了酒店位置、周边景点、交通便利性和用户可能的旅行需求。北京作为热门旅游城市，选择住宿区域需平衡景点可达性与舒适度。\n" +
                "\n" +
                "# 方案一：住故宫/市中心区域  \n" +
                "**优势分析**：靠近天安门广场、故宫博物院等核心景点，步行可达王府井小吃街、王府井百货，餐饮购物便利。  \n" +
                "**劣势分析**：周边商务酒店较多，亲子设施较少，部分景点如环球影城需1小时以上车程。  \n" +
                "<f_card&1>≌type:hotel_recommend;shid:51450018,10004139≌</f_card&1>  \n" +
                "\n" +
                "# 方案二：住国贸/朝阳区域  \n" +
                "**优势分析**：交通四通八达，地铁覆盖广，适合商务出行，邻近CBD商圈和北京站，跨区通勤高效。  \n" +
                "**劣势分析**：景点分散，需依赖公共交通，部分酒店周边餐饮选择偏向商务简餐。  \n" +
                "<f_card&2>≌type:hotel_recommend;shid:10004139,50033494≌</f_card&2>  \n" +
                "\n" +
                "# 方案三：住亦庄/经济技术开发区区域  \n" +
                "**优势分析**：紧邻环球影城（30分钟车程），适合家庭游客，周边博大公园、南海子公园等自然景观可短途休闲。  \n" +
                "**劣势分析**：商业配套有限，餐饮选择相对单一，跨区域景点需额外规划时间。  \n" +
                "<f_card&3>≌type:hotel_recommend;shid:82307060,60599019≌</f_card&3>  \n" +
                "\n" +
                "# 方案对比  \n" +
                "| **维度** | **住故宫/市中心** | **住国贸/朝阳** | **住亦庄/开发区** |  \n" +
                "| ------- | ------------------ | ---------------- | ------------------ |  \n" +
                "| 酒店价格 | ￥100起          | ￥100起         | ￥100起           |  \n" +
                "| 适合行程 | 深度游览故宫      | 商务及市区观光   | 环球影城+亲子游    |  \n" +
                "| 核心优势 | 景点密集          | 交通枢纽         | 公园便利           |  \n" +
                "| 推荐指数 | ⭐⭐⭐⭐           | ⭐⭐⭐⭐⭐        | ⭐⭐⭐              |  \n" +
                "\n" +
                "# 更多诉求  \n" +
                "<f_more>酒店价格受多因素影响，如果告诉我这些，我可以为你提供更好得方案。  \n" +
                "你有什么样的酒店要求？（例如：星级、价格、位置）</f_more>";
        System.out.println(extractTags(input, "<f_more>(.*?)</f_more>"));
        List<String> fMoreContents = extractFMoreTags(input);
        System.out.println("F_MORE Contents:");
        for (String content : fMoreContents) {
            System.out.println(content);
        }

        List<String> fInspirationContents = extractFInspirationTags(input);
        System.out.println("F_INSPIRATION Contents:");
        for (String content : fInspirationContents) {
            System.out.println(content);
        }

        String input2 = "根据您需求，杭州住宿选择需结合景点分布与交通便利性，现提供以下三个区域方案，价格区间覆盖经济型至特色民宿，确保游玩体验与性价比平衡：\n" +
                "\n" +
                "---\n" +
                "\n" +
                "### 方案一：住西湖区（核心景区附近）\n" +
                "- **优势分析**：毗邻西湖、雷峰塔、河坊街等核心景点，步行可达南宋御街，适合深度体验杭州文化，周边餐饮丰富，如知味观、西湖醋鱼老字号。\n" +
                "- **劣势分析**：旺季酒店价格较高，部分区域早晚高峰地铁拥挤，部分民宿需提前预约。\n" +
                "- **推荐酒店**：  \n" +
                "  <f_card&1>≌type:hotel_recommend;shid:81333587,81330075,82967276;checkIn:20250404;checkOut:20250405≌</f_card&1>\n" +
                "\n" +
                "---\n" +
                "\n" +
                "### 方案二：住拱墅区（运河商务区）\n" +
                "- **优势分析**：地铁1号线直达西湖景区，运河周边有文创园、电竞中心等特色去处，价格较西湖区低30%-50%，适合预算有限且追求便利的游客。\n" +
                "- **劣势分析**：距离西湖约6公里，需20分钟车程，部分公寓无早餐服务，需自备餐饮。\n" +
                "- **推荐酒店**：  \n" +
                "  <f_card&2>≌type:hotel_recommend;shid:81284862,82529025,74472071;checkIn:20250404;checkOut:20250405≌</f_card&2>\n" +
                "\n" +
                "---\n" +
                "\n" +
                "### 方案三：住临平区（临平新城）\n" +
                "- **优势分析**：临平理想银泰城、水景公园等配套完善，地铁直达东站/西湖，适合家庭短租或商务出行，价格最低可至￥200起。\n" +
                "- **劣势分析**：距离西湖30分钟车程，景区游览需规划时间，周边自然景观较少。\n" +
                "- **推荐酒店**：  \n" +
                "  <f_card&3>≌type:hotel_recommend;shid:64264090,81295628,80972536;checkIn:20250404;checkOut:20250405≌</f_card&3>\n" +
                "\n" +
                "---\n" +
                "\n" +
                "### 方案对比  \n" +
                "| <biztype>hotel_compare</biztype>维度 | 西湖区              | 拱墅区              | 临平区              |\n" +
                "|---------------------------------------|---------------------|---------------------|---------------------|\n" +
                "| 酒店价格                             | ￥1399起            | ￥200起             | ￥219起             |\n" +
                "| 适合行程                             | 深度游览西湖景区    | 商务/跨区交通中转   | 家庭购物+短途出行   |\n" +
                "| 核心优势                             | 景区零距离          | 价格适中+地铁直达   | 商业配套完善        |\n" +
                "| 推荐指数                             | ⭐⭐⭐⭐              | ⭐⭐⭐⭐⭐           | ⭐⭐⭐⭐             |\n" +
                "\n" +
                "---\n" +
                "\n" +
                "<f_more>  \n" +
                "酒店价格受景点距离和房型影响较大，若需调整推荐，请告知：  \n" +
                "- 预算范围（如300元内/500元以上）  \n" +
                "- 重点游览区域（西湖/运河/钱江新城等）  \n" +
                "</f_more>\n" +
                "\n" +
                "<f_inspiration>  \n" +
                "西湖边特色民宿推荐;地铁直达的临平酒店;运河周边性价比住宿</f_inspiration>";
        removeTags(input);
    }
}
