package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.content;

import com.alibaba.boot.switchcenter.annotation.SwitchGroup;
import com.alibaba.fastjson.JSON;
import com.alibaba.mtop3.invocation.MtopStream;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.overview.ChatOverviewGeneratorProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.stream.AbstractOutputStreamProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.stream.NodeStreamCollector;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.stream.TextStreamCollector;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.stream.impl.ModelWithoutSplitorOutputStreamProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.stream.impl.ModelXmlOutputStreamProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.template.JourneyPlanTemplateRenderer;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.template.JourneyPlanParallelProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.BaseDependencyBean;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiCsiHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneySceneModel;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.biz.frt.llm.LlmGatewayService;
import com.taobao.trip.jourprod.biz.frt.llm.domain.*;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.jourprod.common.sal.exception.AiAskInterruptException;
import com.taobao.trip.jourprod.common.sal.hsf.config.LLMSwitcher;
import com.taobao.trip.jourprod.core.service.converter.AiJourneySceneModelConvert;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatRequest;
import com.taobao.trip.jourprod.service.facade.model.response.JourPlanError;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult;
import com.taobao.trip.jourprod.utils.AskMonitorLoggerUtil;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.TriRpcStatus;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Value;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.constant.JourneyPlanAiAssistantConstant.LOG_LEVEL_TYPE;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanStatusEnum.*;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch.AI_STREAM_PRE_AFTER_TOKEN_S;

/**
 * @Description 聊天内容生成,每个场景都有自己的内容生成方式,同时会提供记录历史消息、更新状态等基础功能
 * <AUTHOR>
 * @Date 2025/1/30
 **/
@Component
@SwitchGroup
public class ChatContentGenerator extends BaseDependencyBean {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(ChatContentGenerator.class);

    protected static final Map<String, String> MESSAGE_ID_2_CONTENT_MAP = Maps.newConcurrentMap();

    protected static final Map<String, Integer> PROGRESS_MAP = Maps.newConcurrentMap();

    /**
     * 滑动窗口每轮内容缓存,key为messageId,value为每轮内容的列表
     */
    protected static final Map<String, List<String>> SLIDING_WINDOW_CHUNKS_MAP = Maps.newConcurrentMap();

    @AppSwitch(des = "绿网检测白名单", level = Level.p4)
    public static Set<Long> CSI_USER_IDS = Sets.newHashSet();

    @AppSwitch(des = "绿网检测开启", level = Level.p4)
    public static boolean CSI_OPEN = false;

    /**
     * 基础窗口大小,实际窗口大小为WINDOW_SIZE * 3
     */
    @AppSwitch(des = "滑动窗口基础大小", level = Level.p4)
    public static int CSI_CHECK_WINDOW_SIZE = 20;

    @AppSwitch(des = "是否启用模板渲染", level = Level.p4)
    public static boolean templateEnabled = false;

    @AppSwitch(des = "支持模板展示的场景", level = Level.p4)
    public static Set<String> templateEnabledScenes = Sets.newHashSet("JOURNEY_PLAN");

    @AppSwitch(des = "模板渲染超时时间", level = Level.p4)
    public static int TEMPLATE_RENDERING_TIMEOUT_SECONDS = 30;

    @AppSwitch(des = "解析json需要替换的字符", level = Level.p4)
    public static List<String> PARSE_JSON_REPLACE_STR = Lists.newArrayList("\\n", "\n", "\\r", "\r", "```json", "```");

    @Resource
    protected ChatOverviewGeneratorProcessor chatOverviewGeneratorProcessor;

    @Resource
    private LlmGatewayService llmGatewayService;

    @Resource
    private JourneyPlanAiCsiHelper journeyPlanAiCsiHelper;

    @Resource
    private AiJourneySceneModelConvert aiJourneySceneModelConvert;

    @Resource
    private JourneyPlanTemplateRenderer templateRenderer;

    @Resource
    private JourneyPlanParallelProcessor parallelProcessor;

    /**
     * 生成聊天内容
     *
     * @param chatContext 对话上下文
     * @param mtopStream  mtop输出流
     */
    public void generateChatContent(ChatContext chatContext, MtopStream mtopStream) throws Throwable {
        JourneyPlanAiChatRequest request = chatContext.getRequest();
        // 更新状态
        AiJourneySceneModel aiJourneySceneModel = chatContext.getAiJourneySceneModel();

        // 快速返回,不走后续推荐逻辑
        String fastReturnContent = aiJourneySceneModelConvert.fastReturnWithoutRecommend(chatContext, mtopStream);
        // 生成对话内容
        try {
            if (StringUtils.isNotBlank(fastReturnContent)) {
                chatContext.setOriginalContent(fastReturnContent);
            } else {
                doGenerateChatContent(request, aiJourneySceneModel, mtopStream, chatContext);
            }
            mtopStream.writeAndEnd(JSON.toJSONString(StreamMessageResult.heartBeat()));
            // 更新状态
            messageStatusHelper.updateStatus(chatContext.getSystemMessageId(), END);
        } catch (Exception e) {
            messageStatusHelper.updateStatus(chatContext.getSystemMessageId(), STOP);
            throw  e;
        } finally {
            // 启动总结任务
            chatOverviewGeneratorProcessor.generateChatOverview(request, aiJourneySceneModel, chatContext);
        }

    }

    /**
     * 拼接完整内容,大模型是一点一点吐的
     */
    protected String recordContent(String currentMessageId, String content) {
        String oldContent = MapUtils.getString(MESSAGE_ID_2_CONTENT_MAP, currentMessageId, StringUtils.EMPTY);
        oldContent = oldContent + content;
        MESSAGE_ID_2_CONTENT_MAP.put(currentMessageId, oldContent);
        return oldContent;
    }

    /**
     * 清理map中记录的中间值
     */
    protected void clearContentMap(String currentMessageId) {
        SLIDING_WINDOW_CHUNKS_MAP.remove(currentMessageId);
        MESSAGE_ID_2_CONTENT_MAP.remove(currentMessageId);
        PROGRESS_MAP.remove(currentMessageId);
        journeyPlanAiAssistantContentHelper.clearContentSeq(currentMessageId);
    }

    /**
     * 生成对话内容
     *
     * @param request
     * @param aiJourneySceneModel
     * @param mtopStream
     */
    protected void doGenerateChatContent(JourneyPlanAiChatRequest request, AiJourneySceneModel aiJourneySceneModel, MtopStream mtopStream, ChatContext chatContext) throws Throwable {
        final String systemMessageId = chatContext.getSystemMessageId();
        final String userMessageId = chatContext.getUserMessageId();
        final String sessionId = chatContext.getSessionId();

        String requestLogStr = String.format("会话ID:%s, 系统消息ID:%s", sessionId, userMessageId);

        // Setup
        LlmRequest llmRequest = new LlmRequest();
        llmRequest.setChatContext(chatContext);
        llmRequest.setAiJourneySceneModel(aiJourneySceneModel);
        llmRequest.setJourneyPlanAiChatRequest(request);
        llmRequest.setMessages(getMessages(chatContext));

        long start = System.currentTimeMillis();
        final long[] preStart = {System.currentTimeMillis()};
        final boolean[] success = {true};
        final Throwable[] interruptThrowable = {null};

        // 流式内容解析
        List<AbstractOutputStreamProcessor> outputStreamProcessors = generateOutputStreamProcessors(request, chatContext, aiJourneySceneModel, mtopStream);
        try {
            boolean stop = messageStatusHelper.checkMessageStatus(systemMessageId, Sets.newHashSet(STOP, END, BLOCK));
            if (stop){
                LOGGER.recordEntry(new LogModel("doGenerateChatContent")
                        .message("当前消息已被用户手动停止，不进行模型服务调用")
                        .code(LOG_LEVEL_TYPE)
                        .request(requestLogStr));
                return;
            }
            LOGGER.recordOutput(new LogModel("doGenerateChatContent")
                    .message("当前消息没有被停止，可以调用AiSearch服务获取模型输出")
                    .request(requestLogStr));

            llmGatewayService.executeStream(this.select(aiJourneySceneModel), llmRequest, new LlmStreamResponseCallback() {
                @Override
                public void onMessage(LlmStreamResponse chunkResponse) {
                    // todo 初始判断状态
                    chatContext.getStreamCount().incrementAndGet();

                    // 获取流式输出内容
                    String content = Optional.ofNullable(chunkResponse.getContent()).orElse(StringUtils.EMPTY);

                    Map<String, Object> thinkingThought = chunkResponse.getThinkingThought();
                    if (thinkingThought != null) {
                        if (chatContext.getFirstThinkingTokenMs() == 0L) {
                            chatContext.setFirstThinkingTokenMs(System.currentTimeMillis());
                        } else {
                            chatContext.setLastThinkingTokenTimeMs(System.currentTimeMillis());
                        }
                        chatContext.getThinkingThought().add(thinkingThought);
                    } else {
                        if (StringUtils.isNotBlank(content)) {
                            if (chatContext.getFirstContentTokenTimeMs() == 0L) {
                                chatContext.setFirstContentTokenTimeMs(System.currentTimeMillis());
                            }
                        }
                    }

                    String outputContent = extractStreamContent(systemMessageId, content, outputStreamProcessors, chatContext, mtopStream);
                    // 输出内容,并记录历史消息
                    journeyPlanAiAssistantContentHelper.outputContent(mtopStream, thinkingThought, outputContent, null, request, chatContext);
                    long endTime = System.currentTimeMillis();

                    // 记录消息
                    if (endTime - preStart[0] > AI_STREAM_PRE_AFTER_TOKEN_S * 1000) {
                        LOGGER.recordOutput(new LogModel("genContent_token_gap_time")
                                .request(JSON.toJSONString(request))
                                .code(String.join(",", chatContext.getSessionId(), systemMessageId, chatContext.getUserMessageId())));
                    }
                    preStart[0] = endTime;
                }

                @Override
                public void onError(Throwable throwable) {
                    // todo 状态记录到日志
                    // todo 销毁异常日志记录
                    if (throwable instanceof AiAskInterruptException) {
                        AiAskInterruptException interruptException = (AiAskInterruptException) throwable;
                        // 对话被中断需要打印异常
                        // thinking首字输出时间
                        printPerformance(interruptException);
                        // 如果是CANCEL，则认定为正常操作，仅记录日志即可，不需要调用onError方法
                        if (TriRpcStatus.CANCELLED.code.equals(interruptException.getErrorCode().code)) {
                            return;
                        } else {
                            interruptThrowable[0] = interruptException;
                        }
                    }
                    journeyPlanAiAssistantContentHelper.clearContentMap(chatContext.getSystemMessageId());
                    messageStatusHelper.updateStatus(chatContext.getSystemMessageId(), END);
                    LOGGER.recordNormalException(new LogModel("doGenerateChatContentWithGateWay_streamChat_error")
                            .response(JSON.toJSONString(request))
                            .code(String.join(",", chatContext.getSessionId(), chatContext.getSystemMessageId(), chatContext.getUserMessageId()))
                            .e(throwable)
                            .message("对话接口异常"));
                    success[0] = false;
                    mtopStream.write(StreamMessageResult.failed(JourPlanError.AI_JOURNEY_CHAT_ERROR));
                    mtopStream.endExceptionally(throwable);
                }

                private void printPerformance(AiAskInterruptException interruptException) {
                    Long thinkFirstTime = null;
                    if(chatContext.getFirstThinkingTokenMs() > 0){
                        thinkFirstTime = chatContext.getFirstThinkingTokenMs() - start;
                    }
                    Long contentFirstTime = null;

                    // 正文首字输出时间
                    if(chatContext.getFirstContentTokenTimeMs() > 0){
                        contentFirstTime = chatContext.getFirstContentTokenTimeMs() - start;
                    }
                    Long thinkTime = null;
                    // thinking输出耗时
                    if(chatContext.getLastThinkingTokenTimeMs() > 0 && chatContext.getFirstThinkingTokenMs() > 0){
                        thinkTime = chatContext.getLastThinkingTokenTimeMs() - chatContext.getFirstThinkingTokenMs();
                    }

                    // 正文输出耗时
                    Long contentTime = null;
                    if(chatContext.getFirstContentTokenTimeMs() > 0){
                        contentTime = System.currentTimeMillis() - chatContext.getFirstContentTokenTimeMs();
                    }

                    Long chainContentGap = null;
                    if(chatContext.getFirstContentTokenTimeMs() > 0 && chatContext.getLastThinkingTokenTimeMs() > 0){
                        chainContentGap = chatContext.getFirstContentTokenTimeMs() - chatContext.getLastThinkingTokenTimeMs();
                    }

                    LOGGER.recordOutput(new LogModel("performanceGenerateChatContent")
                        .message(thinkFirstTime + "^" + contentFirstTime + "^" + thinkTime + "^" + contentTime + "^" + chainContentGap));
                    AskMonitorLoggerUtil.logInterruptMessage(chatContext.getSystemMessageId(), chatContext.getRequest().getUserId(),
                            chatContext.getAiJourneySceneModel().getType(),
                            interruptException.getErrorCode().code.name(), interruptException.getErrorMsg(), thinkFirstTime, thinkTime, contentTime, chainContentGap);
                }

                @Override
                public void onComplete() {
                    // 思考过程或者内容返回为空,返回错误,让前端用户感知并可以重试,不能让用户感觉一直在卡住
                    String content = MESSAGE_ID_2_CONTENT_MAP.get(chatContext.getSystemMessageId());
                    if (StringUtils.isBlank(content)) {
                        LOGGER.recordDangerException(new LogModel("onCompleted")
                                .message("内容为空")
                                .code(String.join(",", chatContext.getSessionId(), chatContext.getSystemMessageId(), chatContext.getUserMessageId())));
                        mtopStream.write(StreamMessageResult.failed(JourPlanError.AI_JOURNEY_CONTENT_NULL_ERROR));
                        mtopStream.endExceptionally(new RuntimeException());
                    } else {
                        LOGGER.recordOutput(new LogModel("onCompleted")
                                .message("正常输出")
                                .code(String.join(",", chatContext.getSessionId(), chatContext.getSystemMessageId(), chatContext.getUserMessageId())));
                    }
                }
            });
        } catch (Exception ex) {
            LOGGER.recordNormalException(new LogModel("doGenerateChat")
                    .code(String.join(",", chatContext.getSessionId(), chatContext.getSystemMessageId(), chatContext.getUserMessageId()))
                    .e(ex));
        } finally {
            // 记录输出的原始内容
            String content = MESSAGE_ID_2_CONTENT_MAP.get(chatContext.getSystemMessageId());
            chatContext.setOriginalContent(content);

            // 记录日志
            LOGGER.recordOutput(new LogModel("doGenerateChatContent")
                    .request(JSONUtil.toJSONStringForLog(request))
                    .code(String.join(",", chatContext.getSessionId(), chatContext.getSystemMessageId(), chatContext.getUserMessageId()))
                    .message("回答结束")
                    .response("content:" + JSONUtil.removeVerticalBar(content))
                    .path("content is blank:" + StringUtils.isNotBlank(content))
                    .success(success[0])
                    .cost(System.currentTimeMillis() - start));

            // 完整输出再过一次风控
            journeyPlanAiCsiHelper.syncCheckBlocked(chatContext.getSystemMessageId(), content, chatContext);

            // 清理map中记录的中间值
            clearContentMap(chatContext.getSystemMessageId());

            // 性能日志,只在正常输出的情况下统计性能
            if (success[0] && StringUtils.isNotBlank(content)) {
                logPerformanceLog(chatContext, request.getRequestTime().getTime(), content);
            }
        }

        if (!success[0]) {
            LOGGER.recordOutput(new LogModel("doGenerateChatContent")
                .request(JSONUtil.toJSONStringForLog(request))
                .code(String.join(",", chatContext.getSessionId(), chatContext.getSystemMessageId(), chatContext.getUserMessageId()))
                .message("回答异常结束")
                .success(success[0]));
            // 详细异常继续往外抛
            if (interruptThrowable[0] == null) {
                throw new RuntimeException("对话接口异常,执行onError回调");
            } else {
                throw interruptThrowable[0];
            }
        }

    }

    private void logPerformanceLog(ChatContext chatContext, long start, String content) {
        if (chatContext.getFirstThinkingTokenMs() == 0 || chatContext.getFirstContentTokenTimeMs() == 0 || chatContext.getLastThinkingTokenTimeMs() == 0) {
            return;
        }

        // thinking首字输出时间
        long thinkFirstTime = chatContext.getFirstThinkingTokenMs() - start;
        // 正文首字输出时间
        long contentFirstTime = chatContext.getFirstContentTokenTimeMs() - start;
        // thinking输出耗时
        long thinkTime = chatContext.getLastThinkingTokenTimeMs() - chatContext.getFirstThinkingTokenMs();
        // 正文输出耗时
        long contentTime = System.currentTimeMillis() - chatContext.getFirstContentTokenTimeMs();

        long chainContentGap = chatContext.getFirstContentTokenTimeMs() - chatContext.getLastThinkingTokenTimeMs();
        // 正文每秒输出的token数（or 字符数）
        long charsPerSecond = (contentTime == 0 ? content.length() : content.length() / (contentTime / 1000));
        AskMonitorLoggerUtil.logSuccessMessage(chatContext.getSystemMessageId(), chatContext.getRequest().getUserId(), chatContext.getAiJourneySceneModel().getType(), thinkFirstTime, thinkTime, contentTime, chainContentGap);
        LOGGER.recordOutput(new LogModel("performanceGenerateChatContent")
                .message(thinkFirstTime + "^" + contentFirstTime + "^" + thinkTime + "^" + contentTime + "^" + charsPerSecond + "^" + chainContentGap));
    }

    private List<MessageParam> getMessages(ChatContext chatContext) {
        List<MessageParam> messages = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(chatContext.getOriginalMessageInfo())) {
            messages.addAll(chatContext.getOriginalMessageInfo());
        }
        return messages;
    }

    /**
     * 生成流式处理器
     */
    public List<AbstractOutputStreamProcessor> generateOutputStreamProcessors(JourneyPlanAiChatRequest request, ChatContext chatContext, AiJourneySceneModel aiJourneySceneModel, MtopStream mtopStream) {
        ModelXmlOutputStreamProcessor modelXmlOutputStreamProcessor = new ModelXmlOutputStreamProcessor(request, response -> {
            NodeStreamCollector processor = new NodeStreamCollector(aiJourneySceneModel.getSceneEnum());
            processor.collect(response, request, mtopStream, chatContext);
        });
        ModelWithoutSplitorOutputStreamProcessor modelWithoutSplitorOutputStreamProcessor = new ModelWithoutSplitorOutputStreamProcessor(request, (content) -> {
            TextStreamCollector textStreamCollector = new TextStreamCollector(chatContext, aiJourneySceneModel.getSceneEnum());
            textStreamCollector.collect(content);
        });
        return Lists.newArrayList(modelXmlOutputStreamProcessor, modelWithoutSplitorOutputStreamProcessor);
    }

    /**
     * 处理滑动窗口内容
     * @param currentMessageId 当前消息ID
     * @param content 新内容
     */
    private void processSlidingWindow(String currentMessageId, String content, ChatContext chatContext) {
        if (!CSI_OPEN && !CSI_USER_IDS.contains(chatContext.getRequest().getUserId())) {
            return;
        }

        int currentCount = chatContext.getStreamCount().get();
        synchronized (currentMessageId.intern()) {
            List<String> chunks = SLIDING_WINDOW_CHUNKS_MAP.computeIfAbsent(currentMessageId,
                    k -> Lists.newArrayList());
            chunks.add(content);

            int maxChunks = CSI_CHECK_WINDOW_SIZE * 3;
            if (chunks.size() > maxChunks) {
                chunks = new ArrayList<>(chunks.subList(chunks.size() - maxChunks, chunks.size()));
                SLIDING_WINDOW_CHUNKS_MAP.put(currentMessageId, chunks);
            }

            if (currentCount % CSI_CHECK_WINDOW_SIZE == 0) {
                String windowContent = String.join("", chunks);
                journeyPlanAiCsiHelper.asyncCheckBlocked(currentMessageId, windowContent, chatContext);
            }
        }
    }

    /**
     * 获取并记录输出内容
     */
    protected String extractStreamContent(String currentMessageId, String content, List<AbstractOutputStreamProcessor> outputStreamProcessors, ChatContext chatContext, MtopStream mtopStream) {
        if (StringUtils.isEmpty(content)) {
            return content;
        }

        if (messageStatusHelper.isStop(currentMessageId)) {
            return StringUtils.EMPTY;
        }

        // 记录完整内容
        // todo 记录思维链
        String fullContent = recordContent(currentMessageId, content);

        // 处理滑动窗口
        processSlidingWindow(currentMessageId, content, chatContext);

        // 排查问题,每50个token打印一次
        if (chatContext.getStreamCount().get() % LLMSwitcher.OUTPUT_TOKENS_PRINT_LOG == 0) {
            LOGGER.recordOutput(new LogModel("ai.originContent.JourneyPlanNodeProcessor")
                    .message("currentMessageId-{0}", currentMessageId)
                    .response(JSONUtil.removeVerticalBar(fullContent)));
        }

        // ========== 模板渲染逻辑处理 ==========
        // 判断是否使用模板渲染模式
        if (shouldUseTemplateRendering(chatContext.getAiJourneySceneModel().getSceneEnum())) {
            content = handleTemplateRendering(chatContext, fullContent, currentMessageId, outputStreamProcessors);
        }

        // 依次处理消息
        for (AbstractOutputStreamProcessor processor : outputStreamProcessors) {
            processor.onTokenReceived(content);
        }

        // 提取要输出的消息
        List<String> messageList = chatContext.getMessageList();
        String allMessage = String.join("", messageList);
        chatContext.getMessageList().clear();

        return allMessage;
    }

    /**
     * 判断走ai哪一种模式
     * @return
     */
    private LlmProviderIdentityEnum select(AiJourneySceneModel aiJourneySceneModel) {
        if (aiJourneySceneModel == null || aiJourneySceneModel.getSceneEnum() == null || aiJourneySceneModel.getSceneEnum() == AiJourneyPlanSceneEnum.NOT_SUPPORT_SCENE) {
            return LlmProviderIdentityEnum.CHIT_CHAT;
        }
        return LlmProviderIdentityEnum.AI_STRATEGY;
    }

    /**
     * 处理模板渲染逻辑
     *
     * @param chatContext 聊天上下文
     * @param fullContent 完整内容
     * @param currentMessageId 当前消息ID
     * @param outputStreamProcessors 输出流处理器列表
     * @return 如果需要跳过后续处理返回相应字符串，否则返回null继续原有流程
     */
    private String handleTemplateRendering(ChatContext chatContext,
                                         String fullContent,
                                         String currentMessageId,
                                         List<AbstractOutputStreamProcessor> outputStreamProcessors) {
        // 检查是否为完整的JSON
        if (isCompleteJson(fullContent)) {
            LOGGER.recordOutput(new LogModel("templateRendering")
                    .message("检测到完整JSON，开始模板渲染，messageId: {0}", currentMessageId)
                    .response("JSON长度: " + fullContent.length()));

            try {
                // 简单的JSON完整性检查
                for (String str : PARSE_JSON_REPLACE_STR) {
                    fullContent = fullContent.replace(str, StringUtils.EMPTY);
                }
                // 解析JSON并进行模板渲染
                JSONObject jsonData = JSON.parseObject(fullContent);
                return processTemplateRendering(jsonData, chatContext, outputStreamProcessors);
            } catch (Exception e) {
                LOGGER.recordDangerException(new LogModel("templateRendering")
                        .message("模板渲染失败，降级到流式处理，messageId: {0}", currentMessageId)
                        .e(e));

                // 渲染失败时继续使用流式处理
                return StringUtils.EMPTY;
            }
        } else {
            // JSON未完成，不输出任何内容，等待更多数据
            LOGGER.recordOutput(new LogModel("templateRendering")
                    .message("JSON未完成，跳过输出，messageId: {0}", currentMessageId)
                    .response("当前内容长度: " + fullContent.length()));

            return StringUtils.EMPTY;
        }
    }

    /**
     * 判断是否应该使用模板渲染
     */
    private boolean shouldUseTemplateRendering(AiJourneyPlanSceneEnum sceneEnum) {
        return templateEnabled &&
               Objects.nonNull(sceneEnum) &&
               templateEnabledScenes.contains(sceneEnum.name());
    }

    /**
     * 检查是否为完整的JSON
     */
    private boolean isCompleteJson(String content) {
        if (StringUtils.isBlank(content)) {
            return false;
        }

        try {
            // 简单的JSON完整性检查
            for (String str : PARSE_JSON_REPLACE_STR) {
                content = content.replace(str, StringUtils.EMPTY);
            }
            if (StringUtils.isBlank(content)) {
                return false;
            }
            JSON.parseObject(content);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 处理模板渲染
     */
    private String processTemplateRendering(JSONObject jsonData,
                                          ChatContext chatContext,
                                          List<AbstractOutputStreamProcessor> outputStreamProcessors) {
        try {
            // 1. 数据验证和预处理
            validateJsonData(jsonData);

            // 2. 并行处理和模板渲染
            CompletableFuture<String> renderFuture = parallelProcessor.processJourneyPlanParallel(jsonData, chatContext);

            // 3. 获取渲染结果（设置超时）
            return renderFuture.get(TEMPLATE_RENDERING_TIMEOUT_SECONDS, TimeUnit.SECONDS);

        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("processTemplateRendering")
                    .message("模板渲染处理失败")
                    .request(JSONUtil.toJSONStringForLog(jsonData))
                    .e(e));
            throw new RuntimeException("模板渲染失败", e);
        }
    }

    /**
     * 验证JSON数据
     */
    private void validateJsonData(JSONObject jsonData) {
        // 验证必要字段
        if (!jsonData.containsKey("journeyIntro")) {
            throw new IllegalArgumentException("缺少journeyIntro字段");
        }
        if (!jsonData.containsKey("dailyPlans")) {
            throw new IllegalArgumentException("缺少dailyPlans字段");
        }

        // 验证ID连续性
        //templateRenderer.validateAndProcessIds(jsonData);
    }

}
