package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.mtop3.invocation.MtopStream;
import com.google.common.collect.Lists;
import com.taobao.eagleeye.EagleEye;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.CollectionBizTypeEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantContentHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.common.sal.hsf.collection.CollectionHelper;
import com.taobao.trip.jourprod.common.sal.hsf.config.Switcher;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.ComponentDataResponse;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatRequest;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult.StreamMessageCardModel;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.wireless.Ssif;
import fliggy.content.common.logger.LogRunnable;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.*;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch.IS_DEBUG_OUTPUT_LOGGING;
import static com.taobao.trip.jourprod.utils.JSONUtil.getObject;

/**
 * 展示卡片渲染处理器
 *
 * <AUTHOR>
 * @date 2025/3/16
 */
public abstract class AbstractNodeProcessor {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(AbstractNodeProcessor.class);

    @Resource
    private JourneyPlanAiAssistantContentHelper journeyPlanAiAssistantContentHelper;
    @Resource
    private CollectionHelper collectionHelper;
    /**
     * 线程池
     */
    private static final ThreadPoolExecutor executor = new ThreadPoolExecutor(50, 200, 30, TimeUnit.SECONDS,
        new LinkedBlockingQueue<>(10),
        new CustomizableThreadFactory("NodeProcessorExecutor"),new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 异步执行渲染逻辑，结束后输出到mtop流中
     *
     * @param mtopStream    mtop流
     * @param request       请求
     * @param response      节点数据信息
     * @param chatContext   对话上下文
     */
    public void process(ComponentDataResponse response, JourneyPlanAiChatRequest request, MtopStream mtopStream, ChatContext chatContext) {
        Class<? extends AbstractNodeProcessor> clazz = getClass();

        // 获取当前消息的异步处理器等待列表
        List<CompletableFuture<Void>> futures = chatContext.getOrCreateFutures(chatContext.getUserMessageId());
        CompletableFuture<Void> future = CompletableFuture.runAsync(new LogRunnable() {
            @Override
            public void logRun() {
                try {
                    List<StreamMessageCardModel> cardModelList = new CopyOnWriteArrayList<>();
                    // 获取所有符合条件的处理方法
                    Method method = findEligibleMethod(clazz, response.getData());

                    // 准备方法参数
                    Object[] params = new Object[]{response, chatContext};
                    if (method != null) {
                        List<StreamMessageCardModel<Object>> result = Lists.newArrayList();
                        try {
                            method.setAccessible(true);
                            result = (List<StreamMessageCardModel<Object>>) method.invoke(AbstractNodeProcessor.this, params);
                            if (Objects.nonNull(result)) {
                                cardModelList.addAll(result);
                            }
                        } catch (Exception e) {
                            LOGGER.recordDangerException(new LogModel(method.getName())
                                    .message("methodExecuteError")
                                    .request(IS_DEBUG_OUTPUT_LOGGING ? JSONUtil.toJSONStringForLog(params) : JSONUtil.toJSONStringForLog(request))
                                    .e(e));
                        }

                        try {
                            // 处理收藏标识
                            processCollectionData(method, result, request);
                        } catch (Exception e) {
                            LOGGER.recordDangerException(new LogModel("processCollectionData").message("execute_error")
                                    .request(JSONUtil.toJSONStringForLog(params)).response(JSONUtil.toJSONStringForLog(result)).e(e));
                        }
                    }
                    // 兜底方法
                    else {
                        Optional.ofNullable(doProcess(response, chatContext)).ifPresent(cardModelList::addAll);
                    }
                    if (CollectionUtils.isNotEmpty(cardModelList)) {
                        chatContext.addComponents(cardModelList);
                    }
                    if(null != mtopStream){
                        journeyPlanAiAssistantContentHelper.outputContent(mtopStream, StringUtils.EMPTY, StringUtils.EMPTY, cardModelList, request, chatContext);
                    }
                    // 输出内容，并记录历史消息
                    LOGGER.recordOutput(new LogModel("AbstractNodeProcessor_process")
                            .message("output_components")
                            .request(IS_DEBUG_OUTPUT_LOGGING ? JSONUtil.toJSONStringForLog(params) : JSONUtil.toJSONStringForLog(request)));
                } catch (Exception ex) {
                    LOGGER.recordDangerException(new LogModel("AbstractNodeProcessor_process").message("execute_error").e(ex));
                } finally {
                    EagleEye.clearRpcContext();
                    MDC.clear();

                    Ssif.removeSsifContext();
                }
            }
        }, executor);
        // 添加到等待器里
        futures.add(future);
        LOGGER.recordOutput(new LogModel("AbstractNodeProcessor_process").message("add_components_future").request(JSONUtil.toJSONStringForLog(response.getData())));
    }

    /**
     * 执行渲染逻辑
     *
     * @param chatContext   对话上下文
     */
    protected abstract List<StreamMessageCardModel<?>> doProcess(ComponentDataResponse response, ChatContext chatContext);

    /**
     * 支持的场景
     */
    protected abstract AiJourneyPlanSceneEnum supportAgentType();

    /**
     * 支持的组件类型
     */
    protected abstract Set<String> supportComponentType();



    /**
     * 是否需要完整数据，默认为false
     */
    public boolean needCompleteData(){
        return false;
    }

    /**
     * 查找所有符合条件的处理方法
     *
     * @param clazz 待检测的类
     * @param data  当前数据对象
     * @return 符合条件的方法列表
     */
    private Method findEligibleMethod(Class<?> clazz, JSONObject data) {
        // 遍历当前类及其父类的方法
        while (clazz != Object.class) {
            for (Method method : clazz.getDeclaredMethods()) {
                // 检查是否存在条件注解
                MethodCondition condition = method.getAnnotation(MethodCondition.class);
                if (condition == null) {
                    continue;
                }

                // 提取配置信息
                String targetField = condition.field();
                String requiredValue = condition.value();

                // 获取实际数据值
                String actualValue = data.getString(targetField);

                // 匹配成功则直接返回该方法
                if (requiredValue.equals(actualValue)) {
                    return method;
                }
            }

            // 继续向上层类查找
            clazz = clazz.getSuperclass();
        }

        return null;
    }

    private void processCollectionData(Method method, List<StreamMessageCardModel<Object>> cardModelList, JourneyPlanAiChatRequest request) {
        // 返回的卡片信息不包含收藏
        if (!method.isAnnotationPresent(EnableCollection.class) || !Switcher.AI_ASK_COLLECTION_ENABLED) {
            return;
        }
        if (CollectionUtils.isEmpty(cardModelList) || request == null || request.getUserId() == null) {
            LOGGER.recordNormalException(new LogModel("processCollectionData")
                    .message("param_error")
                    .request(JSONUtil.toJSONStringForLog(request)));
            return;
        }

        EnableCollection enableCollection = method.getAnnotation(EnableCollection.class);
        for (StreamMessageCardModel<Object> streamMessageCardModel : cardModelList) {
            if (streamMessageCardModel == null || streamMessageCardModel.getData() == null) {
                continue;
            }
            try {
                Object data = streamMessageCardModel.getData();
                if (data instanceof List) {
                    // 兼容返回的数据是卡片列表场景
                    List<JSONObject> dataList = JSONArray.parseArray(JSONObject.toJSONString(data), JSONObject.class);
                    dataList.forEach(cardJson -> parseData(enableCollection, cardJson, request.getUserId()));
                    streamMessageCardModel.setData(dataList);
                } else {
                    // 返回的卡片数据
                    JSONObject cardJson = JSON.parseObject(JSONUtil.toJSONString(data));
                    parseData(enableCollection, cardJson, request.getUserId());
                    streamMessageCardModel.setData(cardJson);
                }
            } catch (Exception e) {
                LOGGER.recordDangerException(new LogModel("processCollectionData").message("execute_error")
                        .request(JSONUtil.toJSONStringForLog(method)).response(JSONUtil.toJSONStringForLog(cardModelList)).e(e));
            }
        }
    }

    /**
     * todo 异常日志
     * @param enableCollection
     * @param cardJson
     * @param userId
     */
    private void parseData(EnableCollection enableCollection, JSONObject cardJson, Long userId) {
        // 收藏的业务类型
        CollectionBizTypeEnum collectionBizTypeEnum = enableCollection.bizType();
        // 提取业务id和type
        StringUtils.isNotBlank(enableCollection.bizIdColumn());
        String bizType = collectionBizTypeEnum.getBizType();
        String bizIdColumn = StringUtils.isNotBlank(enableCollection.bizIdColumn()) ? enableCollection.bizIdColumn() : collectionBizTypeEnum.getDefaultBizIdColumn();;
        // 无法确定物料类型的卡片物料类型需要从返回数据中获取
        if (collectionBizTypeEnum == CollectionBizTypeEnum.DYNAMIC) {
            bizType = Optional.ofNullable(getObject(cardJson, enableCollection.dynamicBizTypeStr())).orElse("").toString();
        }
        String bizId = Optional.ofNullable(getObject(cardJson, bizIdColumn)).orElse("").toString();
        if (StringUtils.isBlank(bizId) || StringUtils.isBlank(bizType)) {
            return;
        }
        // 设置收藏标识
        cardJson.put("collectStatus", collectionHelper.isCollected(userId, bizId, bizType));
        cardJson.put("collectEnable", true);
        // 构建收藏数据
        JSONObject collectionData = new JSONObject();
        collectionData.put("bizId", bizId);
        collectionData.put("bizType", bizType);
        collectionData.put("title", getObject(cardJson, enableCollection.titleColumn()));
        collectionData.put("picUrl", getObject(cardJson, enableCollection.picUrlColumn()));
        collectionData.put("jumpUrl", getObject(cardJson, enableCollection.jumpUrlColumn()));
        collectionData.put("price", getObject(cardJson, enableCollection.priceColumn()));
        collectionData.put("originalPrice", getObject(cardJson, enableCollection.originalPriceColumn()));
        collectionData.put("trainBiz", getObject(cardJson, enableCollection.trainBizColumn()));
        collectionData.put("startTime", getObject(cardJson, enableCollection.startTimeColumn()));
        collectionData.put("endTime", getObject(cardJson, enableCollection.endTimeColumn()));
        cardJson.put("collectData", collectionData);
    }
}
