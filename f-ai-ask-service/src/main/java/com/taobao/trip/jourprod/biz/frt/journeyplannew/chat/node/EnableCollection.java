package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node;

import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.CollectionBizTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义注解，用于标识卡片是否展示收藏字段以及补充收藏所需字段
 * <AUTHOR>
 * @date 2025/3/30
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface EnableCollection {

    CollectionBizTypeEnum bizType();

    String dynamicBizTypeStr() default "";

    String bizIdColumn() default "";

    String titleColumn() default "title";

    String picUrlColumn() default "picUrl";

    String jumpUrlColumn() default "jumpUrl";

    String priceColumn() default "price";

    String originalPriceColumn() default "originalPrice";

    String trainBizColumn() default "";

    String startTimeColumn() default "";

    String endTimeColumn() default "";
}
