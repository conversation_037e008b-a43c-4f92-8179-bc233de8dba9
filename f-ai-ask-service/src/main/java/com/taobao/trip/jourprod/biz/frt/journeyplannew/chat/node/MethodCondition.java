package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */ // 自定义注解：用于标记条件处理方法
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface MethodCondition {

    /**
     * 数据字段名
     * @return
     */
    String field();

    /**
     * 目标匹配值
     * @return
     */
    String value();

    /**
     * 描述
     * @return
     */
    String desc() default "";
}
