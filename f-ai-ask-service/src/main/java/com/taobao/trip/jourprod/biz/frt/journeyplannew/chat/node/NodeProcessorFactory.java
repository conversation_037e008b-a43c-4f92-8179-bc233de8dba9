package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node;

import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl.DemoNodeProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/16
 */
@Component
public class NodeProcessorFactory implements InitializingBean {

    @Autowired
    private List<AbstractNodeProcessor> nodeProcessors;

    private static final Map<String, AbstractNodeProcessor> processorMap = new HashMap<>();

    public static AbstractNodeProcessor getProcessor(AiJourneyPlanSceneEnum agentType, String nodeType) {
        if (processorMap == null || agentType == null) {
            return null;
        }
        return processorMap.get(agentType.getAgentType() + "_" + nodeType);
    }

    public static void register(AiJourneyPlanSceneEnum aiJourneyPlanSceneEnum, String overview, DemoNodeProcessor demoNodeProcessor) {
        processorMap.put(aiJourneyPlanSceneEnum.getAgentType() + "_" + overview, demoNodeProcessor);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if (CollectionUtils.isNotEmpty(nodeProcessors)) {
            for (AbstractNodeProcessor nodeProcessor : nodeProcessors) {
                if (CollectionUtils.isEmpty(nodeProcessor.supportComponentType())) {
                    continue;
                }
                for (String componentType : nodeProcessor.supportComponentType()) {
                    processorMap.put(nodeProcessor.supportAgentType().getAgentType() + "_" + componentType, nodeProcessor);
                }
            }
        }
    }
}
