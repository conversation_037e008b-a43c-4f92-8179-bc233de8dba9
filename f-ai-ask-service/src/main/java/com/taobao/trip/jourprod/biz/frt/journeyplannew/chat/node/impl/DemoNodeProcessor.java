package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.AbstractNodeProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.MethodCondition;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.ComponentDataResponse;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult.StreamMessageCardModel;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/3/16
 */
@Component
public class DemoNodeProcessor extends AbstractNodeProcessor {
    /**
     * 执行渲染逻辑
     *
     * @param chatContext 对话上下文
     */
    @Override
    protected List<StreamMessageCardModel<?>> doProcess(ComponentDataResponse response, ChatContext chatContext) {
        //node 转为 card 或者 段落文本
        //通过 chatContext.getMtopStream().write()发送给客户端
        //可以根据chatContext中的历史node列表进行复杂的逻辑处理，例如：处理两个节点之间调用高德接口计算距离
//        StreamMessageCardModel<JSONObject> cardModel = new StreamMessageCardModel<>();
//        cardModel.setData(response.getData());
//        cardModel.setId(response.getId());
//        System.out.println("成功解析标签内容: " + response.getAllData() + ";隐含结构包括 " + JSON.toJSONString(response.getData()));
        System.out.println("doProcess默认逻辑,组件名" + response.getComponentType() + response.getId());
        return Lists.newArrayList();
    }

    @MethodCondition(field = "type", value = "overview")
    private List<StreamMessageCardModel<?>> test1(ComponentDataResponse response, ChatContext chatContext) {
        System.out.println("进入DemoNodeProcessor类的test测试方法里面");
        return Lists.newArrayList();
    }

    @MethodCondition(field = "type", value = "1")
    private List<StreamMessageCardModel<?>> test2(ComponentDataResponse response, ChatContext chatContext) {
        System.out.println("进入DemoNodeProcessor类的test2测试方法里面");
        return Lists.newArrayList();
    }

    @Override
    public AiJourneyPlanSceneEnum supportAgentType() {
        return AiJourneyPlanSceneEnum.TEST;
    }

    @Override
    public Set<String> supportComponentType() {
        return Sets.newHashSet("card", "container");
    }



}
