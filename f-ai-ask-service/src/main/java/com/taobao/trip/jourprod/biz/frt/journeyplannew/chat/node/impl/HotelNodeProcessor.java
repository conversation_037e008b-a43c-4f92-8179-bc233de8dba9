package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alitrip.aisearch.model.search.hotel.entity.HotelInfo;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.taobao.mtop.common.Result;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.AbstractNodeProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.EnableCollection;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.MethodCondition;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.CollectionBizTypeEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch;
import com.taobao.trip.jourprod.common.sal.hsf.common.EnvSwitch;
import com.taobao.trip.jourprod.hotel.HwSearchService;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageComponentTypeEnum;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.ComponentDataResponse;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.HotelRecommendCardVO;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.RateTagVO;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult.StreamMessageCardModel;
import com.taobao.trip.wireless.hotel.domain.SearchHotelListParam;
import com.taobao.trip.wireless.hotel.domain.SearchHotelListVO;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch.*;
import static com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageItemTypeEnum.HOTEL_RECOMMEND;

/**
 * 酒店节点处理器
 * 主要功能：
 * 1. 处理酒店推荐卡片
 * 2. 批量查询酒店信息
 * 3. 转换酒店数据为展示模型
 * <AUTHOR>
 */
@Component
public class HotelNodeProcessor extends AbstractNodeProcessor {
    @Resource
    private HwSearchService hwSearchService;
    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(HotelNodeProcessor.class);

    // 酒店搜索相关
    static final int DEFAULT_ADULT_NUM = 2;
    static final int DEFAULT_ROOM_NUM = 1;
    static final int SHOW_HOUR_ROOM = 1;
    static final String SOURCE_FROM = "tripSearch4AI";
    // 日期格式
    static final String DATE_FORMAT_INPUT = "yyyyMMdd";
    static final String DATE_FORMAT_OUTPUT = "yyyy-MM-dd";
    // 价格相关
    static final int PRICE_DIVIDE = 100;
    static final String PRICE_SUFFIX = "起";
    // 评分相关
    static final int MAX_RATE_TAGS = 4;
    static final String BOOKING_BTN = "预定";

    // ====================== 核心业务方法 start ======================

    @Override
    protected List<StreamMessageCardModel<?>> doProcess(ComponentDataResponse response, ChatContext chatContext) {
        return null;
    }

    @Override
    public AiJourneyPlanSceneEnum supportAgentType() {
        return AiJourneyPlanSceneEnum.HOTEL_BOOKING;
    }

    @Override
    public Set<String> supportComponentType() {
        return Sets.newHashSet(
                AiJourneyMessageComponentTypeEnum.F_CARD.getCode(),
                AiJourneyMessageComponentTypeEnum.F_CONTAINER.getCode()
        );
    }

    /**
     * 处理酒店推荐卡片请求
     * 主要流程：
     * 1. 获取酒店ID列表
     * 2. 查询酒店详细信息
     * 3. 构建推荐卡片
     */
    @EnableCollection(bizType = CollectionBizTypeEnum.HOTEL, bizIdColumn = "shid", startTimeColumn = "checkIn", endTimeColumn = "checkOut")
    @MethodCondition(field = "type", value = "hotel_recommend", desc = "酒店推荐卡")
    private List<StreamMessageCardModel> hotelRecommend(ComponentDataResponse response, ChatContext chatContext) {
        String shids = response.getData().getString("shid");
        if (StringUtils.isBlank(shids)) {
            LOGGER.recordNormalException(new LogModel("hotelRecommend")
                .message("shid is empty")
                .request(JSON.toJSONString(response))
                .response(shids));
            return null;
        }

        List<String> shidList = this.getShidList(shids, chatContext);
        List<HotelRecommendCardVO> hotelRecommendCardVOList = this.getHotelRecommendCardVOList(shidList, response, chatContext);

        StreamMessageCardModel cardModel = StreamMessageCardModel.finishAndReplace(
            response.getId(), 
            HOTEL_RECOMMEND.getCode(), 
            hotelRecommendCardVOList
        );
        
        LOGGER.recordOutput(new LogModel("hotelRecommend")
            .message("return success")
            .request(JSON.toJSONString(response)));
        return Lists.newArrayList(cardModel);
    }

    /**
     * 获取酒店推荐卡片列表
     * 主要步骤：
     * 1. 解析日期范围
     * 2. 批量查询酒店信息
     * 3. 构建推荐卡片列表
     */
    public List<HotelRecommendCardVO> getHotelRecommendCardVOList(List<String> shidIdList, ComponentDataResponse response, ChatContext chatContext) {
        if (CollectionUtils.isEmpty(shidIdList)) {
            return null;
        }

        // 过滤非数字的shid
        final boolean[] hasInvalidShid = {false};
        List<String> filteredShidList = shidIdList.stream()
            .filter(shid -> {
                boolean isNumeric = StringUtils.isNumeric(shid);
                if (!isNumeric && !hasInvalidShid[0]) {
                    hasInvalidShid[0] = true;
                }
                return isNumeric;
            })
            .collect(Collectors.toList());

        // 如果存在非法shid，打印原始列表
        if (hasInvalidShid[0]) {
            LOGGER.recordDangerException(new LogModel("hotelRecommend")
                .code("invalidShid")
                .message("original shids={0}", String.join(",", shidIdList)));
        }

        // 如果过滤后的列表为空，直接返回
        if (CollectionUtils.isEmpty(filteredShidList)) {
            return null;
        }

        Map<String, HotelInfo> hotelInfoMap = getHotelInfoMap(chatContext);
        String shids = Joiner.on(",").join(filteredShidList);

        /** 入离日期解析 */
        DateRange dateRange = parseDateRange(response.getData());

        List<SearchHotelListVO.HotelListInfo> hotelListInfos = batchQueryHotelList(shids, dateRange.getCheckIn(), dateRange.getCheckOut(), chatContext);
        if (CollectionUtils.isEmpty(hotelListInfos)) {
            return null;
        }

        return buildHotelRecommendCards(hotelListInfos, hotelInfoMap, dateRange);
    }

    /**
     * 批量查询酒店列表信息
     */
    public List<SearchHotelListVO.HotelListInfo> batchQueryHotelList(String shids, String checkIn, String checkOut, ChatContext chatContext) {
        if (StringUtils.isBlank(shids)) {
            LOGGER.recordOutput(new LogModel("hotelRecommend")
                    .message("shids is empty")
                    .request("shids=" + shids));
            return null;
        }

        SearchHotelListParam param = buildSearchParam(shids, checkIn, checkOut, chatContext);
        String printParam = JSON.toJSONString(param);

        try {
            Result<SearchHotelListVO> result = hwSearchService.batchQuerySearchHotel(param);
            return processSearchResult(result, printParam);
        } catch (Exception ex) {
            LOGGER.recordDangerException(new LogModel("hotelRecommend")
                    .message("batchQuerySearchHotel error")
                    .request(printParam)
                    .e(ex));
            return null;
        }
    }
    // ====================== 核心业务方法 end ======================

    @SuppressWarnings("unchecked")
    private Map<String, HotelInfo> getHotelInfoMap(ChatContext chatContext) {
        return (Map<String, HotelInfo>) chatContext.getInternalData().getOrDefault("hotel", Collections.EMPTY_MAP);
    }

    // ====================== 数据转换方法 start ======================

    private List<String> getShidList(String shids, ChatContext chatContext) {
        List<String> shidList = Splitter.on(",").splitToList(shids);
        if (shouldUseMockData(chatContext)) {
            shidList = NODE_PROCESSOR_MOCK_ID_MAP.get("hotel");
        }
        return shidList;
    }

    private boolean shouldUseMockData(ChatContext chatContext) {
        return NODE_PROCESSOR_MOCK_USER_ID_LIST.contains(String.valueOf(chatContext.getRequest().getUserId()))
                && CollectionUtils.isNotEmpty(NODE_PROCESSOR_MOCK_ID_MAP.get("hotel"));
    }

    private SearchHotelListParam buildSearchParam(String shids, String checkIn, String checkOut, ChatContext chatContext) {
        SearchHotelListParam param = new SearchHotelListParam();
        param.setShids(shids);
        param.setUserId(chatContext.getRequest().getUserId());
        param.setAdultNum(DEFAULT_ADULT_NUM);
        param.setRoomNum(DEFAULT_ROOM_NUM);

        //如果checkIn和checkOut为空，则取明天和后天
        // 处理日期逻辑
        if (StringUtils.isBlank(checkIn) && StringUtils.isBlank(checkOut)) {
            checkIn = LocalDate.now().plusDays(1).format(DateTimeFormatter.ofPattern(DATE_FORMAT_OUTPUT));
            checkOut = LocalDate.now().plusDays(2).format(DateTimeFormatter.ofPattern(DATE_FORMAT_OUTPUT));
        }

        param.setCheckIn(checkIn);
        param.setCheckOut(checkOut);

        param.setIsShowHourRoom(SHOW_HOUR_ROOM);
        param.setTtid(chatContext.getRequest().getTtid());
        param.setSourceFrom(SOURCE_FROM);
        return param;
    }
    /**
     * 构建酒店推荐卡片
     */
    private HotelRecommendCardVO buildHotelRecommendCard(SearchHotelListVO.HotelListInfo hotelInfo, Map<String, HotelInfo> hotelInfoMap, DateRange dateRange) {
        long shid = hotelInfo.getShid();
        HotelInfo hotelSearchInfo = hotelInfoMap.get(String.valueOf(shid));

        HotelRecommendCardVO card = new HotelRecommendCardVO();
        setBasicInfo(card, hotelInfo);
        setPriceInfo(card, hotelInfo);
        setHotelSearchInfo(card, hotelInfo, hotelSearchInfo);
        if (dateRange != null) {
            card.setCheckIn(dateRange.getCheckIn());
            card.setCheckOut(dateRange.getCheckOut());
        }
        return card;
    }

    private List<HotelRecommendCardVO> buildHotelRecommendCards(List<SearchHotelListVO.HotelListInfo> hotelList, Map<String, HotelInfo> hotelInfoMap, DateRange dateRange) {
        return hotelList.stream()
                .map(hotelInfo -> buildHotelRecommendCard(hotelInfo, hotelInfoMap, dateRange))
                .collect(Collectors.toList());
    }

    private void setBasicInfo(HotelRecommendCardVO card, SearchHotelListVO.HotelListInfo hotelInfo) {
        card.setTitle(hotelInfo.getName());
        card.setPicUrl(hotelInfo.getPicUrl());
        card.setShid(String.valueOf(hotelInfo.getShid()));
        card.setRateScore(hotelInfo.getRateCountWithoutUnit());
        card.setStar(hotelInfo.getStar());
        card.setIsCollect(hotelInfo.getIsFavourite() != 0);
        card.setBookingBtnName(BOOKING_BTN);
        card.setBottomTips(HOTEL_BOTTOM_COMMON_TIPS);
        card.setBottomTipIcon(HOTEL_BOTTOM_COMMON_ICON);
        card.setOriginalData(hotelInfo);

        String env = EnvSwitch.isOnline() ? "m" : "wapa";
        String discountDetailLayerUrl = MessageFormat.format(HOTEL_DISCOUNT_DETAIL_LAYER_URL, new Object[]{env});
        card.setDiscountDetailLayerUrl(discountDetailLayerUrl);
    }

    private void setPriceInfo(HotelRecommendCardVO card, SearchHotelListVO.HotelListInfo hotelInfo) {
        Long price = hotelInfo.getPriceWithTax() / PRICE_DIVIDE;
        card.setPrice(String.valueOf(price));
        card.setPriceSuffix(PRICE_SUFFIX);
        // 打印日志，统计价格缺失率
        if (price <= 0) {
            LOGGER.recordDangerException(new LogModel("hotelRecommend")
                    .message("invaildPrice")
                    .request("shid=" + hotelInfo.getShid())
                    .response(String.valueOf(price)));
        } else {
            LOGGER.recordOutput(new LogModel("hotelRecommend")
                    .message("vaildPrice")
                    .request("shid=" + hotelInfo.getShid())
                    .response(String.valueOf(price)));
        }
    }

    private void setHotelSearchInfo(HotelRecommendCardVO card, SearchHotelListVO.HotelListInfo hotelInfo, HotelInfo searchInfo) {
        if (searchInfo != null) {
            card.setPicUrls(hotelInfo.getPicUrls());
            card.setRecommendReason(searchInfo.getConciseDescription());
            card.setDistince(searchInfo.getInterestsPoi());
            card.setJumpUrl(hotelInfo.getJumpLink());
            card.setRateTagList(convertRateTagVO(hotelInfo.getShid(), searchInfo));
        } else {
            LOGGER.recordDangerException(new LogModel("hotelRecommend")
                    .message("searchInfo is null")
                    .request("shid=" + hotelInfo.getShid()));
        }

        if (StringUtils.isBlank(card.getDistince())) {
            card.setDistince(hotelInfo.getNearbyStrengthPoi());
        }
    }

    /**
     * 转换酒店评分标签
     */
    public static List<RateTagVO> convertRateTagVO(Long shid, HotelInfo hotelSearchInfo) {
        if (hotelSearchInfo == null || CollectionUtils.isEmpty(hotelSearchInfo.getHotelLabels())) {
            return null;
        }

        LOGGER.recordOutput(new LogModel("hotelRecommend")
                .message("hotelLabels")
                .request("shid=" + shid)
                .response(JSON.toJSONString(hotelSearchInfo.getHotelLabels())));

        try {
            return hotelSearchInfo.getHotelLabels().stream()
                    .map(label -> {
                        JSONObject rateTag = JSONObject.parseObject(label.toString());
                        RateTagVO tagVO = new RateTagVO();
                        tagVO.setTagName(rateTag.getString("hotword_name"));
                        tagVO.setCount(rateTag.getString("rate_count"));
                        String tabFilter = String.format("[{\"code\":\"0\",\"type\":0,\"selected\":true,\"name\":\"全部\"},{\"code\":\"%s\",\"type\":1,\"selected\":true}]",
                                rateTag.getString("hotword_code"));
                        tagVO.setJumpUrl(String.format(AiJourneySwitch.RATE_JUMP_URL, "1000", shid, tabFilter));
                        return tagVO;
                    })
                    .sorted(Comparator.comparing(RateTagVO::getCount).reversed())
                    .limit(MAX_RATE_TAGS)
                    .collect(Collectors.toList());
        } catch (Exception ex) {
            LOGGER.recordDangerException(new LogModel("hotelRecommend")
                    .message("rate tag convert error")
                    .request(JSON.toJSONString(hotelSearchInfo.getHotelLabels()))
                    .e(ex));
            return null;
        }
    }

    private DateRange parseDateRange(JSONObject data) {
        try {
            String checkIn = formatDate(data.getString("checkIn"));
            String checkOut = formatDate(data.getString("checkOut"));
            return new DateRange(checkIn, checkOut);
        } catch (Exception ex) {
            LOGGER.recordNormalException(new LogModel("hotelRecommend")
                    .message("parseDate error")
                    .request("checkIn=" + data.getString("checkIn") + ",checkOut=" + data.getString("checkOut"))
                    .e(ex));
            return new DateRange();
        }
    }

    private String formatDate(String date) throws Exception {
        return DateFormatUtils.format(
                DateUtils.parseDate(date, DATE_FORMAT_INPUT),
                DATE_FORMAT_OUTPUT
        );
    }

    private List<SearchHotelListVO.HotelListInfo> processSearchResult(Result<SearchHotelListVO> result, String printParam) {
        if (result == null || !result.isSuccess()) {
            LOGGER.recordDangerException(new LogModel("hotelRecommend")
                    .message("batchQuerySearchHotel fail")
                    .request(printParam)
                    .response(JSON.toJSONString(result)));
            return null;
        }

        SearchHotelListVO model = result.getModel();
        if (model == null || CollectionUtils.isEmpty(model.getHotelList())) {
            LOGGER.recordDangerException(new LogModel("hotelRecommend")
                    .message("model or hotelList empty")
                    .request(printParam)
                    .response(JSON.toJSONString(result)));
            return null;
        }

        return model.getHotelList();
    }
    // ====================== 数据转换方法 end ======================

    /**
     * 日期范围数据类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class DateRange {
        private String checkIn;
        private String checkOut;
    }
}
