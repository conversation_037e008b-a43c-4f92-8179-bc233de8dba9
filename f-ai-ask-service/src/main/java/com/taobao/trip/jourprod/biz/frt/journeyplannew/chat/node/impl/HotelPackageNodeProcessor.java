package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alitrip.aisearch.model.search.hotel_pkg.entity.HotelPkgInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.AbstractNodeProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.EnableCollection;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.MethodCondition;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.CollectionBizTypeEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageComponentTypeEnum;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.ComponentDataResponse;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.HotelPackageRecommendCardVO;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult.StreamMessageCardModel;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageItemTypeEnum.HOTEL_PACKAGE_RECOMMEND;

/**
 * 酒店套餐节点处理器
 * <AUTHOR>
 */
@Component
public class HotelPackageNodeProcessor extends AbstractNodeProcessor {
    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(HotelPackageNodeProcessor.class);
    private static final String PRICE_PRE_DESC = "券后价";
    private static final String PRICE_SUFFIX = "起";
    private static final String BOOKING_BTN_NAME = "抢购";
    private static final String HOTEL_PACKAGE_KEY = "hotel_package";

    @Override
    protected List<StreamMessageCardModel<?>> doProcess(ComponentDataResponse response, ChatContext chatContext) {
        return null;
    }

    @EnableCollection(bizType = CollectionBizTypeEnum.ITEM, bizIdColumn = "itemId", titleColumn = "itemTitle")
    @MethodCondition(field = "type", value = "hotel_pkg_recommend", desc = "酒店套餐推荐卡")
    private List<StreamMessageCardModel> hotelPackageRecommend(ComponentDataResponse response, ChatContext chatContext) {
        Map<String, HotelPkgInfo> hotelPkgInfoMap = getHotelPackageMap(chatContext);
        List<HotelPackageRecommendCardVO> hotelPkgRecommendList = buildHotelPackageList(response, hotelPkgInfoMap);
        
        StreamMessageCardModel cardModel = StreamMessageCardModel.finishAndReplace(
            response.getId(), 
            HOTEL_PACKAGE_RECOMMEND.getCode(), 
            hotelPkgRecommendList
        );
        
        LOGGER.recordOutput(new LogModel("hotel_package_recommend")
            .request(JSON.toJSONString(response))
            .response(JSON.toJSONString(hotelPkgRecommendList)));
            
        return Lists.newArrayList(cardModel);
    }

    @Override
    public AiJourneyPlanSceneEnum supportAgentType() {
        return AiJourneyPlanSceneEnum.HOTEL_PACKAGE_BOOKING;
    }

    @Override
    public Set<String> supportComponentType() {
        return Sets.newHashSet(AiJourneyMessageComponentTypeEnum.F_CARD.getCode(), AiJourneyMessageComponentTypeEnum.F_CONTAINER.getCode());
    }

    private HotelPackageRecommendCardVO convert(HotelPkgInfo hotelPkgInfo, ComponentDataResponse response) {
        if (hotelPkgInfo == null) {
            return null;
        }

        HotelPackageRecommendCardVO cardVO = new HotelPackageRecommendCardVO();
        setBasicInfo(cardVO, hotelPkgInfo);
        setPrice(cardVO, hotelPkgInfo);
        setRecommendReason(cardVO, hotelPkgInfo, response);
        setHotelInfo(cardVO, hotelPkgInfo);
        setDisplayInfo(cardVO, hotelPkgInfo);

        return cardVO;
    }

    private void setBasicInfo(HotelPackageRecommendCardVO cardVO, HotelPkgInfo hotelPkgInfo) {
        cardVO.setItemId(hotelPkgInfo.getItemId());
        cardVO.setItemTitle(hotelPkgInfo.getItemTitle());
        cardVO.setJumpUrl(hotelPkgInfo.getUrl());
        cardVO.setIsCollect(hotelPkgInfo.getIsCollect());
        cardVO.setOriginalData(hotelPkgInfo);
    }

    private void setPrice(HotelPackageRecommendCardVO cardVO, HotelPkgInfo hotelPkgInfo) {
        String price = hotelPkgInfo.getPrice() == null ? hotelPkgInfo.getDiscountPrice() : hotelPkgInfo.getPrice();
        cardVO.setPrice(price);
        cardVO.setPricePreDesc(PRICE_PRE_DESC);
        cardVO.setPriceSuffix(PRICE_SUFFIX);
        
        logPriceValidation(hotelPkgInfo.getItemId(), price);
    }

    private void setRecommendReason(HotelPackageRecommendCardVO cardVO, HotelPkgInfo hotelPkgInfo, ComponentDataResponse response) {
        String recommendReason = StringUtils.isNotBlank(hotelPkgInfo.getRecommend()) 
            ? hotelPkgInfo.getRecommend() 
            : response.getData().getString("reason");
        cardVO.setRecommendReason(recommendReason);
    }

    private void setHotelInfo(HotelPackageRecommendCardVO cardVO, HotelPkgInfo hotelPkgInfo) {
        List<JSONObject> hotelList = hotelPkgInfo.getHotelList();
        if (CollectionUtils.isNotEmpty(hotelList) && hotelList.size() == 1) {
            cardVO.setRateScore(hotelList.get(0).getString("rateScore"));
        }
    }

    private void setDisplayInfo(HotelPackageRecommendCardVO cardVO, HotelPkgInfo hotelPkgInfo) {
        cardVO.setPicUrls(hotelPkgInfo.getPics());
        cardVO.setBookingBtnName(BOOKING_BTN_NAME);
    }

    @SuppressWarnings("unchecked")
    private Map<String, HotelPkgInfo> getHotelPackageMap(ChatContext chatContext) {
        return (Map<String, HotelPkgInfo>) chatContext.getInternalData()
            .getOrDefault(HOTEL_PACKAGE_KEY, Collections.EMPTY_MAP);
    }

    private List<HotelPackageRecommendCardVO> buildHotelPackageList(ComponentDataResponse response, Map<String, HotelPkgInfo> hotelPkgInfoMap) {
        String itemId = response.getData().getString("itemId");
        if (StringUtils.isBlank(itemId)) {
            return Collections.emptyList();
        }

        List<HotelPackageRecommendCardVO> hotelPkgRecommendList = Lists.newArrayList();
        Arrays.stream(itemId.split(","))
            .map(hotelPkgInfoMap::get)
            .filter(Objects::nonNull)
            .map(info -> convert(info, response))
            .filter(Objects::nonNull)
            .forEach(hotelPkgRecommendList::add);
            
        return hotelPkgRecommendList;
    }

    private void logPriceValidation(Long itemId, String price) {
        LogModel logModel = new LogModel("hotelPackageRecommend")
            .request("itemId=" + itemId)
            .response(price);
            
        if (price == null || Long.valueOf(price) <= 0) {
            LOGGER.recordDangerException(logModel.message("invaildPrice"));
        } else {
            LOGGER.recordOutput(logModel.message("vaildPrice"));
        }
    }
}
