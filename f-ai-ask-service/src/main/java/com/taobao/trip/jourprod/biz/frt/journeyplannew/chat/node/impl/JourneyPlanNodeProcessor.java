package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjsonfordrm.JSON;
import com.alibaba.trip.tripdivision.domain.TrdiDivisionDO;
import com.alibaba.trip.tripdivision.domain.enumerate.TrdiDivisionLevel;

import com.alitrip.aisearch.model.search.poi.entity.PoiInfo;
import com.alitrip.aisearch.model.search.traffic.entity.TrafficJourneyInfo;
import com.alitrip.aisearch.model.search.traffic.entity.TrafficSegmentInfo;
import com.alitrip.aisearch.model.search.traffic.entity.TrafficSolutionItem;
import com.fliggy.ffa.touch.prod.customize.client.common.param.MemberDTO;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.recommendplatform.client.RecommendClient;
import com.taobao.trip.jourdprod.core.model.common.ConstantElement;
import com.taobao.trip.jourprod.biz.common.CommonString;
import com.taobao.trip.jourprod.biz.common.ContextInnerKeyCommon;
import com.taobao.trip.jourprod.biz.common.DrawMapSceneType;
import com.taobao.trip.jourprod.biz.common.PoiAndVacationFieldConvertUtil;
import com.taobao.trip.jourprod.biz.common.StringCommonUtils;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.AbstractNodeProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.EnableCollection;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.MethodCondition;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.overview.JourneyPlanChatOverviewGenerator;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.constant.JourneyPlanAiAssistantConstant;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.diamond.DiamondUtil;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiAgentTypeEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.CollectionBizTypeEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.DistanceUtils;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.GaodeStaticMapUtils;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.TripPlanNodeProcessorHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.*;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneyMessageContentDTO.StructRoute;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.drawmap.DrawMapContextDTO;
import com.taobao.trip.jourprod.common.lang.utils.CIDateUtil;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.jourprod.common.sal.hsf.config.Switcher;
import com.taobao.trip.jourprod.common.sal.hsf.tripdivision.TrdiDivisionReadServiceClient;
import com.taobao.trip.jourprod.domain.AiPoiIconDO;
import com.taobao.trip.jourprod.domain.AiPoiIconParam;
import com.taobao.trip.jourprod.dto.DivisionDTO;
import com.taobao.trip.jourprod.mapper.AiPoiIconDAO;
import com.taobao.trip.jourprod.member.MemberServiceHelper;
import com.taobao.trip.jourprod.poi.PoiSearchServiceGateWay;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.*;
import com.taobao.trip.jourprod.service.facade.model.request.gaode.GaoDeSearchQueryRequest;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.PoiInfoExtend;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.CapsuleVO;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.ComponentDataResponse;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.HotelRecommendCardVO;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.Poi;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult.StreamMessageCardModel;
import com.taobao.trip.rsp.Route;
import com.taobao.trip.rsp.collect.JourneyCollectDTO;
import com.taobao.trip.rsp.collect.JourneyCollectItemDTO;
import com.taobao.trip.rsp.collect.TitleCollectDTO;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.taobao.trip.jourprod.biz.common.ContextInnerKeyCommon.JOURNEY_TRAFFIC;
import static com.taobao.trip.jourprod.biz.common.ContextInnerKeyCommon.*;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl.PoiNodeProcessor.GAODE_POI_DETAIL_PAGE_URL_FORMATTER;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl.PoiNodeProcessor.POI_DETAIL_PAGE_URL_FORMATTER;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.constant.JourneyPlanAiAssistantConstant.*;
import static com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageItemTypeEnum.*;

/**
 * 行程规划节点处理器
 *
 * <AUTHOR>
 */
@Component
public class JourneyPlanNodeProcessor extends AbstractNodeProcessor implements InitializingBean {

    @AppSwitch(des = "行程规划静态地图图片默认尺寸", level = Switch.Level.p4)
    public static String JOURNEY_PLAN_STATIC_MAP_DEFAULT_SIZE = "768*452";

    @AppSwitch(des = "poi编辑按钮返回字段", level = Switch.Level.p4)
    public static List<Map<String, Object>> EDIT_KEYBOARD_TAGS = Lists.newArrayList();

    @AppSwitch(des = "编辑按钮返回字段V2 -- 上线后需要把EDIT_KEYBOARD_TAGS删除掉", level = Switch.Level.p4)
    public static List<Map<String, Object>> EDIT_KEYBOARD_TAGS_V2 = Lists.newArrayList();


    //初始化的id为4
    @AppSwitch(des = "根据cityName填充城市经纬度信息查询维度", level = Switch.Level.p4)
    public static List<Integer> queryLevel = Lists.newArrayList(4);

    @AppSwitch(des = "手绘地图确认场景--使用城市名称来计算城市数量", level = Switch.Level.p4)
    public static Boolean useCityNameGetCitySize = Boolean.TRUE;

    @AppSwitch(des = "手绘地图，需要降级场景", level = Switch.Level.p4)
    public static Map<String, Object> needDownScene = Maps.newHashMap();

    @AppSwitch(des = "真实地图按钮V2", level = Switch.Level.p4)
    public static Boolean RealMapV2 = Boolean.FALSE;


    static {
        Map<String, Object> tag = Maps.newHashMap();
        tag.put("title", "快速编辑");
        tag.put("keys", Lists.newArrayList("删除", "替换", "调整顺序"));

        EDIT_KEYBOARD_TAGS.add(tag);

        Map<String, Object> tagMap = Maps.newHashMap();
        tagMap.put("title", "快速编辑");
        tagMap.put("keys", Lists.newArrayList(
                new CapsuleVO("删除", "删除这个地点"),
                new CapsuleVO("移动", "移动至"),
                new CapsuleVO("换一换", "换一换")
        ));
        EDIT_KEYBOARD_TAGS_V2.add(tagMap);
    }

    @AppSwitch(des = "兜底返回null开关", level = Switch.Level.p4)
    public static boolean DOUDI_BACK = true;

    @AppSwitch(des = "跟团游相似度召回APPID", level = Switch.Level.p4)
    public static Long PACKAGE_TOUR_APP_ID = 48486L;

    @AppSwitch(des = "默认头像", level = Switch.Level.p4)
    public static String FLIGGY_DEFAULT_AVATAR
        = "https://img.alicdn.com/imgextra/i3/O1CN01z3zVgc20XvyOghQXd_!!6000000006860-0-tps-400-400.jpg";

    @AppSwitch(des = "POI榜单ICON链接", level = Switch.Level.p4)
    public static String POI_LIST_ICON
        = "https://gw.alicdn.com/imgextra/i2/O1CN01YKtrew1JaCXSpgpTN_!!6000000001044-2-tps-56-56.png";

    @AppSwitch(des = "市内交通是否需要详细方案", level = Switch.Level.p4)
    public static Boolean needMoreTransportation = Boolean.FALSE;

    @AppSwitch(des = "跟团游商品召回分数(0-1)", level = Switch.Level.p4)
    public static String ITEM_CARD_REL_SCORE = "0.9";

    @AppSwitch(des = "poi icon大模型生产类型", level = Switch.Level.p4)
    public static String POI_ICON_LLM = "4o";

    @AppSwitch(des = "跟团游商品预订开关", level = Switch.Level.p4)
    public static Boolean ITEM_RESERVE_CLOSE_SWITCH = true;

    @AppSwitch(des = "限制天级poi为空，不展示地图，默认为false，不需要限制。 ", level = Switch.Level.p4)
    public static Boolean LIMIT_DAYPOI_EXIST_EMPTY = false;

    @AppSwitch(des = "快速预订V2版本", level = Switch.Level.p4)
    public static Boolean FAST_RESERVE_V2 = false;

    @AppSwitch(des = "替换长poiName换成短poiName", level = Switch.Level.p4)
    public static Boolean replacePoiNameToShortPoiName = true;
    @AppSwitch(des = "poi没有城市名字，使用去程/返程交通的站点城市名称", level = Switch.Level.p4)
    public static Boolean NOPOICITYNAME_USE_TRAFFIC_CITYNAME = true;

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneyPlanNodeProcessor.class);

    @Resource
    private TripPlanNodeProcessorHelper tripPlanNodeProcessorHelper;

    @Resource
    private TrafficNodeProcessor trafficNodeProcessor;

    @Resource
    private AiPoiIconDAO aiPoiIconDAO;

    @Resource
    private PoiSearchServiceGateWay poiSearchServiceGateWay;

    @Resource
    private RecommendClient recommendClient;

    @Resource
    private HotelNodeProcessor hotelNodeProcessor;

    @Resource
    private MemberServiceHelper memberServiceHelper;

    @Resource
    private PoiNodeProcessor poiNodeProcessor;

    @Resource
    private TrdiDivisionReadServiceClient trdiDivisionReadServiceClient;

    @Resource
    private JourneyPlanChatOverviewGenerator journeyPlanChatOverviewGenerator;

    @Resource
    private DiamondUtil diamondUtil;

    /**
     * 兜底逻辑
     *
     * @param response    解析出来的节点数据
     * @param chatContext 对话上下文
     * @return
     */
    @Override
    protected List<StreamMessageCardModel<?>> doProcess(ComponentDataResponse response, ChatContext chatContext) {
        StreamMessageCardModel<Map<String, Object>> cardModel = new StreamMessageCardModel<>();
        try {
            if (DOUDI_BACK) {
                return null;
            }
            // 卡片数据
            cardModel.setId(response.getId());
            if (Objects.equals(response.getComponentType(), AiJourneyMessageComponentTypeEnum.F_CONTAINER.getCode())) {
                cardModel.setItemType(ROUND_CARD.getCode());
            } else {
                cardModel.setItemType(OTHER_CARD.getCode());
                return null;
            }
            cardModel.setStatus(AiJourneyMessageCardStatusEnum.FINISH.getCode());
            cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());

            // 业务数据
            Map<String, Object> data = response.getData();
            cardModel.setData(data);
        } catch (Throwable e) {
            LOGGER.recordDangerException(new LogModel("JourneyPlanNodeProcessor.doProcess").e(e)
                .message("查询用户行中态信息包错"));
        } finally {
            LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.doProcess")
                .request(JSONUtil.toJSONStringForLog(response))
                .response(JSON.toJSONString(cardModel)));
        }
        return Lists.newArrayList(cardModel);
    }

    /**
     * 构建圆角卡片 container
     */
    @MethodCondition(field = "itemType", value = "round_card")
    private List<StreamMessageCardModel<?>> buildRoundCard(ComponentDataResponse response, ChatContext chatContext) {
        StreamMessageCardModel<Map<String, Object>> cardModel = new StreamMessageCardModel<>();
        try {
            // 参数校验
            tripPlanNodeProcessorHelper.checkResponseParams(response);
            // 构建基础卡片
            tripPlanNodeProcessorHelper.buildBaseCardModel(cardModel, response, ROUND_CARD.getCode());

            // 业务数据
            Map<String, Object> data = response.getData();
            cardModel.setData(data);
        } catch (Throwable e) {
            LOGGER.recordDangerException(new LogModel("JourneyPlanNodeProcessor.buildRoundCard").e(e)
                .message("round_card_exception"));
        } finally {
            LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.buildRoundCard")
                .request(JSONUtil.toJSONStringForLog(response))
                .response(JSON.toJSONString(cardModel)));
        }
        return Lists.newArrayList(cardModel);
    }

    /**
     * 行程地图卡片
     */
    @MethodCondition(field = "itemType", value = "trip_map_card")
    private List<StreamMessageCardModel<?>> buildTripMapCard(ComponentDataResponse response, ChatContext chatContext) {
        StreamMessageCardModel<MapCard> cardModel = new StreamMessageCardModel<>();

        try {
            // 参数校验
            tripPlanNodeProcessorHelper.checkResponseParams(response);
            // 构建基础卡片
            tripPlanNodeProcessorHelper.buildBaseCardModel(cardModel, response, "trip_map_card");

            JSONObject dataMap = response.getData();
            String lat = MapUtils.getString(dataMap, "firstScenicLatitude");
            String lon = MapUtils.getString(dataMap, "firstScenicLongitude");
            String cityNum = MapUtils.getString(dataMap, "cityNum");
            String scenicNum = MapUtils.getString(dataMap, "scenicNum");
            String staticMapUrl = GaodeStaticMapUtils.buildStaticMap(lat, lon, JOURNEY_PLAN_STATIC_MAP_DEFAULT_SIZE);

            MapCard card = new MapCard();
            card.setMapUrl(staticMapUrl);
            card.setSummaryDes(String.format("%s个城市·%s个景点", cityNum, scenicNum));
            card.setJumpUrl(staticMapUrl);
            cardModel.setData(card);
        } catch (Throwable e) {
            LOGGER.recordDangerException(new LogModel("JourneyPlanNodeProcessor.buildTripMapCard").e(e)
                .message("查询用户行中态信息包错"));
        } finally {
            LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.buildTripMapCard")
                .request(JSONUtil.toJSONStringForLog(response))
                .response(JSON.toJSONString(cardModel)));
        }
        return Lists.newArrayList(cardModel);
    }

    /**
     * 时间线
     */
    @MethodCondition(field = "itemType", value = "timeline")
    private List<StreamMessageCardModel<?>> buildTimeLineCard(ComponentDataResponse response, ChatContext chatContext) {

        StreamMessageCardModel<Map<String, Object>> cardModel = new StreamMessageCardModel<>();
        try {
            // 参数校验
            tripPlanNodeProcessorHelper.checkResponseParams(response);
            // 构建基础卡片
            tripPlanNodeProcessorHelper.buildBaseCardModel(cardModel, response, "timeline");

            Map<String, Object> data = response.getData();
            cardModel.setData(data);
        } catch (Throwable e) {
            cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
            LOGGER.recordDangerException(new LogModel("JourneyPlanNodeProcessor.buildTimeLineCard").e(e)
                .message("timeline_exception"));
        } finally {
            LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.buildTimeLineCard")
                .request(JSONUtil.toJSONStringForLog(response))
                .response(JSON.toJSONString(cardModel)));
        }
        return Lists.newArrayList(cardModel);
    }

    @MethodCondition(field = "itemType", value = "journey_traffic")
    @EnableCollection(bizType = CollectionBizTypeEnum.DYNAMIC, bizIdColumn = "collectData.bizId",
        dynamicBizTypeStr = "collectData.bizType", jumpUrlColumn = "collectData.jumpUrl",
        trainBizColumn = "collectData.trainBiz")
    private List<StreamMessageResult.StreamMessageCardModel<?>> buildTrafficCard(ComponentDataResponse response,
                                                                                 ChatContext chatContext) {
        StreamMessageResult.StreamMessageCardModel<AiTrafficCardVO> cardModel
            = new StreamMessageResult.StreamMessageCardModel<>();
        TrafficSolutionItem trafficGroupItem = null;
        try {
            tripPlanNodeProcessorHelper.checkResponseParams(response);

            tripPlanNodeProcessorHelper.buildBaseCardModel(cardModel, response,
                AiJourneyMessageItemTypeEnum.JOURNEY_TRAFFIC.getCode());
            // 解析响应数据
            JSONObject data = response.getData();
            String bizId = MapUtils.getString(data, "bizId");
            if (StringUtils.isBlank(bizId)) {
                cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
                return Lists.newArrayList(cardModel);
            }
            String tripDirection = MapUtils.getString(data, "tripDirection");

            // 获取内部数据
            Map<String, Object> internalData = chatContext.getInternalData();

            trafficGroupItem = trafficNodeProcessor.getTrafficGroupItem(internalData, bizId);
            if (trafficGroupItem == null || trafficGroupItem.getJourneyInfos() == null) {
                cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
                return Lists.newArrayList(cardModel);
            }
            //特殊处理交通提示信息
            trafficNodeProcessor.specialProcessNotice(trafficGroupItem);
            // 创建交通卡视图对象
            AiTrafficCardVO aiTrafficCardVO = new AiTrafficCardVO();

            if (trafficGroupItem.getJourneyInfos().size() == 1) {
                aiTrafficCardVO.setTrafficGroupItem(trafficGroupItem);
            } else {
                TrafficSolutionItem rebuildTrafficItem = new TrafficSolutionItem();
                rebuildTrafficItem.setId(trafficGroupItem.getId());
                rebuildTrafficItem.setJumpUrl(trafficGroupItem.getJumpUrl());
                rebuildTrafficItem.setEncryptRouteInfoKey(trafficGroupItem.getEncryptRouteInfoKey());
                rebuildTrafficItem.setPrice(trafficGroupItem.getPrice());
                rebuildTrafficItem.setPriceStr(trafficGroupItem.getPriceStr());
                rebuildTrafficItem.setRouteInfoKey(trafficGroupItem.getRouteInfoKey());
                TrafficJourneyInfo trafficJourneyInfo = trafficGroupItem.getJourneyInfos().get(
                    Objects.equals(tripDirection, "back") ? 1 : 0);
                rebuildTrafficItem.setJourneyInfos(Lists.newArrayList(trafficJourneyInfo));
                aiTrafficCardVO.setTrafficGroupItem(rebuildTrafficItem);
            }

            // 设置卡片数据
            cardModel.setData(aiTrafficCardVO);
            //如果是机火，设置为交通机火卡
            boolean multiTraffic = tripPlanNodeProcessorHelper.isMultiTraffic(aiTrafficCardVO);
            if (multiTraffic){
                aiTrafficCardVO.setScene(AiAgentTypeEnum.JOURNEY_PLAN.getCodeToFront());
                cardModel.setItemType(AiJourneyMessageItemTypeEnum.TRAFFIC_MULTI_CARD.getCode());
            }
            tripPlanNodeProcessorHelper.buildTrafficContext(aiTrafficCardVO, chatContext);
        } catch (Throwable e) {
            cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
            LOGGER.recordNormalException(new LogModel("JourneyPlanNodeProcessor.buildTrafficCard").e(e)
                .message("journey_traffic_exception"));
        } finally {
            LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.buildTrafficCard")
                .request(JSONUtil.toJSONStringForLog(response))
                .message("trafficGroupItem-{0}", JSONUtil.toJSONString(trafficGroupItem))
                .response(JSON.toJSONString(cardModel)));
        }
        return Lists.newArrayList(cardModel);
    }

    @MethodCondition(field = "itemType", value = "journey_traffic_slide")
    @EnableCollection(bizType = CollectionBizTypeEnum.DYNAMIC, bizIdColumn = "collectData.bizId",
        dynamicBizTypeStr = "collectData.bizType", jumpUrlColumn = "collectData.jumpUrl",
        trainBizColumn = "collectData.trainBiz")
    private List<StreamMessageResult.StreamMessageCardModel<Object>> buildReserveTrafficCard(
        ComponentDataResponse response, ChatContext chatContext) {
        StreamMessageResult.StreamMessageCardModel<Object> cardModel
            = new StreamMessageResult.StreamMessageCardModel<>();
        List<TrafficSolutionItem> trafficSolutionList = null;
        try {
            // 参数校验
            tripPlanNodeProcessorHelper.checkResponseParams(response);
            // 构建基础卡片
            tripPlanNodeProcessorHelper.buildBaseCardModel(cardModel, response, JOURNEY_TRAFFIC_SLIDE_CARD.getCode());
            //判断是否使用快速预订V2版本，V2上线后，卡片处理可以删掉。
            if(BooleanUtils.isTrue(FAST_RESERVE_V2)){
                cardModel.setStatus(AiJourneyMessageCardActionEnum.DELETE.getCode());
                return Lists.newArrayList(cardModel);
            }

            JSONObject dataMap = response.getData();
            String trafficInfos = MapUtils.getString(dataMap, "trafficInfos");
            Map<String, List<String>> typeBizIdMap = buildTrafficInfosV2(trafficInfos);

            List<AiJourneyTrafficSlideCard> aiJourneyTrafficSlideCardList = Lists.newArrayList();
            for (String type : typeBizIdMap.keySet()) {
                List<String> bizIds = typeBizIdMap.get(type);
                if (CollectionUtils.isEmpty(bizIds)) {
                    continue;
                }
                trafficSolutionList = trafficNodeProcessor.getTrafficSolution(bizIds, chatContext);
                if (CollectionUtils.isEmpty(trafficSolutionList)) {
                    cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
                    return Lists.newArrayList(cardModel);
                }

                for (TrafficSolutionItem trafficSolutionItem : trafficSolutionList) {
                    AiTrafficCardVO aiTrafficCardVO = new AiTrafficCardVO(trafficSolutionItem);
                    if (Objects.isNull(trafficSolutionItem)) {
                        continue;
                    }
                    AiJourneyTrafficSlideCard aiJourneyTrafficSlideCard = new AiJourneyTrafficSlideCard();
                    if (Objects.isNull(trafficSolutionItem.getJourneyInfos())
                        || trafficSolutionItem.getJourneyInfos().size() > 2) {
                        continue;
                    }
                    aiJourneyTrafficSlideCard.setType(type);
                    aiJourneyTrafficSlideCard.setIsGoAndBack(Objects.equals(type, "goAndBack"));
                    TrafficJourneyInfo trafficJourneyInfo = null;
                    trafficJourneyInfo = trafficSolutionItem.getJourneyInfos().get(0);
                    if (Objects.equals(type, "back") && trafficSolutionItem.getJourneyInfos().size() == 2) {
                        trafficJourneyInfo = trafficSolutionItem.getJourneyInfos().get(1);
                    }

                    List<TrafficSegmentInfo> segments = trafficJourneyInfo.getSegments();
                    List<Integer> transportTypeList = segments.stream()
                        .map(TrafficSegmentInfo::getTransportType)
                        .distinct().collect(Collectors.toList());
                    aiJourneyTrafficSlideCard.setTransportType(transportTypeList);
                    String depCityName = segments.get(0).getDepCityName();
                    aiJourneyTrafficSlideCard.setDepCityName(depCityName);
                    // 构建卡片的时间样式
                    buildTrafficCardTime(aiJourneyTrafficSlideCard, trafficSolutionItem, type);
                    aiJourneyTrafficSlideCard.setArrCityName(segments.get(segments.size() - 1).getArrCityName());
                    aiJourneyTrafficSlideCard.setPrice(trafficSolutionItem.getPrice());
                    aiJourneyTrafficSlideCard.setPriceStr(trafficSolutionItem.getPriceStr());
                    aiJourneyTrafficSlideCard.setJumpUrl(trafficSolutionItem.getJumpUrl());
                    JSONObject collectData = aiTrafficCardVO.getCollectData();
                    aiJourneyTrafficSlideCard.setCollectData(collectData);
                    aiJourneyTrafficSlideCardList.add(aiJourneyTrafficSlideCard);
                }
            }
            // 如果卡片列表为空，则删除组件
            if (CollectionUtils.isEmpty(aiJourneyTrafficSlideCardList)) {
                cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
                return Lists.newArrayList(cardModel);
            }
            cardModel.setData(aiJourneyTrafficSlideCardList);
            // 设置“快捷预订”是否需要展示的标
            tripPlanNodeProcessorHelper.buildFastReserveTag(chatContext);
        } catch (Exception e) {
            //如果异常，则删除组件
            LOGGER.recordDangerException(new LogModel("JourneyPlanNodeProcessor.buildReserveTrafficCard").e(e)
                .message("journey_traffic_slide_exception"));
            cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
        } finally {
            LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.buildReserveTrafficCard")
                .request(JSONUtil.toJSONStringForLog(response))
                .message("trafficSolutionList-{0}", JSONUtil.toJSONString(trafficSolutionList))
                .response(JSON.toJSONString(cardModel)));
        }
        return Lists.newArrayList(cardModel);
    }

    private void buildTrafficCardTime(AiJourneyTrafficSlideCard card, TrafficSolutionItem trafficSolutionItem,
                                      String type) {
        if (Objects.equals(type, "goAndBack")) {
            // 出发行程
            TrafficJourneyInfo goJourneyInfo = trafficSolutionItem.getJourneyInfos().get(0);
            List<TrafficSegmentInfo> goSegments = goJourneyInfo.getSegments();
            String goDepDateTime = goSegments.get(0).getDepDateTime();
            String goArrDateTime = goSegments.get(goSegments.size() - 1).getArrDateTime();

            // 返程
            TrafficJourneyInfo backJourneyInfo = trafficSolutionItem.getJourneyInfos().get(
                trafficSolutionItem.getJourneyInfos().size() > 1 ? 1 : 0);
            List<TrafficSegmentInfo> backSegments = backJourneyInfo.getSegments();
            String backDepDateTime = backSegments.get(0).getDepDateTime();
            String backArrDateTime = backSegments.get(goSegments.size() - 1).getArrDateTime();

            card.setDepDateTime(covertTrafficCardTime(goDepDateTime) + "-" + covertTrafficCardTime(goArrDateTime));
            card.setArrDateTime(covertTrafficCardTime(backDepDateTime) + "-" + covertTrafficCardTime(backArrDateTime));
            return;
        }
        TrafficJourneyInfo journeyInfo = trafficSolutionItem.getJourneyInfos().get(0);
        if (Objects.equals(type, "back")) {
            if (trafficSolutionItem.getJourneyInfos().size() == 2) {
                journeyInfo = trafficSolutionItem.getJourneyInfos().get(1);
            }
        }
        List<TrafficSegmentInfo> segments = journeyInfo.getSegments();
        card.setDepDateTime(covertTrafficCardTime(segments.get(0).getDepDateTime()));
        card.setArrDateTime(covertTrafficCardTime(segments.get(segments.size() - 1).getArrDateTime()));
    }

    /**
     * yyyy-MM-dd HH:mm:ss 转 HH:mm
     */
    private String covertTrafficCardTime(String dateTimeStr) {
        if (StringUtils.isBlank(dateTimeStr)) {
            return dateTimeStr;
        }
        // 将字符串解析为 LocalDateTime 对象
        LocalDateTime dateTime = LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        // 格式化为 "HH:mm" 的时间格式
        return dateTime.format(DateTimeFormatter.ofPattern("HH:mm"));
    }

    public Map<String, List<String>> buildTrafficInfosV2(String trafficInfos) {
        // 初始化结果 Map
        Map<String, List<String>> resultMap = new HashMap<>();
        if (StringUtils.isBlank(trafficInfos)) {
            return resultMap;
        }
        // 按逗号分割字符串
        String[] pairs = trafficInfos.split(",");
        for (String pair : pairs) {
            // 按竖线分割键值对
            String[] keyValue = pair.split("\\|");
            // 检查是否符合格式：必须有两个部分（key 和 value）
            if (keyValue.length == 2) {
                String key = keyValue[0].trim();   // 去掉多余空格
                String value = keyValue[1].trim();
                // 检查 key 是否有效（例如 go 或 back）
                if ("go".equals(key) || "back".equals(key)) {
                    // 如果 key 已存在，则追加到对应的 List 中；否则创建新的 List
                    resultMap.computeIfAbsent(key, k -> new ArrayList<>()).add(value);
                }
            }
        }
        // 找出同时存在于 go 和 back 的 bizId，并提取到 goAndBack 中
        List<String> goList = resultMap.getOrDefault("go", Collections.emptyList());
        List<String> backList = resultMap.getOrDefault("back", Collections.emptyList());
        // 找到交集（即同时存在于 go 和 back 的 bizId）
        Set<String> commonBizIds = new HashSet<>(goList);
        commonBizIds.retainAll(backList);
        // 如果有交集，添加到 goAndBack 键中
        if (!commonBizIds.isEmpty()) {
            resultMap.put("goAndBack", new ArrayList<>(commonBizIds));
        }
        // 从原来的 go 和 back 中移除这些交集的 bizId
        if (!commonBizIds.isEmpty()) {
            resultMap.put("go", removeCommon(goList, commonBizIds));
            resultMap.put("back", removeCommon(backList, commonBizIds));
        }
        return resultMap;
    }

    // 辅助方法：从列表中移除交集部分
    private static List<String> removeCommon(List<String> originalList, Set<String> commonSet) {
        List<String> result = new ArrayList<>();
        for (String item : originalList) {
            if (!commonSet.contains(item)) {
                result.add(item);
            }
        }
        return result;
    }

    private Map<String, List<String>> buildTrafficInfos(String trafficInfos) {
        // 初始化结果 Map
        Map<String, List<String>> resultMap = new HashMap<>();
        if (StringUtils.isBlank(trafficInfos)) {
            return resultMap;
        }
        // 按逗号分割字符串
        String[] pairs = trafficInfos.split(",");
        for (String pair : pairs) {
            // 按竖线分割键值对
            String[] keyValue = pair.split("\\|");
            // 检查是否符合格式：必须有两个部分（key 和 value）
            if (keyValue.length == 2) {
                String key = keyValue[0].trim();   // 去掉多余空格
                String value = keyValue[1].trim();

                // 检查 key 是否有效（例如 go 或 back）
                if ("go".equals(key) || "back".equals(key)) {
                    // 如果 key 已存在，则追加到对应的 List 中；否则创建新的 List
                    resultMap.computeIfAbsent(key, k -> new ArrayList<>()).add(value);
                }
            }
        }
        return resultMap;
    }

    /**
     * 文本通用卡片
     */
    @MethodCondition(field = "itemType", value = "journey_text")
    private List<StreamMessageCardModel<?>> buildJourneyTextCard(ComponentDataResponse response,
                                                                 ChatContext chatContext) {
        StreamMessageCardModel<Map<String, Object>> cardModel = new StreamMessageCardModel<>();

        try {
            // 参数校验
            tripPlanNodeProcessorHelper.checkResponseParams(response);
            // 构建基础卡片
            tripPlanNodeProcessorHelper.buildBaseCardModel(cardModel, response, JOURNEY_TEXT.getCode());

            Map<String, Object> data = response.getData();
            if (MapUtils.isEmpty(data) || StringUtils.isBlank(MapUtils.getString(data, "text"))) {
                cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
                return Lists.newArrayList(cardModel);
            }

            String type = MapUtils.getString(data, "type");
            if (Objects.equals(type, "title")) {
                data.put("edit", true);
                data.put("clickText", MapUtils.getString(data, "text"));
                data.put("keyboardTags", EDIT_KEYBOARD_TAGS);
                data.put("keyboardTagsV2", EDIT_KEYBOARD_TAGS_V2);
            }
            if (Objects.equals(type, "fastReserve")) {
                // 写入上下文
                tripPlanNodeProcessorHelper.buildFastReserveCardId(response.getId(), chatContext);
            }
            cardModel.setData(data);
        } catch (Throwable e) {
            cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
            LOGGER.recordDangerException(new LogModel("JourneyPlanNodeProcessor.buildTextCommonCard").e(e)
                .message("journey_text_exception"));
        } finally {
            LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.buildTrafficCard")
                .request(JSONUtil.toJSONStringForLog(response))
                .response(JSON.toJSONString(cardModel)));
        }
        return Lists.newArrayList(cardModel);
    }

    /**
     * 路线规划 卡片
     */
    @MethodCondition(field = "itemType", value = "router_plan_card")
    private List<StreamMessageCardModel<?>> buildRouterTextCard(ComponentDataResponse response,
                                                                ChatContext chatContext) {
        StreamMessageCardModel<RouterPlanCard> cardModel = new StreamMessageCardModel<>();
        try {
            // 参数校验
            tripPlanNodeProcessorHelper.checkResponseParams(response);
            // 构建基础卡片
            tripPlanNodeProcessorHelper.buildBaseCardModel(cardModel, response, ROUTER_PLAN_CARD.getCode());

            JSONObject dataMap = response.getData();
            // 获取经纬度
            String depLocation = MapUtils.getString(dataMap, "depLocation");
            String arrLocation = MapUtils.getString(dataMap, "arrLocation");
            Boolean detailPath = MapUtils.getBoolean(dataMap, "detailPath");
            if (StringUtils.isAnyBlank(depLocation, arrLocation)) {
                cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
                return Lists.newArrayList(cardModel);
            }

            Double distance = DistanceUtils.calculateDistance(depLocation, arrLocation);
            if (Objects.isNull(distance)) {
                cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
                return Lists.newArrayList(cardModel);
            }

            String formatDistance = DistanceUtils.formatDistance(distance);
            // 距离为 0，则删除卡片
            if (Objects.equals(formatDistance, "0m") || Objects.equals(formatDistance, "100km+")) {
                cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
                return Lists.newArrayList(cardModel);
            }

            String[] depLocationArr = depLocation.split(",");
            String[] arrLocationArr = arrLocation.split(",");

            // 根据经纬度查询cityCode
            DivisionDTO depDivisionDO = poiSearchServiceGateWay.getByLngAndLat(Double.valueOf(depLocationArr[0]),
                Double.valueOf(depLocationArr[1]));
            String depCityCode = Objects.isNull(depDivisionDO) ? null : depDivisionDO.getCityCode3();
            DivisionDTO arrDivisionDO = poiSearchServiceGateWay.getByLngAndLat(Double.valueOf(arrLocationArr[0]),
                Double.valueOf(arrLocationArr[1]));
            String arrCityCode = Objects.isNull(arrDivisionDO) ? null : arrDivisionDO.getCityCode3();
            if (StringUtils.isAnyBlank(depCityCode, arrCityCode)) {
                RouterPlanCard routerPlanCard = buildDefaultRouteCard(distance, formatDistance);
                cardModel.setData(routerPlanCard);
                return Lists.newArrayList(cardModel);
            }

            Map<DistanceUtils.TransportMode, TripPlanNodeProcessorHelper.RouterPlanInfo> planInfoMap
                = tripPlanNodeProcessorHelper.queryRoutePlanByGaode(depLocation, arrLocation, depCityCode, arrCityCode);
            if (MapUtils.isEmpty(planInfoMap)) {
                // 降级走兜底方案，给经验值
                RouterPlanCard routerPlanCard = buildDefaultRouteCard(distance, formatDistance);
                cardModel.setData(routerPlanCard);
                return Lists.newArrayList(cardModel);
            }

            // 公共交通方案
            if (BooleanUtils.isTrue(detailPath) && BooleanUtils.isTrue(needMoreTransportation)) {
                TripPlanNodeProcessorHelper.RouterPlanInfo busPlanInfo = planInfoMap.remove(
                    DistanceUtils.TransportMode.BUS);
                if (Objects.nonNull(busPlanInfo)) {
                    // 将市内交通成本塞到上下文中
                    tripPlanNodeProcessorHelper.buildBusCostContext(chatContext, busPlanInfo.getCost());
                    // 构建卡片
                    RouterPlanCard card = RouterPlanCard.builder().routerText(busPlanInfo.getRouterTextList())
                        .costTimeText(busPlanInfo.getCostTimeText() + " " + busPlanInfo.getCostMoneyText())
                        .iconUrl(busPlanInfo.getIconUrl()).build();
                    cardModel.setData(card);
                    return Lists.newArrayList(cardModel);
                }
            }

            if (distance > 1) {
                // 驾车方案
                TripPlanNodeProcessorHelper.RouterPlanInfo carPlanInfo = planInfoMap.get(
                    DistanceUtils.TransportMode.CAR);
                if (Objects.nonNull(carPlanInfo)) {
                    // 构建卡片
                    RouterPlanCard card = RouterPlanCard.builder().costTimeText(
                            carPlanInfo.getDistanceText() + " " + carPlanInfo.getCostTimeText())
                        .iconUrl(carPlanInfo.getIconUrl())
                        .build();
                    cardModel.setData(card);
                    return Lists.newArrayList(cardModel);
                }
            } else {
                // 步行方案
                TripPlanNodeProcessorHelper.RouterPlanInfo walkPlanInfo = planInfoMap.get(
                    DistanceUtils.TransportMode.WALK);
                if (Objects.nonNull(walkPlanInfo)) {
                    // 构建卡片
                    RouterPlanCard card = RouterPlanCard.builder().costTimeText(
                            walkPlanInfo.getDistanceText() + " " + walkPlanInfo.getCostTimeText())
                        .iconUrl(walkPlanInfo.getIconUrl())
                        .build();
                    cardModel.setData(card);
                    return Lists.newArrayList(cardModel);
                }
            }

            // 兜底方案
            RouterPlanCard routerPlanCard = buildDefaultRouteCard(distance, formatDistance);
            cardModel.setData(routerPlanCard);
            return Lists.newArrayList(cardModel);
        } catch (Throwable e) {
            cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
            LOGGER.recordDangerException(new LogModel("JourneyPlanNodeProcessor.buildRouterTextCard").e(e)
                .message("router_plan_card_exception"));
        } finally {
            LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.buildRouterTextCard")
                .request(JSONUtil.toJSONStringForLog(response))
                .response(JSON.toJSONString(cardModel)));
        }
        return Lists.newArrayList(cardModel);
    }

    private static RouterPlanCard buildDefaultRouteCard(double distance, String formatDistance) {
        // 走经验值兜底
        if (distance <= 1d) {
            String walkTimeText = DistanceUtils.calculateTravelTime(distance, DistanceUtils.TransportMode.WALK);
            RouterPlanCard routerPlanCard = RouterPlanCard.builder().costTimeText(
                    String.format("%s %s 约%s", DistanceUtils.TransportMode.WALK.getName(), formatDistance,
                        walkTimeText))
                .iconUrl("https://gw.alicdn.com/imgextra/i3/O1CN01O8MFp11wj9I1v3hza_!!6000000006343-2-tps-60-60.png")
                .build();
            return routerPlanCard;
        }
        String carTimeText = DistanceUtils.calculateTravelTime(distance, DistanceUtils.TransportMode.CAR);
        RouterPlanCard routerPlanCard = RouterPlanCard.builder().costTimeText(
                String.format("%s %s 约%s", DistanceUtils.TransportMode.CAR.getName(), formatDistance, carTimeText))
            .iconUrl("https://gw.alicdn.com/imgextra/i3/O1CN01JPPQJs1VET8IgGR78_!!6000000002621-2-tps-60-60.png")
            .build();
        return routerPlanCard;
    }

    /**
     * 测试
     */
    public static void main2(String[] args) {
        // TripPoiResult<List<FliggyPoi>> poiResult = tripPoiReadService.getPois(ids);
        TripPlanNodeProcessorHelper tripPlanNodeProcessorHelper1 = new TripPlanNodeProcessorHelper();
        Map<DistanceUtils.TransportMode, TripPlanNodeProcessorHelper.RouterPlanInfo> transportModeRouterPlanInfoMap
            = tripPlanNodeProcessorHelper1.queryRoutePlanByGaode("120.235,30.358", "120.257,30.152", "330100",
            "330100");
        System.out.println(JSON.toJSONString(transportModeRouterPlanInfoMap));
    }

    @MethodCondition(field = "itemType", value = "poi_link_card")
    @EnableCollection(bizType = CollectionBizTypeEnum.POI, bizIdColumn = "poiId", titleColumn = "poiName",
        picUrlColumn = "poiImg")
    private List<StreamMessageCardModel<?>> buildPoiLinkCard(ComponentDataResponse response, ChatContext chatContext) {
        StreamMessageCardModel<PoiLinkCard> cardModel = new StreamMessageCardModel<>();
        try {
            // 参数校验
            tripPlanNodeProcessorHelper.checkResponseParams(response);
            // 构建基础卡片
            tripPlanNodeProcessorHelper.buildBaseCardModel(cardModel, response, POI_LINK_CARD.getCode());

            JSONObject data = response.getData();
            String poiId = tripPlanNodeProcessorHelper.convertPoiIdByMappingId(MapUtils.getString(data, "poiId"));
            String poiName = MapUtils.getString(data, "poiName");
            String playTime = MapUtils.getString(data, "playTime");
            Integer day = MapUtils.getInteger(data, "day");
            //如果poiId为空，快速返回
            if (StringUtils.isBlank(poiId) || Objects.equals("无", poiId)) {
                cardModel.setData(PoiLinkCard.builder().poiName(poiName).playTime(playTime).clickText(poiName)
                    .isCollected(false).edit(Boolean.TRUE).keyboardTags(EDIT_KEYBOARD_TAGS).keyboardTagsV1(EDIT_KEYBOARD_TAGS_V2).build());
                return Lists.newArrayList(cardModel);
            }
            // 高德点位支持
            String poiType = tripPlanNodeProcessorHelper.getPoiType(poiId);
            if (Objects.equals(poiType, "gaode")) {
                PoiLinkCard gaodeLinkCard = buildGaodePoiLinkCard(poiId, playTime, "gaode", chatContext);
                if (Objects.isNull(gaodeLinkCard)) {
                    cardModel.setData(PoiLinkCard.builder().poiName(poiName).playTime(playTime).isCollected(false)
                        .edit(Boolean.TRUE).keyboardTags(EDIT_KEYBOARD_TAGS).keyboardTagsV1(EDIT_KEYBOARD_TAGS_V2)
                        .build());
                    return Lists.newArrayList(cardModel);
                }
                cardModel.setData(gaodeLinkCard);
                return Lists.newArrayList(cardModel);
            }
            // 先从缓存取，不行再查接口
            PoiInfo poi = tripPlanNodeProcessorHelper.queryPoiInfo(chatContext, poiId);
            if (Objects.isNull(poi)) {
                PoiLinkCard poiLinkCard = PoiLinkCard.builder().poiName(poiName).playTime(playTime).isCollected(false)
                    .edit(Boolean.TRUE).keyboardTags(EDIT_KEYBOARD_TAGS).keyboardTagsV1(EDIT_KEYBOARD_TAGS_V2).build();
                cardModel.setData(poiLinkCard);
                return Lists.newArrayList(cardModel);
            }

            List<Tag> tags = buildPoiTags(poi);

            PoiLinkCard card = PoiLinkCard.builder().poiId(poiId)
                .poiName(poi.getPoiName())
                .jumpUrl(poiNodeProcessor.buildJumpUrl(POI_DETAIL_PAGE_URL_FORMATTER, poiId))
                .playTime(playTime)
                .edit(Boolean.TRUE)
                //待从上下文中查询真实数据 -- 寒蝉统一处理
                .isCollected(Boolean.TRUE)
                //待 转换真实数据
                .listRank(poiNodeProcessor.buildListRank(poiId))
                .halfYearUv(PoiAndVacationFieldConvertUtil.buildFuzzyUv(poi.getHalfYearUv()))
                .poiGrade(tripPlanNodeProcessorHelper.covertPoiGrade(poi.getPoiGrade()))
                // .suggestPlayTime(poi.getSuggestPlayTime()) // 产品要求先注释掉
                .tags(tags)
                .clickText(poi.getPoiName())
                .keyboardTags(EDIT_KEYBOARD_TAGS)
                .keyboardTagsV1(EDIT_KEYBOARD_TAGS_V2)
                .poiImg(poi.getPoiImg())
                .isJourney(Boolean.TRUE)
                .build();
            cardModel.setData(card);
            // 构建当前poi上下文
            tripPlanNodeProcessorHelper.buildCurretPoiContext(day, poi, chatContext);
        } catch (Throwable e) {
            cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
            LOGGER.recordDangerException(new LogModel("JourneyPlanNodeProcessor.buildPoiLinkCard").e(e)
                .message("poi_link_card_exception"));
        } finally {
            LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.buildPoiLinkCard")
                .request(JSONUtil.toJSONStringForLog(response))
                .response(JSONUtil.toJSONStringForLog(cardModel)));
        }
        return Lists.newArrayList(cardModel);
    }

    @MethodCondition(field = "itemType", value = "fast_reserve")
    private List<StreamMessageCardModel<?>> buildFastReserve(ComponentDataResponse response, ChatContext chatContext) {
        StreamMessageCardModel<FastReserveDTO> cardModel = new StreamMessageCardModel<>();
        try {
            tripPlanNodeProcessorHelper.buildBaseCardModel(cardModel, response, FAST_RESERVE.getCode());
            //控制使用版本1还是版本2
            if (BooleanUtils.isFalse(FAST_RESERVE_V2)) {
                cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
                return Lists.newArrayList(cardModel);
            }
            Map<String, Object> internalData = chatContext.getInternalData();
            //如果不包含酒店和机票数据，可以提前退出了。
            if (!internalData.containsKey(ContextInnerKeyCommon.JOURNEY_HOTEL) && !internalData.containsKey(
                JOURNEY_TRAFFIC)) {
                //删除卡片
                cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
                return Lists.newArrayList(cardModel);
            }
            FastReserveDTO fastReserveDTO = new FastReserveDTO();
            List<JourneyAskItemDTO> itemList = new ArrayList<>();
            if (internalData.containsKey(ContextInnerKeyCommon.JOURNEY_HOTEL)
                && Objects.nonNull(internalData.get(ContextInnerKeyCommon.JOURNEY_HOTEL))
                && CollectionUtils.isNotEmpty(
                (List<HotelRecommendCardVO>)internalData.get(ContextInnerKeyCommon.JOURNEY_HOTEL))) {
                itemList.addAll(buildHotelItemList(
                    (List<HotelRecommendCardVO>)internalData.get(ContextInnerKeyCommon.JOURNEY_HOTEL)));
            }
            if (internalData.containsKey(ContextInnerKeyCommon.JOURNEY_TRAFFIC)
                && Objects.nonNull(internalData.get(ContextInnerKeyCommon.JOURNEY_TRAFFIC))
                &&CollectionUtils.isNotEmpty((List<AiTrafficCardVO>)internalData.get(ContextInnerKeyCommon.JOURNEY_TRAFFIC))){
                itemList.addAll(buildTrafficItemList(
                    (List<AiTrafficCardVO>)internalData.get(ContextInnerKeyCommon.JOURNEY_TRAFFIC)));
            }
            fastReserveDTO.setItemList(itemList);
            fastReserveDTO.setCollectData(buildCollectData(chatContext));
            cardModel.setData(fastReserveDTO);
        } catch (Throwable e) {
            cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
            LOGGER.recordDangerException(new LogModel("JourneyPlanNodeProcessor.buildPoiLinkCard").e(e)
                .message("poi_link_card_exception"));
        } finally {
            LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.buildPoiLinkCard")
                .request(JSONUtil.toJSONStringForLog(response))
                .response(JSONUtil.toJSONStringForLog(cardModel)));
        }
        return Lists.newArrayList(cardModel);
    }

    /**
     * 快捷预订模块中，收藏数据
     *
     * @param chatContext 上下文
     * @return 收藏数据
     */
    private List<JourneyCollectItemDTO> buildCollectData(ChatContext chatContext) {
        List<JourneyCollectItemDTO> journeyCollectItemDTOList = new ArrayList<>();
        journeyCollectItemDTOList.addAll(journeyPlanChatOverviewGenerator.buildHotelItemList(chatContext));
        journeyCollectItemDTOList.addAll(journeyPlanChatOverviewGenerator.buildTraffic(chatContext));
        return journeyCollectItemDTOList;
    }


    private Collection<? extends JourneyAskItemDTO> buildTrafficItemList(List<AiTrafficCardVO> aiTrafficCardVOS) {
        if(CollectionUtils.isEmpty(aiTrafficCardVOS)) {
            return new ArrayList<>();
        }
        List<JourneyAskItemDTO> itemList = new ArrayList<>();
        for (AiTrafficCardVO aiTrafficCardVO : aiTrafficCardVOS) {
            itemList.add(JourneyAskItemDTO.builder().type("traffic").trafficInfo(aiTrafficCardVO).build());
        }
        return itemList;
    }

    private Collection<? extends JourneyAskItemDTO> buildHotelItemList(
        List<HotelRecommendCardVO> hotelRecommendCardVOS) {
        if(CollectionUtils.isEmpty(hotelRecommendCardVOS)) {
            return new ArrayList<>();
        }
        List<JourneyAskItemDTO> itemList = new ArrayList<>();
        for (HotelRecommendCardVO hotelRecommendCardVO : hotelRecommendCardVOS) {
            itemList.add(JourneyAskItemDTO.builder().type("hotel").hotelInfo(hotelRecommendCardVO).build());
        }
        return itemList;
    }

    private PoiLinkCard buildGaodePoiLinkCard(String poiId, String playTime, String poiType, ChatContext chatContext) {
        Map<String, PoiInfoExtend> amapPoiInfoMap = (Map<String, PoiInfoExtend>) chatContext.getInternalData().getOrDefault("amap", Collections.EMPTY_MAP);
        PoiInfoExtend aiSearchPoiInfo = amapPoiInfoMap.get(poiId);

        if (Objects.nonNull(aiSearchPoiInfo)) {
            String source = aiSearchPoiInfo.getSource();
            if ("gmap".equals(source)) {
                poiType = GOOGLE_TYPE;
            } else if ("amap".equals(source)) {
                poiType = GAO_DE_TYPE;
            }
            PoiLinkCard poiLinkCard = PoiLinkCard.builder().poiId(poiId)
                .poiName(aiSearchPoiInfo.getPoiName())
                .playTime(playTime)
                .jumpUrl(poiNodeProcessor.buildJumpUrl(GAODE_POI_DETAIL_PAGE_URL_FORMATTER, poiId, poiType))
                .clickText(aiSearchPoiInfo.getPoiName())
                .edit(Boolean.TRUE)
                .keyboardTags(EDIT_KEYBOARD_TAGS)
                .keyboardTagsV1(EDIT_KEYBOARD_TAGS_V2)
                .build();
            return poiLinkCard;
        }

        return null;


        // 之前慧一写的，感觉没啥用了，先删了看下逻辑
        //PoiInfo poiInfo = poiNodeProcessor.getPoiByPoiId(poiId, poiType);
        //if (Objects.nonNull(poiInfo)) {
        //    PoiLinkCard poiLinkCard = PoiLinkCard.builder().poiId(poiId)
        //        .poiName(poiInfo.getPoiName())
        //        .poiImg(poiInfo.getPoiImg())
        //        .playTime(playTime)
        //        .jumpUrl(poiNodeProcessor.buildJumpUrl(GAODE_POI_DETAIL_PAGE_URL_FORMATTER, poiId, poiType))
        //        .clickText(poiInfo.getPoiName())
        //        .edit(Boolean.TRUE)
        //        .keyboardTags(EDIT_KEYBOARD_TAGS)
        //        .keyboardTagsV1(EDIT_KEYBOARD_TAGS_V2)
        //        .build();
        //    return poiLinkCard;
        //}
        //PoiInfoDTO poiInfoDTO = tripPlanNodeProcessorHelper.queryPoiByGaodePoiId(poiId);
        //if (Objects.isNull(poiInfoDTO)) {
        //    return null;
        //}
        //PoiLinkCard poiLinkCard = PoiLinkCard.builder().poiId(poiId)
        //    .poiName(poiInfoDTO.getPoiName())
        //    .poiImg(poiInfoDTO.getPoiImg())
        //    .playTime(playTime)
        //    .jumpUrl(poiNodeProcessor.buildJumpUrl(GAODE_POI_DETAIL_PAGE_URL_FORMATTER, poiId, poiType))
        //    .clickText(poiInfoDTO.getPoiName())
        //    .edit(Boolean.TRUE)
        //    .keyboardTags(EDIT_KEYBOARD_TAGS)
        //    .keyboardTagsV1(EDIT_KEYBOARD_TAGS_V2)
        //    .build();
        //return poiLinkCard;
    }

    private List<Tag> buildPoiTags(PoiInfo poi) {
        List<Tag> tags = Lists.newArrayList();
        String fullYearUv = PoiAndVacationFieldConvertUtil.buildFuzzyUv(poi.getFullYearUv());
        if (StringUtils.isNotBlank(fullYearUv)) {
            tags.add(Tag.builder().text(fullYearUv).build());
        }

        String poiGrade = tripPlanNodeProcessorHelper.covertPoiGrade(poi.getPoiGrade());
        if (StringUtils.isNotBlank(poiGrade)) {
            tags.add(Tag.builder().text(poiGrade).build());
        }
        if (CollectionUtils.isNotEmpty(tags)) {
            return tags;
        }
        return null;
    }

    private ListRank covertPoiRank(JSONObject poiRank) {
        if (poiRank == null) {
            return null;
        }
        return ListRank.builder()
            .text(MapUtils.getString(poiRank, "poi_list_name"))
            .icon(POI_LIST_ICON).build();
    }

    @MethodCondition(field = "itemType", value = "item_reserve_card")
    @EnableCollection(bizType = CollectionBizTypeEnum.ITEM, bizIdColumn = "vacationId", titleColumn = "vacationName",
        picUrlColumn = "vacationImg")
    private List<StreamMessageCardModel<?>> buildItemReserveCard(ComponentDataResponse response,
                                                                 ChatContext chatContext) {
        StreamMessageCardModel<Object> cardModel = new StreamMessageCardModel<>();
        StreamMessageCardModel<Object> fastItemCardModel = new StreamMessageCardModel<>();
        String recommendResult = null;
        try {
            // 参数校验
            tripPlanNodeProcessorHelper.checkResponseParams(response);

            // 构建基础卡片
            tripPlanNodeProcessorHelper.buildBaseCardModel(cardModel, response, ITEM_RESERVE_CARD.getCode());
            if(BooleanUtils.isTrue(FAST_RESERVE_V2)){
                cardModel.setStatus(AiJourneyMessageCardActionEnum.DELETE.getCode());
                return Lists.newArrayList(cardModel);
            }
            if (BooleanUtils.isTrue(ITEM_RESERVE_CLOSE_SWITCH)) {
                cardModel.setStatus(AiJourneyMessageCardActionEnum.DELETE.getCode());
                // 删除快捷预订卡片
                fastItemCardModel = buildFastReserveTitleCard(chatContext);
                if (Objects.nonNull(fastItemCardModel)) {
                    return Lists.newArrayList(cardModel, fastItemCardModel);
                }
                return Lists.newArrayList(cardModel);
            }

            Map<String, PoiInfo> poiMap = tripPlanNodeProcessorHelper.getPoiMap(chatContext);
            if (MapUtils.isEmpty(poiMap)) {
                cardModel.setStatus(AiJourneyMessageCardActionEnum.DELETE.getCode());
                // 删除快捷预订卡片
                fastItemCardModel = buildFastReserveTitleCard(chatContext);
                if (Objects.nonNull(fastItemCardModel)) {
                    return Lists.newArrayList(cardModel, fastItemCardModel);
                }
                return Lists.newArrayList(cardModel);
            }

            Map<String, String> params = Maps.newHashMap();
            params.put("scene", "ai_search");
            params.put("searchType", "vacation_ssim_recall");
            params.put("searchPlanType", "b");

            Map<String, Object> searchFilterMap = Maps.newHashMap();
            searchFilterMap.put("poiIds", Joiner.on(",").join(poiMap.keySet()));
            searchFilterMap.put("relScore", ITEM_CARD_REL_SCORE);
            params.put("searchFilter", JSON.toJSONString(searchFilterMap));

            // 查询qp
            recommendResult = recommendClient.recommend(PACKAGE_TOUR_APP_ID, chatContext.getRequest().getUserId(),
                params, ConstantElement.APP_NAME);
            LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.buildItemReserveCard.recommend")
                .request("appId" + PACKAGE_TOUR_APP_ID + ",userId" + chatContext.getRequest().getUserId() + ",params"
                    + JSON.toJSONString(params))
                .response(recommendResult));
            if (StringUtils.isBlank(recommendResult)) {
                cardModel.setStatus(AiJourneyMessageCardActionEnum.DELETE.getCode());
                // 删除快捷预订卡片
                fastItemCardModel = buildFastReserveTitleCard(chatContext);
                if (Objects.nonNull(fastItemCardModel)) {
                    return Lists.newArrayList(cardModel, fastItemCardModel);
                }
                return Lists.newArrayList(cardModel);
            }

            JSONObject recommendResultMap = JSONObject.parseObject(recommendResult);
            Boolean success = MapUtils.getBoolean(recommendResultMap, "success");
            String errorCode = MapUtils.getString(recommendResultMap, "errorCode");
            String errorMessage = MapUtils.getString(recommendResultMap, "errorMessage");
            if (BooleanUtils.isFalse(success)) {
                cardModel.setStatus(AiJourneyMessageCardActionEnum.DELETE.getCode());
                // 删除快捷预订卡片
                fastItemCardModel = buildFastReserveTitleCard(chatContext);
                if (Objects.nonNull(fastItemCardModel)) {
                    return Lists.newArrayList(cardModel, fastItemCardModel);
                }
                LOGGER.recordDangerException(new LogModel("JourneyPlanNodeProcessor.item_reserve_card")
                    .message("buildItemReserveCard.invoke_tpp_exception, errorCode-{0}, errorMessage-{1}", errorCode,
                        errorMessage));
                return Lists.newArrayList(cardModel);
            }

            JSONArray resultArray = recommendResultMap.getJSONArray("result");

            List<VacationInfo> vacationInfos = Lists.newArrayList();
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject result = resultArray.getJSONObject(i);
                JSONObject info = result.getJSONObject("info");
                if (MapUtils.isEmpty(info)) {
                    continue;
                }
                VacationInfo vacationInfo = JSONObject.parseObject(JSON.toJSONString(info), VacationInfo.class);
                vacationInfos.add(vacationInfo);
            }

            if (CollectionUtils.isEmpty(vacationInfos)) {
                // 删除快捷预订卡片
                fastItemCardModel = buildFastReserveTitleCard(chatContext);
                if (Objects.nonNull(fastItemCardModel)) {
                    return Lists.newArrayList(cardModel, fastItemCardModel);
                }
                cardModel.setStatus(AiJourneyMessageCardActionEnum.DELETE.getCode());
                return Lists.newArrayList(cardModel);
            }
            cardModel.setData(vacationInfos);
            tripPlanNodeProcessorHelper.buildJourneyItemList(vacationInfos, chatContext);
            return Lists.newArrayList(cardModel);
        } catch (Throwable e) {
            LOGGER.recordDangerException(new LogModel("JourneyPlanNodeProcessor.item_reserve_card").e(e)
                .message("item_reserve_card_exception"));
            cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
            // 删除快捷预订卡片
            fastItemCardModel = buildFastReserveTitleCard(chatContext);
            if (Objects.nonNull(fastItemCardModel)) {
                return Lists.newArrayList(cardModel, fastItemCardModel);
            }
            return Lists.newArrayList(cardModel);
        } finally {
            LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.item_reserve_card")
                .request(JSONUtil.toJSONStringForLog(response))
                .message("recommendResult-{0}", JSON.toJSONString(recommendResult))
                .response("itemCard: " + JSONUtil.toJSONStringForLog(cardModel) + " streamMessageCardModel: "
                    + JSONUtil.toJSONStringForLog(fastItemCardModel)));
        }
    }

    private StreamMessageCardModel buildFastReserveTitleCard(ChatContext chatContext) {
        Boolean fastReserveTag = MapUtils.getBoolean(chatContext.getInternalData(), JOURNEY_FAST_RESERVE_TAG);
        if (BooleanUtils.isTrue(fastReserveTag)) {
            return null;
        }
        String fastReserveCardId = MapUtils.getString(chatContext.getInternalData(), JOURNEY_FAST_RESERVE_CARD_ID);
        if (StringUtils.isBlank(fastReserveCardId)) {
            return null;
        }
        StreamMessageCardModel<Object> fastReserveCardModel = new StreamMessageCardModel<>();
        fastReserveCardModel.setItemType(JOURNEY_TEXT.getCode());
        fastReserveCardModel.setStatus(AiJourneyMessageCardStatusEnum.FINISH.getCode());
        fastReserveCardModel.setId(fastReserveCardId);
        fastReserveCardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
        return fastReserveCardModel;
    }

    private List<VacationInfo> buildDefaultItemReserveCard() {
        List<VacationInfo> vacationInfos = Lists.newArrayList();

        VacationInfo vacationInfo = new VacationInfo();
        // 设置属性值
        vacationInfo.setVacationId("0");
        vacationInfo.setVacationName("杭州雷锋塔大门票-成人票");
        vacationInfo.setCategoryId("50015000");
        vacationInfo.setCategoryName("国内景点门票");
        vacationInfo.setVacationImg(
            "https://gw.alicdn.com/imgextra/i2/O1CN01XbMRXP26BOFqlry66_!!6000000007623-0-tps-1360-907.jpg");
        vacationInfo.setOriginalPrice("159.0");
        vacationInfo.setLowestPrice("135.0");
        vacationInfo.setYearSold(482);
        vacationInfo.setShopInfo("");

        vacationInfo.setShopJumpInfo(ShopJumpInfo.builder().h5Url(
                "https://outfliggys.m.taobao.com/app/trip/rx-travel-detail/pages/index?id=***********&_fli_online"
                    + "=true&titleBarHidden=2&disableNav=YES&_fli_webview=true&hybrid=true&_use_stream=1&categoryId"
                    + "=50258004&fpt=abfpt_fsd(flutter)fsk(sug)abfpt.poi_upgrade(1ebf401a73828a71)"
                    + "&shareId=copy_password&from=fshare&shareSpm=181.7850105&_projVer=2.9.0")
            .url(
                "https://outfliggys.m.taobao.com/app/trip/rx-travel-detail/pages/index?id=***********&_fli_online"
                    + "=true&titleBarHidden=2&disableNav=YES&_fli_webview=true&hybrid=true&_use_stream=1&categoryId"
                    + "=50258004&fpt=abfpt_fsd(flutter)fsk(sug)abfpt.poi_upgrade(1ebf401a73828a71)"
                    + "&shareId=copy_password&from=fshare&shareSpm=181.7850105&_projVer=2.9.0")
            .build());

        // 设置 BusinessTags
        List<BusinessTag> businessTags = new ArrayList<>();
        businessTags.add(new BusinessTag("可订明日"));
        businessTags.add(new BusinessTag("全额退"));
        businessTags.add(new BusinessTag("无购物"));
        businessTags.add(new BusinessTag("无自费"));
        vacationInfo.setBusinessTags(businessTags);
        vacationInfos.add(vacationInfo);

        return vacationInfos;
    }

    /**
     * 跟团游商品
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VacationInfo {
        private String vacationId;
        private String vacationName;
        private String categoryId;
        private String categoryName;
        private String vacationImg;
        private String originalPrice;
        private String lowestPrice;
        private int yearSold;
        private String shopInfo;
        private ShopJumpInfo shopJumpInfo;
        private JumpInfo jumpInfo;
        private String shopHonorInfo;
        private List<BusinessTag> businessTags;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    static class JumpInfo {
        private String url;
        private String h5Url;
        private String pcUrl;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    static class ShopJumpInfo {
        private String url;
        private String h5Url;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BusinessTag {
        private String text;
    }

    @MethodCondition(field = "itemType", value = "hotel_link_card")
    @EnableCollection(bizType = CollectionBizTypeEnum.HOTEL, bizIdColumn = "shid", titleColumn = "title",
        startTimeColumn = "checkIn", endTimeColumn = "checkOut")
    private List<StreamMessageCardModel<?>> buildHotelLinkCard(ComponentDataResponse response,
                                                               ChatContext chatContext) {
        StreamMessageCardModel<HotelRecommendCardVO> cardModel = new StreamMessageCardModel<>();
        Map<String, Object> internalData = chatContext.getInternalData();
        Object journeyHotel = MapUtils.getObject(internalData, ContextInnerKeyCommon.JOURNEY_HOTEL);
        try {
            // 参数校验
            tripPlanNodeProcessorHelper.checkResponseParams(response);
            // 构建基础卡片
            tripPlanNodeProcessorHelper.buildBaseCardModel(cardModel, response, HOTEL_LINK_CARD.getCode());

            String shid = response.getData().getString("hotelId");
            String hotelName = response.getData().getString("hotelName");
            if (StringUtils.isBlank(shid) || !NumberUtil.isNumber(shid)) {
                cardModel.setData(HotelRecommendCardVO.builder().title(hotelName).edit(true).clickText(hotelName)
                        .keyboardTags(EDIT_KEYBOARD_TAGS).keyboardTagsV1(EDIT_KEYBOARD_TAGS_V2).build());
                return Lists.newArrayList(cardModel);
            }

            List<HotelRecommendCardVO> hotelRecommendCardVOList = hotelNodeProcessor.getHotelRecommendCardVOList(
                Lists.newArrayList(shid), response, chatContext);
            if (CollectionUtils.isEmpty(hotelRecommendCardVOList)) {
                cardModel.setData(HotelRecommendCardVO.builder().title(hotelName).edit(true).clickText(hotelName)
                        .keyboardTags(EDIT_KEYBOARD_TAGS).keyboardTagsV1(EDIT_KEYBOARD_TAGS_V2).build());
                return Lists.newArrayList(cardModel);
            }

            HotelRecommendCardVO hotelRecommendCardVO = hotelRecommendCardVOList.get(0);
            hotelRecommendCardVO.setClickText(hotelRecommendCardVO.getTitle());
            hotelRecommendCardVO.setKeyboardTags(EDIT_KEYBOARD_TAGS);
            hotelRecommendCardVO.setKeyboardTagsV1(EDIT_KEYBOARD_TAGS_V2);
            hotelRecommendCardVO.setEdit(true);
            cardModel.setData(hotelRecommendCardVO);
            tripPlanNodeProcessorHelper.buildHotelContext(hotelRecommendCardVO, chatContext);
        } catch (Throwable e) {
            cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
            LOGGER.recordDangerException(new LogModel("JourneyPlanNodeProcessor.buildHotelLinkCard")
                .e(e).message("hotel_link_card_exception"));
        } finally {
            LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.buildHotelLinkCard")
                .request(JSONUtil.toJSONStringForLog(response))
                .message("journeyHotel-{0}", JSONUtil.toJSONStringForLog(journeyHotel))
                .response(JSONUtil.toJSONStringForLog(cardModel)));
        }
        return Lists.newArrayList(cardModel);
    }

    /**
     * 酒店预定卡片
     */
    @MethodCondition(field = "itemType", value = "hotel_reserve_card")
    @EnableCollection(bizType = CollectionBizTypeEnum.HOTEL, bizIdColumn = "shid", titleColumn = "title")
    private List<StreamMessageCardModel<?>> buildHotelReserveCard(ComponentDataResponse response,
                                                                  ChatContext chatContext) {
        StreamMessageCardModel cardModel = new StreamMessageCardModel<>();
        Map<String, Object> internalData = chatContext.getInternalData();
        Object journeyHotel = MapUtils.getObject(internalData, ContextInnerKeyCommon.JOURNEY_HOTEL);
        try {
            // 参数校验
            tripPlanNodeProcessorHelper.checkResponseParams(response);
            // 构建基础卡片
            tripPlanNodeProcessorHelper.buildBaseCardModel(cardModel, response, HOTEL_RESERVE_CARD.getCode());

            //判断是否使用快速预订V2版本，V2上线后，卡片处理可以删掉。
            if(BooleanUtils.isTrue(FAST_RESERVE_V2)){
                cardModel.setStatus(AiJourneyMessageCardActionEnum.DELETE.getCode());
                return Lists.newArrayList(cardModel);
            }
            List<HotelRecommendCardVO> hotelRecommendCardVOList = safeGetHotelList(chatContext);
            if (CollectionUtils.isEmpty(hotelRecommendCardVOList)) {
                cardModel.setStatus(AiJourneyMessageCardActionEnum.DELETE.getCode());
                return Lists.newArrayList(cardModel);
            }
            cardModel.setData(hotelRecommendCardVOList);
            // 设置“快捷预订”是否需要展示的标
            tripPlanNodeProcessorHelper.buildFastReserveTag(chatContext);
        } catch (Throwable e) {
            LOGGER.recordDangerException(new LogModel("JourneyPlanNodeProcessor.buildHotelReserveCard")
                .e(e).message("hotel_reserve_card_exception"));
            cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
        } finally {
            LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.buildHotelReserveCard")
                .request(JSONUtil.toJSONStringForLog(response))
                .message("journeyHotel-{0}", JSONUtil.toJSONStringForLog(journeyHotel))
                .response(JSONUtil.toJSONStringForLog(cardModel)));
        }
        return Lists.newArrayList(cardModel);
    }

    public List<HotelRecommendCardVO> safeGetHotelList(ChatContext chatContext) {
        List<HotelRecommendCardVO> hotelList;
        Map<String, Object> internalData = chatContext.getInternalData();
        if (internalData.containsKey(ContextInnerKeyCommon.JOURNEY_HOTEL)) {
            hotelList = (List<HotelRecommendCardVO>)chatContext.getInternalData().get(
                ContextInnerKeyCommon.JOURNEY_HOTEL);
        } else {
            hotelList = new ArrayList<>();
        }
        return hotelList;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LlmHotelInfo {
        private String shid; // 酒店 ID
        private String checkIn; // 入住日期（字符串格式）
        private String checkOut; // 退房日期（字符串格式）
    }

    @AteyeInvoker(description = "绘制地图ateye", paraDesc = "response")
    public String getDrawingMap(String llmInput){
        Map<String, List<String>> dayPoiMap = tripPlanNodeProcessorHelper.parseDayPoi(llmInput);
        Result result = new Result(true, "success", "操作成功", null, "-", "-");
        List<RouteDay> routeDayList = buildRouteDayList(dayPoiMap, null,result);
        DrawMapContextDTO drawMapContextDTO = new DrawMapContextDTO();
        drawMapContextDTO.setJourneyTitle("测试标题，非真实");
        ChatContext chatContext = new ChatContext();
        AiJourneyMessageContentDTO.StructRoute  structRoute = buildRouteListForJourney(routeDayList, drawMapContextDTO,chatContext);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("routeDayList",routeDayList);
        jsonObject.put("structRoute",structRoute);
        return JSONUtil.toJSONString(jsonObject);

    }
    /**
     * 绘制地图
     */
    @MethodCondition(field = "itemType", value = "drawing_map")
    private List<StreamMessageCardModel<?>> buildDrawingMapCard(ComponentDataResponse response, ChatContext chatContext) {
        Result result = new Result(true, "success", "操作成功", response.getAllData(), "-", "-");
        StreamMessageCardModel<Map<String, Object>> cardModel = new StreamMessageCardModel<>();
        Long userId = 0L;
        if (chatContext != null && chatContext.getRequest() != null) {
            userId = chatContext.getRequest().getUserId();
        }
        long start = System.currentTimeMillis();

        try {
            // 参数校验
            tripPlanNodeProcessorHelper.checkResponseParams(response);
            // 构建基础卡片
            tripPlanNodeProcessorHelper.buildBaseCardModel(cardModel, response, DRAWING_MAP.getCode());
            // 构建原始query
            buildChat(chatContext, result);
            // 获取隐藏token 根据大模型输入，生成数据存入上下文中。
            DrawMapContextDTO drawMapContextDTO = buildLlmOutDTO(response.getData());
            Map<String, List<String>> dayPoiMap = tripPlanNodeProcessorHelper.parseDayPoi(drawMapContextDTO.getDayPoiInfos());
            if (MapUtils.isEmpty(dayPoiMap)) {
                result.buildResult(true, "parseDayPoi_res_empty", "解析基模天级路线结果为空");
                return deleteActionCardModel(cardModel);
            }
            // 构建卡片
            Map<String, Object> card = new HashMap<>();
            // 构建天级路线
            List<RouteDay> routeDayList = buildRouteDayList(dayPoiMap, chatContext,result);
            // copy出来一份onlyPoiDayList
            List<RouteDay> onlyPoiDayList = getOnlyPoiDayList(routeDayList);
            drawMapContextDTO.setOnlyPoiDayList(onlyPoiDayList);
            // buildRouteListForJourney 构建行程规划查看地图数据。
            AiJourneyMessageContentDTO.StructRoute  structRoute = buildRouteListForJourney(routeDayList, drawMapContextDTO,chatContext);
            card.put(CommonString.STRUCTROUTE,structRoute);
            //  前置判断， 只有一个景点或者没有景点数据，直接不出地图
            if(BooleanUtils.isTrue(earlyCheckData(drawMapContextDTO,result))){
                return deleteActionCardModel(cardModel);
            }
            // buildScene 确认场景 场景中的城市，不应该判断交通的。
            buildScene(routeDayList,result,card);
            // 这里进行去程和返程的逻辑调整，如果是第一天的话，技术方案：https://aliyuque.antfin.com/qnwrq9/si8e4v/wgmivbgogghcxs8x
            dealGoAndBackRouteV2(routeDayList, drawMapContextDTO.getGoTraffic(), drawMapContextDTO.getBackTraffic(), userId);
            // 中间如果有poi为空的天，直接不出地图
            long count = routeDayList.stream().filter(Objects::nonNull).filter(
                routeDay -> CollectionUtils.isEmpty(routeDay.getPoiList())).count();
            if (BooleanUtils.isTrue(LIMIT_DAYPOI_EXIST_EMPTY) && count > 0) {
                result.buildResult(true, "daypoi_exist_empty", "存在天级poi为空");
                return deleteActionCardModel(cardModel);
            }

            // 校验点位有效性
            if(!checkPointValidity(routeDayList,result)) {
                return deleteActionCardModel(cardModel);
            }

            // 获取头像和昵称
            buildMemberInfo(card, chatContext);

            // 处理城市name，各各点如果城市name一样，则去掉城市名称
            buildRouteDayCityName(routeDayList);

            // 构建是否展示实时地理位置类型
            buildShowType(card, routeDayList,result);

            // 每个icon只能出现一次，如果出现多次，第二个要干掉
            removeDuplicateIcon(routeDayList);

            // 新增手绘地图手绘类型
            card.put("theme",
                StringUtils.isBlank(drawMapContextDTO.getTheme())
                    ? String.valueOf(RouteThemeEnum.COMMON.getCode()) : drawMapContextDTO.getTheme());

            // 对于手绘地图，需要将poiName从长的改为短的。
            if(BooleanUtils.isTrue(replacePoiNameToShortPoiName)){
                replaceRoutePoiListForPoiName(routeDayList);
            }
            card.put("dayPoiList", routeDayList);
            card.put("poiImgList", dealPoiImageList(drawMapContextDTO));
            card.put("drawPicForReplace",
                chatContext.getSystemMessageId() + "_" + response.getId() + "_draw_pic_value");
            card.put("systemMessageId", chatContext.getSystemMessageId());
            //构建跳转链接
            buildMapJumpUrl(card,chatContext.getSystemMessageId(), cardModel.getId());
            //构建标题
            card.put("title", buildJourneyTitle(drawMapContextDTO,chatContext));
            card.put("hiddenBtn", buildHiddenBtn(drawMapContextDTO.getHiddenBtn()));
            cardModel.setData(card);
            //将游玩图存入至上下文中 用于收藏数据构建
            chatContext.getInternalData().put(ContextInnerKeyCommon.JOURNEY_ROUTE_MAP_CARD_ID, response.getId());
        } catch (Throwable e) {
            result.buildResult(false, "exception", e.getMessage());
            cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
            LOGGER.recordDangerException(new LogModel("JourneyPlanNodeProcessor.buildDrawingMapCard").e(e)
                .message("drawing_map_exception").userId(userId));
        } finally {
            LOGGER.recordOutput(new LogModel("buildDrawingMapCard_trace")
                .message("success={0},code={1},msg={2},query={3},llmOutput={4},showType={5},showMsg={6},scene={7},cityDesc={8},time={9},userid={10},messageId={11},addErrorMsg={12} ",
                        result.isSuccess(), result.getCode(), result.getMsg(),
                    result.getQuery(), JSONUtil.removeVerticalBar(result.getLlmOutput()),
                    result.getShowType(),result.getShowMsg(),result.getScene(),result.getCityDesc()
                    , CIDateUtil.getTimeToString(new Date())
                    ,String.valueOf(chatContext.getRequest().getUserId()),chatContext.getSystemMessageId(),result.getAddErrorMsg())
                .userId(userId));

            LOGGER.recordOutput(new LogModel("buildDrawingMapCard_log")
                    .request(JSONUtil.toJSONStringForLog(response))
                    .response(JSONUtil.toJSONStringForLog(cardModel))
                    .cost(System.currentTimeMillis() - start)
                    .userId(userId));
        }
        return Lists.newArrayList(cardModel);
    }

    private List<RouteDay> getOnlyPoiDayList(List<RouteDay> routeDayList) {
        if(CollectionUtils.isEmpty(routeDayList)){
            return new ArrayList<>();
        }
        String jsonString = JSONUtil.toJSONString(routeDayList);
        return JSONArray.parseArray(jsonString, RouteDay.class);
    }

    private void buildMapJumpUrl(Map<String, Object> card, String systemMessageId, String cardId) {
        if (BooleanUtils.isTrue(RealMapV2)) {
            card.put(ContextInnerKeyCommon.MAP_JUMP_URL,
                tripPlanNodeProcessorHelper.genMapJumpUrl(systemMessageId, cardId));
        } else {
            card.put(ContextInnerKeyCommon.MAP_JUMP_URL,
                tripPlanNodeProcessorHelper.genMapJumpUrl(systemMessageId));
        }
    }

    private String buildHiddenBtn(String hiddenBtn) {
        if (StringUtils.isBlank(hiddenBtn)) {
            return "false";
        }
        if (!Objects.equals(hiddenBtn, "true")) {
            return "false";
        }
        //hiddenBtn为true时，代表多方案，那么后端控制是否真正隐藏
        // 新版本 不需要隐藏 一直为false。
        return BooleanUtils.isTrue(RealMapV2) ? "false" : "true";
    }

    private Boolean earlyCheckData(DrawMapContextDTO drawMapContextDTO, Result result) {
        List<RouteDay> onlyPoiDayList = drawMapContextDTO.getOnlyPoiDayList();
        if (CollectionUtils.isEmpty(onlyPoiDayList)) {
            result.buildResult(true, "buildRouteDayList_res_empty", "构建天级线路结果为空");
            return Boolean.TRUE;
        }
        // 如果只有一个景点，不需要展示地图
        if (getPoiNumber(onlyPoiDayList) == 1) {
            result.buildResult(true, "justOneCanUsePoi", "只有一个真实景点，不展示地图");
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 获取poiNumber
     * @param routeDayList
     * @return
     */
    private int getPoiNumber(List<RouteDay> routeDayList) {
        return routeDayList.stream().filter(Objects::nonNull).mapToInt(
            e -> CollectionUtils.isEmpty(e.getPoiList()) ? 0 : e.getPoiList().size()).sum();
    }

    /**
     * 构建手绘地图标题
     * @param drawMapContextDTO drawMapContextDTO
     * @param chatContext chatContext
     * @return journeyTitle
     */
    private String buildJourneyTitle(DrawMapContextDTO drawMapContextDTO, ChatContext chatContext) {

        if (StringUtils.isNotEmpty(drawMapContextDTO.getJourneyTitle())) {
            return drawMapContextDTO.getJourneyTitle();
        }
        // journeyTitle为空，代表单方案，从上下文取
        return tripPlanNodeProcessorHelper.safeGetJourneyTitle(chatContext);
    }

    private DrawMapContextDTO buildLlmOutDTO(JSONObject data) {
        DrawMapContextDTO drawMapContextDTO = new DrawMapContextDTO();
        String dayPoiInfos = MapUtils.getString(data, "poiInfos");
        String theme = MapUtils.getString(data, "theme");
        String goTraffic = MapUtils.getString(data, "goTraffic");
        String backTraffic = MapUtils.getString(data, "backTraffic");
        String hiddenBtn = MapUtils.getString(data, "hiddenBtn");
        String journeyTitle = MapUtils.getString(data, "journeyTitle");
        drawMapContextDTO.setDayPoiInfos(dayPoiInfos);
        drawMapContextDTO.setTheme(theme);
        drawMapContextDTO.setGoTraffic(goTraffic);
        drawMapContextDTO.setBackTraffic(backTraffic);
        drawMapContextDTO.setHiddenBtn(hiddenBtn);
        drawMapContextDTO.setJourneyTitle(journeyTitle);
        return drawMapContextDTO;
    }

    /**
     * 构建手绘地图查看地图数据
     * @param drawMapContextDTO drawMapContextDTO
     * @param chatContext chatContext
     * @return structRoute
     */
    private AiJourneyMessageContentDTO.StructRoute  buildRouteListForJourney(List<RouteDay> baseRouteData,
        DrawMapContextDTO drawMapContextDTO, ChatContext chatContext) {
        AiJourneyMessageContentDTO.StructRoute  structRoute = new AiJourneyMessageContentDTO.StructRoute();
        //设置标题
        structRoute.setTitle(buildJourneyTitle(drawMapContextDTO, chatContext));
        List<AiJourneyRecommendLineModel.Route> routeList = new ArrayList<>();
        structRoute.setRouteList(routeList);
        for(int i = 0;i<baseRouteData.size();i++){
            RouteDay baseDay = baseRouteData.get(i);
            List<PoiInfoDTO> basePoiList = baseDay.getPoiList();
            //i即是第几天 不使用手绘地图中的天是因为，怕大模型给中文的数字。 一 二 三
            AiJourneyRecommendLineModel.Route route = new AiJourneyRecommendLineModel.Route();
            List<com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.Poi> poiList = new ArrayList<>();
            route.setPoiList(poiList);
            route.setDay(String.valueOf(i));
            //  basePoiList 可能为空，如果没有查询到poi的话，或者是城市兜底的话。没有数据
            if(CollectionUtils.isNotEmpty(basePoiList)){
                buildPoiList(poiList,basePoiList);
            }
            routeList.add(route);
        }
        LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.buildRouteListForJourney")
            .request(JSONUtil.toJSONStringForLog(baseRouteData))
            .response(JSONUtil.toJSONStringForLog(structRoute)));
        return structRoute;
    }

    private void buildPoiList(List<Poi> poiList, List<PoiInfoDTO> basePoiList) {
        for(PoiInfoDTO poiInfoDTO:  basePoiList){
            com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.Poi poi = new com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.Poi();
            // 数据是高德还是飞猪poi
            poi.setType(poiInfoDTO.getPoiType());
            //类型是poi还是酒店
            poi.setPoiType(POI_TYPE);
            poi.setPoiId(poiInfoDTO.getPoiId());
            poi.setLongitude(Double.parseDouble(poiInfoDTO.getPoiLng()));
            poi.setLatitude(Double.parseDouble(poiInfoDTO.getPoiLat()));
            poi.setPoiName(poiInfoDTO.getPoiName());
            poi.setAddress(poiInfoDTO.getAddress());
            poi.setPoiDestTreeName(poiInfoDTO.getPoiDestTreeName());
            poi.setPoiDestTreeId(poiInfoDTO.getPoiDestTreeId());
            poiList.add(poi);
        }
    }

    /**
     * 替换poiName为短poiName
     * @param routeDayList
     */
    private void replaceRoutePoiListForPoiName(List<RouteDay> routeDayList) {
        if (CollectionUtils.isEmpty(routeDayList)) {
            return;
        }
        for (RouteDay routeDay : routeDayList) {
            if (Objects.isNull(routeDay)) {
                continue;
            }
            if (CollectionUtils.isEmpty(routeDay.getPoiList())) {
                continue;
            }
            List<PoiInfoDTO> poiList = routeDay.getPoiList();
            for (PoiInfoDTO routePoi : poiList) {
                if (Objects.isNull(routePoi) || StringUtils.isEmpty(routePoi.getPoiName()) ) {
                    continue;
                }
                // poiName小于等于5个字符，就跳过
                if(routePoi.getPoiName().length() <= 4){
                    continue;
                }
                routePoi.setPoiName(diamondUtil.getShortPoiName(routePoi.getPoiName()));
            }
        }
    }

    /**
     * 构建确认场景
     * @param routeDayList
     */
    private void buildScene(List<RouteDay> routeDayList, Result result, Map<String, Object> card) {

        Set<String> baseCityNameList = routeDayList.stream().filter(
            routeDay -> StringUtils.isNotBlank(routeDay.getCityName()))
            .map(RouteDay::getCityName)
            .collect(
            Collectors.toSet());
        // cityName可能存在简称和全程， 例如成都、成都市，认为是一个城市
        Set<String> cityNameList = baseCityNameList.stream().map(
            name -> name.endsWith("市") ? name.substring(0, name.length() - 1) : name).collect(
            Collectors.toSet());
        Set<String> divisionIds = routeDayList.stream().map(RouteDay::getDivisionId).filter(Objects::nonNull).collect(
            Collectors.toSet());
        Integer citySize ;
        String message;
        Integer day = routeDayList.size();
        if(BooleanUtils.isTrue(useCityNameGetCitySize)){
            citySize = cityNameList.size();
            message = "行程天数:"+day+"城市数量:"+citySize + "原cityNameList ： "
                +JSONUtil.toJSONString(baseCityNameList)  + " 去重后的 cityNameList :  "+ JSONUtil.toJSONString(cityNameList);
        }else {
            citySize = divisionIds.size();
            message = "行程天数:"+day+"城市数量:"+citySize + " divisionIds :  "+ JSONUtil.toJSONString(divisionIds);
        }

        DrawMapSceneType drawMapSceneType = DrawMapSceneType.buildType(citySize, day);
        LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.buildScene")
            .request(JSONUtil.toJSONString(routeDayList))
            .message(message)
            .response(drawMapSceneType.toString()));
        card.put("cardScene", drawMapSceneType.getCode());

        //添加降级逻辑
        card.put("needDown", needDownScene.get(drawMapSceneType.getCode()));

        //以下为便于日志查询，封装数据
        result.setScene(drawMapSceneType.getDesc());
        result.setCityDesc(message);
    }

    @AteyeInvoker(description = "手绘地图获取场景测试")
    public String  getType(String citySize,String day) {
        DrawMapSceneType drawMapSceneType = DrawMapSceneType.buildType(Integer.valueOf(citySize), Integer.valueOf(day));
        return drawMapSceneType.toString();
    }

    private void removeDuplicateIcon(List<RouteDay> routeDayList) {
        if (CollectionUtils.isEmpty(routeDayList)) {
            return;
        }
        Set<String> iconSet = new HashSet<>();
        for (RouteDay routeDay : routeDayList) {
            if (Objects.isNull(routeDay)) {
                continue;
            }
            if (StringUtils.isBlank(routeDay.getIcon())) {
                continue;
            }
            if (iconSet.contains(routeDay.getIcon())) {
                routeDay.setIcon(null);
            }
            iconSet.add(routeDay.getIcon());
        }
    }

    private void buildChat(ChatContext chatContext, Result result) {
        if (chatContext == null || chatContext.getRequest() == null) {
            return;
        }
        result.setQuery(chatContext.getRequest().getChat());

    }

    private void buildRouteDayCityName(List<RouteDay> routeDayList) {
        if (CollectionUtils.isEmpty(routeDayList)) {
            return;
        }
        Set<String> cityNameSet = routeDayList.stream().filter(
            routeDay -> StringUtils.isNotBlank(routeDay.getCityName())).map(RouteDay::getCityName).collect(
            Collectors.toSet());
        if (CollectionUtils.isEmpty(cityNameSet)) {
            return;
        }
        if (cityNameSet.size() > 1) {
            return;
        }
        // 如果城市名称一样，则去掉城市名称
        routeDayList.stream().forEach(routeDay -> {
            routeDay.setCityName(null);
        });
    }

    private boolean checkPointValidity(List<RouteDay> routeDayList,Result result) {

        if (CollectionUtils.isEmpty(routeDayList)) {
            result.buildResult(true, "checkPointValidity_rule_failed", "每日行程为空");
            return false;
        }

        long count = routeDayList.stream().filter(Objects::isNull).count();
        if (count > 0) {
            result.buildResult(true, "checkPointValidity_rule_failed", "每日行程为空");
            return false;
        }

        // 获取地图上点位总数
        Integer pointSize = buildPointSize(routeDayList);
        // 小于3的直接不展示
        if(pointSize < 3){
            result.buildResult(true, "checkPointValidity_rule_failed", "景点数量小于3");
            return false;
        }
        // 总天数大于12天也不展示
        if(routeDayList.size() > Switcher.AI_ASK_DAY_MORE){
            result.buildResult(true, "checkPointValidity_rule_failed", String.format("总天数大于%d天", Switcher.AI_ASK_DAY_MORE));
            return false;
        }
        // 三天内poi个数大于12个也不展示
        if(routeDayList.size() < Switcher.AI_ASK_DAY_LESS && pointSize > Switcher.AI_ASK_DAY_POI){
            result.buildResult(true, "checkPointValidity_rule_failed", String.format("%d内poi个数大于%d个", Switcher.AI_ASK_DAY_LESS, Switcher.AI_ASK_DAY_POI));
            return false;
        }
        //if (pointSize < 3 || routeDayList.size() > Switcher.AI_ASK_DAY_MORE
        //    || (routeDayList.size() < Switcher.AI_ASK_DAY_LESS && pointSize > Switcher.AI_ASK_DAY_POI)) {
        //    return false;
        //}
        return true;
    }



    private void buildShowType(Map<String, Object> card, List<RouteDay> routeDayList, Result result) {

        card.put("showType", "common");
        result.setShowType("common");
        if (CollectionUtils.isEmpty(routeDayList)) {
            return;
        }

        // 兜底开关直接展示普通地图，默认关
        if (BooleanUtils.isTrue(Switcher.DRAWING_MAP_SHOW_COMMON_MAP)) {
            card.put("showType", "common");
            result.setShowType("common");
            result.setShowMsg("全量兜底");
            return;
        }

        // 全部走新地图，默认关
        if (BooleanUtils.isTrue(Switcher.DRAWING_MAP_SHOW_NEW_MAP)) {
            card.put("showType", "new");
            result.setShowType("new");
            return;
        }

        // 存在多城市多poi，则展示新地图，默认关
        if (BooleanUtils.isTrue(Switcher.DRAWING_MAP_MUTIL_CITY_AND_MUTIL_POI)) {

            //// 存在空经纬度的点，则展示普通地图
            //long count = routeDayList.stream().filter(Objects::nonNull).filter(
            //    routeDay -> StringUtils.isBlank(routeDay.getLat()) || StringUtils.isBlank(routeDay.getLon())).count();
            //if (count > 0) {
            //    card.put("showType", "common");
            //    result.setShowType("common");
            //    result.setShowMsg("城市经纬度有空");
            //    return;
            //}

            if(BooleanUtils.isTrue(Switcher.DRAWING_MAP_ABORAD)){
                // 存在境外poi，则展示普通地图
                for (RouteDay routeDay : routeDayList) {
                    List<PoiInfoDTO> poiList = routeDay.getPoiList();
                    if (CollectionUtils.isEmpty(poiList)) {
                        continue;
                    }
                    for (PoiInfoDTO poiInfoDTO : poiList) {
                        if (Objects.nonNull(poiInfoDTO.getIsAbroad()) && poiInfoDTO.getIsAbroad() == 1) {
                            card.put("isAbroad", true);
                            card.put("showType", "common");
                            result.setShowType("common");
                            result.setShowMsg("国外兜底");
                            return;
                        }
                    }
                }
            }

            int cityNameNum = dealCityNameNum(routeDayList);
            long poiCount = routeDayList.stream().filter(Objects::nonNull).mapToInt(
                e -> CollectionUtils.isEmpty(e.getPoiList()) ? 0 : e.getPoiList().size()).sum();
            int dayCount = routeDayList.size();
            if (cityNameNum > Switcher.DRAWING_MAP_CITYNAME_NUM_MIN
                && cityNameNum < Switcher.DRAWING_MAP_CITYNAME_NUM_MAX && poiCount > Switcher.DRAWING_MAP_POI_NUM_MIN
                && poiCount < Switcher.DRAWING_MAP_POI_NUM_MAX && dayCount > Switcher.DRAWING_MAP_DAY_NUM_MIN
                && dayCount < Switcher.DRAWING_MAP_DAY_NUM_MAX) {
                card.put("showType", "new");
                result.setShowType("new");
                return;
            }
            card.put("showType", "common");
            result.setShowType("common");
            result.setShowMsg("不满足多城市多天");
            return;
        }

    }

    private int dealCityNameNum(List<RouteDay> routeDayList) {
        try {
            if (CollectionUtils.isEmpty(routeDayList)) {
                return 0;
            }
            Set<String> cityNameNums = new HashSet<>();
            for (RouteDay routeDay : routeDayList) {
                String cityName = routeDay.getCityName();
                if (StringUtils.isBlank(cityName)) {
                    continue;
                }
                cityNameNums.add(cityName);
            }
            return cityNameNums.size();
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("JourneyPlanNodeProcessor.buildDrawingMapCard").e(e)
                .message("dealCityNameNum_exception"));
            return 0;
        }
    }

    private void buildIsAbroad(Map<String, Object> card, List<RouteDay> routeDayList) {
        if (CollectionUtils.isEmpty(routeDayList)) {
            return;
        }
        for (RouteDay routeDay : routeDayList) {
            List<PoiInfoDTO> poiList = routeDay.getPoiList();
            if (CollectionUtils.isEmpty(poiList)) {
                continue;
            }
            for (PoiInfoDTO poiInfoDTO : poiList) {
                if (Objects.nonNull(poiInfoDTO.getIsAbroad()) && poiInfoDTO.getIsAbroad() == 1) {
                    card.put("isAbroad", true);
                    return;
                }
            }
        }
    }

    private void buildCardType(Map<String, Object> card, List<RouteDay> routeDayList) {
        if (CollectionUtils.isEmpty(routeDayList)) {
            return;
        }
        long count = routeDayList.stream().filter(Objects::nonNull).filter(
            routeDay -> StringUtils.isBlank(routeDay.getLat()) || StringUtils.isBlank(routeDay.getLon())).count();
        if (count > 0) {
            card.put("cardType", "location_empty");
        }
    }

    /**
     * 目前发现有最后一天重复的情况， 去除最后一天的重复项
     *
     * @param routeDayList
     */
    private void tempFix(List<RouteDay> routeDayList) {

    }

    private List<RouteDay> dealPoiImageList(DrawMapContextDTO drawMapContextDTO) {
        if (CollectionUtils.isEmpty(drawMapContextDTO.getOnlyPoiDayList())) {
            LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.dealPoiImageList")
                .request("drawMapContextDTO:" + JSONUtil.toJSONStringForLog(drawMapContextDTO)).response("poiImageList:null"));
            return null;
        }
        List<RouteDay> poiImageList = drawMapContextDTO.getOnlyPoiDayList();
        poiImageList.removeIf(routeDay -> CollectionUtils.isEmpty(routeDay.getPoiList()));
        return poiImageList;
    }

    private void dealGoAndBackRouteV2(List<RouteDay> routeDayList, String goTraffic, String backTraffic, Long userId) {
        try {
            if (CollectionUtils.isEmpty(routeDayList)) {
                return;
            }

            // 处理去程交通
            buildGoTraffic(routeDayList, goTraffic);

            // 处理返程程交通
            buildBackTraffic(routeDayList, backTraffic);
        } catch (Throwable e) {
            LOGGER.recordDangerException(new LogModel("JourneyPlanNodeProcessor.dealGoAndBackRouteV2").e(e)
                .message("dealGoAndBackRouteV2_exception").userId(userId));
        } finally {
            LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.dealGoAndBackRouteV2")
                .request(
                    "routeDayList:" + JSON.toJSONString(routeDayList) + ", goTraffic:" + goTraffic + ",backTraffic:"
                        + backTraffic)
                .userId(userId)
                .response(JSON.toJSONString(routeDayList)));
        }
    }

    private void buildBackTraffic(List<RouteDay> routeDayList, String backTraffic) {
        if (StringUtils.isBlank(backTraffic)) {
            return;
        }
        String[] typeAndCityName = backTraffic.trim().split("\\|");
        if (typeAndCityName == null || typeAndCityName.length < 2) {
            return;
        }
        String trafficType = typeAndCityName[0];
        String cityName = typeAndCityName[1];
        // 为了兼容历史版本
        String backStationName = StringUtils.EMPTY;
        if (typeAndCityName.length > 2) {
            backStationName = typeAndCityName[2];
        }

        RouteDay routeDay = routeDayList.get(routeDayList.size() - 1);
        List<PoiInfoDTO> poiList = routeDay.getPoiList();
        if (CollectionUtils.isEmpty(poiList)) {
            poiList = new LinkedList<>();
        }

        List<String> noTrafficIconList = poiList.stream().filter(Objects::nonNull).filter(
            poiInfoDTO -> StringUtils.isNotBlank(poiInfoDTO.getIcon())).map(PoiInfoDTO::getIcon).collect(
            Collectors.toList());

        PoiInfoDTO poiInfoDTO = new PoiInfoDTO();
        poiInfoDTO.setPoiName(cityName + "返程");

        // 调用高德查询车站的经纬度
        buildTrafficExtInfo(backStationName, poiInfoDTO, routeDay);

        poiList.add(poiInfoDTO);
        if (StringUtils.isBlank(routeDay.getCityName())) {
            routeDay.setCityName(cityName);
        }
        routeDay.setPoiList(poiList);
        if (CollectionUtils.isNotEmpty(noTrafficIconList)) {
            routeDay.setIcon(noTrafficIconList.get(0));
            routeDay.setIconType("scenic");
            return;
        }
        routeDay.setIcon(MapUtils.getString(Switcher.BACK_TRAFFIC_ICON_MAP, trafficType));
        routeDay.setIconType("traffic");
    }

    private void buildGoTraffic(List<RouteDay> routeDayList, String goTraffic) {
        if (StringUtils.isBlank(goTraffic)) {
            return;
        }
        String[] typeAndCityName = goTraffic.trim().split("\\|");
        if (typeAndCityName == null || typeAndCityName.length < 2) {
            return;
        }
        String trafficType = typeAndCityName[0];
        String cityName = typeAndCityName[1];
        // 为了兼容历史版本
        String goStationName = StringUtils.EMPTY;
        if (typeAndCityName.length > 2) {
            goStationName = typeAndCityName[2];
        }

        RouteDay routeDay = routeDayList.get(0);
        List<PoiInfoDTO> poiList = routeDay.getPoiList();
        if (CollectionUtils.isEmpty(poiList)) {
            poiList = new LinkedList<>();
        }

        List<String> noTrafficIconList = poiList.stream().filter(Objects::nonNull).filter(
            poiInfoDTO -> StringUtils.isNotBlank(poiInfoDTO.getIcon())).map(PoiInfoDTO::getIcon).collect(
            Collectors.toList());

        PoiInfoDTO poiInfoDTO = new PoiInfoDTO();
        poiInfoDTO.setPoiName(cityName + "出发");

        // 调用高德查询车站的经纬度
        buildTrafficExtInfo(goStationName, poiInfoDTO, routeDay);

        poiList.add(0, poiInfoDTO);
        if (BooleanUtils.isTrue(NOPOICITYNAME_USE_TRAFFIC_CITYNAME)&& StringUtils.isBlank(routeDay.getCityName())) {
            routeDay.setCityName(cityName);
        }
        routeDay.setPoiList(poiList);
        if (CollectionUtils.isNotEmpty(noTrafficIconList)) {
            routeDay.setIcon(noTrafficIconList.get(0));
            routeDay.setIconType("scenic");
            return;
        }
        routeDay.setIcon(MapUtils.getString(Switcher.GO_TRAFFIC_ICON_MAP, trafficType));
        routeDay.setIconType("traffic");
    }

    private void buildTrafficExtInfo(String goStationName, PoiInfoDTO poiInfoDTO, RouteDay routeDay) {
        if (StringUtils.isBlank(goStationName)) {
            return;
        }
        GaoDeSearchQueryRequest request = new GaoDeSearchQueryRequest();
        request.setKeywords(goStationName);
        request.setTypes("150000");
        request.setPageSize(1);
        List<PoiInfoDTO> poiInfoDTOList = tripPlanNodeProcessorHelper.queryGaoPoiByKeywords(request);
        if (CollectionUtils.isEmpty(poiInfoDTOList)) {
            return;
        }
        PoiInfoDTO dto = poiInfoDTOList.get(0);
        poiInfoDTO.setLocation(dto.getLocation());
        poiInfoDTO.setPoiLat(dto.getPoiLat());
        poiInfoDTO.setPoiLng(dto.getPoiLng());
        poiInfoDTO.setFirstCategory(1L);
        poiInfoDTO.setFirstCategoryName("交通");

        // 经纬度赋值
        if (StringUtils.isBlank(routeDay.getLat()) || StringUtils.isBlank(routeDay.getLon())) {
            routeDay.setLat(poiInfoDTO.getPoiLat());
            routeDay.setLon(poiInfoDTO.getPoiLng());
        }
    }



    private String dealIcon(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        return Switcher.iconMap.get(type);
    }

    private Integer buildPointSize(List<RouteDay> routeDayList) {
        if (CollectionUtils.isEmpty(routeDayList)) {
            return 0;
        }
        return routeDayList.stream().filter(Objects::nonNull).mapToInt(
            e -> CollectionUtils.isEmpty(e.getPoiList()) ? 0 : e.getPoiList().size()).sum();
    }

    private void buildMemberInfo(Map<String, Object> card, ChatContext chatContext) {
        if (Objects.isNull(chatContext.getRequest()) || Objects.isNull(chatContext.getRequest().getUserId())) {
            return;
        }
        card.put("avatar", FLIGGY_DEFAULT_AVATAR);
        MemberDTO memberDTO = memberServiceHelper.getByUserIdV2(chatContext.getRequest().getUserId());
        if (Objects.isNull(memberDTO)) {
            return;
        }
        card.put("avatar", memberDTO.getAvatar());
        card.put("nick", memberDTO.getNick());
    }

    private List<StreamMessageCardModel<?>> deleteActionCardModel(
        StreamMessageCardModel<Map<String, Object>> cardModel) {
        cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
        return Lists.newArrayList(cardModel);
    }

    private List<RouteDay> buildRouteDayList(Map<String, List<String>> dayPoiMap, ChatContext chatContext, Result result) {
        List<RouteDay> routeDayList = dayPoiMap.keySet().stream().map(day -> {
            List<String> poiList = dayPoiMap.get(day);
            if (CollectionUtils.isEmpty(poiList)) {

                RouteDay routeDay = new RouteDay();
                routeDay.setDay("Day " + day);
                return routeDay;
            }

            // 查询poi详情
            List<PoiInfoDTO> dayPoiList = poiList.stream().map(poiId -> {
                PoiInfoDTO poi = tripPlanNodeProcessorHelper.queryPoiInfoByHsf(poiId);
                if (Objects.isNull(poi)) {
                    return null;
                }
                // 判断简称是否为空，不为空优先取简称
                if (StringUtils.isNotBlank(poi.getPoiNameAbbr())) {
                    poi.setPoiName(poi.getPoiNameAbbr());
                }
                return poi;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(dayPoiList)) {
                RouteDay routeDay = new RouteDay();
                routeDay.setDay("Day " + day);
                // 若天级poi为空 有可能是自由行，那么只会传入城市信息，这里尝试使用城市名称反查点位。来补全城市名和经纬度。
                TrdiDivisionDO trdiDivisionDO = buildCityNameForCityName(day, poiList, result);
                if (trdiDivisionDO != null) {
                    //添加行政区化id，用于合并。
                    routeDay.setDivisionId(String.valueOf(trdiDivisionDO.getId()));
                    routeDay.setCityName(StringUtils.isEmpty( trdiDivisionDO.getNameAbbr())? trdiDivisionDO.getName() : trdiDivisionDO.getNameAbbr());
                    routeDay.setLon(String.valueOf(trdiDivisionDO.getLongitude()));
                    routeDay.setLat(String.valueOf(trdiDivisionDO.getLatitude()));
                }
                return routeDay;
            }
            // 构建天级路线
            RouteDay routeDay = new RouteDay();
            routeDay.setPoiList(dayPoiList);

            // 拿第一个poi的城市就好了
            List<Long> poiIdList = dayPoiList.stream().filter(e -> StringUtils.isNumeric(e.getPoiId())).map(
                dayPoi -> Long.parseLong(dayPoi.getPoiId())).collect(
                Collectors.toList());

            // 获取icon
            List<AiPoiIconDO> icons = null;
            if(CollectionUtils.isNotEmpty(poiIdList)){
                try {
                    AiPoiIconParam param = new AiPoiIconParam();
                    param.createCriteria().andPoiIdInWhenPresent(poiIdList).andStatusEqualTo(Short.valueOf("2"))
                        .andLlmEqualToWhenPresent(POI_ICON_LLM);
                    icons = aiPoiIconDAO.selectByParam(param);
                    if (CollectionUtils.isNotEmpty(icons)) {
                        String iconUrl = icons.stream().map(AiPoiIconDO::getIcon).filter(StringUtils::isNotBlank)
                            .findFirst().get();
                        routeDay.setIcon(iconUrl);

                        // 将图片塞到dayPoiList中
                        Map<String, String> poiId2Icon = icons.stream().collect(
                            Collectors.toMap(e -> String.valueOf(e.getPoiId()), AiPoiIconDO::getIcon, (v1, v2) -> v1));
                        dayPoiList.forEach(dayPoi -> dayPoi.setIcon(MapUtils.getString(poiId2Icon, dayPoi.getPoiId())));
                    }
                } catch (Exception e) {
                    LOGGER.recordDangerException(new LogModel("aiPoiIconDAO.selectByParam")
                        .e(e).message("查询icon包错"));
                } finally {
                    LOGGER.recordOutput(new LogModel("aiPoiIconDAO.selectByParam")
                        .request(JSON.toJSONString(poiIdList))
                        .response(JSON.toJSONString(icons)));
                }
            }


            PoiInfoDTO poiInfoDTO = dayPoiList.get(0);
            LOGGER.recordOutput(new LogModel("buildRouteDayList")
                .request("first poiInfoDTO : "+ JSONUtil.toJSONString(poiInfoDTO))
                .response(JSONUtil.toJSONString(dayPoiList)));
            routeDay.setCityName(poiInfoDTO.getCityName());
            routeDay.setLon(poiInfoDTO.getPoiLng());
            routeDay.setLat(poiInfoDTO.getPoiLat());
            routeDay.setDivisionId(poiInfoDTO.getDivisionId());
            routeDay.setDay("Day " + day);
            return routeDay;
        }).filter(Objects::nonNull).collect(Collectors.toList());


        for(RouteDay routeDay : routeDayList) {
            if(CollectionUtils.isEmpty(routeDay.getPoiList())){
                routeDay.setNoPoi(Boolean.TRUE);
            }
        }
        return routeDayList;
    }

    /**
     * 构建城市经纬度，通过cityName返查
     * @param poiList poiList
     * @param result routeDay
     */
    private TrdiDivisionDO buildCityNameForCityName(String day, List<String> poiList, Result result) {
        if (CollectionUtils.isEmpty(poiList)) {
            LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.buildCityNameForCityName")
                .request("第 " + day + "天没有poi"));
            return null;
        }
        if (poiList.size() != 1) {
            LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.buildCityNameForCityName")
                .request("第 " + day + "poi 数量不为1，跳过城市名称填充")
                .response(JSONUtil.toJSONString(poiList)));
            return null;
        }
        String cityName = poiList.get(0);
        if (!StringUtils.isNumeric(cityName) && StringCommonUtils.checkContainChineseChar(cityName)){
            // 中文名称，查询点位经纬度。
            List<TrdiDivisionDO> trdiDivisionDOS = trdiDivisionReadServiceClient.queryDivisionList(null, cityName,
                buildQueryLevel(queryLevel));
            LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.buildCityNameForCityName")
                .request(JSONUtil.toJSONString(poiList))
                .response(JSONUtil.toJSONString(trdiDivisionDOS)));
            if (CollectionUtils.isEmpty(trdiDivisionDOS)) {
                LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.buildCityNameForCityName")
                    .request("第 " + day + " buildCityNameForCityName  根据城市名称没有查询到城市 ")
                    .response(JSONUtil.toJSONString(poiList)));

                String addErrorMsg = result.getAddErrorMsg();
                addErrorMsg = addErrorMsg + "第" + day + "天通过cityName"+ JSONUtil.toJSONString(poiList) +" 未查到城市数据" ;
                result.setAddErrorMsg(addErrorMsg);
                return null;
            }
            return trdiDivisionDOS.get(0);
        }
        LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.buildCityNameForCityName")
            .request("第 " + day + " buildCityNameForCityName cityname 格式要求不满足： 不是数字且包含汉字")
            .response(JSONUtil.toJSONString(poiList)));
        return null;
    }

    private List<TrdiDivisionLevel> buildQueryLevel(List<Integer> queryLevel) {
        if(CollectionUtils.isEmpty(queryLevel)) {
            return Lists.newArrayList(TrdiDivisionLevel.CITY);
        }
        return queryLevel.stream().map(TrdiDivisionLevel::codeOf).filter(Objects::nonNull).collect(
            Collectors.toList());
    }

    @AteyeInvoker(description = "测试酒店查询服务", paraDesc = "jsonData")
    public List<AiPoiIconDO> queryAiPoiIconByPoiIdAndStatus(String poiIds, Short status) {
        List<Long> poiIdList = Arrays.asList(poiIds.split(",")).stream().map(Long::parseLong).collect(
            Collectors.toList());
        AiPoiIconParam param = new AiPoiIconParam();
        param.createCriteria().andPoiIdInWhenPresent(poiIdList).andStatusEqualTo(status);
        List<AiPoiIconDO> aiPoiIconDOS = aiPoiIconDAO.selectByParam(param);
        Ateye.out.println(JSON.toJSONString(aiPoiIconDOS));
        return aiPoiIconDOS;
    }

    /**
     * 预算定制卡片
     */
    @MethodCondition(field = "itemType", value = "budget_table")
    private List<StreamMessageCardModel<?>> buildBudgetTable(ComponentDataResponse response, ChatContext chatContext) {
        StreamMessageCardModel<Map<String, Object>> cardModel = new StreamMessageCardModel<>();
        try {
            // 参数校验
            tripPlanNodeProcessorHelper.checkResponseParams(response);
            // 构建基础卡片
            tripPlanNodeProcessorHelper.buildBaseCardModel(cardModel, response, BUDGET_TABLE.getCode());

            JSONObject data = response.getData();
            Integer trafficCost = MapUtils.getInteger(data, "trafficCost");
            Integer hotelCost = MapUtils.getInteger(data, "hotelCost");
            Integer poiCost = MapUtils.getInteger(data, "poiCost");
            Integer totalCost = MapUtils.getInteger(data, "totalCost");

            Map<String, Object> internalData = chatContext.getInternalData();
            Integer busCost = MapUtils.getInteger(internalData, JOURNEY_BUS_COST, 0);
            // Integer totalCost = buildTotalCost(trafficCost, hotelCost, poiCost, busCost);

            List<Map<String, String>> budgetTable = Lists.newArrayList();
            if (Objects.nonNull(trafficCost) && trafficCost > 0) {
                budgetTable.add(buildCostMap("往返交通", trafficCost, "¥", "起"));
            }

            if (Objects.nonNull(hotelCost) && hotelCost > 0) {
                budgetTable.add(buildCostMap("住宿", hotelCost, "¥", "起"));
            }

            if (Objects.nonNull(poiCost) && poiCost > 0) {
                budgetTable.add(buildCostMap("门票", poiCost, "约¥", ""));
            }

            if (Objects.nonNull(busCost) && busCost > 0) {
                budgetTable.add(buildCostMap("市内交通", busCost, "约¥", ""));
            }
            if (Objects.nonNull(totalCost) && totalCost > 0) {
                budgetTable.add(buildCostMap("总计", totalCost, "约¥", ""));
            }

            Map<String, Object> map = new HashMap<>();
            map.put("list", budgetTable);
            map.put("tips", "价格受预定时间影响，请以实际为准");

            cardModel.setData(map);
            //ConcurrentHashMap 不允许为空， 兼容planA问题
            if (Objects.nonNull(totalCost)) {
                chatContext.getInternalData().put(ContextInnerKeyCommon.JOURNEY_BUDGET_TOTAL_COST, totalCost);
            }
        } catch (Throwable e) {
            cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
            LOGGER.recordDangerException(new LogModel("JourneyPlanNodeProcessor.buildBudgetTable").e(e)
                .message("budget_table_exception"));
        } finally {
            LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.buildBudgetTable")
                .request(JSON.toJSONString(response))
                .response(JSON.toJSONString(cardModel)));
        }
        return Lists.newArrayList(cardModel);
    }

    private Map<String, String> buildCostMap(String name, Integer cost, String pre, String after) {
        Map<String, String> busCostMap = new HashMap<>();
        busCostMap.put("key", name);
        busCostMap.put("value", pre + cost + after);

        return busCostMap;
    }

    private Integer buildTotalCost(Integer trafficCost, Integer hotelCost, Integer poiCost, Integer busCost) {
        return Optional.ofNullable(trafficCost).orElse(0) +
            Optional.ofNullable(hotelCost).orElse(0) +
            Optional.ofNullable(poiCost).orElse(0) +
            Optional.ofNullable(busCost).orElse(0);
    }

    /**
     * 预算定制卡片
     */
    @MethodCondition(field = "itemType", value = "budget_card")
    private List<StreamMessageCardModel<?>> buildBudgetCard(ComponentDataResponse response, ChatContext chatContext) {
        StreamMessageCardModel<BudgetCard> cardModel = new StreamMessageCardModel<>();
        try {
            // 参数校验
            tripPlanNodeProcessorHelper.checkResponseParams(response);
            // 构建基础卡片
            tripPlanNodeProcessorHelper.buildBaseCardModel(cardModel, response, BUDGET_CARD.getCode());
            // 判断是否展示预算调整模块
            String chat = chatContext.getRequest().getChat();
            if (StringUtils.isNotBlank(chat) && chat.startsWith("预算调整为")) {
                cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
                //删除
                return Lists.newArrayList(cardModel);
            }
            JSONObject data = response.getData();
            Integer currentFee = MapUtils.getInteger(data, "currentFee");
            Integer minFee = MapUtils.getInteger(data, "minFee");
            Integer maxFee = MapUtils.getInteger(data, "maxFee");
            String fees = MapUtils.getString(data, "fees");
            String levelDes = MapUtils.getString(data, "levelDes");
            String[] levelDesArr = levelDes.split(",");

            String[] feesArr = fees.split(",");
            String[] newFeeArr = updateFees(String.valueOf(currentFee), feesArr);

            List<LevelFee> feesList = Lists.newArrayList();
            for (int i = 0; i < newFeeArr.length; i++) {
                feesList.add(LevelFee.builder().fee(Integer.parseInt(newFeeArr[i])).text(levelDesArr[i]).build());
            }
            // 把feesList按照fee从小到大排序，避免大模型给的顺序不准。
            //feesList.sort(Comparator.comparingInt(LevelFee::getFee));

            BudgetCard card = BudgetCard.builder().currentFee(currentFee).minFee(minFee).maxFee(maxFee).fees(feesList)
                .build();
            cardModel.setData(card);
        } catch (Throwable e) {
            cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
            LOGGER.recordDangerException(new LogModel("JourneyPlanNodeProcessor.buildBudgetCard").e(e)
                .message("budget_card_exception"));
        } finally {
            LOGGER.recordOutput(new LogModel("JourneyPlanNodeProcessor.buildBudgetCard")
                .request(JSON.toJSONString(response))
                .response(JSON.toJSONString(cardModel)));
        }
        return Lists.newArrayList(cardModel);
    }

    public String[] updateFees(String currentFee, String[] feesArr) {
        // 如果 feesArr 已经包含 currentFee，则直接返回原数组
        if (Arrays.asList(feesArr).contains(currentFee)) {
            return feesArr;
        }

        // 将字符串数组转换为整数数组，方便计算
        int[] feesIntArr = Arrays.stream(feesArr).mapToInt(Integer::parseInt).toArray();
        int currentFeeInt = Integer.parseInt(currentFee);

        // 找到与 currentFee 最接近的值的索引
        int closestIndex = 0;
        int minDifference = Integer.MAX_VALUE;
        for (int i = 0; i < feesIntArr.length; i++) {
            int difference = Math.abs(feesIntArr[i] - currentFeeInt);
            if (difference < minDifference) {
                minDifference = difference;
                closestIndex = i;
            }
        }

        // 替换最接近的值为 currentFee
        feesIntArr[closestIndex] = currentFeeInt;

        // 将整数数组转换回字符串数组
        return Arrays.stream(feesIntArr).mapToObj(String::valueOf).toArray(String[]::new);
    }

    @Override
    public boolean needCompleteData() {
        return true;
    }

    @Override
    public AiJourneyPlanSceneEnum supportAgentType() {
        return AiJourneyPlanSceneEnum.READY_TO_PLAN;
    }

    @Override
    public Set<String> supportComponentType() {
        return Sets.newHashSet(AiJourneyMessageComponentTypeEnum.F_CARD.getCode(),
            AiJourneyMessageComponentTypeEnum.F_CONTAINER.getCode(),
            AiJourneyMessageComponentTypeEnum.F_TIMELINE.getCode());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        SwitchManager.init(JourneyPlanNodeProcessor.class);
    }

    @MethodCondition(field = "itemType", value = "journey_title_card", desc = "形成标题卡片")
    @SuppressWarnings("unchecked")
    private List<StreamMessageResult.StreamMessageCardModel> journeyTitleCard(ComponentDataResponse response,
                                                                              ChatContext chatContext) {
        StreamMessageCardModel<TitleCollectDTO> cardModel = new StreamMessageCardModel<>();
        try {
            // 参数校验
            tripPlanNodeProcessorHelper.checkResponseParams(response);
            // 构建基础卡片
            tripPlanNodeProcessorHelper.buildBaseCardModel(cardModel, response, JOURNEY_TITLE_CARD.getCode());
            String titleName = response.getData().getString("titleName");
            // 把标题添加到上下文中
            TitleCollectDTO titleCollectDTO = new TitleCollectDTO();
            titleCollectDTO.setTitle(titleName);
            titleCollectDTO.setCollectEnable(false);
            titleCollectDTO.setCollectStatus(false);
            titleCollectDTO.setCollectData(new JourneyCollectDTO());
            cardModel.setData(titleCollectDTO);
            tripPlanNodeProcessorHelper.buildTitleAndRouteMapUrl(cardModel, chatContext);
            return Lists.newArrayList(cardModel);
        } catch (Throwable t) {
            LOGGER.recordNormalException(new LogModel("PoiNodeProcessor.journeyTitleCard").e(t)
                .message("构建标题卡片异常"));
            cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
            return Lists.newArrayList(cardModel);
        } finally {
            LOGGER.recordOutput(new LogModel("PoiNodeProcessor.journeyTitleCard")
                .request(JSON.toJSONString(response))
                .response(JSON.toJSONString(cardModel)));
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DrawingMapCard {
        /**
         * 每天的poi
         */
        private List<RouteDay> dayPoiList;

        /**
         * 路线地图
         */
        private String routeMapImg;

        /**
         * 含有poi图片
         */
        private List<RouteDay> poiImgList;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    static class BudgetCard {
        private Integer currentFee;
        private Integer minFee;
        private Integer maxFee;
        private List<LevelFee> fees;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    static class LevelFee {
        private Integer fee;
        private String text;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RouteDay {
        private String day;
        private String cityName;
        /**
         * cityId 城市id，前端用于合并点位。
         */
        private String divisionId;
        private String lon;
        private String lat;
        private String icon;
        private String iconType;
        private List<PoiInfoDTO> poiList;
        /**
         * 当天没有poi数据
         */
        private Boolean noPoi;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MapCard {
        /**
         * 地图url
         */
        private String mapUrl;
        /**
         * 总结描述
         */
        private String summaryDes;
        /**
         * 跳转url
         */
        private String jumpUrl;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    static class RouterPlanCard {
        /**
         * 路线文本
         */
        private List<String> routerText;
        /**
         * 耗时文本
         */
        private String costTimeText;

        /**
         * 花钱文本
         */
        private String costMoneyText;

        /**
         * 路线icon
         */
        private String iconUrl;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    static class LinkCard {
        /**
         * poiId
         */
        private String id;
        /**
         * 名称
         */
        private String name;
        /**
         * 类型，POI/景点
         */
        private String type;
        /**
         * 链接的地址
         */
        private String linkUrl;

        /**
         * 附加描述
         */
        private String appendDes;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    static class DailyPoiDTO {

        /**
         * 日期
         */
        private Integer day;

        /**
         * poi卡片
         */
        private List<PoiLinkCard> poiCard;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ListRank {
        /**
         * 榜单文案
         */
        private String text;
        /**
         * 榜单icon
         */
        private String icon;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PoiLinkCard {
        /**
         * poiId
         */
        private String poiId;
        /**
         * 业务类型
         */
        private String bizType;
        /**
         * 链接的地址
         */
        private String jumpUrl;
        /**
         * 名称
         */
        private String poiName;
        /**
         * 游玩时间
         */
        private String playTime;
        /**
         * 是否收藏
         */
        private Boolean isCollected;
        /**
         * 是否编辑
         */
        private Boolean edit;
        /**
         * 点击后展示文案
         */
        private String clickText;
        /**
         * 榜单
         */
        private ListRank listRank;
        /**
         * XX人去过
         */
        private String halfYearUv;
        /**
         * poiGrade
         */
        private String poiGrade;
        /**
         * 建议游玩时长
         */
        private String suggestPlayTime;
        /**
         * 标签
         */
        private List<Tag> tags;
        /**
         * 标签
         */
        private List<Map<String, Object>> keyboardTags;
        /**
         * 标签 --上线后需要把keyboardTags字段删掉
         */
        private List<Map<String, Object>> keyboardTagsV1;

        private String poiImg;

        /**
         * 是否行程规划场景
         */
        private Boolean isJourney;

        /**
         * 埋点信息
         */
        private JSONObject trackInfo;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    static class HotelLinkCard {
        /**
         * poiId
         */
        private String id;
        /**
         * 名称
         */
        private String name;
        /**
         * 类型，POI/景点
         */
        private String type;
        /**
         * 链接的地址
         */
        private String linkUrl;

        /**
         * 附加描述
         */
        private String appendDes;
    }

    @Data
    static class TrafficCard {

        // 出发站点名称
        private String depStationName;

        // 到达站点名称
        private String arrStationName;

        // 时长（单位：分钟）
        private int duration;

        // 时长文本
        private int durationText;

        // 营销交通编号（如高铁车次）
        private String marketingTransportNo;

        // 票价（单位：分）
        private int price;

        /**
         * 价格
         */
        private String priceText;

        /**
         * 跳转链接
         */
        private String jumpUrl;

        /**
         * 图标链接
         */
        private String marketingTransportIcon;

        /**
         * 运输类型（参考 TransportTypeEnum 代码，0-飞机,1-火车,2-顺风车,3-汽车,4-租车"）
         */
        private String transportType;
    }

    @Data
    static class Result {
        private boolean success;
        private String code;
        private String msg;
        private String llmOutput;
        private String query;
        private String showType;
        private String showMsg;
        //场景
        private String scene;
        //天数和城市描述
        private String cityDesc;

        /**
         * 一些累加的错误，方便查询问题
         */
        private String addErrorMsg = " ";

        public Result(boolean success, String code, String msg, String llmOutput) {
            this.success = success;
            this.code = code;
            this.msg = msg;
            this.llmOutput = llmOutput;
        }

        public Result(boolean success, String code, String msg, String llmOutput, String showType, String showMsg) {
            this.success = success;
            this.code = code;
            this.msg = msg;
            this.llmOutput = llmOutput;
            this.setShowType(showType);
            this.setShowMsg(showMsg);
        }

        public Result buildResult(boolean isSuccess, String errCode, String errMsg) {
            this.setSuccess(isSuccess);
            this.setCode(errCode);
            this.setMsg(errMsg);
            return this;
        }
    }
}
