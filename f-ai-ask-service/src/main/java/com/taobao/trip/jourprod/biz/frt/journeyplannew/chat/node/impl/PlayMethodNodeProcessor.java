package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.trippoi.domain.dto.ChildPlayMethodDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.taobao.trip.jourprod.biz.frt.MddService;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.AbstractNodeProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.MethodCondition;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.KeyboardTagDTO;
import com.taobao.trip.jourprod.biz.frt.playmethod.PlayMethodClient;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageComponentTypeEnum;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.ComponentDataResponse;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.mdd.*;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult.StreamMessageCardModel;
import com.taobao.trip.jourprod.utils.JSONUtil;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.*;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl.PoiNodeProcessor.POI_IMAGE_HEIGHT;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl.PoiNodeProcessor.POI_IMAGE_WIDTH;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiMddJourneySwitch.*;
import static com.taobao.trip.jourprod.biz.frt.llm.converter.MddFusionSearchConvert.DEST_CATEGORY;
import static com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageItemTypeEnum.IMAGE_CARD;
import static com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageItemTypeEnum.MDD_LINK_CARD;

/**
 * 目的地场景 节点处理器
 * <AUTHOR>
 */
@Component
public class PlayMethodNodeProcessor extends AbstractNodeProcessor {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(PlayMethodNodeProcessor.class);

    @Resource
    private MddService mddService;
    @Resource
    private PlayMethodClient playMethodClient;

    private static final String DEST_ID = "destId";
    private static final String MAIN_PLAY_ID = "mainPlayId";
    private static final String SUB_PLAY_ID = "subPlayInfoId";
    private static final String INDEX = "index";
    private static final String DEST_NAME = "destName";

    public static final String DEPARTURE_CITY = "departureCity";
    public static final String NAME = "name";
    public static final String TRAFFIC_DEP_NAME = "trafficDepName";
    public static final String SPM_CD = "contentcard_dest.picture";

    public static final String SPLIT = "_";

    //是国外
    private static final Integer ABROAD = 1;
    private static final String COUNTRY_NAME = "中国";



    @Override
    protected List<StreamMessageCardModel<?>> doProcess(ComponentDataResponse response, ChatContext chatContext) {
        return null;
    }

    @MethodCondition(field = "type", value = "mdd_link_card")
    private List<StreamMessageCardModel> mddPlayTitleCard(ComponentDataResponse response, ChatContext chatContext) {
        StreamMessageCardModel cardModel = null;
        try {
            Map<String, MddDestInfo> map = (Map<String, MddDestInfo>) chatContext.getInternalData().getOrDefault(DEST_CATEGORY, Collections.EMPTY_MAP);

            String destId = response.getData().getString(DEST_ID);
            String subPlayId = response.getData().getString(SUB_PLAY_ID);
            String index = response.getData().getString(INDEX);
            String destName = response.getData().getString(DEST_NAME);

            MddPlayTitleCardVO mddPlayTitleCardVO = new MddPlayTitleCardVO();
            if (StringUtils.isNotBlank(index)) {
                mddPlayTitleCardVO.setIndex(AI_MDD_EMOJI_NUMBER_MAP.get(index));
            }
            mddPlayTitleCardVO.setKeyboardTags(Lists.newArrayList(KeyboardTagDTO.builder().keys(AI_MDD_TITLE_KEYWORDS).build()));

            //兼容飞猪无数据的情况
            if (StringUtils.isBlank(destId) && StringUtils.isNotBlank(destName)) {
                mddPlayTitleCardVO.setDestName(FieldCardVO.builder().content(destName).build());
                mddPlayTitleCardVO.setClickText(destName);

                cardModel = StreamMessageCardModel.finishAndReplace(response.getId(), MDD_LINK_CARD.getCode(), mddPlayTitleCardVO);
                LOGGER.recordOutput(new LogModel("mddPlayTitleCard").request(JSON.toJSONString(response.getData())).response(JSON.toJSONString(mddPlayTitleCardVO)));
                return Lists.newArrayList(cardModel);
            }


            MddDestInfo destInfo = map.get(destId);
            if (Objects.isNull(destInfo)) {
                LOGGER.recordDangerException(new LogModel("mddPlayTitleCard_not_found").request(JSON.toJSONString(response.getData())));
                return Lists.newArrayList();
            }
            String realDestId = "";
            if (StringUtils.isNotBlank(destId) && destId.contains(SPLIT)) {
                realDestId = destId.split(SPLIT)[1];
            }

            if (Objects.nonNull(destInfo.getAbroad()) && Objects.equals(destInfo.getAbroad(), ABROAD)
                && !StringUtils.equals(destInfo.getCountryName(), destInfo.getDestName())
                && !StringUtils.equals(destInfo.getCountryName(), COUNTRY_NAME)) {
                mddPlayTitleCardVO.setCountryName(FieldCardVO.builder().content(destInfo.getCountryName()).build());
            }
            String visaPolicy = destInfo.getVisaPolicy();
            if (StringUtils.isNotBlank(visaPolicy)) {
                mddPlayTitleCardVO.setVisaPolicy(FieldCardVO.builder().content(visaPolicy).build());
            }
            int monthValue = LocalDate.now().getMonthValue();

            //热度标签
            String hotTag = "";
            String searchCount = destInfo.getSearchCount();
            String searchIncr = destInfo.getSearchIncr();
            if (StringUtils.isNotBlank(searchCount) && StringUtils.isNotBlank(searchIncr)
                && Integer.parseInt(searchCount) > AI_MDD_DEST_SEARCH_COUNT
                && Double.parseDouble(searchIncr) * 100 > AI_MDD_DEST_SEARCH_INCR) {

                int preMonthValue = LocalDate.now().plusMonths(-1).getMonthValue();
                int dayOfMonth = LocalDate.now().getDayOfMonth();
                String hotTagPre = (dayOfMonth < AI_MDD_DEST_SEARCH_DAY_LIMIT ? preMonthValue : monthValue) + "月";

                hotTag = hotTagPre + "搜索量提升" + (long) Math.floor(Double.parseDouble(searchIncr) * 100) + "%";
            }

            MddDestInfo.SubPlayInfo subPlayInfo = destInfo.getSubPlayInfoList().stream()
                .filter(subInfo -> Objects.equals(subPlayId, subInfo.getSubPlayInfoId()))
                .findFirst()
                .orElse(null);
            if (Objects.nonNull(subPlayInfo)) {
                String hotRankingNum = subPlayInfo.getHotRankingNum();
                if (StringUtils.isNotBlank(hotRankingNum) && Integer.parseInt(hotRankingNum) <= AI_MDD_DEST_SUB_PLAY_TOP) {
                    String hotRanking = subPlayInfo.getHotRanking();
                    if (StringUtils.isNotBlank(hotRanking)) {
                        hotTag = hotRanking;
                    }
                }
                String mainPlayId = subPlayInfo.getMainPlayInfo().getMainPlayId();
                mddPlayTitleCardVO.setTrackInfo(TrackInfo.builder().destId(realDestId).mainPlayId(mainPlayId).subPlayId(subPlayId).build());
            }

            //mock
            if (StringUtils.isNotBlank(AI_MDD_HOT_TAG_MOCK_DATA)) {
                hotTag = AI_MDD_HOT_TAG_MOCK_DATA;
            }

            mddPlayTitleCardVO.setDestName(FieldCardVO.builder().content(destInfo.getDestName()).build());
            mddPlayTitleCardVO.setClickText(destInfo.getDestName());

            //判断目的地是否在白名单内
            boolean inWhiteList = mddService.isInWhiteList(Long.valueOf(realDestId));
            if (BooleanUtils.isTrue(inWhiteList)) {
                try {
                    //加跳转链接
                    String name = "";
                    String nameTree = "";
                    ChildPlayMethodDTO methodDTO = playMethodClient.findById(subPlayId);
                    if (Objects.nonNull(methodDTO)) {
                        List<ChildPlayMethodDTO.VacationLabel> vacationLabelList = methodDTO.getVacationLabelList();
                        if (CollectionUtils.isNotEmpty(vacationLabelList)) {
                            ChildPlayMethodDTO.VacationLabel vacationLabel = vacationLabelList.get(0);
                            name = vacationLabel.getName();
                            nameTree = vacationLabel.getNameTree();
                        }
                    }
                    String jumpUrl = MessageFormat.format(AI_MDD_JUMP_LINK, realDestId, URLEncoder.encode(name, "UTF-8"),
                        URLEncoder.encode(nameTree, "UTF-8"), subPlayId, chatContext.getRequest().getTtid(), chatContext.getRequest().getDeviceId());
                    mddPlayTitleCardVO.getDestName().setJumpUrl(jumpUrl);
                } catch (Exception e) {
                    LOGGER.recordDangerException(new LogModel("mddPlayTitleCard_jumpUrl").e(e));
                }
            }

            if (StringUtils.isNotBlank(hotTag)) {
                mddPlayTitleCardVO.setHotTag(FieldCardVO.builder().content(hotTag).build());
            }

            cardModel = StreamMessageCardModel.finishAndReplace(response.getId(), MDD_LINK_CARD.getCode(), mddPlayTitleCardVO);
            LOGGER.recordOutput(new LogModel("mddPlayTitleCard").request(JSON.toJSONString(response.getData())).response(JSON.toJSONString(mddPlayTitleCardVO)));
            return Lists.newArrayList(cardModel);
        } catch (Exception e) {
            LOGGER.recordNormalException(new LogModel("mddPlayTitleCard_error")
                .request(JSONUtil.toJSONStringForLog(response))
                .e(e));
        } finally {
            LOGGER.recordOutput(new LogModel("mddPlayTitleCard_finally")
                .request(JSONUtil.toJSONStringForLog(response))
                .response(JSONUtil.toJSONStringForLog(cardModel)));
        }
        return Lists.newArrayList();
    }

    @MethodCondition(field = "type", value = "image_card")
    private List<StreamMessageCardModel> mddImgCard(ComponentDataResponse response, ChatContext chatContext) {
        Map<String, MddDestInfo> map = (Map<String, MddDestInfo>) chatContext.getInternalData().getOrDefault(DEST_CATEGORY, Collections.EMPTY_MAP);
        String destId = response.getData().getString(DEST_ID);
        String subPlayId = response.getData().getString(SUB_PLAY_ID);

        MddImgCardVO mddImgCardVO = new MddImgCardVO();

        MddDestInfo destInfo = map.get(destId);
        if (Objects.isNull(destInfo)) {
            return Lists.newArrayList();
        }

        String realDestId = "";
        if (StringUtils.isNotBlank(destId) && destId.contains(SPLIT)) {
            realDestId = destId.split(SPLIT)[1];
        }
        MddDestInfo.SubPlayInfo subPlayInfo = destInfo.getSubPlayInfoList().stream()
                .filter(subInfo -> Objects.equals(subPlayId, subInfo.getSubPlayInfoId()))
                .findFirst()
                .orElse(null);
        String image = destInfo.getDestImg();
        String mainPlayId = "";
        if (Objects.nonNull(subPlayInfo)) {
            image = subPlayInfo.getSubPlayInfoImg().get(0);
            mainPlayId = subPlayInfo.getMainPlayInfo().getMainPlayId();
        }
        if (StringUtils.isBlank(image)) {
            return Lists.newArrayList();
        }
        mddImgCardVO.setHeight(POI_IMAGE_HEIGHT);
        mddImgCardVO.setWidth(POI_IMAGE_WIDTH);
        mddImgCardVO.setUrl(image);
        mddImgCardVO.setTrackInfo(TrackInfo.builder().destId(realDestId).mainPlayId(mainPlayId).subPlayId(subPlayId).spmCD(SPM_CD).build());

        StreamMessageCardModel cardModel = StreamMessageCardModel.finishAndReplace(response.getId(), IMAGE_CARD.getCode(), mddImgCardVO);
        LOGGER.recordOutput(new LogModel("mddImgCard").request(JSON.toJSONString(response.getData())).response(JSON.toJSONString(mddImgCardVO)));
        return Lists.newArrayList(cardModel);
    }

    @Override
    public AiJourneyPlanSceneEnum supportAgentType() {
        return AiJourneyPlanSceneEnum.DESTINATION_RECOMMEND;
    }

    @Override
    public Set<String> supportComponentType() {
        return Sets.newHashSet(AiJourneyMessageComponentTypeEnum.F_CARD.getCode());
    }

}
