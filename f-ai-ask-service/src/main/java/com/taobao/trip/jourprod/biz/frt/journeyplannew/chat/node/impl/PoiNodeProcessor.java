package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl;

import com.ali.com.google.common.collect.Lists;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fliggypoi.client.domain.FliggyPoi;
import com.alibaba.fliggypoi.client.domain.PoiBase;
import com.alibaba.fliggypoi.client.domain.PoiDivision;
import com.alibaba.trip.trippoi.domain.enumerate.TrpoPoiSourceType;
import com.alibaba.tripc.logicengine.domain.biz.PoiToPoilist;
import com.alitrip.aisearch.model.search.poi.entity.PoiInfo;
import com.alitrip.aisearch.model.search.vacation.entity.VacationInfo;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.trip.jourprod.biz.common.PoiAndVacationFieldConvertUtil;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.AbstractNodeProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.EnableCollection;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.MethodCondition;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.CollectionBizTypeEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.Tag;
import com.taobao.trip.jourprod.common.sal.hsf.common.EnvSwitch;
import com.taobao.trip.jourprod.common.sal.hsf.poi.PoiListServiceClient;
import com.taobao.trip.jourprod.common.sal.hsf.poi.PoiReadServiceClient;
import com.taobao.trip.jourprod.core.service.gaode.AMapService;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageCardActionEnum;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageCardStatusEnum;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageComponentTypeEnum;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.PoiInfoExtend;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.ComponentDataResponse;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.ItemCardVO;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.PoiCardVO;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.mdd.MddImgCardVO;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.mdd.TrackInfo;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.util.*;

import static com.taobao.trip.jourprod.biz.common.PoiAndVacationFieldConvertUtil.*;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.constant.JourneyPlanAiAssistantConstant.*;
import static com.taobao.trip.jourprod.core.service.gaode.AMapService.DefaultShowFields;
import static com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageItemTypeEnum.*;

@Component
public class PoiNodeProcessor extends AbstractNodeProcessor implements InitializingBean {
    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(PoiNodeProcessor.class);

    @Resource
    EnvSwitch envSwitch;

    @AppSwitch(des = "poi详情页url", level = Switch.Level.p4)
    public static String POI_DETAIL_PAGE_URL_FORMATTER = "https://market.{0}.taobao.com/app/trip/rx-trip-ticket/pages/detail?_fli_newpage=1&un_flutter=true&flutter_path=%2Fpoi_detail_page&_fli_unify=false&titleBarHidden=2&disableNav=YES&poiId={1}&_projVer=4.4.21";
    @AppSwitch(des = "高德poi详情页url", level = Switch.Level.p4)
    public static String GAODE_POI_DETAIL_PAGE_URL_FORMATTER = "https://outfliggys.{0}.taobao.com/app/trip/rx-travel-qa/pages/poi-pad?disableNav=YES&titleBarHidden=2&_fli_background_transparent=true&_fli_anim_type=none&=&poiId={1}&poiType={2}";

    @AppSwitch(des = "商品详情页url", level = Switch.Level.p4)
    public static String ITEM_DETAIL_PAGE_URL_FORMATTER = "https://outfliggys.{0}.taobao.com/app/trip/rx-travel-detail/pages/index?id={1}&_da=1&titleBarHidden=2&disableNav=YES&_fli_online=true&hybrid=true&_use_stream=1&_projVer=2.8.22";

    @AppSwitch(des = "榜单icon", level = Switch.Level.p4)
    public static String POI_LIST_RANK_ICON = "https://gw.alicdn.com/tfs/TB1x.92aND1gK0jSZFKXXcJrVXa-72-72.png";

    @AppSwitch(des = "景点等级字体颜色", level = Switch.Level.p4)
    public static String POI_GRADE_TEXT_COLOR = "0xFF0F131A";

    @AppSwitch(des = "景点等级背景颜色", level = Switch.Level.p4)
    public static String POI_GRADE_BACKGROUND_COLOR = "0xFFF2F3F5";

    @AppSwitch(des = "感兴趣人数字体颜色", level = Switch.Level.p4)
    public static String POI_INTEREST_TEXT_COLOR = "0xFFFF5533";

    @AppSwitch(des = "感兴趣人数背景颜色", level = Switch.Level.p4)
    public static String POI_INTEREST_BACKGROUND_COLOR = "0xFFFFF0F0";

    @AppSwitch(des = "景点等级允许透出的等级", level = Switch.Level.p4)
    public static List<String> POI_GRADE_ENABLED_GRADE = Lists.newArrayList("4A", "5A");

    @AppSwitch(des = "poi图片宽度", level = Switch.Level.p4)
    public static String POI_IMAGE_WIDTH = "321";

    @AppSwitch(des = "poi图片高度", level = Switch.Level.p4)
    public static String POI_IMAGE_HEIGHT = "150";

    @AppSwitch(des = "业务标颜色映射", level = Switch.Level.p4)
    public static Map<String, String> BUSINESS_TAG_COLOR_MAP = Maps.newHashMap();
    static {
        BUSINESS_TAG_COLOR_MAP.put("可订今日", "#6666FF");
        BUSINESS_TAG_COLOR_MAP.put("可订明日", "#6666FF");
        BUSINESS_TAG_COLOR_MAP.put("随时全额退", "#6666FF");
        BUSINESS_TAG_COLOR_MAP.put("无需取票", "#919499");
        BUSINESS_TAG_COLOR_MAP.put("纯玩无购物", "#919499");
        BUSINESS_TAG_COLOR_MAP.put("无自费", "#919499");

    }

    @AppSwitch(des = "店铺荣誉允许透出的图标", level = Switch.Level.p4)
    public static List<String> SHOP_HONOR_ENABLE_ICON = Lists.newArrayList();
    static {
        SHOP_HONOR_ENABLE_ICON.add("https://gw.alicdn.com/imgextra/i4/O1CN01TzoOWa1RXSuDdRpTJ_!!6000000002121-2-tps-160-56.png");
    }

    @AppSwitch(des = "不展示的图片url", level = Switch.Level.p4)
    public static List<String> NOT_SHOW_PIC_URLS = Lists.newArrayList();
    static {
        NOT_SHOW_PIC_URLS.add("https://gw.alicdn.com/tfs/TB19n9kjoY1gK0jSZFCXXcwqXXa-750-468.png");
        NOT_SHOW_PIC_URLS.add("https://gw.alicdn.com/tfs/TB1GmygjkP2gK0jSZPxXXacQpXa-750-468.png");
    }

    @AppSwitch(des = "不展示的图片poi", level = Switch.Level.p4)
    public static List<String> NOT_SHOW_PIC_POIS = Lists.newArrayList();
    static {
        NOT_SHOW_PIC_POIS.add("97");
        NOT_SHOW_PIC_POIS.add("110");
        NOT_SHOW_PIC_POIS.add("31138351");
        NOT_SHOW_PIC_POIS.add("16673979");
        NOT_SHOW_PIC_POIS.add("36655237");
        NOT_SHOW_PIC_POIS.add("36655237");
        NOT_SHOW_PIC_POIS.add("210149001");
        NOT_SHOW_PIC_POIS.add("184499009");
        NOT_SHOW_PIC_POIS.add("14944005");
        NOT_SHOW_PIC_POIS.add("17168676");
        NOT_SHOW_PIC_POIS.add("146836027");
    }

    @Resource
    PoiReadServiceClient poiReadServiceClient;

    @Resource
    AMapService aMapService;

    @Resource
    PoiListServiceClient poiListServiceClient;

    @Override
    protected List<StreamMessageResult.StreamMessageCardModel<?>> doProcess(ComponentDataResponse response, ChatContext chatContext) {
        StreamMessageResult.StreamMessageCardModel<Map<String, Object>> cardModel = new StreamMessageResult.StreamMessageCardModel<>();
        try {
            // 卡片数据
            cardModel.setId(response.getId());
            if (Objects.equals(response.getComponentType(), AiJourneyMessageComponentTypeEnum.F_CONTAINER.getCode())) {
                cardModel.setItemType("round_corner_card");
            } else {
                cardModel.setItemType("other_card");
            }
            cardModel.setStatus(AiJourneyMessageCardStatusEnum.FINISH.getCode());
            cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());

            // 业务数据
            Map<String, Object> data = response.getData();
            cardModel.setData(data);
            return Lists.newArrayList(cardModel);
        } catch (Throwable e) {
            LOGGER.recordNormalException(new LogModel("PoiNodeProcessor.doProcess").e(e).message("查询poi卡片错误"));
        } finally {
            LOGGER.recordOutput(new LogModel("PoiNodeProcessor.doProcess")
                    .message("response-{0}, chatContext-{0}", JSON.toJSONString(response), JSON.toJSONString(chatContext))
                    .response(JSON.toJSONString(cardModel)));
        }
        return null;
    }
    @MethodCondition(field = "itemType", value = "poi_link_card", desc = "POI链接卡")
    @EnableCollection(bizType = CollectionBizTypeEnum.DYNAMIC, dynamicBizTypeStr = "bizType", bizIdColumn = "poiId", titleColumn = "poiName", picUrlColumn = "poiImg")
    @SuppressWarnings("unchecked")
    private List<StreamMessageResult.StreamMessageCardModel> poiLinkCardRecommend(ComponentDataResponse response, ChatContext chatContext) {
        StreamMessageResult.StreamMessageCardModel cardModel = new StreamMessageResult.StreamMessageCardModel<>();
        cardModel.setId(response.getId());
        cardModel.setStatus(AiJourneyMessageCardStatusEnum.FINISH.getCode());
        cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
        try {
            Map<String, PoiInfo> poiInfoMap = (Map<String, PoiInfo>) chatContext.getInternalData().getOrDefault("poi", Collections.EMPTY_MAP);
            Map<String, PoiInfoExtend> amapPoiInfoMap = (Map<String, PoiInfoExtend>) chatContext.getInternalData().getOrDefault("amap", Collections.EMPTY_MAP);
            String poiId = response.getData().getString("poiId");
            // 高德点位支持
            String poiType = getPoiType(poiId);
            JourneyPlanNodeProcessor.PoiLinkCard card;
            PoiInfoExtend renderPoiInfo = new PoiInfoExtend();
            if ("gaode".equals(poiType)) {
                // 先查平台or高德接口
                PoiInfoExtend poiInfo = getPoiByPoiId(poiId, poiType);
                // 查询到平台poi且挂货，展示平台poi数据
                if (poiInfo != null && "poi".equals(poiInfo.getSource()) && poiInfo.isHasPoiShelf()) {
                    renderPoiInfo = poiInfo;
                } else if (poiInfo != null && "gaode".equals(poiInfo.getSource())) {
                    // 已查询到高德数据
                    renderPoiInfo = poiInfo;
                } else {
                    // 结果为空或平台poi未挂货，上下文可以找到poi
                    if (poiInfo == null && amapPoiInfoMap.containsKey(poiId)) {
                        renderPoiInfo = amapPoiInfoMap.get(poiId);
                    } else {
                        // 上下文中未查询到高德数据或未挂货的平台poi
                        renderPoiInfo = getPoiByGaodePoiId(poiId);
                    }
                }
                // 高德接口、上下文均无数据
                if (renderPoiInfo == null) {
                    return Lists.newArrayList(cardModel);
                }
            } else {
                PoiInfo poiInfo = poiInfoMap.get(poiId);
                PoiInfoExtend platformPoiInfo = getPoiByPoiId(poiId, poiType);
                if (poiInfo == null && platformPoiInfo == null) {
                    return Lists.newArrayList(cardModel);
                }
                renderPoiInfo = mergePoiInfo(poiInfo, platformPoiInfo);
            }
            // poi类型
            String source = renderPoiInfo.getSource();
            if ("gmap".equals(source)) {
                poiType = GOOGLE_TYPE;
            } else if ("amap".equals(source)) {
                poiType = GAO_DE_TYPE;
            } else {
                poiType = POI_TYPE;
            }
            card = JourneyPlanNodeProcessor.PoiLinkCard.builder()
                    .poiId(renderPoiInfo.getPoiId())
                    .halfYearUv(buildFuzzyUv(renderPoiInfo.getHalfYearUv()))
                    .poiGrade(renderPoiInfo.getPoiGrade())
                    .poiName(renderPoiInfo.getPoiName())
                    // 兜底使用浮层链接
                    .jumpUrl(buildJumpUrl(GAODE_POI_DETAIL_PAGE_URL_FORMATTER, poiId, poiType))
                    .listRank(buildListRank(renderPoiInfo.getPoiId()))
                    .edit(true)
                    .clickText(renderPoiInfo.getPoiName())
                    .tags(buildTags(renderPoiInfo))
                    .trackInfo(buildTrackInfo(poiId, poiType))
                    .build();
            // 是否可收藏，除平台点位之外不出收藏
            if (poiType.equals(POI_TYPE)) {
                card.setBizType(CollectionBizTypeEnum.POI.getBizType());
            }
            // 是否挂货判断,平台poi挂货直接跳poi详情页
            if (poiType.equals(POI_TYPE) && renderPoiInfo.isHasPoiShelf()) {
                card.setJumpUrl(buildJumpUrl(POI_DETAIL_PAGE_URL_FORMATTER, renderPoiInfo.getPoiId()));
            }
            cardModel = StreamMessageResult.StreamMessageCardModel.finishAndReplace(response.getId(), POI_LINK_CARD.getCode(), card);
            cardModel.setData(card);
            return Lists.newArrayList(cardModel);
        } catch (Throwable t) {
            LOGGER.recordNormalException(new LogModel("poiLinkCardRecommend").e(t).message("构建poi链接卡异常"));
        } finally {
            LOGGER.recordOutput(new LogModel("poiLinkCardRecommend")
                    .message("response-{0}, chatContext-{0}", JSON.toJSONString(response), JSON.toJSONString(chatContext))
                    .response(JSON.toJSONString(cardModel)));
        }
        return Lists.newArrayList(cardModel);
    }

    public JourneyPlanNodeProcessor.ListRank buildListRank(String poiIdStr) {
        if (!StringUtils.isNumeric(poiIdStr)) {
            return null;
        }
        Long poiId = Long.valueOf(poiIdStr);
        PoiToPoilist listRank = poiListServiceClient.getFirstPoiList(poiId);
        if (listRank == null || listRank.getPoilistName() == null || listRank.getRank() == null) {
            return null;
        }
        String poiListName = listRank.getPoilistName();
        String rank = listRank.getRank().toString();
        return JourneyPlanNodeProcessor.ListRank.builder().text(poiListName + "第" + rank + "名").icon(POI_LIST_RANK_ICON).build();
    }

    /**
     * 合并poi信息，货架信息取自平台poi，其他信息优先取上下文poi
     * @param contextPoi 上下文poi
     * @param platformPoi 平台poi
     * @return 合并后的poi信息
     */
    private PoiInfoExtend mergePoiInfo(PoiInfo contextPoi, PoiInfoExtend platformPoi) {
        PoiInfoExtend poiInfoExtend = new PoiInfoExtend();
        if (contextPoi == null) {
            BeanUtils.copyProperties(platformPoi, poiInfoExtend);
            return poiInfoExtend;
        }
        if (platformPoi == null) {
            BeanUtils.copyProperties(contextPoi, poiInfoExtend);
            return poiInfoExtend;
        }
        BeanUtils.copyProperties(contextPoi, poiInfoExtend);
        poiInfoExtend.setHasPoiShelf(platformPoi.isHasPoiShelf());
        poiInfoExtend.setSource("poi");
        return poiInfoExtend;
    }

    public PoiInfoExtend getPoiByPoiId(String poiIdStr, String poiType) {
        Long poiId;
        if ("gaode".equals(poiType)) {
            poiId =poiReadServiceClient.getPoiIdBySourceId(TrpoPoiSourceType.GAODE_MAP, poiIdStr);
            // 没有平台id，直接查高德接口
            if (poiId == null) {
                LOGGER.recordOutput(new LogModel("getPoiByPoiId"));
                return getPoiByGaodePoiId(poiIdStr);
            }
        } else {
            poiId = Long.valueOf(poiIdStr);
        }
        FliggyPoi fliggyPoi = poiReadServiceClient.getFliggyPoiByPoiId(poiId);
        if (fliggyPoi != null) {
            return convertFliggyPoi2PoiInfo(fliggyPoi);
        }
        return null;
    }

    public PoiInfoExtend getPoiByGaodePoiId(String poiId) {
        List<Map<String, Object>> maps = aMapService.queryGaoDeSearchByIdWithVersion(poiId, true, DefaultShowFields);
        if (CollectionUtils.isNotEmpty(maps)) {
            return convertAmapPoi2PoiInfo(maps.get(0));
        }
        return null;
    }

    private PoiInfoExtend convertFliggyPoi2PoiInfo(FliggyPoi fliggyPoi) {
        if (fliggyPoi == null || fliggyPoi.getPoiBase() == null) {
            return null;
        }
        PoiInfoExtend poiInfo = new PoiInfoExtend();
        PoiBase poiBase = fliggyPoi.getPoiBase();
        poiInfo.setPoiId(String.valueOf(poiBase.getPoiId()));
        poiInfo.setPoiName(poiBase.getName());
        poiInfo.setPoiImg(poiBase.getMainPic());
        poiInfo.setAddress(poiBase.getAddress());
        poiInfo.setPoiLat(String.valueOf(poiBase.getLatitude()));
        poiInfo.setPoiLng(String.valueOf(poiBase.getLongitude()));
        poiInfo.setSource("poi");
        if (fliggyPoi.getPoiDivision() != null) {
            PoiDivision poiDivision = fliggyPoi.getPoiDivision();
            poiInfo.setPoiDestTreeId(poiDivision.getTreeId());
            poiInfo.setPoiDestTreeName(poiDivision.getTreeName());
        }
        if (fliggyPoi.getPoiFluctuantProperties() != null) {
            poiInfo.setHasPoiShelf(BooleanUtils.isTrue(fliggyPoi.getPoiFluctuantProperties().getHasPoiShelf()));
        }
        return poiInfo;
    }

    private PoiInfoExtend convertAmapPoi2PoiInfo(Map<String, Object> amapPoi) {
        if (amapPoi == null) {
            return null;
        }
        PoiInfoExtend poiInfo = new PoiInfoExtend();
        poiInfo.setSource("amap");
        poiInfo.setPoiId(String.valueOf(amapPoi.get("id")));
        poiInfo.setPoiName(String.valueOf(amapPoi.get("name")));
        poiInfo.setAddress(String.valueOf(amapPoi.get("address")));
        Object location = amapPoi.get("location");
        if (location != null) {
            String[] split = StringUtils.split(location.toString(), ",");
            if (split != null && split.length == 2 && NumberUtils.isCreatable(split[0]) && NumberUtils.isCreatable(split[1])) {
                poiInfo.setPoiLng(split[0]);
                poiInfo.setPoiLat(split[1]);
            }
        }
        List<String> photoList = buildAmapPoiPhotos(amapPoi.get("photos"));
        if (CollectionUtils.isNotEmpty(photoList)) {
            poiInfo.setPhotos(photoList);
            poiInfo.setPoiImg(photoList.get(0));
        }
        return poiInfo;
    }

    private List<String> buildAmapPoiPhotos(Object photosObj) {
        if (photosObj == null) {
            return com.google.common.collect.Lists.newArrayList();
        }
        List<String> res = com.google.common.collect.Lists.newArrayList();
        try {
            JSONArray photos = JSONArray.parseArray(JSON.toJSONString(photosObj));
            for (int i = 0; i < photos.size(); i++) {
                res.add(photos.getJSONObject(i).getString("url"));
            }
        } catch (Exception ignore) {
        }
        return res;
    }

    private String getPoiType(String poiId) {
        if (StringUtils.isNumeric(poiId)) {
            return "poi";
        }
        return "gaode";
    }

    private List<Tag> buildTags(PoiInfo poiInfo) {
        if (poiInfo == null) {
            return null;
        }
        // xx人感兴趣
        String text = buildFuzzyUv(poiInfo.getFullYearUv());
        ArrayList<Tag> res = Lists.newArrayList();
        if (StringUtils.isNotBlank(text)) {
            res.add(Tag.builder().text(text).textColor(POI_INTEREST_TEXT_COLOR).backgroundColor(POI_INTEREST_BACKGROUND_COLOR).build());
        }
        // 景点等级
        if (StringUtils.isNotBlank(poiInfo.getPoiGrade()) && POI_GRADE_ENABLED_GRADE.contains(poiInfo.getPoiGrade())) {
            res.add(Tag.builder().text(poiInfo.getPoiGrade()).backgroundColor(POI_GRADE_BACKGROUND_COLOR).textColor(POI_GRADE_TEXT_COLOR).build());
        }
        return res;
    }

    private JSONObject buildTrackInfo(String poiId, String poiType) {
        JSONObject trackInfo = new JSONObject();
        if ("gaode".equals(poiType)) {
            trackInfo.put("gaodeId", poiId);
        } else {
            trackInfo.put("poiId", poiId);
        }
        return trackInfo;
    }



    @MethodCondition(field = "itemType", value = "image_card", desc = "poi图片卡")
    @SuppressWarnings("unchecked")
    private List<StreamMessageResult.StreamMessageCardModel> poiImageCardRecommend(ComponentDataResponse response, ChatContext chatContext) {
        StreamMessageResult.StreamMessageCardModel cardModel = new StreamMessageResult.StreamMessageCardModel<>();
        cardModel.setStatus(AiJourneyMessageCardStatusEnum.FINISH.getCode());
        cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
        cardModel.setId(response.getId());
        try {
            Map<String, PoiInfo> poiInfoMap = (Map<String, PoiInfo>) chatContext.getInternalData().getOrDefault("poi", Collections.EMPTY_MAP);
            Map<String, PoiInfoExtend> amapPoiInfoMap = (Map<String, PoiInfoExtend>) chatContext.getInternalData().getOrDefault("amap", Collections.EMPTY_MAP);
            String poiId = response.getData().getString("poiId");
            String poiType = getPoiType(poiId);
            PoiInfo poiInfo;
            if ("gaode".equals(poiType)) {
                poiInfo = amapPoiInfoMap.get(poiId);
            } else {
                poiInfo = poiInfoMap.get(poiId);
            }
            if (NOT_SHOW_PIC_POIS.contains(poiId)) {
                return Lists.newArrayList(cardModel);
            }
            if (poiInfo == null) {
                // 兜底
                PoiInfoExtend poiByPoiId = getPoiByPoiId(poiId, getPoiType(poiId));
                if (poiByPoiId == null) {
                    return Lists.newArrayList(cardModel);
                }
                poiInfo = poiByPoiId;
            }
            if (StringUtils.isBlank(poiInfo.getPoiImg()) || NOT_SHOW_PIC_URLS.contains(poiInfo.getPoiImg())) {
                return Lists.newArrayList(cardModel);
            }
            MddImgCardVO mddTrafficCardVO = new MddImgCardVO();
            mddTrafficCardVO.setUrl(poiInfo.getPoiImg());
            mddTrafficCardVO.setHeight(POI_IMAGE_HEIGHT);
            mddTrafficCardVO.setWidth(POI_IMAGE_WIDTH);
            TrackInfo trackInfo = new TrackInfo();
            trackInfo.setPoiId(poiId);
            trackInfo.setSpmCD("contentcard_poi.picture");
            mddTrafficCardVO.setTrackInfo(trackInfo);
            cardModel = StreamMessageResult.StreamMessageCardModel.finishAndReplace(response.getId(), IMAGE_CARD.getCode(), mddTrafficCardVO);
            cardModel.setData(mddTrafficCardVO);
            return Lists.newArrayList(cardModel);
        } catch (Throwable t) {
            LOGGER.recordNormalException(new LogModel("poiImageCardRecommend").e(t).message("构建poi图片卡异常"));
        } finally {
            LOGGER.recordOutput(new LogModel("poiImageCardRecommend")
                    .message("response-{0}, chatContext-{0}", JSON.toJSONString(response), JSON.toJSONString(chatContext))
                    .response(JSON.toJSONString(cardModel)));
        }
        return Lists.newArrayList(cardModel);
    }

    @MethodCondition(field = "itemType", value = "vacation_poi_card", desc = "poi卡")
    @EnableCollection(bizType = CollectionBizTypeEnum.POI, bizIdColumn = "poiId", titleColumn = "name", picUrlColumn = "mainPic")
    @SuppressWarnings("unchecked")
    private List<StreamMessageResult.StreamMessageCardModel> poiCardRecommend(ComponentDataResponse response, ChatContext chatContext) {
        StreamMessageResult.StreamMessageCardModel cardModel = new StreamMessageResult.StreamMessageCardModel<>();
        cardModel.setStatus(AiJourneyMessageCardStatusEnum.FINISH.getCode());
        cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
        cardModel.setId(response.getId());
        try {
            Map<String, PoiInfo> poiInfoMap = (Map<String, PoiInfo>) chatContext.getInternalData().getOrDefault("poi", Collections.EMPTY_MAP);
            String poiId = response.getData().getString("poiId");
            PoiInfo poiInfo = poiInfoMap.get(poiId);
            if (poiInfo == null) {
                return Lists.newArrayList(cardModel);
            }
            // 无价格不透出卡片
            if (poiInfo.getLowestPrice() == null) {
                LOGGER.recordNormalFailedRpc(new LogModel("poiCardRecommend").message("无价格不透出卡片").request(JSON.toJSONString(poiInfo)));
                return Lists.newArrayList(cardModel);
            }
            PoiCardVO poiCardVO = new PoiCardVO();
            poiCardVO.setPoiId(poiId);
            poiCardVO.setName(poiInfo.getPoiName());
            poiCardVO.setJumpUrl(buildJumpUrl(POI_DETAIL_PAGE_URL_FORMATTER, poiId));
            poiCardVO.setMainPic(poiInfo.getPoiImg());

            poiCardVO.setPrice(poiInfo.getLowestPrice());
            poiCardVO.setSold(buildFuzzySold(poiInfo.getYearSold()));
            poiCardVO.setTags(PoiAndVacationFieldConvertUtil.buildTags(poiInfo.getBusinessTags()));
            poiCardVO.setPriceInfo(poiInfo.getPriceInfo());
            if (StringUtils.isNotBlank(poiInfo.getPriceCateType())) {
                JSONObject jsonObject = JSON.parseObject(poiInfo.getPriceCateType());
                poiCardVO.setPriceCateType(jsonObject.getString("text"));
            }
            JSONObject trackInfo = new JSONObject();
            trackInfo.put("poi_id", poiId);
            poiCardVO.setTrackInfo(trackInfo);
            cardModel = StreamMessageResult.StreamMessageCardModel.finishAndReplace(response.getId(), VACATION_POI_CARD.getCode(), poiCardVO);
            cardModel.setData(poiCardVO);
            return Lists.newArrayList(cardModel);
        } catch (Throwable t) {
            LOGGER.recordNormalException(new LogModel("poiCardRecommend").e(t).message("构建poi卡异常"));
        } finally {
            LOGGER.recordOutput(new LogModel("poiCardRecommend")
                    .message("response-{0}, chatContext-{0}", JSON.toJSONString(response), JSON.toJSONString(chatContext))
                    .response(JSON.toJSONString(cardModel)));
        }
        return Lists.newArrayList(cardModel);
    }

    /**
     * 将给定的价格（单位为分）转换为元，并移除不必要的尾随零。
     *
     * @param priceCent 包含原始价格的对象
     * @return 转换后的价格字符串
     */
    public static String convertToYuan(String priceCent) {
        try {
            BigDecimal priceInCents = new BigDecimal(priceCent);
            BigDecimal priceInYuan = priceInCents.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);

            DecimalFormat df = new DecimalFormat("#.##"); // 移除不必要的尾随零
            return df.format(priceInYuan);
        } catch (Exception e) {
            return priceCent;
        }
    }

    @MethodCondition(field = "itemType", value = "vacation_poi_item_card", desc = "商品卡")
    @EnableCollection(bizType = CollectionBizTypeEnum.ITEM, bizIdColumn = "itemId", titleColumn = "name", picUrlColumn = "mainPic")
    @SuppressWarnings("unchecked")
    private List<StreamMessageResult.StreamMessageCardModel> itemCardRecommend(ComponentDataResponse response, ChatContext chatContext) {
        StreamMessageResult.StreamMessageCardModel cardModel = new StreamMessageResult.StreamMessageCardModel<>();
        cardModel.setStatus(AiJourneyMessageCardStatusEnum.FINISH.getCode());
        cardModel.setAction(AiJourneyMessageCardActionEnum.DELETE.getCode());
        cardModel.setId(response.getId());
        try {
            Map<String, VacationInfo> vacationInfoMap = (Map<String, VacationInfo>) chatContext.getInternalData().getOrDefault("vacation", Collections.EMPTY_MAP);
            String itemId = response.getData().getString("vacationId");
            VacationInfo vacationInfo = vacationInfoMap.get(itemId);
            if (vacationInfo == null) {
                return Lists.newArrayList(cardModel);
            }
            ItemCardVO itemCardVO = new ItemCardVO();
            itemCardVO.setItemId(vacationInfo.getVacationId());
            itemCardVO.setName(vacationInfo.getVacationName());
            itemCardVO.setMainPic(vacationInfo.getVacationImg());
            itemCardVO.setPrice(vacationInfo.getLowestPrice());
            itemCardVO.setSold(buildFuzzySold(vacationInfo.getYearSold()));
            itemCardVO.setJumpUrl(buildJumpUrl(ITEM_DETAIL_PAGE_URL_FORMATTER, vacationInfo.getVacationId()));
            itemCardVO.setTags(PoiAndVacationFieldConvertUtil.buildTags(vacationInfo.getBusinessTags()));
            itemCardVO.setShopInfo(buildShopInfo(vacationInfo.getShopInfo()));
            itemCardVO.setShopHonorInfo(buildShopHonorInfo(vacationInfo.getShopHonorInfo()));
            JSONObject trackInfo = new JSONObject();
            trackInfo.put("item_id", itemId);
            itemCardVO.setTrackInfo(trackInfo);
            itemCardVO.setPriceInfo(vacationInfo.getPriceInfo());
            cardModel = StreamMessageResult.StreamMessageCardModel.finishAndReplace(response.getId(), VACATION_POI_ITEM_CARD.getCode(), itemCardVO);
            cardModel.setData(itemCardVO);
            return Lists.newArrayList(cardModel);
        } catch (Throwable t) {
            LOGGER.recordNormalException(new LogModel("itemCardRecommend").e(t).message("构建商品卡异常"));
        } finally {
            LOGGER.recordOutput(new LogModel("itemCardRecommend")
                    .message("response-{0}, chatContext-{0}", JSON.toJSONString(response), JSON.toJSONString(chatContext))
                    .response(JSON.toJSONString(cardModel)));
        }
        return Lists.newArrayList(cardModel);
    }

    @Override
    protected AiJourneyPlanSceneEnum supportAgentType() {
        return AiJourneyPlanSceneEnum.SCENIC_RECOMMEND;
    }

    @Override
    protected Set<String> supportComponentType() {
        return Sets.newHashSet(AiJourneyMessageComponentTypeEnum.F_CARD.getCode(), AiJourneyMessageComponentTypeEnum.F_CONTAINER.getCode());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        SwitchManager.init(PoiNodeProcessor.class);
    }

    public String buildJumpUrl(String urlFormatter, String id) {
        String envFormat = "m";
        if ("pre".equals(envSwitch.getEnv())) {
            envFormat = "wapa";
        }
        return MessageFormat.format(urlFormatter, envFormat, id);
    }

    public String buildJumpUrl(String urlFormatter, String id, String type) {
        String envFormat = "m";
        if ("pre".equals(envSwitch.getEnv())) {
            envFormat = "wapa";
        }
        return MessageFormat.format(urlFormatter, envFormat, id, type);
    }
}
