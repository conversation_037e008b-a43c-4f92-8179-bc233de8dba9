package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjsonfordrm.JSON;
import com.alitrip.aisearch.model.search.traffic.entity.TrafficSolutionItem;
import com.fliggy.flyway.multitransport.model.res.ai.AiNoticeInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.AbstractNodeProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.EnableCollection;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.MethodCondition;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.CollectionBizTypeEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantMessageHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiTrafficCardVO;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.jourprod.common.sal.hsf.tair.MdbTairHelper;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageCardActionEnum;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageCardStatusEnum;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageComponentTypeEnum;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageItemTypeEnum;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.ComponentDataResponse;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2025/3/21 15:47
 * @desc
 */
@Component
public class TrafficNodeProcessor extends AbstractNodeProcessor {

    @Resource
    private MdbTairHelper mdbTairHelper;
    @Resource
    private JourneyPlanAiAssistantMessageHelper journeyPlanAiAssistantMessageHelper;

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(TrafficNodeProcessor.class);


    @Override
    protected List<StreamMessageResult.StreamMessageCardModel<?>> doProcess(ComponentDataResponse response, ChatContext chatContext) {
        return Lists.newArrayList();
    }

    @Override
    protected AiJourneyPlanSceneEnum supportAgentType() {
        return AiJourneyPlanSceneEnum.TRAFFIC;
    }

    @Override
    protected Set<String> supportComponentType() {
        return Sets.newHashSet(AiJourneyMessageComponentTypeEnum.F_CARD.getCode(), AiJourneyMessageComponentTypeEnum.F_CONTAINER.getCode());
    }


    @MethodCondition(field = "type", value = "traffic_card")
    @EnableCollection(bizType = CollectionBizTypeEnum.DYNAMIC, bizIdColumn = "collectData.bizId", dynamicBizTypeStr = "collectData.bizType", jumpUrlColumn = "collectData.jumpUrl", trainBizColumn = "collectData.trainBiz")
    public List<StreamMessageResult.StreamMessageCardModel<?>> buildTrafficCard(ComponentDataResponse response, ChatContext chatContext) {
        return buildCard(response, chatContext, null);
    }


    @MethodCondition(field = "type", value = "cheap_flight_card")
    @EnableCollection(bizType = CollectionBizTypeEnum.DYNAMIC, bizIdColumn = "collectData.bizId", dynamicBizTypeStr = "collectData.bizType", jumpUrlColumn = "collectData.jumpUrl", trainBizColumn = "collectData.trainBiz")
    public List<StreamMessageResult.StreamMessageCardModel<?>> buildCheapFlightTrafficCard(ComponentDataResponse response, ChatContext chatContext) {
        return buildCard(response, chatContext, AiJourneyMessageItemTypeEnum.CHEAP_FLIGHT_CARD.getCode());
    }


    /**
     * 构建交通卡片模型的方法。
     *
     * @param response    包含组件数据响应的对象
     * @param chatContext 聊天上下文对象
     * @param itemType    卡片类型标识符
     * @return 返回一个包含构建好的卡片模型的列表
     */
    private List<StreamMessageResult.StreamMessageCardModel<?>> buildCard(ComponentDataResponse response, ChatContext chatContext, String itemType) {
        // 初始化卡片模型
        StreamMessageResult.StreamMessageCardModel<AiTrafficCardVO> cardModel = initializeCardModel(response);

        try {
            // 创建一个新的交通卡片视图对象
            AiTrafficCardVO aiTrafficCardVO = new AiTrafficCardVO();

            // 提取业务ID
            String bizId = extractBizId(response);

            // 获取内部数据映射
            Map<String, Object> internalData = chatContext.getInternalData();

            // 根据内部数据和业务ID获取交通解决方案项
            TrafficSolutionItem trafficGroupItem = getTrafficGroupItem(internalData, bizId);
            if (trafficGroupItem == null) {
                // 如果没有找到交通解决方案项，直接返回初始化的卡片模型
                return Lists.newArrayList(cardModel);
            }
            //特殊处理提示信息
            this.specialProcessNotice(trafficGroupItem);

            // 设置交通解决方案项到卡片视图对象中
            aiTrafficCardVO.setTrafficGroupItem(trafficGroupItem);
            cardModel.setData(aiTrafficCardVO);

            // 判断是否需要设置卡片类型
            if (StringUtils.isEmpty(itemType)) {
                // 获取旅程信息列表，并根据这些信息设置卡片类型
                cardModel.setItemType(aiTrafficCardVO.getType());
            } else {
                // 直接使用传入的卡片类型
                cardModel.setItemType(itemType);
            }

        } catch (Throwable e) {
            // 记录异常日志
            LOGGER.recordNormalException(new LogModel("buildCard").e(e).message("处理卡片信息出错"));
        } finally {
            // 记录输出日志
            LOGGER.recordOutput(new LogModel("buildCard")
                    .request(JSON.toJSONString(response))
                    .response(JSON.toJSONString(cardModel)));
        }

        // 返回包含构建好的卡片模型的列表
        return Lists.newArrayList(cardModel);
    }


    /**
     * 获取交通方案信息
     *
     * @param internalData
     * @param bizId
     * @return
     */
    public TrafficSolutionItem getTrafficGroupItem(Map<String, Object> internalData, String bizId) {
        Map<String, TrafficSolutionItem> trafficGroupItemMap = null;
        TrafficSolutionItem trafficSolutionItem = null;
        try {
            trafficGroupItemMap = (Map<String, TrafficSolutionItem>) internalData.get("traffic");
            if (trafficGroupItemMap == null) {
                return null;
            }
            trafficSolutionItem = trafficGroupItemMap.get(bizId);
            if (Objects.nonNull(trafficSolutionItem)) {
                return trafficSolutionItem;
            }
            Object value = mdbTairHelper.getValue(journeyPlanAiAssistantMessageHelper.genAiStrategyTrafficKey(bizId));
            if (Objects.nonNull(value)) {
                trafficSolutionItem = (TrafficSolutionItem) value;
            }
            return trafficSolutionItem;
        } catch (Throwable e) {
            LOGGER.recordNormalException(new LogModel("TrafficNodeProcessor.getTrafficGroupItem").e(e).message("getTrafficGroupItem_exception"));
        } finally {
            LOGGER.recordOutput(new LogModel("TrafficNodeProcessor.getTrafficGroupItem")
                    .request(bizId)
                    .message("traffic_context_data - {0}", JSONUtil.toJSONStringForLog(trafficGroupItemMap))
                    .response(JSON.toJSONString(trafficSolutionItem)));
        }
        return null;
    }


    /**
     * 获取多个交通卡视图对象
     *
     * @param bizIds
     * @param chatContext
     * @return
     */
    public List<TrafficSolutionItem> getTrafficSolution(List<String> bizIds, ChatContext chatContext) {
        List<TrafficSolutionItem> trafficSolutionList = Lists.newArrayList();
        try {
            Map<String, Object> internalData = chatContext.getInternalData();
            for (String bizId : bizIds) {
                TrafficSolutionItem trafficGroupItem = getTrafficGroupItem(internalData, bizId);
                trafficSolutionList.add(trafficGroupItem);
            }
            return trafficSolutionList;
        } catch (Throwable e) {
            LOGGER.recordNormalException(new LogModel("TrafficNodeProcessor.getTrafficSolution").e(e).message("getTrafficSolution_exception"));
        } finally {
            LOGGER.recordOutput(new LogModel("TrafficNodeProcessor.getTrafficSolution")
                    .request(JSONUtil.toJSONStringForLog(bizIds))
                    .response(JSONUtil.toJSONStringForLog(trafficSolutionList)));
        }
        return trafficSolutionList;
    }


    private StreamMessageResult.StreamMessageCardModel<AiTrafficCardVO> initializeCardModel(ComponentDataResponse response) {
        StreamMessageResult.StreamMessageCardModel<AiTrafficCardVO> cardModel = new StreamMessageResult.StreamMessageCardModel<>();
        cardModel.setId(response.getId());
        cardModel.setStatus(AiJourneyMessageCardStatusEnum.FINISH.getCode());
        cardModel.setAction(AiJourneyMessageCardActionEnum.REPLACE.getCode());
        return cardModel;
    }

    private String extractBizId(ComponentDataResponse response) {
        JSONObject data = response.getData();
        return (String) data.get("bizId");
    }

    public void specialProcessNotice(TrafficSolutionItem trafficGroupItem) {
        AiNoticeInfo noticeInfo = trafficGroupItem.getNoticeInfo();
        if (noticeInfo == null || StringUtils.isEmpty(noticeInfo.getText())) {
            return;
        }
        String text = noticeInfo.getText();
        if (CollectionUtils.isEmpty(JourneyPlanAiAssistantHelper.traffic_notice_text_filter)) {
            return;
        }
        boolean contains = JourneyPlanAiAssistantHelper.traffic_notice_text_filter.contains(text);
        //特殊处理提示信息
        if (contains) {
            trafficGroupItem.setNoticeInfo(null);
        }
    }

}
