package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alitrip.aisearch.model.search.visa.entity.VisaArea;
import com.alitrip.aisearch.model.search.visa.entity.VisaContent;
import com.alitrip.aisearch.model.search.visa.entity.VisaInfo;
import com.alitrip.aisearch.model.search.visa.entity.VisaQa;
import com.fliggy.travelvc.sbc.dilu.client.model.VisaHotSkuSearchItemDTO;
import com.fliggy.travelvc.sbc.dilu.client.model.VisaHotSkuSearchItemQueryRequest;
import com.fliggy.travelvc.sbc.dilu.client.model.country.VisaItemLabelFeature;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.AbstractNodeProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.EnableCollection;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.MethodCondition;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.CollectionBizTypeEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.biz.frt.visa.CountryVisaSearchClient;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageComponentTypeEnum;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.ComponentDataResponse;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.mdd.MddImgCardVO;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.mdd.MddVisaSkuCardVO;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult.StreamMessageCardModel;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl.PoiNodeProcessor.POI_IMAGE_WIDTH;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiMddJourneySwitch.*;
import static com.taobao.trip.jourprod.biz.frt.llm.converter.MddVisaFusionSearchConvert.VISA_CATEGORY;
import static com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageItemTypeEnum.IMAGE_CARD;
import static com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageItemTypeEnum.VISA_SKU_CARD;

/**
 * 签证 节点处理器
 * <AUTHOR>
 */
@Component
public class VisaNodeProcessor extends AbstractNodeProcessor {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(VisaNodeProcessor.class);

    public static final String RECOMMEND_SPLIT = "、";
    private static final String VISA_TYPE_VID = "visaTypeVid";
    private static final String INDEX = "index";
    private static final String VISA_MATERIAL = "visaMaterial";
    private static final String VISA_QA = "visaQa";
    private static final String SPLIT = "_";


    @Resource
    private CountryVisaSearchClient countryVisaSearchClient;


    @Override
    protected List<StreamMessageCardModel<?>> doProcess(ComponentDataResponse response, ChatContext chatContext) {
        return null;
    }

    @MethodCondition(field = "type", value = "vacation_visa_card")
    @EnableCollection(bizType = CollectionBizTypeEnum.ITEM, bizIdColumn = "itemId", titleColumn = "skuTitle")
    private List<StreamMessageCardModel> mddVisaSkuCard(ComponentDataResponse response, ChatContext chatContext) {
        Map<String, VisaInfo> map = (Map<String, VisaInfo>) chatContext.getInternalData().getOrDefault(VISA_CATEGORY, Collections.EMPTY_MAP);

        VisaInfo visaInfo = map.get(new ArrayList<>(map.keySet()).get(0));
        if (Objects.isNull(visaInfo)) {
            LOGGER.recordOutput(new LogModel("mddVisaSkuCard").request(JSON.toJSONString(response.getData())));
            return Lists.newArrayList();
        }
        String visaTypeVid = response.getData().getString(VISA_TYPE_VID);

        Long visaTypeId = null;
        if (StringUtils.isNotBlank(visaTypeVid)) {
            visaTypeId = Long.valueOf(visaTypeVid);
        }

        Long usualPlaceProvCode = chatContext.getCurrentLbsProCode();
        VisaArea areaInfo = visaInfo.getAreaInfo();
        if (Objects.nonNull(areaInfo)) {
            if (StringUtils.isNotBlank(areaInfo.getId()) && StringUtils.equals(areaInfo.getId(), AI_VISA_QUERY_SKU_DALIAN_ID)) {
                usualPlaceProvCode = Long.valueOf(areaInfo.getId());
            } else {
                String idTree = areaInfo.getIdTree();
                if (StringUtils.isNotBlank(idTree)) {
                    String[] split = idTree.split(",");
                    if (split.length > 2) {
                        usualPlaceProvCode = Long.valueOf(split[2]);
                    }
                }
            }
        }

        VisaHotSkuSearchItemQueryRequest request = new VisaHotSkuSearchItemQueryRequest();
        request.setBuyerId(chatContext.getRequest().getUserId());
        request.setCountryVid(Long.valueOf(visaInfo.getCountryVid()));
        request.setUsualPlaceProvCode(usualPlaceProvCode);
        request.setVisaTypeVid(visaTypeId);
        VisaHotSkuSearchItemDTO module = countryVisaSearchClient.searchVisaItemForHotSku(request);

        if (Objects.isNull(module)) {
            return Lists.newArrayList();
        }

        MddVisaSkuCardVO visaSkuCardVO = JSON.parseObject(JSON.toJSONString(module, SerializerFeature.DisableCircularReferenceDetect), MddVisaSkuCardVO.class);

        //处理推荐理由
        Boolean isFastSku = module.getIsFastSku();
        Boolean isSimpleSku = module.getIsSimpleSku();

        String twoValue = "";
        String twoAfterValueTag = "";
        String twoAfterValueMaterial = "";
        List<VisaItemLabelFeature> serviceTag = module.getPersonalizedServiceTags();
        if (CollectionUtils.isNotEmpty(serviceTag)) {
            String tagString = serviceTag.stream()
                    .limit(RECOMMEND_ITEM_SERVICE_TAG_COUNT)
                    .map(VisaItemLabelFeature::getFeatureTitle)
                    .collect(Collectors.joining(RECOMMEND_SPLIT));
            if (StringUtils.isNotBlank(tagString)) {
                twoAfterValueTag = "，支持" + tagString;
            }
        }
        List<String> simplifyMaterialList = module.getSimplifyMaterialList();
        if (CollectionUtils.isNotEmpty(simplifyMaterialList)) {
            String materialString = simplifyMaterialList.stream()
                    .limit(RECOMMEND_ITEM_SERVICE_TAG_COUNT)
                    .collect(Collectors.joining(RECOMMEND_SPLIT));
            if (StringUtils.isNotBlank(materialString)) {
                twoAfterValueMaterial = "，已减免" + materialString;
            }
        }

        if (BooleanUtils.isTrue(isFastSku) && BooleanUtils.isTrue(isSimpleSku)) {
            twoValue = "支持加急及简化办理" + twoAfterValueTag + twoAfterValueMaterial;
        } else if (BooleanUtils.isTrue(isFastSku)) {
            twoValue = "支持加急办理" + twoAfterValueTag;
        } else if (BooleanUtils.isTrue(isSimpleSku)) {
            twoValue = "支持简化办理" + twoAfterValueMaterial;
        }

        List<MddVisaSkuCardVO.RecommendReason> recommend = Lists.newArrayList();
        String skuHandledNum = module.getSkuHandledNum();
        if (StringUtils.isNotBlank(skuHandledNum)) {
            recommend.add(MddVisaSkuCardVO.RecommendReason.builder().key(RECOMMEND_ITEM_ONE).value(RECOMMEND_ITEM_ONE_VALUE + skuHandledNum).build());
        }
        if (StringUtils.isNotBlank(twoValue)) {
            recommend.add(MddVisaSkuCardVO.RecommendReason.builder().key(RECOMMEND_ITEM_TWO).value(twoValue).build());
        }
        visaSkuCardVO.setRecommend(recommend);

        StreamMessageCardModel cardModel = StreamMessageCardModel.finishAndReplace(response.getId(), VISA_SKU_CARD.getCode(), visaSkuCardVO);
        LOGGER.recordOutput(new LogModel("mddVisaSkuCard").request(JSON.toJSONString(response.getData())).response(JSON.toJSONString(visaSkuCardVO)));
        return Lists.newArrayList(cardModel);
    }

    @MethodCondition(field = "type", value = "image_card")
    private List<StreamMessageCardModel> mddVisaImgCard(ComponentDataResponse response, ChatContext chatContext) {
        Map<String, VisaInfo> map = (Map<String, VisaInfo>) chatContext.getInternalData().getOrDefault(VISA_CATEGORY, Collections.EMPTY_MAP);

        VisaInfo visaInfo = map.get(new ArrayList<>(map.keySet()).get(0));
        if (Objects.isNull(visaInfo)) {
            return Lists.newArrayList();
        }
        String index = response.getData().getString(INDEX);
        if (StringUtils.isBlank(index)) {
            return Lists.newArrayList();
        }
        String[] split = index.split(SPLIT);
        String img = "";
        if (index.contains(VISA_MATERIAL)) {
            List<VisaContent> visaMaterial = visaInfo.getVisaMaterial();
            if (CollectionUtils.isNotEmpty(visaMaterial)) {
                VisaContent visaContent = visaMaterial.stream()
                        .filter(info -> Objects.equals(Integer.parseInt(split[1]), info.getKey()) && info.getContentType() == 2)
                        .findFirst()
                        .orElse(null);
                if (Objects.nonNull(visaContent)) {
                    img = visaContent.getContent();
                }
            }
        } else if (index.contains(VISA_QA)) {
            List<VisaQa> visaQaList = visaInfo.getVisaQa();
            if (CollectionUtils.isNotEmpty(visaQaList)) {
                VisaQa visaQa = visaQaList.get(Integer.parseInt(split[1]));
                if (Objects.nonNull(visaQa)) {
                    List<VisaContent> answerList = visaQa.getAnswer();
                    if (CollectionUtils.isNotEmpty(answerList)) {
                        VisaContent visaContent = answerList.stream()
                                .filter(info -> Objects.equals(Integer.parseInt(split[2]), info.getKey()) && info.getContentType() == 2)
                                .findFirst()
                                .orElse(null);
                        if (Objects.nonNull(visaContent)) {
                            img = visaContent.getContent();
                        }
                    }
                }
            }
        }

        if (StringUtils.isBlank(img)){
            return Lists.newArrayList();
        }
        MddImgCardVO mddImgCardVO = new MddImgCardVO();
        mddImgCardVO.setUrl(img);
        mddImgCardVO.setWidth(POI_IMAGE_WIDTH);
        mddImgCardVO.setNoPlaceholder(Boolean.TRUE);

        StreamMessageCardModel cardModel = StreamMessageCardModel.finishAndReplace(response.getId(), IMAGE_CARD.getCode(), mddImgCardVO);
        LOGGER.recordOutput(new LogModel("mddVisaImgCard").request(JSON.toJSONString(response.getData())).response(JSON.toJSONString(mddImgCardVO)));
        return Lists.newArrayList(cardModel);
    }

    @Override
    public AiJourneyPlanSceneEnum supportAgentType() {
        return AiJourneyPlanSceneEnum.VISA;
    }

    @Override
    public Set<String> supportComponentType() {
        return Sets.newHashSet(AiJourneyMessageComponentTypeEnum.F_CARD.getCode());
    }

}
