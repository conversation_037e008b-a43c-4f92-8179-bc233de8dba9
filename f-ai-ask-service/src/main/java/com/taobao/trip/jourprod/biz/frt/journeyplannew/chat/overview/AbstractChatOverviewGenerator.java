package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.overview;

import com.alibaba.fastjson.JSON;
import com.fliggy.fic.dto.person.PoiInfo;
import com.google.common.collect.Lists;
import com.taobao.trip.jourprod.biz.common.ContextInnerKeyCommon;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.AbstractSceneSupport;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.TagExtractor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.stream.impl.ModelWithoutSplitorOutputStreamProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanStatusEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantContentHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantMessageStatusHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.TripPlanNodeProcessorHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.*;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneyMessageContentDTO.StructRoute;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneyRecommendLineModel.Route;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch;
import com.taobao.trip.jourprod.core.service.converter.AiJourneySceneModelConvert;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatRequest;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanMessageSummaryResult;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 生成总结内容的抽象类
 * <AUTHOR>
 * @Date 2025/1/30
 **/
public abstract class AbstractChatOverviewGenerator extends AbstractSceneSupport{

    private final FliggyNewLogger newLogger = LogUtil.getFliggyNewLogger(AbstractChatOverviewGenerator.class);

    @Resource
    protected JourneyPlanAiAssistantContentHelper journeyPlanAiAssistantContentHelper;

    @Resource
    protected JourneyPlanAiAssistantMessageStatusHelper messageStatusHelper;

    @Resource
    private TripPlanNodeProcessorHelper tripPlanNodeProcessorHelper;

    /**
     * 生成总结
     */
    public void generateChatOverview(JourneyPlanAiChatRequest request, AiJourneySceneModel aiJourneySceneModel, ChatContext chatContext) {
        // 更新状态
        messageStatusHelper.updateStatus(chatContext.getSystemMessageId(), AiJourneyPlanStatusEnum.GENERATE_OVERVIEW);
        // 生成总结内容
        JourneyPlanMessageSummaryResult result = doGenerateChatOverview(request, aiJourneySceneModel, chatContext);
        // 插入历史记录
        journeyPlanAiAssistantContentHelper.insertUserHistoryContent(request, chatContext);
        journeyPlanAiAssistantContentHelper.insertSystemHistoryContent(request, result, chatContext);
    }

    /**
     * 生成总结，总结完成之后放缓存，之后读取
     * 线路总结，生成替换文本
     * 生成线路卡片内容
     * 生成关联问题
     * 生成问题
     */
    public JourneyPlanMessageSummaryResult doGenerateChatOverview(JourneyPlanAiChatRequest request, AiJourneySceneModel aiJourneySceneModel, ChatContext chatContext) {
        JourneyPlanMessageSummaryResult result = new JourneyPlanMessageSummaryResult();
        result.setMessageId(chatContext.getSystemMessageId());
        //获取形成结构数据，并生成替换数据
        setRefreshCardMap(result, chatContext);
        // 生成更多诉求和灵感词
        fillMoreAndInspiration(result, chatContext);
        // 把生成的结果放到缓存中
        recordResult(request, result, aiJourneySceneModel, chatContext);
        // 生成回答的文本内容
        fillInfo(result, chatContext);
        // 生成结构化展示的信息，这部分数据只用记录到历史信息，不需要在总结中放出来
        fillSystemStructRoute(chatContext.getSystemMessageId(), result, aiJourneySceneModel, chatContext);
        return result;
    }

    protected void setRefreshCardMap(JourneyPlanMessageSummaryResult result
        , ChatContext chatContext) {
        //非行程规划没有行程规划收藏结构。
        return;
    }


    private AiJourneyRecommendLineModel getStructure(String systemMessageId, ChatContext chatContext) {
        // 回答取消了的话，直接返回null
        if (messageStatusHelper.isStop(systemMessageId)) {
            return null;
        }
        AiJourneyRecommendLineModel result = new AiJourneyRecommendLineModel();
        try {
            Map<String, Object> internalData = chatContext.getInternalData();
            if (MapUtils.isNotEmpty(internalData)) {
                return null;
            }
            Object rawValue = internalData.get(ContextInnerKeyCommon.DAILY_POI_LIST);
            if (Objects.isNull(rawValue)) {
                return null;
            }
            Map<Integer, List<PoiInfo>> poiListMap = (Map<Integer, List<PoiInfo>>)rawValue;
            result.setTitle(tripPlanNodeProcessorHelper.safeGetJourneyTitle(chatContext));
            result.setRouteList(getRouteList(poiListMap));
            return result;
        } catch (Throwable throwable) {
            newLogger.recordNormalException(new LogModel("getStructure_parseItinerary")
                .e(throwable)
                .message("解析行程规划报错"));
            return null;
        } finally {
            newLogger.recordOutput(new LogModel("getStructure").request(systemMessageId)
                .response(JSON.toJSONString(result)));
        }
    }

    /**
     * 记录回答的内容，之后写入到历史记录里
     */
    protected void fillInfo(JourneyPlanMessageSummaryResult result, ChatContext chatContext) {
        String originalContent = chatContext.getOriginalContent();
        result.setInfo(ModelWithoutSplitorOutputStreamProcessor.staticProcessChunk(originalContent));
    }

    private void recordResult(JourneyPlanAiChatRequest request, JourneyPlanMessageSummaryResult result, AiJourneySceneModel aiJourneySceneModel, ChatContext chatContext) {
        // 记录当前问题类型
        AiJourneyPlanSceneEnum aiJourneyPlanSceneEnum = AiJourneySceneModelConvert.extractAiJourneyScene(aiJourneySceneModel);
        if (Objects.nonNull(aiJourneyPlanSceneEnum)) {
            result.setType(aiJourneyPlanSceneEnum.getAgentType().getCodeToFront());
        }
        // 记录用户回答
        result.setUserChat(request.getChat());
        journeyPlanAiAssistantContentHelper.recordSummaryResult(chatContext.getSystemMessageId(), result);
    }

    /**
     * todo 简化结构
     */
    protected void fillSystemStructRoute(String systemMessageId, JourneyPlanMessageSummaryResult result, AiJourneySceneModel aiJourneySceneModel, ChatContext chatContext) {
        StructRoute structRoute = new StructRoute();
        result.setStructRoute(structRoute);
        structRoute.setGuide(chatContext.getOriginalContent());
    }

    /**
     * 提取灵感词和更多诉求区的数据
     */
    protected void fillMoreAndInspiration(JourneyPlanMessageSummaryResult result, ChatContext chatContext) {
        try {
            String originalContent = chatContext.getOriginalContent();
            // 更多诉求
            String more = TagExtractor.extractFMoreString(originalContent);
            result.setMoreDemands(more);

            // 更多诉求对应的键盘标签
            // 键盘标签1，从快速回答中解析
            if (CollectionUtils.isNotEmpty(chatContext.getKeyboardTags())) {
                result.setKeyboardTags(chatContext.getKeyboardTags());
            }
            // 更多诉求对应的键盘标签v2
            // 键盘标签2，从快速回答中解析
            if (CollectionUtils.isNotEmpty(chatContext.getKeyboardTagsV2())) {
                result.setKeyboardTagsV2(chatContext.getKeyboardTagsV2());
            }
            // 键盘标签1，从正文中解析
            List<KeyboardTagDTO> keyboardTags = Lists.newArrayList();
            List<String> moreList = TagExtractor.extractFMoreTags(originalContent);
            if (CollectionUtils.isNotEmpty(moreList)) {
                // for循环保证顺序
                for (String word : moreList) {
                    KeyboardTagDTO keyboardTagDTO = AiJourneySwitch.MORE_QUESTION_KEYBORAD_MAP.get(word);
                    Optional.ofNullable(keyboardTagDTO).ifPresent(keyboardTags::add);
                }
                result.setKeyboardTags(keyboardTags);
            }
            //把键盘标签1 填充到键盘标签2中
            if (CollectionUtils.isNotEmpty(result.getKeyboardTags())) {
                result.setKeyboardTagsV2(result.getKeyboardTags().stream()
                        .map(v1 -> KeyboardTagDTOV2.builder()
                                .title(v1.getTitle())
                                .keys(v1.getKeys().stream()
                                        .map(key -> KeyboardTagValueDTOV2.builder().name(key).clickText(key).build()).collect(Collectors.toList()))
                                .build())
                        .collect(Collectors.toList()));
            }

            // 灵感词
            List<String> inspirationList = TagExtractor.extractFInspirationTags(originalContent);
            result.setInspirations(inspirationList);

            // 灵感词v2
            if (CollectionUtils.isNotEmpty(chatContext.getInspirationsV2())) {
                result.setInspirationsV2(chatContext.getInspirationsV2());
            }

            //设置是否是澄清
            if (BooleanUtils.isTrue(chatContext.getIsClearQuery())) {
                result.setIsClearQuery(true);
            }

            // 去除更多诉求和灵感的标签数据
            chatContext.setOriginalContent(TagExtractor.removeTags(originalContent));
        } catch (Exception ex) {
            newLogger.recordNormalException(new LogModel("fillMoreAndInspiration_extractTags").message("提取灵感词和更多诉求区数据报错").e(ex));
        }
    }

    /**
     * 获取生成的线路结果
     */
    protected List<Route> getRouteList(Map<Integer, List<PoiInfo>> poiListMap) {
        List<Route> routeList = new ArrayList<>();
        journeyPlanAiAssistantContentHelper.getRoute(poiListMap, routeList);
        return routeList;
    }

}
