package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.overview;

import com.alibaba.fastjson.JSON;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneySceneModel;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatRequest;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * @Description 总结内容生成路由工具类
 * <AUTHOR>
 * @Date 2025/1/30
 **/
@Component
public class ChatOverviewGeneratorProcessor {

    private static final FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(ChatOverviewGeneratorProcessor.class);

    @Autowired
    private List<AbstractChatOverviewGenerator> chatOverviewGeneratorList;

    /**
     * 启动生成总结任务
     */
    public void generateChatOverview(JourneyPlanAiChatRequest request, AiJourneySceneModel aiJourneySceneModel, ChatContext chatContext) {
        AbstractChatOverviewGenerator chatOverviewGenerator = chatOverviewGeneratorList.stream().filter(item -> item.support(aiJourneySceneModel)).findFirst().orElse(null);
        if (Objects.isNull(chatOverviewGenerator)) {
            LOGGER.recordNormalException(new LogModel("generateChatOverview")
                    .request(JSON.toJSONString(request))
                    .code(String.join("，", chatContext.getSessionId(), chatContext.getSystemMessageId(), chatContext.getUserMessageId()))
                    .message("没找到对应的总结生成器-{0}", JSON.toJSONString(aiJourneySceneModel)));
            return;
        }
        chatOverviewGenerator.generateChatOverview(request, aiJourneySceneModel, chatContext);
    }

}
