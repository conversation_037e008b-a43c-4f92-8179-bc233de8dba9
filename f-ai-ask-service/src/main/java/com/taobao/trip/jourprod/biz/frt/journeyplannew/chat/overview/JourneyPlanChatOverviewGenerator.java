package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.overview;

import com.alitrip.aisearch.model.search.poi.entity.PoiInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.taobao.ateye.annotation.Switch;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.trip.jourprod.biz.common.ContextInnerKeyCommon;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl.JourneyPlanNodeProcessor.VacationInfo;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl.PoiNodeProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantContentHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantMessageHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.TripPlanNodeProcessorHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneyMessageContentDTO.StructRoute;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneyRecommendLineModel.Route;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneySceneModel;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiTrafficCardVO;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.HotelRecommendCardVO;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanMessageSummaryResult;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.Poi;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult.StreamMessageCardModel;
import com.taobao.trip.rsp.collect.JourneyCollectDTO;
import com.taobao.trip.rsp.collect.JourneyCollectItemDTO;
import com.taobao.trip.rsp.collect.JourneyRecommendRouteInfo;
import com.taobao.trip.rsp.collect.TitleCollectDTO;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl.PoiNodeProcessor.POI_DETAIL_PAGE_URL_FORMATTER;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum.READY_TO_PLAN;

/**
 * @Description 已经生成路线的总结
 * <AUTHOR>
 * @Date 2025/2/3
 **/
@Component
public class JourneyPlanChatOverviewGenerator extends AbstractChatOverviewGenerator implements InitializingBean {

    @AppSwitch(des = "poi兜底空图片列表", level = Level.p4)
    public static Set<String> POI_BACKUP_PICTURE_LIST = Sets.newHashSet();

    @Switch(description = "行程收藏状态, 默认为false 2025-4-15以后改为true")
    public static volatile boolean showRouteCollectStatus = false;

    static {
        POI_BACKUP_PICTURE_LIST.add("https://gw.alicdn.com/tfs/TB19n9kjoY1gK0jSZFCXXcwqXXa-750-468.png");
        POI_BACKUP_PICTURE_LIST.add("https://gw.alicdn.com/tfs/TB1GgSiji_1gK0jSZFqXXcpaXXa-750-468.png");
    }

    @AppSwitch(des = "支持的场景", level = Level.p4)
    public static Set<String> SUPPORT_SCENE_STR = Sets.newHashSet();

    static {
        SUPPORT_SCENE_STR.add(READY_TO_PLAN.getSceneCode());
    }
    private final FliggyNewLogger newLogger = LogUtil.getFliggyNewLogger(JourneyPlanChatOverviewGenerator.class);

    @Resource
    private JourneyPlanAiAssistantContentHelper journeyPlanAiAssistantContentHelper;
    @Resource
    private JourneyPlanAiAssistantMessageHelper journeyPlanAiAssistantMessageHelper;

    @Resource
    private PoiNodeProcessor poiNodeProcessor;

    @Resource
    private TripPlanNodeProcessorHelper tripPlanNodeProcessorHelper;

    protected void setRefreshCardMap(JourneyPlanMessageSummaryResult result
        , ChatContext chatContext) {
        try {
            //取出components中 itemType为journey_title_card 首条数据
            Map<String, Object> internalData = chatContext.getInternalData();
            if(!internalData.containsKey(ContextInnerKeyCommon.TITLE)) {
                return;
            }
            Object o = internalData.get(ContextInnerKeyCommon.TITLE);
            StreamMessageCardModel<TitleCollectDTO> baseJourneyTitleCard = (StreamMessageCardModel<TitleCollectDTO>)o;
            //构建行程收藏卡片参数
            StreamMessageCardModel<TitleCollectDTO> journeyTitleCard = buildJourneyTitleCardCollectData(chatContext,
                baseJourneyTitleCard);
            //塞入刷新卡片结构中
            Map<String, Object> componentsRefreshMap = result.getComponentsRefreshMap();
            componentsRefreshMap.put(journeyTitleCard.getId(), journeyTitleCard);
            // 标题卡片在一开始是无法收藏且数据错误的，保存库中，要保存完整数据
            List<StreamMessageCardModel> components = chatContext.getComponents();
            //找到itemType为journey_title_card的元素，并使用baseJourneyTitleCard替换它
            for (int i = 0; i < components.size(); i++) {
                if (Objects.isNull(components.get(i))) {
                    continue;
                }
                if (Objects.equals(components.get(i).getItemType(), "journey_title_card")) {
                    components.set(i, journeyTitleCard);
                }
            }
        } catch (Exception e) {
            newLogger.recordNormalException(new LogModel("setRefreshCardMap")
                .e(e)
                .message("生成重置卡片结构报错"));
        }

    }

    /**
     * todo 日志打印
     */
    private StreamMessageCardModel<TitleCollectDTO> buildJourneyTitleCardCollectData(ChatContext chatContext, StreamMessageCardModel<TitleCollectDTO> journeyTitleCard) {
        //将poi 、 酒店 数据放入至收藏卡片数据中
        TitleCollectDTO titleCard = journeyTitleCard.getData();
        String title = titleCard.getTitle();
        titleCard.setCollectEnable(showRouteCollectStatus);
        JourneyCollectDTO collectData = new JourneyCollectDTO();
        collectData.setBizId(chatContext.getSystemMessageId());
        //构建收藏参数
        collectData.setRecommendRouteInfo(buildRecommendRouteInfo(chatContext, title));
        //构建收藏参数中物料信息
        collectData.setBatchAddJson(buildJourneyCollectItemDTOList(chatContext));
        titleCard.setCollectData(collectData);
        journeyTitleCard.setData(titleCard);
        return journeyTitleCard;

    }
    private List<JourneyCollectItemDTO> buildPoiItemList(ChatContext chatContext) {
        try{
            Map<String, Object> internalData = chatContext.getInternalData();
            if(!internalData.containsKey(ContextInnerKeyCommon.DAILY_POI_LIST)){
                return new ArrayList<>();
            }
            Map<Integer, List<com.alitrip.aisearch.model.search.poi.entity.PoiInfo>> dailyToPoiListMap =
                (Map<Integer, List<com.alitrip.aisearch.model.search.poi.entity.PoiInfo>>)internalData.get(ContextInnerKeyCommon.DAILY_POI_LIST);
            newLogger.recordOutput(new LogModel("buildPoiItemList").message("buildPoiItemList").response(JSONUtil.toJSONString(dailyToPoiListMap)));
            List<JourneyCollectItemDTO> result = new ArrayList<>();
            if (MapUtils.isEmpty(dailyToPoiListMap)) {
                return new ArrayList<>();
            }
            Long userId = chatContext.getRequest().getUserId();
            //拿出所有的values
            List<com.alitrip.aisearch.model.search.poi.entity.PoiInfo> allPoi = dailyToPoiListMap.values().stream()
                .flatMap(Collection::stream).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(allPoi)) {
                return Lists.newArrayList();
            }
            for (com.alitrip.aisearch.model.search.poi.entity.PoiInfo poiInfo : allPoi) {
                JourneyCollectItemDTO poiItemDTO = new JourneyCollectItemDTO();
                poiItemDTO.setUserId(userId);
                poiItemDTO.setBizId(poiInfo.getPoiId());
                poiItemDTO.setBizType("poi");
                poiItemDTO.setTitle(poiInfo.getPoiName());
                poiItemDTO.setJumpUrl(poiNodeProcessor.buildJumpUrl(POI_DETAIL_PAGE_URL_FORMATTER, poiInfo.getPoiId()));
                poiItemDTO.setPicUrl(poiInfo.getPoiImg());
                //景点没有价格，没有开始和结束时间。
                result.add(poiItemDTO);
            }
            return result;
        }catch (Exception e){
            newLogger.recordNormalException(new LogModel("buildPoiItemList")
                .e(e)
                .message("生成景点物料结构报错"));
            return new ArrayList<>();
        }

    }

    private List<JourneyCollectItemDTO> buildJourneyCollectItemDTOList(ChatContext chatContext) {
        List<JourneyCollectItemDTO> journeyCollectItemDTOList = new ArrayList<>();
        //物料分为两种， poi 、酒店  商品现在获取不到。
        journeyCollectItemDTOList.addAll(buildPoiItemList(chatContext));
        journeyCollectItemDTOList.addAll(buildHotelItemList(chatContext));
        //构建交通，包含机票和火车票。
        journeyCollectItemDTOList.addAll(buildTraffic(chatContext));
        //构建宝贝类型商品
        journeyCollectItemDTOList.addAll(buildJourneyItemList(chatContext));
        return journeyCollectItemDTOList;
    }

    private List<JourneyCollectItemDTO> buildJourneyItemList(ChatContext chatContext) {
        try{
            Map<String, Object> internalData = chatContext.getInternalData();
            if (!internalData.containsKey(ContextInnerKeyCommon.JOURNEY_ITEM_TYPE)) {
                return new ArrayList<>();
            }
            List<VacationInfo> vacationInfoList = (List<VacationInfo>)internalData.get(ContextInnerKeyCommon.JOURNEY_ITEM_TYPE);
            if (CollectionUtils.isEmpty(vacationInfoList)) {
                return new ArrayList<>();
            }
            newLogger.recordOutput(new LogModel("buildJourneyItemList").message("buildJourneyItemList").response(JSONUtil.toJSONString(vacationInfoList)));
            List<VacationInfo> vacationInfos = vacationInfoList.stream()
                .collect(Collectors.collectingAndThen(
                    Collectors.toMap(
                        VacationInfo::getVacationId,    // Key：vacationId
                        item -> item,                    // Value：VacationInfo 对象
                        (first, duplicate) -> first      // 合并策略：保留第一个出现的
                    ),
                    map -> new ArrayList<>(map.values()) // 将 Map 的 Values 转换为 List
                ));
            List<JourneyCollectItemDTO> result = new ArrayList<>();
            for (VacationInfo vacationInfo : vacationInfos) {
                JourneyCollectItemDTO journeyCollectItemDTO = new JourneyCollectItemDTO();
                journeyCollectItemDTO.setBizId(vacationInfo.getVacationId());
                journeyCollectItemDTO.setBizType("item");
                journeyCollectItemDTO.setTitle(vacationInfo.getVacationName());
                journeyCollectItemDTO.setUserId(chatContext.getRequest().getUserId());
                result.add(journeyCollectItemDTO);
            }
            return result;
        } catch (Exception e) {
            newLogger.recordNormalException(new LogModel("buildJourneyItemList")
                .e(e)
                .message("生成商品物料结构报错"));
            return new ArrayList<>();
        }

    }

    public List<JourneyCollectItemDTO> buildTraffic(ChatContext chatContext) {
        try {
            Map<String, Object> internalData = chatContext.getInternalData();
            if (!internalData.containsKey(ContextInnerKeyCommon.JOURNEY_TRAFFIC)) {
                return new ArrayList<>();
            }
            List<AiTrafficCardVO> trafficCardVOList =
                (List<AiTrafficCardVO>)internalData.get(ContextInnerKeyCommon.JOURNEY_TRAFFIC);
            if (CollectionUtils.isEmpty(trafficCardVOList)) {
                return new ArrayList<>();
            }
            newLogger.recordOutput(new LogModel("buildTraffic").message("buildTraffic").response(JSONUtil.toJSONString(trafficCardVOList)));
            Long userId = chatContext.getRequest().getUserId();
            List<JourneyCollectItemDTO> result = new ArrayList<>();
            for (AiTrafficCardVO trafficCardVO : trafficCardVOList) {
                JourneyCollectItemDTO trafficItem = new JourneyCollectItemDTO();
                // todo npe optinal
                trafficItem.setBizId(String.valueOf(trafficCardVO.getCollectData().get("bizId")));
                trafficItem.setBizType(String.valueOf(trafficCardVO.getCollectData().get("bizType")));
                trafficItem.setUserId(userId);
                trafficItem.setJumpUrl(String.valueOf(trafficCardVO.getCollectData().get("jumpUrl")));
                trafficItem.setTrainBiz(trafficCardVO.getCollectData().get("trainBiz"));
                result.add(trafficItem);
            }
            return result;
        } catch (Exception e) {
            newLogger.recordNormalException(new LogModel("buildTraffic")
                .e(e)
                .message("生成交通物料结构报错"));
            return new ArrayList<>();
        }

    }

    public List<JourneyCollectItemDTO> buildHotelItemList(ChatContext chatContext) {
        try{
            Map<String, Object> internalData = chatContext.getInternalData();
            if (!internalData.containsKey(ContextInnerKeyCommon.JOURNEY_HOTEL)) {
                return new ArrayList<>();
            }
            List<HotelRecommendCardVO> hotelCardList =
                (List<HotelRecommendCardVO>)internalData.get(ContextInnerKeyCommon.JOURNEY_HOTEL);
            if (CollectionUtils.isEmpty(hotelCardList)) {
                return new ArrayList<>();
            }
            newLogger.recordOutput(new LogModel("buildHotelItemList").message("buildHotelItemList").response(JSONUtil.toJSONString(hotelCardList)));
            Long userId = chatContext.getRequest().getUserId();
            List<JourneyCollectItemDTO> result = new ArrayList<>();
            for (HotelRecommendCardVO hotelCardVO : hotelCardList) {
                JourneyCollectItemDTO hotelItem = new JourneyCollectItemDTO();
                hotelItem.setBizId(hotelCardVO.getShid());
                hotelItem.setBizType("hotel");
                hotelItem.setTitle(hotelCardVO.getTitle());
                hotelItem.setUserId(userId);
                hotelItem.setJumpUrl(String.valueOf(hotelCardVO.getJumpUrl()));
                hotelItem.setPicUrl(hotelCardVO.getPicUrl());
                hotelItem.setStartTime(hotelCardVO.getCheckIn());
                hotelItem.setEndTime(hotelCardVO.getCheckOut());
                result.add(hotelItem);
            }
            return result;
        }catch (Exception e){
            newLogger.recordNormalException(new LogModel("buildHotelItemList")
                .e(e)
                .message("生成酒店物料结构报错"));
            return new ArrayList<>();
        }
    }
    private JourneyRecommendRouteInfo buildRecommendRouteInfo(ChatContext chatContext, String title) {


        JourneyRecommendRouteInfo journeyRecommendRouteInfo = new JourneyRecommendRouteInfo();
        String systemMessageId = chatContext.getSystemMessageId();
        //兼容收藏时提取用户query，所以userId也要加进去。
        journeyRecommendRouteInfo.setMessageId(systemMessageId);
        journeyRecommendRouteInfo.setTitle(title);
        //构建路线 poiName
        journeyRecommendRouteInfo.setRoute(getRecommendRouteList(chatContext));
        // 从上下文中获取cardId
        Map<String, Object> internalData = chatContext.getInternalData();
        String cardId = MapUtils.getString(internalData, ContextInnerKeyCommon.JOURNEY_ROUTE_MAP_CARD_ID, StringUtils.EMPTY);
        String value = journeyPlanAiAssistantMessageHelper.getMapUrl(systemMessageId, cardId);
        if(StringUtils.isNotBlank(value)){
            journeyRecommendRouteInfo.setRouteMapImg(value);
        }else {
            journeyRecommendRouteInfo.setDrawPicForReplace(systemMessageId + "_draw_pic_value");
        }
        //添加预算。
        if(internalData.containsKey(ContextInnerKeyCommon.JOURNEY_BUDGET_TOTAL_COST)){
            String string = MapUtils.getString(chatContext.getInternalData(),
                ContextInnerKeyCommon.JOURNEY_BUDGET_TOTAL_COST);
            if(!Objects.equals(string,"0")){
                journeyRecommendRouteInfo.setPrice(string);
            }
        }

        return journeyRecommendRouteInfo;
    }


    private List<String> getRecommendRouteList(ChatContext chatContext) {

        Map<String, Object> internalData = chatContext.getInternalData();
        if(!internalData.containsKey(ContextInnerKeyCommon.DAILY_POI_LIST)){
            return new ArrayList<>();
        }
        Map<Integer, List<com.alitrip.aisearch.model.search.poi.entity.PoiInfo>> dailyToPoiListMap =
            (Map<Integer, List<com.alitrip.aisearch.model.search.poi.entity.PoiInfo>>)internalData.get(ContextInnerKeyCommon.DAILY_POI_LIST);
        if(MapUtils.isEmpty(dailyToPoiListMap)){
            return Lists.newArrayList();
        }
        ArrayList<String> poiNameList = new ArrayList<>();
        //dailyToPoiListMap 先按照 key进行从小到大排序
        Set<Integer> integers = dailyToPoiListMap.keySet();
        //integers 从小到大排序
        List<Integer> sortedIntegers = integers.stream().sorted(Comparator.naturalOrder()).collect(Collectors.toList());
        for(Integer day : sortedIntegers){
            List<com.alitrip.aisearch.model.search.poi.entity.PoiInfo> poiInfos = dailyToPoiListMap.get(day);
            if(CollectionUtils.isEmpty(poiInfos)){
                continue;
            }
            for(com.alitrip.aisearch.model.search.poi.entity.PoiInfo poiInfo : poiInfos){
                if(StringUtils.isBlank(poiInfo.getPoiName())){
                    continue;
                }
                poiNameList.add(poiInfo.getPoiName());
            }
        }
        return poiNameList;
    }

    private StreamMessageCardModel<TitleCollectDTO> getJourneyTitleCardInfo(ChatContext chatContext, String cardName) {
        List<StreamMessageCardModel> components = chatContext.getComponents();
        for (StreamMessageCardModel card : components) {
            if (card.getItemType().equals(cardName)) {
                return (StreamMessageCardModel<TitleCollectDTO>)card;
            }
        }
        return null;
    }
    @Override
    protected void fillSystemStructRoute(String systemMessageId, JourneyPlanMessageSummaryResult result,
        AiJourneySceneModel aiJourneySceneModel, ChatContext chatContext) {
        try {
            StructRoute structRoute = new StructRoute();
            result.setStructRoute(structRoute);
            structRoute.setGuide(chatContext.getOriginalContent());
            // 从 chatContext 中获取行程信息
            // 获取天数
            //Object day = chatContext.getInternalData().get("day");
            List<Route> routeList = new ArrayList<>();
            structRoute.setRouteList(routeList);
            structRoute.setTitle(tripPlanNodeProcessorHelper.safeGetJourneyTitle(chatContext));
            if (!chatContext.getInternalData().containsKey(ContextInnerKeyCommon.DAILY_POI_LIST)) {
                return;
            }
            Object dayToPoi = chatContext.getInternalData().get(ContextInnerKeyCommon.DAILY_POI_LIST);
            Map<Integer, List<PoiInfo>> dailyToPoiListMap = (Map<Integer, List<PoiInfo>>)dayToPoi;
            if (MapUtils.isEmpty(dailyToPoiListMap)) {
                return;
            }
            dailyToPoiListMap.forEach((k, v) -> {
                Route route = new Route();
                route.setDay(String.valueOf(k));
                List<Poi> poiList = v.stream().map(poi -> {
                    Poi poi1 = new Poi();
                    poi1.setPoiId(poi.getPoiId());
                    poi1.setType("poi");
                    poi1.setPoiType("poi");
                    return poi1;
                }).collect(Collectors.toList());
                route.setPoiList(poiList);
                routeList.add(route);
            });
        } catch (Exception e) {
            newLogger.recordNormalException(new LogModel("fillSystemStructRoute").message("填充系统结构化线路报错").e(e)
                .request(JSONUtil.toJSONString(chatContext.getInternalData())));
        }
    }

    /**
     * 获取生成的线路结果
     */
    protected String getRouteResult(String currentMessageId) {
        return journeyPlanAiAssistantContentHelper.getRouteResult(currentMessageId);
    }

    /**
     * 支持的场景
     */
    @Override
    protected Set<AiJourneyPlanSceneEnum> getSupportScene() {
        if (SUPPORT_SCENE_STR.isEmpty()) {
            return Sets.newHashSet();
        }
        return SUPPORT_SCENE_STR.stream()
                .map(AiJourneyPlanSceneEnum::getByCode)
                .collect(java.util.stream.Collectors.toSet());
    }

    /**
     * Invoked by a BeanFactory after it has set all bean properties supplied
     * (and satisfied BeanFactoryAware and ApplicationContextAware).
     * <p>This method allows the bean instance to perform initialization only
     * possible when all bean properties have been set and to throw an
     * exception in the event of misconfiguration.
     *
     * @throws Exception in the event of misconfiguration (such
     * as failure to set an essential property) or if initialization fails.
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        SwitchManager.init(JourneyPlanChatOverviewGenerator.class);
    }
}
