package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.overview;

import com.google.common.collect.Sets;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.Set;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum.*;

/**
 * @Description 其他行程问题总结生成
 * <AUTHOR>
 * @Date 2025/2/15
 **/
@Component
public class OtherJourneyQuestionChatOverviewGenerator extends AbstractChatOverviewGenerator implements InitializingBean {

    @AppSwitch(des = "支持的场景", level = Level.p4)
    public static Set<String> SUPPORT_SCENE_STR = Sets.newHashSet();

    static {
        SUPPORT_SCENE_STR.add(TRAFFIC.getSceneCode());
        SUPPORT_SCENE_STR.add(DESTINATION_RECOMMEND.getSceneCode());
        SUPPORT_SCENE_STR.add(VISA.getSceneCode());
        SUPPORT_SCENE_STR.add(NOT_SUPPORT_SCENE.getSceneCode());
        SUPPORT_SCENE_STR.add(SCENIC_RECOMMEND.getSceneCode());
        SUPPORT_SCENE_STR.add(HOTEL_PACKAGE_BOOKING.getSceneCode());
        SUPPORT_SCENE_STR.add(HOTEL_BOOKING.getSceneCode());
    }

    /**
     * 支持的场景
     */
    @Override
    protected Set<AiJourneyPlanSceneEnum> getSupportScene() {
        if (SUPPORT_SCENE_STR.isEmpty()) {
            return Sets.newHashSet();
        }
        return SUPPORT_SCENE_STR.stream()
                .map(AiJourneyPlanSceneEnum::getByCode)
                .collect(java.util.stream.Collectors.toSet());
    }

    /**
     * Invoked by a BeanFactory after it has set all bean properties supplied
     * (and satisfied BeanFactoryAware and ApplicationContextAware).
     * <p>This method allows the bean instance to perform initialization only
     * possible when all bean properties have been set and to throw an
     * exception in the event of misconfiguration.
     *
     * @throws Exception in the event of misconfiguration (such
     * as failure to set an essential property) or if initialization fails.
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        SwitchManager.init(OtherJourneyQuestionChatOverviewGenerator.class);
    }
}
