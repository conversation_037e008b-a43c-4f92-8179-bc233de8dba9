package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.stream;

import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatRequest;

/**
 * 流解析器抽象类
 */
public abstract class AbstractOutputStreamProcessor {

    protected JourneyPlanAiChatRequest request;

    public AbstractOutputStreamProcessor(JourneyPlanAiChatRequest request) {
        this.request = request;
    }

    /**
     * 处理流式内容
     */
    public abstract void onTokenReceived(String chunk);

}
