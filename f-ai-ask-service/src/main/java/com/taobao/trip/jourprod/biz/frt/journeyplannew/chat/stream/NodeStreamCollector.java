package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.stream;

import com.alibaba.mtop3.invocation.MtopStream;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.AbstractNodeProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.NodeProcessorFactory;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.ComponentDataResponse;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatRequest;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;

/**
 * <AUTHOR>
 * @date 2025/3/16
 */
public class NodeStreamCollector {

    private static final FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(NodeStreamCollector.class);

    final private AiJourneyPlanSceneEnum agentType;

    public NodeStreamCollector( AiJourneyPlanSceneEnum agentType) {
        this.agentType = agentType;
    }

    /**
     * 根据传入的节点信息处理数据
     * todo request mtopStream 放到 context中
     */
    public void collect(ComponentDataResponse response, JourneyPlanAiChatRequest request, MtopStream mtopStream, ChatContext chatContext) {
        if (response == null) {
            LOGGER.recordNormalException(new LogModel("collect").
                    message("response为空,agentType={0},componentType={1}"
                            , agentType.getAgentType(), response.getComponentType()));
            return;
        }
        if (null == response.getData()) {
            LOGGER.recordNormalException(new LogModel("collect").
                    message("数据为空,agentType={0},componentType={1}"
                            , agentType.getAgentType(), response.getComponentType()));
            return;
        }
        AbstractNodeProcessor processor = NodeProcessorFactory.getProcessor(agentType, response.getComponentType());
        if (null == processor) {
            LOGGER.recordNormalException(new LogModel("collect")
                    .message("没有对应的处理器,agentType={0},componentType={1}"
                            , agentType.getAgentType(), response.getComponentType()));
            return;
        }

        if (response.isComplete()) {
            processor.process(response, request, mtopStream, chatContext);
        } else {
            LOGGER.recordOutput(new LogModel("collect")
                    .message("数据未完成,agentType={0},componentType={1}"
                            , agentType.getAgentType(), response.getComponentType()));
        }
    }

}
