package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.stream;

import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2025/3/16
 */
public class TextStreamCollector {
    public static final Logger logger = LoggerFactory.getLogger(TextStreamCollector.class);
    final private ChatContext chatContext;
    final private AiJourneyPlanSceneEnum agentType;

    public TextStreamCollector(ChatContext chatContext, AiJourneyPlanSceneEnum agentType) {
        this.chatContext = chatContext;
        this.agentType = agentType;
    }

    public void collect(String text) {
        if (null == text || text.isEmpty()) {
            return;
        }
        chatContext.getMessageList().add(text);
    }
}
