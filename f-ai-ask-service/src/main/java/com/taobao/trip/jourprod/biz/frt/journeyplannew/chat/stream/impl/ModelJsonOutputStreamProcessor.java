package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.stream.impl;

import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.stream.AbstractOutputStreamProcessor;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatRequest;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ModelJsonOutputStreamProcessor extends AbstractOutputStreamProcessor {

    public static final Logger logger = LoggerFactory.getLogger(ModelJsonOutputStreamProcessor.class);

    private final StringBuilder buffer = new StringBuilder();

    // 用于收集JSON之外的数据
    private final StringBuilder nonJsonBuffer = new StringBuilder();

    // 处理节点，左侧入参是当前在处理的节点，以及本次增量的内容，右侧入参是当前节点的全部内容
    private final BiConsumer<Pair<String, String>, Pair<String, JSONObject>> callback;

    // 处理非JSON数据的回调函数
    private final Consumer<String> nonJsonCallback;

    // 记录 JSON 结构的 { } 平衡
    private int braceCount = 0;

    // 记录 JSON 结构的 [ ] 平衡
    private int bracketCount = 0;

    // 跟踪是否在字符串内
    private boolean inString = false;

    // 跟踪前一个字符是否为转义字符
    private boolean escaped = false;

    // 是否正在等待新的JSON开始
    private boolean waitingForNewJson = true;

    public ModelJsonOutputStreamProcessor(JourneyPlanAiChatRequest request, BiConsumer<Pair<String, String>, Pair<String, JSONObject>> callback, Consumer<String> nonJsonCallback) {
        super(request);
        this.callback = callback;
        this.nonJsonCallback = nonJsonCallback;
    }

    @Override
    public void onTokenReceived(String token) {
        if (Objects.isNull(token)) {
            return;
        }
        // 逐字符处理以精确跟踪JSON结构
        for (int i = 0; i < token.length(); i++) {
            char c = token.charAt(i);

            // 如果正在等待新的JSON对象
            if (waitingForNewJson) {
                if (c == '{' || c == '[') {
                    // 如果非JSON缓冲区有内容，且存在处理非JSON回调，则调用回调处理
                    processNonJsonContent();

                    waitingForNewJson = false;
                    buffer.setLength(0); // 确保缓冲区为空
                    buffer.append(c);

                    // 更新括号计数
                    if (c == '{') braceCount = 1;
                    else bracketCount = 1;
                } else {
                    // 收集JSON之间的数据
                    nonJsonBuffer.append(c);
                }
                continue;
            }

            buffer.append(c);

            // 处理字符串内的字符
            if (c == '\\' && !escaped) {
                escaped = true;
                continue;
            }

            // 处理引号，需要考虑转义情况
            if (c == '"' && !escaped) {
                inString = !inString;
            }

            // 只有在非字符串内才计数括号
            if (!inString) {
                if (c == '{') braceCount++;
                else if (c == '}') braceCount--;
                else if (c == '[') bracketCount++;
                else if (c == ']') bracketCount--;

                // 检查是否有完整的JSON对象
                if (braceCount == 0 && bracketCount == 0 && (c == '}' || c == ']')) {
                    processCompleteJson();
                    waitingForNewJson = true; // 标记等待下一个JSON开始
                    nonJsonBuffer.setLength(0); // 清空非JSON缓冲区，准备收集下一段非JSON数据
                }
            }

            escaped = c == '\\' && !escaped;
        }

        // 重要：在处理完所有字符后，检查是否有未处理的非JSON内容
        if (waitingForNewJson && nonJsonBuffer.length() > 0) {
            processNonJsonContent();
        }
    }

    private void processNonJsonContent() {
        String nonJsonContent = nonJsonBuffer.toString().trim();
        if (!nonJsonContent.isEmpty() && nonJsonCallback != null) {
            nonJsonCallback.accept(nonJsonContent);
        }
        nonJsonBuffer.setLength(0); // 清空非JSON缓冲区
    }

    private void processCompleteJson() {
        try {
            String jsonString = buffer.toString().trim();
            if (!jsonString.isEmpty()) {
                JSONObject jsonNode = JSON.parseObject(jsonString);
                callback.accept(Pair.of("test", jsonString), Pair.of("test", jsonNode));
            }
        } catch (Exception e) {
            logger.error("processCompleteJson,JSON解析失败,node={},内容={}", e.getMessage(), buffer);
            // 如果提供了非JSON回调函数，则调用它处理无法解析的内容
            if (nonJsonCallback != null) {
                nonJsonCallback.accept(buffer.toString());
            }
        }
    }
}