package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.stream.impl;

import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.TagExtractor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.stream.AbstractOutputStreamProcessor;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatRequest;
import org.apache.commons.lang3.StringUtils;

import java.util.function.Consumer;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch.SPLITOR;

/**
 * @Description 屏蔽分隔符的流解析器
 * <AUTHOR>
 * @Date 2025/3/20
 **/
public class ModelWithoutSplitorOutputStreamProcessor extends AbstractOutputStreamProcessor {


    private enum State { NORMAL, FILTERING }

    private State state = State.NORMAL;

    private final StringBuilder buffer = new StringBuilder();

    public Consumer<String> streamCallback;

    public ModelWithoutSplitorOutputStreamProcessor(JourneyPlanAiChatRequest request) {
        super(request);
    }

    public ModelWithoutSplitorOutputStreamProcessor(JourneyPlanAiChatRequest request, Consumer<String> streamCallback) {
        super(request);
        this.streamCallback = streamCallback;
    }

    /**
     * 处理流式内容
     *
     * @param chunk
     */
    @Override
    public void onTokenReceived(String chunk) {
        String output = processChunk(chunk);
        if (StringUtils.isNotEmpty(output)) {
            streamCallback.accept(output);
        }
    }

    public String processChunk(String chunk) {
        for (char c : chunk.toCharArray()) {
            if (SPLITOR.equals(String.valueOf(c))) {
                state = (state == State.NORMAL) ? State.FILTERING : State.NORMAL;
                continue;
            }
            if (state == State.NORMAL) {
                buffer.append(c);
            }
        }
        return flushBuffer();
    }

    public String finalizeProcessing() {
        return flushBuffer();
    }

    private String flushBuffer() {
        String result = buffer.toString();
        buffer.setLength(0);
        return result;
    }

    /**
     * 一次性处理文本
     */
    public static String staticProcessChunk(String message) {
        if (StringUtils.isBlank(message)) {
            return StringUtils.EMPTY;
        }
        // 处理隐藏token
        String content = new ModelWithoutSplitorOutputStreamProcessor(null).processChunk(message);
        // 处理更多诉求和灵感区
        content = TagExtractor.removeTags(content);

        // 兜底逻辑，DeepSeek-V3输出不稳定时，最后会输出一个结束符<｜end▁of▁sentence｜>，需要手动替换掉，历史消息里不要展示
        content = content.replace("<|end of sentence|>", "");

        return content;
    }

}
