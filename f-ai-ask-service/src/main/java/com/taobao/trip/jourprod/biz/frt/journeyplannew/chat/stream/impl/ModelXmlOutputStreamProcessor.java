package com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.stream.impl;

import com.alibaba.fastjson.JSONObject;

import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl.JourneyPlanNodeProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.stream.AbstractOutputStreamProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.stream.ComponentDataConsumer;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.ComponentDataResponse;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatRequest;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Stack;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch.SPLITOR;

public class ModelXmlOutputStreamProcessor extends AbstractOutputStreamProcessor {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(ModelXmlOutputStreamProcessor.class);

    // 处理节点，左侧入参是当前在处理的节点，以及本次增量的内容，右侧入参是当前节点的全部内容
    private final ComponentDataConsumer callback;

    private Stack<TagContext> tagStack = new Stack<>();

    private StringBuilder textBuffer = new StringBuilder();

    private State state = State.TEXT;

    private StringBuilder currentTagName = new StringBuilder();

    private String currentStream = StringUtils.EMPTY;

    private final static String SPLIT = "&";

    /**
     * 基于xml注解的流式解析
     * @param request           请求数据
     * @param callback          结构化数据的回调函数
     */
    public ModelXmlOutputStreamProcessor(JourneyPlanAiChatRequest request, ComponentDataConsumer callback) {
        super(request);
        this.callback = callback;
    }

    @Override
    public void onTokenReceived(String chunk) {
        currentStream = chunk;
        for (char c : chunk.toCharArray()) {
            switch (state) {
                case TEXT:
                    handleText(c);
                    break;
                case TAG_OPEN_START:
                    handleTagOpenStart(c);
                    break;
                case TAG_OPEN:
                    handleTagOpen(c);
                    break;
                case TAG_CLOSE_START:
                    handleTagCloseStart(c);
                    break;
                case INSIDE_TAG:
                    handleInsideTag(c);
                    break;
            }
        }

        // 处理块结束后，输出当前标签缓冲区的剩余内容
        if (state == State.INSIDE_TAG && !tagStack.isEmpty()) {
            TagContext currentTag = tagStack.peek();
            if (currentTag.buffer.length() > 0) {
                String content = currentTag.buffer.toString();
                Pair<Boolean, JSONObject> extractResult = extractParameter(content);
                ComponentDataResponse dataResponse = new ComponentDataResponse(currentTag.getTagName(), currentTag.getTagId(), content, extractResult.getRight(), extractResult.getLeft());
                callback.accept(dataResponse);
            }
        } else {
            processTextBuffer();
        }
    }

    private void handleText(char c) {
        if (c == '<') {
            processTextBuffer();
            state = State.TAG_OPEN_START;
        } else {
            textBuffer.append(c);
        }
    }

    private void handleTagOpenStart(char c) {
        if (c == '/') {
            state = State.TAG_CLOSE_START;
            currentTagName.setLength(0);
        } else {
            state = State.TAG_OPEN;
            currentTagName.append(c);
        }
    }

    private void handleTagOpen(char c) {
        if (c == '>') {
            tagStack.push(new TagContext(currentTagName.toString()));
            currentTagName.setLength(0);
            state = State.INSIDE_TAG;
        } else {
            currentTagName.append(c);
        }
    }

    private void handleTagCloseStart(char c) {
        if (c == '>') {
            if (!tagStack.isEmpty()) {
                TagContext closedTag = tagStack.pop();
                closedTag.buffer.setLength(0);
            }
            currentTagName.setLength(0);
            state = tagStack.isEmpty() ? State.TEXT : State.INSIDE_TAG;
        } else {
            currentTagName.append(c);
        }
    }

    private void handleInsideTag(char c) {
        if (c == '<') {
            if (!tagStack.isEmpty()) {
                TagContext currentTag = tagStack.peek();
                if (currentTag.buffer.length() > 0) {
                    String content = currentTag.buffer.toString();
                    Pair<Boolean, JSONObject> extractResult = extractParameter(content);
                    ComponentDataResponse dataResponse = new ComponentDataResponse(currentTag.getTagName(), currentTag.getTagId(), content, extractResult.getRight(), extractResult.getLeft());
                    callback.accept(dataResponse);
                }
                currentTag.buffer.setLength(0);
            }
            state = State.TAG_OPEN_START;
        } else {
            if (!tagStack.isEmpty()) {
                tagStack.peek().buffer.append(c);
            }
        }
    }

    private void processTextBuffer() {
        if (textBuffer.length() > 0) {
            textBuffer.setLength(0);
        }
    }

    private enum State {
        TEXT,               // 在标签外部
        TAG_OPEN_START,     // 遇到 '<'，可能开始标签
        TAG_OPEN,           // 解析开始标签名
        TAG_CLOSE_START,    // 遇到 '</'，解析结束标签
        INSIDE_TAG          // 在标签内部收集内容
    }

    private static class TagContext {
        String name;
        StringBuilder buffer = new StringBuilder();

        TagContext(String name) {
            this.name = name;
        }

        public String getTagName() {
            if (StringUtils.isBlank(name)) {
                return StringUtils.EMPTY;
            }
            String[] split = name.split(SPLIT);
            return split[0];
        }

        public String getTagId() {
            if (StringUtils.isBlank(name)) {
                return StringUtils.EMPTY;
            }
            String[] split = name.split(SPLIT);
            return split[split.length - 1];
        }

    }

    /**
     * 从内容中提取隐藏参数
     */
    private static Pair<Boolean, JSONObject> extractParameter(String input) {
        JSONObject result = new JSONObject();
        boolean isComplete = false;

        Pattern pattern = Pattern.compile( SPLITOR + "(.*?)(" + SPLITOR + "|$)", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            String content = matcher.group(1);
            isComplete = matcher.group(2).equals(SPLITOR);

            // 分割键值对并过滤无效数据
            String[] pairs = content.split(";");
            if (!isComplete) {
                pairs = Arrays.copyOfRange(pairs, 0, pairs.length - 1);
            }
            for (String pair : pairs) {
                pair = pair.trim();
                if (pair.isEmpty()) continue;

                String[] kv = pair.split(":", 2);
                if (kv.length == 2) {
                    String key = kv[0].trim();
                    String value = kv[1].trim();
                    result.put(key, value);
                }
            }
        }

        return Pair.of(isComplete, result);
    }

}