package com.taobao.trip.jourprod.biz.frt.journeyplannew.constant;

import com.google.common.collect.Maps;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.csp.switchcenter.core.SwitchManager;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Description ai行程规划常量类
 * <AUTHOR>
 * @Date 2025/2/13
 **/
@Component
public class JourneyPlanAiAssistantConstant implements InitializingBean {

    @AppSwitch(des = "生成行程规划的智能体appid", level = Level.p4)
    public static String GENERATE_JOURNEY_PLAN_APP_ID = "1e13322963e843bba451b73422942c96";

    @AppSwitch(des = "解答其他行程相关问题的智能体appid", level = Level.p4)
    public static String OTHER_JOURNEY_QUESTION_APP_ID = "46e282d1f4754b7aa976aede25ec7fc8";

    @AppSwitch(des = "机票预订相关问题智能体appid", level = Level.p4)
    public static String FLIGHT_APP_ID = "1e13322963e843bba451b73422942c96";

    @AppSwitch(des = "酒店预订相关问题智能体appid", level = Level.p4)
    public static String HOTEL_APP_ID = "1e13322963e843bba451b73422942c96";

    @AppSwitch(des = "火车票预订相关问题智能体appid", level = Level.p4)
    public static String TRAIN_APP_ID = "1e13322963e843bba451b73422942c96";

    @AppSwitch(des = "生成poi的智能体appid", level = Level.p4)
    public static String GENERATE_POI_APP_ID = "1e13322963e843bba451b73422942c96";

    /**
     * 提示语格式
     * （（））中间的内容是用户输入
     * 【【】】中间的内容是飞猪提供
     */
    @AppSwitch(des = "提示语格式-2025-03-11", level = Level.p4)
    public static String PROMPT_FORMAT_20250311 = "（（%s））";

    public static final String ANSWER_KEY = "answer";

    public static final String LACK_KEY = "lack";

    public static final String TIP_KEY = "tip";

    public static final String TRIP_TIPS_KEY = "tripTips";

    public static final String ROUTE_RESULT_KEY = "routeResult";

    public static final String REASON_KEY = "reason";

    public static final String RELATED_CONTENT_KEY = "relatedContent";

    public static final String CURRENT_TIME_KEY = "currentTime";

    public static final String CURRENT_LBS_KEY = "currentLbs";

    /**
     * 换行符
     */
    public final static String NEW_LINE = "\n";

    /**
     * 两个换行符
     */
    public final static String DOUBLE_NEW_LINE = "\n\n";

    public static Map<String, Integer> CHINESE_TO_ARABIC_MAP = Maps.newHashMap();

    static {
        // 初始化映射表
        CHINESE_TO_ARABIC_MAP.put("零", 0);
        CHINESE_TO_ARABIC_MAP.put("一", 1);
        CHINESE_TO_ARABIC_MAP.put("二", 2);
        CHINESE_TO_ARABIC_MAP.put("三", 3);
        CHINESE_TO_ARABIC_MAP.put("四", 4);
        CHINESE_TO_ARABIC_MAP.put("五", 5);
        CHINESE_TO_ARABIC_MAP.put("六", 6);
        CHINESE_TO_ARABIC_MAP.put("七", 7);
        CHINESE_TO_ARABIC_MAP.put("八", 8);
        CHINESE_TO_ARABIC_MAP.put("九", 9);
        CHINESE_TO_ARABIC_MAP.put("十", 10);
        CHINESE_TO_ARABIC_MAP.put("百", 100);
        CHINESE_TO_ARABIC_MAP.put("千", 1000);
        CHINESE_TO_ARABIC_MAP.put("万", 10000);
        CHINESE_TO_ARABIC_MAP.put("亿", 100000000);
    }

    /**
     * 平台poi类型
     */
    public static String POI_TYPE = "poi";

    /**
     * 高德poi类型
     */
    public static String GAO_DE_TYPE = "gaode";

    /**
     * google poi类型
     */
    public static String GOOGLE_TYPE = "google";

    /**
     * poi-酒店
     */
    public static String POI_TYPE_HOTEL = "hotel";

    /**
     * 日志级别类型，是否是核心的
     */
    public static String LOG_LEVEL_TYPE = "core";

    /**
     * Invoked by a BeanFactory after it has set all bean properties supplied
     * (and satisfied BeanFactoryAware and ApplicationContextAware).
     * <p>This method allows the bean instance to perform initialization only
     * possible when all bean properties have been set and to throw an
     * exception in the event of misconfiguration.
     *
     * @throws Exception in the event of misconfiguration (such
     * as failure to set an essential property) or if initialization fails.
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        SwitchManager.init(JourneyPlanAiAssistantConstant.class);
    }
}
