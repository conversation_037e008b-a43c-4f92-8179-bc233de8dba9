package com.taobao.trip.jourprod.biz.frt.journeyplannew.diamond;

import com.alibaba.boot.diamond.annotation.DiamondListener;
import com.alibaba.boot.diamond.listener.DiamondDataCallback;
import com.alibaba.csp.ahas.switchcenter.shaded.com.alibaba.fastjson.JSON;
import com.alibaba.csp.ahas.switchcenter.shaded.com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.diamond.model.KeyboardTagValueDiamondConfigDTO;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.KeyboardTagValueDTOV2;
import com.taobao.trip.jourprod.common.lang.utils.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch.AI_KEYBOARD_TAGS_TITLE_OUT_TIME;
import static com.taobao.trip.jourprod.common.lang.utils.DateUtil.SIMPLE;

/**
 * @description: 特价机票 键盘配置 映射配置
 **/
@DiamondListener(dataId = "ai-cheapFlight-keyBoardValue-config", groupId = "f-ai-ask")
public class CheapFlightKeyBoardTagValueConfigManager implements DiamondDataCallback {


    private Map<String, List<KeyboardTagValueDiamondConfigDTO>> map = new HashMap<>();

    @Override
    public void received(String config) {
        // 处理接收到的配置数据
        if (StringUtils.isBlank(config)) {
            return;
        }
        map = JSON.parseObject(config, new TypeReference<>() {
        });
    }

    /**
     * 获取特价机票的出发时间配置
     *
     * 根据时间过滤
     */
    public List<KeyboardTagValueDTOV2> getCheapFlightOutTimeKeyboardTagValueDTOV2List() {
        List<KeyboardTagValueDiamondConfigDTO> list = map.get(AI_KEYBOARD_TAGS_TITLE_OUT_TIME);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        Date now = new Date();
        return list.stream()
                .filter(config -> {
                    try {
                        String startTime = config.getStartTime();
                        String endTime = config.getEndTime();
                        boolean judgeStart = true;
                        boolean judgeEnd = true;
                        if (StringUtils.isNotBlank(startTime)) {
                            Date startDate = DateUtil.dateParse(startTime, SIMPLE);
                            if (Objects.isNull(startDate) || startDate.after(now)) {
                                judgeStart = false;
                            }
                        }
                        if (StringUtils.isNotBlank(endTime)) {
                            Date endDate = DateUtil.dateParse(endTime, SIMPLE);
                            if (Objects.isNull(endDate) || endDate.before(now)) {
                                judgeEnd = false;
                            }
                        }
                        return judgeStart && judgeEnd;
                    } catch (ParseException ex) {
                        return false;
                    }
                })
                .map(config -> KeyboardTagValueDTOV2.builder()
                        .name(config.getName())
                        .clickText(config.getClickText())
                        .icon(config.getIcon())
                        .build())
                .collect(Collectors.toList());
    }

}
