package com.taobao.trip.jourprod.biz.frt.journeyplannew.diamond;

import javax.annotation.Resource;

import com.taobao.ateye.annotation.AteyeInvoker;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2025/05/20
 **/
@Component
public class DiamondUtil {
    @Resource
    private PoiNameToShortNameConfig1Manager poiNameToShortNameConfig1Manager;

    @Resource
    private PoiNameToShortNameConfig2Manager poiNameToShortNameConfig2Manager;

    @Resource
    private PoiNameToShortNameConfig3Manager poiNameToShortNameConfig3Manager;

    @Resource
    private PoiNameToShortNameConfig4Manager poiNameToShortNameConfig4Manager;

    @Resource
    private PoiNameToShortNameConfig5Manager poiNameToShortNameConfig5Manager;

    @Resource
    private PoiNameToShortNameConfig6Manager poiNameToShortNameConfig6Manager;

    @Resource
    private PoiNameToShortNameConfig7Manager poiNameToShortNameConfig7Manager;




    @AteyeInvoker(description = "获取poiName的简称", paraDesc = "poiName")
    public String getShortPoiName(String basePoiName){
        if(StringUtils.isBlank(basePoiName)){
            return basePoiName;
        }
        if(poiNameToShortNameConfig1Manager.getShortPoiName(basePoiName) != null){
            return poiNameToShortNameConfig1Manager.getShortPoiName(basePoiName);
        }
        if(poiNameToShortNameConfig2Manager.getShortPoiName(basePoiName) != null){
            return poiNameToShortNameConfig2Manager.getShortPoiName(basePoiName);
        }
        if(poiNameToShortNameConfig3Manager.getShortPoiName(basePoiName) != null){
            return poiNameToShortNameConfig3Manager.getShortPoiName(basePoiName);
        }
        if(poiNameToShortNameConfig4Manager.getShortPoiName(basePoiName) != null){
            return poiNameToShortNameConfig4Manager.getShortPoiName(basePoiName);
        }
        if(poiNameToShortNameConfig5Manager.getShortPoiName(basePoiName) != null){
            return poiNameToShortNameConfig5Manager.getShortPoiName(basePoiName);
        }
        if(poiNameToShortNameConfig6Manager.getShortPoiName(basePoiName) != null){
            return poiNameToShortNameConfig6Manager.getShortPoiName(basePoiName);
        }
        if(poiNameToShortNameConfig7Manager.getShortPoiName(basePoiName) != null){
            return poiNameToShortNameConfig7Manager.getShortPoiName(basePoiName);
        }
        return basePoiName;
    }


}
