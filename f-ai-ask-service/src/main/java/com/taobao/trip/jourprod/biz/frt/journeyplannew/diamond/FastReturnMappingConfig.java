package com.taobao.trip.jourprod.biz.frt.journeyplannew.diamond;

import com.alibaba.boot.diamond.annotation.DiamondListener;
import com.alibaba.boot.diamond.listener.DiamondDataCallback;
import com.alibaba.csp.ahas.switchcenter.shaded.com.alibaba.fastjson.JSON;
import com.alibaba.csp.ahas.switchcenter.shaded.com.alibaba.fastjson.TypeReference;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.CapsuleVO;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 快速返回文案映射配置
 **/
@DiamondListener(dataId = "fast-return-mapping.json",groupId = "f-ai-ask")
public class FastReturnMappingConfig implements DiamondDataCallback {

    /**
     * 第一层Key: 场景类型，如：READY_TO_PLAN
     * 第二层Key: 类型或者query，如：行程规划下的READY_TO_PLAN_MISSING_DESTINATION，公关梗下的query
     * Value: 对应的返回文案及胶囊列表，以公关梗举例如下：
     *      key：一丈红
     *      value：{"text":"xxxxxxxx", "capsuleList":[{"name":"《甄嬛传》取景地同款景点", "clickText":"《甄嬛传》取景地同款景点"}, {"name":"春季绝美赏花目的地", "clickText":"春季绝美赏花目的地"}]}
     */
    private Map<String, Map<String, Data>> config = new HashMap<>();

    @Override
    public void received(String config) {
        // 处理接收到的配置数据
        if (StringUtils.isBlank(config)) {
            return;
        }
        // 直接解析成Map<String, Map<String, PrTerrierData>>
        this.config = JSON.parseObject(config, new TypeReference<Map<String, Map<String, Data>>>() {});
    }
    
    public Data getData(String sceneCode, String type) {
        Map<String, Data> sceneMap = config.get(sceneCode);
        return sceneMap != null ? sceneMap.get(type) : null;
    }

    @lombok.Data
    public static class Data {
        /**
         * 【必填】返回的文案内容
         */
        private String text;

        /**
         * 【可选】胶囊列表
         */
        private List<CapsuleVO> capsuleList;
    }
}
