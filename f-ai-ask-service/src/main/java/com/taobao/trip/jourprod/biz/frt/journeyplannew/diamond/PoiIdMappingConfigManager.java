package com.taobao.trip.jourprod.biz.frt.journeyplannew.diamond;

import com.alibaba.boot.diamond.annotation.DiamondListener;
import com.alibaba.boot.diamond.listener.DiamondDataCallback;
import com.alibaba.csp.ahas.switchcenter.shaded.com.alibaba.fastjson.JSON;
import com.alibaba.csp.ahas.switchcenter.shaded.com.alibaba.fastjson.TypeReference;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import java.util.HashMap;
import java.util.Map;

/**
 * @description: 平台poiId映射配置
 * @author: huiyi
 * @create: 2025-04-08 14:16
 **/
@DiamondListener(dataId = "ai-poi-mapping-config",groupId = "f-ai-ask")
public class PoiIdMappingConfigManager implements DiamondDataCallback {
    /**
     * key: 翻译前的poiId
     * value: 翻译后的poiId
     */
    private Map<String, String> poiIdMappingConfig = new HashMap<>();

    @Override
    public void received(String config) {
        // 处理接收到的配置数据
        if (StringUtils.isBlank(config)) {
            return;
        }
        poiIdMappingConfig = JSON.parseObject(config, new TypeReference<Map<String, String>>() {});
    }

    public Map<String, String> getPoiIdMappingConfig() {
        return poiIdMappingConfig;
    }

    public Map<String, String> getMappingPoiIdConfig() {
        if (poiIdMappingConfig == null) {
            return new HashMap<>();
        }
        Map<String, String> mappingPoiIdConfig = new HashMap<>();
        poiIdMappingConfig.forEach((key, value) -> mappingPoiIdConfig.put(value, key));
        return mappingPoiIdConfig;
    }

    public String getPoiId(String mappingPoiId) {
        return MapUtils.getString(poiIdMappingConfig, mappingPoiId);
    }

    public String getMappingPoiId(String poiId) {
        if (StringUtils.isBlank(poiId)) {
            return null;
        }
        return MapUtils.getString(getMappingPoiIdConfig(), poiId);
    }
}
