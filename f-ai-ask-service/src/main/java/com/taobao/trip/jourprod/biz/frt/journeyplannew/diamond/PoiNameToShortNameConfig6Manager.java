package com.taobao.trip.jourprod.biz.frt.journeyplannew.diamond;

import java.util.HashMap;
import java.util.Map;

import com.alibaba.boot.diamond.annotation.DiamondListener;
import com.alibaba.boot.diamond.listener.DiamondDataCallback;
import com.alibaba.csp.ahas.switchcenter.shaded.com.alibaba.fastjson.JSON;
import com.alibaba.csp.ahas.switchcenter.shaded.com.alibaba.fastjson.TypeReference;

import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * @description: 平台poiId映射配置
 * @author: yuyang
 * @create: 2025-05-20 14:50
 **/
@DiamondListener(dataId = "poiNameToShortName6", groupId = "f-ai-ask")
public class PoiNameToShortNameConfig6Manager implements DiamondDataCallback {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(PoiNameToShortNameConfig6Manager.class);

    /**
     * key: 翻译前的poiId
     * value: 翻译后的poiId
     */
    private Map<String, String> poiNameToShortNameMap = new HashMap<>();

    @Override
    public void received(String config) {
        // 处理接收到的配置数据
        if (StringUtils.isBlank(config)) {
            return;
        }
        try{
            poiNameToShortNameMap = JSON.parseObject(config, new TypeReference<Map<String, String>>() {});
        }catch (Exception e){
            LOGGER.recordDangerException(new LogModel("PoiNameToShortNameConfig6Manager.received").e(e)
                .message("receiveError"));
        }
    }

    public String getShortPoiName(String mappingPoiId) {
        return MapUtils.getString(poiNameToShortNameMap, mappingPoiId);
    }

}
