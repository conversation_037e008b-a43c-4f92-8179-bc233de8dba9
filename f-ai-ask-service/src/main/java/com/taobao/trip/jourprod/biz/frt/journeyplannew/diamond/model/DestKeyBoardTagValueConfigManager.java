package com.taobao.trip.jourprod.biz.frt.journeyplannew.diamond.model;

import com.alibaba.boot.diamond.annotation.DiamondListener;
import com.alibaba.boot.diamond.listener.DiamondDataCallback;
import com.alibaba.csp.ahas.switchcenter.shaded.com.alibaba.fastjson.JSON;
import com.alibaba.csp.ahas.switchcenter.shaded.com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.KeyboardTagValueDTOV2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch.*;

/**
 * @description: 目的地 键盘配置 映射配置
 **/
@DiamondListener(dataId = "ai-dest-keyBoardValue-config", groupId = "f-ai-ask")
public class DestKeyBoardTagValueConfigManager implements DiamondDataCallback {

    private Map<String, List<KeyboardTagValueDiamondConfigDTO>> map = new HashMap<>();

    @Override
    public void received(String config) {
        // 处理接收到的配置数据
        if (StringUtils.isBlank(config)) {
            return;
        }
        map = JSON.parseObject(config, new TypeReference<>() {
        });
    }


    public List<KeyboardTagValueDTOV2> getOutPeopleKeyboardTagValueDTOV2List() {
        return getKeyboardTagValueDTOV2List(AI_KEYBOARD_TAGS_TITLE_OUT_PEOPLE);
    }

    public List<KeyboardTagValueDTOV2> getDestRangeKeyboardTagValueDTOV2List() {
        return getKeyboardTagValueDTOV2List(AI_KEYBOARD_TAGS_TITLE_DEST_RANGE);
    }

    /**
     * 获取配置
     *
     */
    public List<KeyboardTagValueDTOV2> getKeyboardTagValueDTOV2List(String key) {
        List<KeyboardTagValueDiamondConfigDTO> list = map.get(key);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream()
                .map(config -> KeyboardTagValueDTOV2.builder()
                        .name(config.getName())
                        .clickText(config.getClickText())
                        .icon(config.getIcon())
                        .build())
                .collect(Collectors.toList());
    }

}
