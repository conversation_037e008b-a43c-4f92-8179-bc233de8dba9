package com.taobao.trip.jourprod.biz.frt.journeyplannew.diamond.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 键盘标签值 对象
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class KeyboardTagValueDiamondConfigDTO {

    /**
     * 内容
     */
    private String name;

    /**
     * 点击内容
     */
    private String clickText;

    /**
     * 图标
     */
    private String icon;

    /**
     * 生效时间
     */
    private String startTime;

    /**
     * 失效时间
     */
    private String endTime;

}
