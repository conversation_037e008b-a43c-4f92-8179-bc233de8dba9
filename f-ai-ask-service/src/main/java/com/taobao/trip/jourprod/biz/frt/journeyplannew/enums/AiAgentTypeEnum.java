package com.taobao.trip.jourprod.biz.frt.journeyplannew.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ai agent枚举
 */
@Getter
@AllArgsConstructor
public enum AiAgentTypeEnum {

    JOURNEY_PLAN("route", "行程规划", "JOURNEY_PLAN"),
    HOTEL_BOOKING("hotel", "酒店预定", "HOTEL_BOOKING"),
    TRAFFIC("traffic", "交通", "TRAFFIC"),
    DESTINATION_RECOMMEND("dest", "目的地推荐", "DESTINATION_RECOMMEND"),
    HOTEL_PACKAGE_BOOKING("hotel_package", "酒店套餐预定", "HOTEL_PACKAGE_BOOKING"),
    POI("poi", "景点推荐",  "POI"),
    NOT_SUPPORT_SCENE("NOT_SUPPORT_SCENE", "不支持的场景", "NOT_SUPPORT_SCENE"),
    VISA("visa", "签证", "VISA"),




    TEST("TEST", "测试", "TEST"),

    ;


    /**
     * 类型，最终映射到行程规划 机票 火车 酒店 等具体场景
     */
    private final String type;

    /**
     * 说明
     */
    private final String desc;

    /**
     * 前端使用字段
     */
    private final String codeToFront;

}
