package com.taobao.trip.jourprod.biz.frt.journeyplannew.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ai行程规划初始化页面的欢迎类型
 */
@Getter
@AllArgsConstructor
public enum AiJourneyInitPageWelcomeTypeEnum {

    JOURNEY_PLAN("journeyPlan", "行程规划普通类型"),

    JOURNEY_IN_TRANSIT("journeyInTransit", "行程行中类型"),

    HOMEPAGE_COMMON("homepageCommon", "首页常规类型"),

    HOMEPAGE_IN_TRANSIT("homepageCommon", "首页行中类型"),
    ;

    /**
     * code
     */
    private final String code;

    /**
     * 名称
     */
    private final String name;

}
