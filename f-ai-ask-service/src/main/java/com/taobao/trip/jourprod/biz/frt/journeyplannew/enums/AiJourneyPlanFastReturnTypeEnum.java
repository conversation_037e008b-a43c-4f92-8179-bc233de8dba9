package com.taobao.trip.jourprod.biz.frt.journeyplannew.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * AI问一问-快速返回文案的类型枚举
 */
@Getter
@AllArgsConstructor
public enum AiJourneyPlanFastReturnTypeEnum {

    /**
     * 酒店相关场景
     */
    HOTEL_MISSING_DESTINATION("HOTEL_MISSING_DESTINATION", "酒店提问输入缺少目的地"),

    /**
     * 行程规划相关场景
     */
    READY_TO_PLAN_MISSING_DESTINATION("READY_TO_PLAN_MISSING_DESTINATION", "行程规划提问输入缺少目的地"),

    /**
     * 公关梗相关场景
     */
    PR_TERRIER_YIZHANGHONG("PR_TERRIER_YIZHANGHONG", "公关梗里提及到一丈红"),

    ;

    /**
     * 出行场景
     */
    private final String type;

    /**
     * 场景名称
     */
    private final String desc;

    /**
     * 根据场景code获取场景
     */
    public static AiJourneyPlanFastReturnTypeEnum getByCode(String sceneCode) {
        for (AiJourneyPlanFastReturnTypeEnum aiJourneyPlanSceneEnum : AiJourneyPlanFastReturnTypeEnum.values()) {
            if (aiJourneyPlanSceneEnum.getType().equals(sceneCode)) {
                return aiJourneyPlanSceneEnum;
            }
        }
        return null;
    }

}
