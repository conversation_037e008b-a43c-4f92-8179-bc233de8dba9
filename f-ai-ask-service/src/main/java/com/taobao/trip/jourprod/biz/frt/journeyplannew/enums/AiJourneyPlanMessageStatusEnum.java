package com.taobao.trip.jourprod.biz.frt.journeyplannew.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ai行程规划消息状态
 */
@Getter
@AllArgsConstructor
public enum AiJourneyPlanMessageStatusEnum {

    NORMAL("normal", "正常"),
    CANCEL("cancel", "取消"),

    BLOCK("block", "风控拦截"),
    ;

    /**
     * 状态code
     */
    private final String statusCode;

    /**
     * 状态名称
     */
    private final String statusName;

}
