package com.taobao.trip.jourprod.biz.frt.journeyplannew.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ai行程规划场景
 */
@Getter
@AllArgsConstructor
public enum AiJourneyPlanSceneEnum {

    /**
     * 行程规划相关场景
     */
    READY_TO_PLAN("READY_TO_PLAN", "可以做行程规划", AiAgentTypeEnum.JOURNEY_PLAN),
    TRAFFIC("TRAFFIC", "交通", AiAgentTypeEnum.TRAFFIC),
    HOTEL_BOOKING("HOTEL_BOOKING", "酒店预定", AiAgentTypeEnum.HOTEL_BOOKING),
    HOTEL_PACKAGE_BOOKING("HOTEL_PACKAGE_BOOKING", "酒店套餐预定", AiAgentTypeEnum.HOTEL_PACKAGE_BOOKING),
    DESTINATION_RECOMMEND("DESTINATION_RECOMMEND", "目的地推荐", AiAgentTypeEnum.DESTINATION_RECOMMEND),
    VISA("VISA", "签证", AiAgentTypeEnum.VISA),
    SCENIC_RECOMMEND("SCENIC_RECOMMEND", "景点推荐", AiAgentTypeEnum.POI),
    NOT_SUPPORT_SCENE("NOT_SUPPORT_SCENE", "不支持的场景", AiAgentTypeEnum.NOT_SUPPORT_SCENE),
    BLACK_USER_LIST("BLACK_USER_LIST", "黑名单用户", AiAgentTypeEnum.NOT_SUPPORT_SCENE),

    PR_TERRIER("PR_TERRIER", "公关梗", AiAgentTypeEnum.NOT_SUPPORT_SCENE),



    TEST("TEST", "测试", AiAgentTypeEnum.TEST),

    ;

    /**
     * 出行场景
     */
    private final String sceneCode;

    /**
     * 场景名称
     */
    private final String sceneName;

    /**
     * 类型，最终映射到行程规划 机票 火车 酒店 等具体场景
     */
    private final AiAgentTypeEnum agentType;

    /**
     * 根据场景code获取场景
     */
    public static AiJourneyPlanSceneEnum getByCode(String sceneCode) {
        for (AiJourneyPlanSceneEnum aiJourneyPlanSceneEnum : AiJourneyPlanSceneEnum.values()) {
            if (aiJourneyPlanSceneEnum.getSceneCode().equals(sceneCode)) {
                return aiJourneyPlanSceneEnum;
            }
        }
        return NOT_SUPPORT_SCENE;
    }

}
