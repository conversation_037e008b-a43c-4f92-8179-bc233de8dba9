package com.taobao.trip.jourprod.biz.frt.journeyplannew.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ai行程规划状态
 */
@Getter
@AllArgsConstructor
public enum AiJourneyPlanStatusEnum {

    GENERATE_SCENE("GENERATE_SCENE", "生成场景"),
    GENERATE_CHAT_CONTENT("GENERATE_CHAT_CONTENT", "生成对话内容"),
    GENERATE_OVERVIEW("GENERATE_OVERVIEW", "生成总结"),
    END("END", "结束"),
    STOP("STOP", "停止"),
    BLOCK("BLOCK", "风控拦截")
    ;

    /**
     * 状态code
     */
    private final String statusCode;

    /**
     * 状态名称
     */
    private final String statusName;

    public static AiJourneyPlanStatusEnum codeOf(String statusCode) {
        for (AiJourneyPlanStatusEnum value : values()) {
            if (value.getStatusCode().equals(statusCode)) {
                return value;
            }
        }
        return null;
    }

}
