package com.taobao.trip.jourprod.biz.frt.journeyplannew.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@Getter
public enum AiPoiBySourceTypeEnum {

    TPP_CONTENT("TPP_CONTENT", "tpp内容"),
    GAO_DE_AROUND("GAO_DE_AROUND", "高德周边"),
    JOURNEY_LINE("JOURNEY_LINE", "路线库"),
    QP("QP", "猪搜"),
    POI_PREDICT("POI_PREDICT", "poi小模型"),

    ;

    public static AiPoiBySourceTypeEnum codeOf(String code) {
        for (AiPoiBySourceTypeEnum fliggyPoiCategoryEnum : AiPoiBySourceTypeEnum.values()) {
            if (StringUtils.equals(fliggyPoiCategoryEnum.getCode(), code)) {
                return fliggyPoiCategoryEnum;
            }
        }
        return null;
    }

    private final String code;

    private final String desc;
}
