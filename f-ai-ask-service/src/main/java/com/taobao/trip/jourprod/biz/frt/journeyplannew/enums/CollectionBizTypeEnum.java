package com.taobao.trip.jourprod.biz.frt.journeyplannew.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CollectionBizTypeEnum {

    POI("poi", "poiId"),

    HOTEL("hotel", "shid"),

    ITEM("item", "itemId"),

    FLIGHT("flight", "bizId"),

    TRAIN("train", "bizId"),

    DYNAMIC("", "")
    ;

    private final String bizType;

    private final String defaultBizIdColumn;
}
