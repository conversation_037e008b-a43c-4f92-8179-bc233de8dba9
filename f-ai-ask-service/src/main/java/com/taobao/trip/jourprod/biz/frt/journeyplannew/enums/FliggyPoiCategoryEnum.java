package com.taobao.trip.jourprod.biz.frt.journeyplannew.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum FliggyPoiCategoryEnum {

    TRAFFIC(1L, "交通", "traffic"),
    HOTEL(4L, "住宿", "hotel"),
    SCENIC(5L, "景点", "scenic"),
    SHOPPING(6L, "购物", "shopping"),
    FOOD(7L, "美食", "food"),
    PLAY(9L, "玩乐", "play"),
    TRAVEL_SERVICE(11L, "旅行服务", "travelService"),
    EDUCATION(12L, "教育", "education"),
    OTHER(100L, "其他", "other"),
    NULL(-1L, null, null)
    ;

    public static FliggyPoiCategoryEnum codeOf(Long code) {
        for (FliggyPoiCategoryEnum fliggyPoiCategoryEnum : FliggyPoiCategoryEnum.values()) {
            if (fliggyPoiCategoryEnum.getCode() == code) {
                return fliggyPoiCategoryEnum;
            }
        }
        return NULL;
    }

    private final long code;

    private final String name;

    private final String nameEn;
}
