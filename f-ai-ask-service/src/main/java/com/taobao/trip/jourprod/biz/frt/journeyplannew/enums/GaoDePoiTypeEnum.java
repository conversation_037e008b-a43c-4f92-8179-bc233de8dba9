package com.taobao.trip.jourprod.biz.frt.journeyplannew.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@Getter
public enum GaoDePoiTypeEnum {

    DEFAULT("", ""),

    CTFW("050000", "餐饮服务"),
    ZCT("050100", "中餐厅"),
    ZHJL("050101", "综合酒楼"),
    SCC("050102", "四川菜(川菜)"),
    GDC("050103", "广东菜(粤菜)"),
    SDC("050104", "山东菜(鲁菜)"),
    JSC("050105", "江苏菜"),
    ZJC("050106", "浙江菜"),
    SHC("050107", "上海菜"),
    HNC("050108", "湖南菜(湘菜)"),
    AHC("050109", "安徽菜(徽菜)"),
    FJC("050110", "福建菜"),
    BJC("050111", "北京菜"),
    HBC("050112", "湖北菜(鄂菜)"),
    DBC("050113", "东北菜"),
    YGC("050114", "云贵菜"),
    XBC("050115", "西北菜"),
    LZH("050116", "老字号"),
    HGD("050117", "火锅店"),
    TSCT("050118", "特色/地方风味餐厅"),
    HXJL("050119", "海鲜酒楼"),
    ZSSCG("050120", "中式素菜馆"),
    QZCG("050121", "清真菜馆"),
    TWC("050122", "台湾菜"),
    CZC("050123", "潮州菜"),
    WGCT("050200", "外国餐厅"),
    XCT("050201", "西餐厅(综合风味)"),
    RBLL("050202", "日本料理"),
    HGLL("050203", "韩国料理"),
    FSCT("050204", "法式菜品餐厅"),
    YSCT("050205", "意式菜品餐厅"),
    TGYNCT("050206", "泰国/越南菜品餐厅"),
    DZHCX("050207", "地中海风格菜品"),
    MSFW("050208", "美式风味"),
    YDFW("050209", "印度风味"),
    YGSCT("050210", "英国式菜品餐厅"),
    NPF("050211", "牛扒店(扒房)"),
    EGC("050212", "俄国菜"),
    PGC("050213", "葡国菜"),
    DGC("050214", "德国菜"),
    BXC("050215", "巴西菜"),
    MXGC("050216", "墨西哥菜"),
    QTYZC("050217", "其它亚洲菜"),
    KCT("050300", "快餐厅"),
    KDD("050301", "肯德基"),
    MDL("050302", "麦当劳"),
    BSK("050303", "必胜客"),
    YHDJ("050304", "永和豆浆"),
    CCT("050305", "茶餐厅"),
    DJL("050306", "大家乐"),
    DKH("050307", "大快活"),
    MX("050308", "美心"),
    JYJ("050309", "吉野家"),
    XJY("050310", "仙跡岩"),
    XPXP("050311", "呷哺呷哺"),
    XXCTCH("050400", "休闲餐饮场所"),
    HFT("050500", "咖啡厅"),
    XBK("050501", "星巴克咖啡"),
    SDKP("050502", "上岛咖啡"),
    PCC("050503", "Pacific Coffee Company"),
    BLKFD("050504", "巴黎咖啡店"),
    CYG("050600", "茶艺馆"),
    LLD("050700", "冷饮店"),
    GBD("050800", "糕饼店"),
    TPD("050900", "甜品店"),

    LYJD("110000", "旅游景点"),
    GYGC("110100", "公园广场"),
    GY("110101", "公园"),
    DWY("110102", "动物园"),
    ZWY("110103", "植物园"),
    SZG("110104", "水族馆"),
    CSGC("110105", "城市广场"),
    GYNBSS("110106", "公园内部设施"),
    FJMS("110200", "风景名胜"),
    SJYC("110201", "世界遗产"),
    GJJJD("110202", "国家级景点"),
    SJJD("110203", "省级景点"),
    JNG("110204", "纪念馆"),
    SMDG("110205", "寺庙道观"),
    JT("110206", "教堂"),
    HJS("110207", "回教寺"),
    HT("110208", "海滩"),
    GJD("110209", "观景点"),
    HSJQ("110210", "红色景区");

    public static GaoDePoiTypeEnum nameOf(String name) {
        for (GaoDePoiTypeEnum fliggyPoiCategoryEnum : GaoDePoiTypeEnum.values()) {
            if (StringUtils.equals(fliggyPoiCategoryEnum.getName(), name)) {
                return fliggyPoiCategoryEnum;
            }
        }
        return DEFAULT;
    }

    private final String code;

    private final String name;
}
