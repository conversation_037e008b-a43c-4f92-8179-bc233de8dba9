package com.taobao.trip.jourprod.biz.frt.journeyplannew.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@Getter
public enum QpContentPoiSourceTypeEnum {

    POI("poi", "poi"),
    gd("gd", "gaode"),

    ;

    public static QpContentPoiSourceTypeEnum preOf(String pre) {
        for (QpContentPoiSourceTypeEnum fliggyPoiCategoryEnum : QpContentPoiSourceTypeEnum.values()) {
            if (StringUtils.equals(fliggyPoiCategoryEnum.getPre(), pre)) {
                return fliggyPoiCategoryEnum;
            }
        }
        return POI;
    }

    private final String pre;

    private final String sourceType;
}
