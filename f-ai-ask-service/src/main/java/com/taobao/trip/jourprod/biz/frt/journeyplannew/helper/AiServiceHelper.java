package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;

import com.alibaba.dashscope.app.Application;
import com.alibaba.dashscope.app.ApplicationParam;
import com.alibaba.dashscope.app.ApplicationResult;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;

import com.taobao.ateye.annotation.AteyeInvoker;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import io.reactivex.Flowable;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @Description 调用大模型服务
 * <AUTHOR>
 * @Date 2025/3/1
 **/
@Component
public class AiServiceHelper {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(AiServiceHelper.class);

    /**
     * 直接调用大模型
     */
    @AteyeInvoker(description = "测试调用大模型", paraDesc = "prompt&appId")
    public String testCallApplication(String prompt, String appId) throws NoApiKeyException, InputRequiredException {
        // 请求大模型
        ApplicationParam param = ApplicationParam.builder()
            // 若没有配置环境变量，可用百炼API Key将下行替换为：.apiKey("sk-xxx")。但不建议在生产环境中直接将API Key硬编码到代码中，以减少API Key泄露风险。
            .apiKey(JourneyPlanAiAssistantHelper.AI_JOURNEY_PLAN_APP_KEY)
            // 替换为实际的应用 ID
            .appId(appId)
            .prompt(prompt)
            .incrementalOutput(true)
            .hasThoughts(true)
            .build();
        Application application = new Application();
        long start = System.currentTimeMillis();
        try {
            LOGGER.recordEntry(new LogModel("testCallApplication")
                .message("开始回答-{0}", prompt));
            // .streamCall（）：流式输出内容
            Flowable<ApplicationResult> result = application.streamCall(param);
            StringBuffer stringBuffer = new StringBuffer();
            result.blockingForEach(data -> {
                String text = data.getOutput().getText();
                if (StringUtils.isNotEmpty(text)) {
                    stringBuffer.append(text);
                }
            });
            return stringBuffer.toString();
        } finally {
            // 记录日志
            LOGGER.recordOutput(new LogModel("testCallApplication")
                .message("prompt-{0}", prompt)
                .cost(System.currentTimeMillis() - start));
        }
    }

}
