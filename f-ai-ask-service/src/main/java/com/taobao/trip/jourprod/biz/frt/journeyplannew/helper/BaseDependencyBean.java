package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;

import com.taobao.trip.jourprod.biz.frt.journeyplannew.JourneyPlanAiAssistantFrt;
import com.taobao.trip.jourprod.common.sal.tair.impl.LdbTairManager;
import com.taobao.trip.jourprod.common.sal.tair.impl.MdbTairCommonHelper;
import com.taobao.trip.jourprod.service.impl.journeyplannew.JourneyAssistantDataOperateService;
import org.springframework.context.annotation.Lazy;

import javax.annotation.Resource;

public class BaseDependencyBean {

    @Lazy
    @Resource
    protected JourneyPlanAiAssistantFrt journeyPlanAiAssistantFrt;

    @Resource
    protected JourneyPlanAiAssistantHelper journeyPlanAiAssistantHelper;

    @Resource
    protected JourneyPlanAiAssistantMessageStatusHelper messageStatusHelper;

    @Resource
    protected JourneyPlanAiAssistantContentHelper journeyPlanAiAssistantContentHelper;

    @Resource
    protected JourneyPlanAiAssistantSessionHelper journeyPlanAiAssistantSessionHelper;

    @Resource
    protected JourneyAssistantDataOperateService journeyAssistantDataOperateFacade;

    @Resource
    protected JourneyPlanAiAssistantShareHelper journeyPlanAiAssistantShareHelper;

    @Resource
    protected MdbTairCommonHelper mdbTairCommonHelper;
    @Resource
    protected LdbTairManager ldbTairManager;

    @Resource
    protected JourneyPlanAiAssistantWhiteHelper journeyPlanAiAssistantWhiteHelper;

    @Resource
    protected JourneyInTransitServiceHelper journeyInTransitServiceHelper;

    @Resource
    protected JourneyPlanAiAssistantMessageHelper journeyPlanAiAssistantMessageHelper;

    @Resource
    protected JourneyPlanAiAssistantTppHelper journeyPlanAiAssistantTppHelper;
}
