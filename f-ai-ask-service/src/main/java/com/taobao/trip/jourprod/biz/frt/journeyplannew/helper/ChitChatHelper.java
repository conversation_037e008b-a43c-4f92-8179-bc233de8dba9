package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import javax.annotation.Resource;

import com.alibaba.dashscope.app.Application;
import com.alibaba.dashscope.app.ApplicationParam;
import com.alibaba.dashscope.app.ApplicationResult;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.mtop3.invocation.MtopStream;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonObject;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanMessageStatusEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneySceneModel;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatRequest;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import io.reactivex.Flowable;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date: 2025/4/12 14:12
 */
@Component
public class ChitChatHelper {
    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(ChitChatHelper.class);

    // 需要过滤的节点ID列表
    private static final Set<String> FILTER_NODE_IDS = new HashSet<>(Arrays.asList("Start_bYxoRU", "LLM_XKdu", "Judge_5xvz"));
    @Resource
    private JourneyPlanAiAssistantSessionHelper journeyPlanAiAssistantSessionHelper;

    public boolean chitChat(JourneyPlanAiChatRequest request, AiJourneySceneModel aiJourneySceneModel, MtopStream mtopStream, ChatContext chatContext) {
        Long userId = request.getUserId();
        String appId = AiJourneySwitch.OTHER_SCENE_CHIT_CHAT_APP_ID;
        String userPrompt = request.getChat();
        String sessionId = chatContext.getSessionId();
        String type = chatContext.getAiJourneySceneModel().getType();
        ApplicationParam param = buildParam(appId, userPrompt, sessionId, type);
        if (!AiJourneySwitch.IS_STREAM_OUTPUT) {
            String answer = getAnswer(userId, param);
            if (StringUtils.isBlank(answer)) {
                return false;
            }
            chatContext.setOriginalContent(answer);
            // 流式输出
            StreamMessageResult result = new StreamMessageResult(chatContext.getSystemMessageId(), chatContext.getUserMessageId(), null, answer, AiJourneyPlanMessageStatusEnum.NORMAL.getStatusCode());
            mtopStream.writeAndEnd(JSON.toJSONString(result));
            return true;
        }else{
            param.setIncrementalOutput(true);
            param.setHasThoughts(true);
            Application application = new Application();
            Flowable<ApplicationResult> result = null;
            try {
                result = application.streamCall(param);
                result.blockingForEach(data -> {
                    String dataStr = JSON.toJSONString(data);
                    if (StringUtils.isNotEmpty(dataStr)) {
                        String ans = NodeResultExtractor.extractNodeResults(dataStr);
                        if (StringUtils.isNotBlank(ans)) {
                            StreamMessageResult streamRest = new StreamMessageResult(chatContext.getSystemMessageId(), chatContext.getUserMessageId(), null, ans, AiJourneyPlanMessageStatusEnum.NORMAL.getStatusCode());
                            mtopStream.write(JSON.toJSONString(streamRest));
                        }
                    }
                });
                mtopStream.end();
            } catch (Exception e) {
                return false;
            }
            return true;
        }
    }


    public ApplicationParam buildParam(String appId, String userInput, String sessionId, String type) {
        JsonObject bizParam = new JsonObject();
        bizParam.addProperty("type", type);
        ApplicationParam param = ApplicationParam.builder()
            //key
            .apiKey(JourneyPlanAiAssistantHelper.AI_JOURNEY_PLAN_APP_KEY)
            //id
            .appId(appId)
            //用户输入
            .prompt(userInput)
            //session
            .sessionId(sessionId).bizParams(bizParam)
            //参数
            .parameter("type", type).build();
        return param;
    }

    public String getAnswer(Long userId, ApplicationParam param) {
        Application application = new Application();
        try {
            ApplicationResult call = application.call(param);
            journeyPlanAiAssistantSessionHelper.updateSessionIdIfNeed(userId, param.getAppId(), call.getOutput().getSessionId(), param.getSessionId());
            return extractAnswer(call.getOutput().getText());
        } catch (Throwable e) {
            LOGGER.recordNormalException(new LogModel("chitChat").e(e).message("兜底闲聊失败"));
            return null;
        }
    }

    public String extractAnswer(String s) {
        if (StringUtils.isNotEmpty(s)) {
            JSONObject jsonObject = JSON.parseObject(s);
            return jsonObject.getString("answer");
        }
        return null;
    }
}
