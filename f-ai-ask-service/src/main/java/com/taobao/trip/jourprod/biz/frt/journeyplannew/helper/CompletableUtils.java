package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;

import com.google.common.collect.Lists;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Supplier;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Completable编排封装 方便简单调用
 * <p>
 * execute处理异常和回调
 *
 * <AUTHOR>
 */

@SuppressWarnings("unchecked")
public class CompletableUtils {

    public static CompletableInstance getInstance(Executor executor, Logger logger) {
        if (Objects.isNull(executor)) {
            throw new IllegalArgumentException("executor is null");
        }

        CompletableInstance completableInstance = new CompletableInstance(executor);
        completableInstance.errorLogger = logger;
        return completableInstance;
    }


    /**
     * 获取并发编排工具实例（推荐使用）
     *
     * @param executor   线程池
     * @param loggerName 日志名称
     * @return 并发编排工具
     */
    public static CompletableInstance getInstance(Executor executor, String loggerName) {
        if (Objects.isNull(executor)) {
            throw new IllegalArgumentException("executor is null");
        }
        if (StringUtils.isBlank(loggerName)) {
            throw new IllegalArgumentException("日志名称不能为空");
        }
        CompletableInstance completableInstance = new CompletableInstance(executor);
        completableInstance.errorLogger = LoggerFactory.getLogger(loggerName);
        return completableInstance;
    }

    public static class CompletableInstance {
        private Logger errorLogger;


        /**
         * 防止并发添加任务时丢失任务 故使用copyOnWriteList保障简单线程安全
         */
        private final List<CompletableFuture<?>> completableFutureList = Lists.newCopyOnWriteArrayList();

        private final Executor executor;


        public <T> CompletableFuture<T> executeCompletableFuture(CompletableFuture<T> completableFuture) {
            completableFutureList.add((CompletableFuture<Object>) completableFuture);
            return completableFuture;
        }

        /**
         * @param supplier 调用方法
         * @param executor 执行线程池
         * @param <T>      方法类型
         * @return
         */
        public <T> CompletableFuture<T> executeCompletableFuture(Supplier<T> supplier, Executor executor) {
            CompletableFuture<T> completableFuture = CompletableFuture.supplyAsync(supplier, executor);
            completableFutureList.add((CompletableFuture<Object>) completableFuture);
            return completableFuture;
        }

        /**
         * 添加并发编排的任务
         *
         * @param supplier 调用方法
         * @param <T>      方法类型
         * @return
         */
        public <T> CompletableFuture<T> executeCompletableFuture(Supplier<T> supplier) {
            if (Objects.isNull(executor)) {
                throw new IllegalStateException("请先初始化线程池");
            }
            CompletableFuture<T> completableFuture = CompletableFuture.supplyAsync(supplier, executor);
            completableFutureList.add((CompletableFuture<Object>) completableFuture);
            return completableFuture;
        }

        /**
         * 批量添加并发编排的任务
         *
         * @param completableFutureList 批量任务
         * @return
         */
        public boolean executeCompletableFutureList(List<CompletableFuture<Object>> completableFutureList) {
            if (CollectionUtils.isEmpty(completableFutureList)) {
                return true;
            }
            return this.completableFutureList.addAll(completableFutureList);
        }

        /**
         * 所有任务完成后执行编排任务异常处理
         */
        public void end() {
            end((throwable) -> {
                errorLogger.error("CompletableExecErr , msg:{}", throwable.getMessage(), throwable);
            });
        }

        /**
         * 所有任务完成后执行编排任务异常处理
         *
         * @param consumer 异常时的处理
         */
        public void end(Consumer<Throwable> consumer) {
            try {
                CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0]))
                    .exceptionally(
                        throwable -> {
                            consumer.accept(extractRealException(throwable));
                            //异常就返回空数据
                            return null;
                        }
                    ).join();
            } finally {
                completableFutureList.clear();
            }
        }

        /**
         * 所有任务完成后执行编排任务异常处理
         * 设置超时时间
         *
         * @param consumer 异常时的处理
         */
        public void end(Consumer<Throwable> consumer, long timeout, TimeUnit unit) throws Exception {
            try {
                CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0]))
                        .exceptionally(
                                throwable -> {
                                    consumer.accept(extractRealException(throwable));
                                    //异常就返回空数据
                                    return null;
                                }
                        ).get(timeout, unit);
            } catch (Throwable e) {
                throw new Exception(e.getMessage());
            } finally {
                completableFutureList.clear();
            }
        }

        /**
         * 执行编排任务 并在各个任务完成后执行reduce方法
         *
         * @param combineSupplier reduce方法
         * @param <T>
         */
        public <T> void doCombineExecute(Supplier<T> combineSupplier) {
            doCombineExecute(combineSupplier, (throwable) -> {
                errorLogger.error("CombineCompletableExecErrExecErr , msg:{}", throwable.getMessage(), throwable);
            });
        }

        /**
         * 执行编排任务 并在各个任务完成后执行reduce方法
         *
         * @param combineSupplier   各个任务执行完后调用的方法
         * @param exceptionConsumer 异常时的处理
         * @param <T>
         */
        public <T> void doCombineExecute(Supplier<T> combineSupplier, Consumer<Throwable> exceptionConsumer) {
            CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0]))
                    .exceptionally(
                            throwable -> {
                                exceptionConsumer.accept(extractRealException(throwable));
                                //异常就返回空数据
                                return null;
                            }
                    ).thenApply((unused) -> combineSupplier.get()).join();

            completableFutureList.clear();
        }


        /**
         * 提取真正的异常
         */
        private Throwable extractRealException(Throwable throwable) {
            if (throwable instanceof CompletionException || throwable instanceof ExecutionException) {
                if (throwable.getCause() != null) {
                    return throwable.getCause();
                }
            }
            return throwable;
        }

        private CompletableInstance(Executor executor) {
            this.executor = executor;
        }

    }
}
