package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.StringJoiner;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 脱敏工具
 */
@Slf4j
public class DesensitizationUtil {

    /***
     * 身份证18位匹配正则
     * xxxxxx yyyy MM dd 375 0     十八位
     *
     * 地区：[1-9]\d{5}
     * 年份 \d{4}
     * 月份 ((0[1-9])|(10|11|12))
     * 天数 (([0-2][1-9])|10|20|30|31)
     * 三位顺序码 \d{3}
     * 校验码 [0-9Xx]
     */
    public static final String ID_CARD_EIGHTEEN = "([1-9]\\d{5}\\d{4}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx])";

    /***
     * 身份证15位匹配正则
     * xxxxxx    yy MM dd   75 0     十五位
     *
     * 地区：[1-9]\d{5}
     * 年份 \d{2}
     * 月份 ((0[1-9])|(10|11|12))
     * 天数 (([0-2][1-9])|10|20|30|31)
     * 两位位顺序码 \d{2}
     * 校验码 [0-9Xx]
     */
    public static final String ID_CARD_FIFTEEN = "([1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{2}[0-9Xx])";

    /***
     * 手机号匹配正则
     *
     * 13
     * 14
     * 15
     * 16
     * 17
     * 18
     * 19
     * 8位 \d{8}
     */
    public static final String PHONE = "((13[0-9]|14[********]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8})";

    public static final String ORDER = "(\\d{19})";

    private static final String REGEX;

    private static final Pattern REGEX_PATTERN;

    private static final Pattern EMAIL_PATTERN = Pattern.compile("(\\w{3})\\w+(?=@[^@]+)");
    private static final Pattern PHONE_PATTERN = Pattern.compile("(0\\d{2,3}-\\d{4})\\d{3,4}");

    private static final Pattern BANK_CARD_ID_PATTERN = Pattern.compile("(\\d{4})\\d{9,12}(\\d{3})"); // 16 17 18 19位


    static {
        StringJoiner stringJoiner = new StringJoiner("|");
        stringJoiner.add(ID_CARD_EIGHTEEN);
        stringJoiner.add(ID_CARD_FIFTEEN);
        stringJoiner.add(PHONE);
        stringJoiner.add(ORDER);
        stringJoiner.add("(\\w{18})"); // 税号 91320213MA1YMX6C61
        REGEX = stringJoiner.toString();
        REGEX_PATTERN = Pattern.compile(REGEX);
    }

    /***
     * 文本数据脱敏
     * 支持身份证18、15位，手机号
     *
     * @param content 数据文本
     * @return 文本
     */
    public static String desensitization(String content){
        if (StrUtil.isEmpty(content)) {
            return content;
        }
        String res = ReUtil.replaceAll(content, REGEX_PATTERN, matcher -> {
            try {
                if (CharSequenceUtil.isBlank(matcher.group())) {
                    return "";
                }
                if (matcher.group().length() == 18) {
                    // 18位身份证处理 & 税号处理
                    return CharSequenceUtil.hide(matcher.group(), 0, 12);
                } else if (matcher.group().length() == 15) {
                    // 15位身份证处理
                    return CharSequenceUtil.hide(matcher.group(), 0, 9);
                } else if (matcher.group().length() == 11) {
                    // 11位手机号处理
                    return CharSequenceUtil.hide(matcher.group(), 3, 7);
                } else if (matcher.group().length() == 19) {
                    // 19位订单号处理
                    return CharSequenceUtil.hide(matcher.group(), 0, 13);
                }
                return matcher.group();
            } catch (Exception e) {
                return "";
            }
        });

        res = desensitizeEmail(res);
        res = desensitizePhoneNumber(res);
        res = desensitizeBankCardId(res);

        return res;
    }

    public static String desensitizeEmail(String email) {
        try {
            Matcher matcher = EMAIL_PATTERN.matcher(email);
            return matcher.replaceAll("$1***");
        } catch (Exception ignored) {
            log.error("desensitizeEmail error, email:{}", email);
        }
        return email;
    }

    public static String desensitizePhoneNumber(String str) {
        try {
            Matcher matcher = PHONE_PATTERN.matcher(str);
            return matcher.replaceAll("$1****");
        } catch (Exception ignored) {
            log.error("desensitizePhoneNumber error, str:{}", str);
        }
        return str;
    }

    public static String desensitizeBankCardId(String str) {
        try {
            Matcher matcher = BANK_CARD_ID_PATTERN.matcher(str);
            return matcher.replaceAll("$1**********$2");
        } catch (Exception ignored) {
            log.error("desensitizePhoneNumber error, str:{}", str);
        }
        return str;
    }


    public static void main(String[] args) {
        System.out.println(desensitization("公司名称：北京水滴科技集团有限公司 统一社会信用代码：91110105MA01F4NU2H 地址：北京市朝阳区利泽中二路2号C座二层201电\n" +
            "话：010-******** 开户行：宁波银行股份有限公司北京望京支行 账号：770601220020112286"));
    }

}