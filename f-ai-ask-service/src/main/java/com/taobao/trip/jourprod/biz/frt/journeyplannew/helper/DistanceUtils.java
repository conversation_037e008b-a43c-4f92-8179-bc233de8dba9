package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;

public class DistanceUtils {

    // 地球半径（单位：公里）
    private static final double EARTH_RADIUS = 6371.0;

    /**
     * 计算两个经纬度点之间的距离
     *
     * @param startLocation 起点的经纬度字符串，格式为 "longitude,latitude"
     * @param endLocation   终点的经纬度字符串，格式为 "longitude,latitude"
     * @return 两点之间的距离（单位：公里）
     */
    public static Double calculateDistance(String startLocation, String endLocation) {
        double[] startPoint = parseLocation(startLocation);
        double[] endPoint = parseLocation(endLocation);

        if (startPoint == null || endPoint == null) {
            return null;
        }

        return calculateDistance(startPoint[1], startPoint[0], endPoint[1], endPoint[0]);
    }

    /**
     * 将距离（单位：公里）转化为纯文本描述
     *
     * @param distance 距离（单位：公里）
     * @return 格式化后的距离描述（例如："500m"、"2.5km"、"100km+"）
     */
    public static String formatDistance(double distance) {
        if (distance < 0) {
            throw new IllegalArgumentException("距离不能为负数");
        }

        if (distance < 1) {
            // 小于 1 公里，转换为米并取整
            int meters = (int) Math.round(distance * 1000);
            return meters + "m";
        } else if (distance < 100) {
            // 大于等于 1 公里且小于 100 公里，保留一位小数
            return String.format("%.1fkm", distance);
        } else {
            // 大于等于 100 公里，显示为 "100km+"
            return "100km+";
        }
    }

    /**
     * 将距离（单位：米）转化为纯文本描述
     *
     * @param distance 距离（单位：米）
     * @return 格式化后的距离描述（例如："500m"、"2.5km"、"100km+"）
     */
    public static String formatDistanceM(int distance) {
        if (distance < 0) {
            throw new IllegalArgumentException("距离不能为负数");
        }
        double km = distance / 1000.0; // 转换为公里
        if (km < 1) {
            return distance + "m"; // 小于 1 公里，直接显示米数
        } else if (km < 100) {
            return String.format("%.1fkm", km); // 大于等于 1 公里且小于 100 公里，保留一位小数
        } else {
            return "100km+"; // 大于等于 100 公里
        }
    }

    /**
     * 解析经纬度字符串
     *
     * @param location 经纬度字符串，格式为 "longitude,latitude"
     * @return 包含经度和纬度的数组 [longitude, latitude]
     */
    private static double[] parseLocation(String location) {
        String[] parts = location.split(",");
        if (parts.length != 2) {
            return null;
        }
        try {
            double longitude = Double.parseDouble(parts[0].trim());
            double latitude = Double.parseDouble(parts[1].trim());
            return new double[]{longitude, latitude};
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 使用 Haversine 公式计算两点之间的距离
     *
     * @param startLng 起点经度（单位：度）
     * @param startLat 起点纬度（单位：度）
     * @param endLng   终点经度（单位：度）
     * @param endLat   终点纬度（单位：度）
     * @return 两点之间的距离（单位：公里）
     */
    private static double calculateDistance(double startLng, double startLat, double endLng, double endLat) {
        // 将经纬度从度转换为弧度
        double lat1 = Math.toRadians(startLat);
        double lng1 = Math.toRadians(startLng);
        double lat2 = Math.toRadians(endLat);
        double lng2 = Math.toRadians(endLng);

        // Haversine 公式的实现
        double deltaLat = lat2 - lat1;
        double deltaLng = lng2 - lng1;

        double a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
                Math.cos(lat1) * Math.cos(lat2) *
                        Math.sin(deltaLng / 2) * Math.sin(deltaLng / 2);

        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        // 计算距离
        return EARTH_RADIUS * c;
    }


    /**
     * 定义交通方式的枚举类
     */
    public enum TransportMode {
        CAR("驾车", 50.0),         // 驾车平均速度：60 km/h
        WALK("步行", 5.0),        // 步行平均速度：5 km/h
        BUS("公交", 30.0);        // 公交平均速度：30 km/h

        private final String name;       // 交通方式名称
        private final double speed;      // 平均速度（单位：公里/小时）

        TransportMode(String name, double speed) {
            this.name = name;
            this.speed = speed;
        }

        public String getName() {
            return name;
        }

        public double getSpeed() {
            return speed;
        }

        /**
         * 根据名称查找对应的交通方式
         *
         * @param name 交通方式名称
         * @return 对应的交通方式枚举值
         */
        public static TransportMode fromName(String name) {
            for (TransportMode mode : TransportMode.values()) {
                if (mode.getName().equalsIgnoreCase(name.trim())) {
                    return mode;
                }
            }
            throw new IllegalArgumentException("无效的交通方式：" + name);
        }
    }

    /**
     * 根据距离和交通方式计算所需时间，并返回格式化的字符串
     *
     * @param distance      距离（单位：公里）
     * @param transportMode 交通方式名称（"驾车"、"步行"、"公交"）
     * @return 所需时间的字符串表达（例如："1 小时 30 分钟" 或 "45 分钟"）
     */
    public static String calculateTravelTime(double distance, TransportMode transportMode) {
        // 计算所需时间（单位：分钟）
        double timeInMinutes = (distance / transportMode.getSpeed()) * 60;

        // 格式化时间为字符串
        return formatTime(timeInMinutes);
    }

    /**
     * 格式化时间为字符串
     *
     * @param timeInMinutes 时间（单位：分钟）
     * @return 格式化后的时间字符串（例如："1 小时 30 分钟" 或 "45 分钟"）
     */
    private static String formatTime(double timeInMinutes) {
        if (timeInMinutes < 60) {
            // 时间小于 60 分钟，直接返回分钟数
            return String.format("%.0f分钟", Math.ceil(timeInMinutes));
        } else {
            // 时间大于等于 60 分钟，拆分为小时和分钟
            int hours = (int) (timeInMinutes / 60);
            int minutes = (int) (timeInMinutes % 60);
            if (minutes == 0) {
                return hours + "小时";
            } else {
                return hours + "小时" + minutes + "分钟";
            }
        }
    }
}
