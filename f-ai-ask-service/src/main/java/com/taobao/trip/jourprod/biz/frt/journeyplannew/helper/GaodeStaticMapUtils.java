package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;


import com.alibaba.common.lang.StringUtil;
import com.taobao.trip.jourprod.common.lang.utils.MapLogUtils;
import com.taobao.trip.jourprod.common.sal.hsf.config.LLMSwitcher;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @description: 高德静态地图
 * @author: huiyi
 * @create: 2025-03-22 11:45
 **/
public class GaodeStaticMapUtils {

    private static final String GAODE_API_DOMAIN_URL = "https://restapi.amap.com/v3";

    private static final String GAODE_STATIC_MAP_PATH = "/staticmap";
    
    public static String buildStaticMap(String lat, String lon, String size, Integer zoom, String customizeMarkerUrl) {
        String staticMapUrl = GAODE_API_DOMAIN_URL + GAODE_STATIC_MAP_PATH;

        if (StringUtil.isEmpty(lat) || StringUtil.isEmpty(lon)) {
            return getDefaultStaticMap();
        }

        String location = lat + "," + lon;
        staticMapUrl += "?location=" + location;

        if (StringUtil.isNotEmpty(size)) {
            staticMapUrl += "?size=" + size;
        }

        if (Objects.nonNull(zoom) && (zoom < 1 || zoom > 17)) {
            staticMapUrl += "&zoom=" + zoom;
        } else {
            staticMapUrl += "&zoom=11";
        }

        if (StringUtil.isEmpty(customizeMarkerUrl)) {
            staticMapUrl += String.format("&markers=-1,%s,0:%s", customizeMarkerUrl, location);
        } else {
            staticMapUrl += String.format("&markers=large,,F:%s", location);
        }

        staticMapUrl += "&key=" + LLMSwitcher.GAODE_STATIC_MAP_AUTH_KEY;
        
        // 记录高德地图API调用日志
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("location", location);
        requestParams.put("size", size);
        requestParams.put("zoom", zoom);
        requestParams.put("markers", customizeMarkerUrl);
        
        MapLogUtils.logAmapApiCall(
            MapLogUtils.AMAP_API_STATIC_MAP, 
            requestParams, 
            staticMapUrl
        );

        return staticMapUrl;
    }

    public static String buildStaticMap(String lat, String lon, String size) {
        return buildStaticMap(lat, lon, size, null, null);
    }

    private static String getDefaultStaticMap() {
        String defaultMapUrl = "https://restapi.amap.com/v3/staticmap?zoom=12&size=768*452&markers=large,,F:116.481485,39.990464&key=" + LLMSwitcher.GAODE_STATIC_MAP_AUTH_KEY;
        
        // 记录高德地图API调用日志
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("default", true);
        requestParams.put("zoom", 12);
        requestParams.put("size", "768*452");
        requestParams.put("markers", "large,,F:116.481485,39.990464");
        
        MapLogUtils.logAmapApiCall(
            MapLogUtils.AMAP_API_STATIC_MAP_DEFAULT, 
            requestParams, 
            defaultMapUrl
        );
        
        return defaultMapUrl;
    }
}