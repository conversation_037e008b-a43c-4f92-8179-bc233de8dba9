package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.trip.tripdivision.domain.TrdiDivisionDO;
import com.fliggy.crowd.service.domain.profile.ProfileValue;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.mtop.api.agent.MtopContext;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyInitPageWelcomeTypeEnum;
import com.taobao.trip.jourprod.common.sal.hsf.common.EnvSwitch;
import com.taobao.trip.jourprod.common.sal.hsf.tripdivision.TrdiDivisionReadServiceClient;
import com.taobao.trip.jourprod.common.sal.hsf.userRelated.UserProfileManager;
import com.taobao.trip.jourprod.common.sal.tair.impl.MdbTairCommonHelper;
import com.taobao.trip.jourprod.core.service.gaode.AMapService;
import com.taobao.trip.jourprod.service.facade.model.request.gaode.GaoDeSearchQueryRequest;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiHistoryRequest;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiInitRequest;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.BottomTab;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.InTransit;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanAiAssistantHistoryWithCurrentChatResult;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanAiInitResult;
import com.taobao.trip.tripjourneyop.api.aiwyw.AiCueWordFacade;
import com.taobao.trip.tripjourneyop.domain.serving.AiCueWordDTO;
import com.taobao.trip.tripjourneyop.domain.serving.AiCueWordParameter;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @Description 行中场景
 * <AUTHOR>
 * @Date 2025/3/10
 **/
@Component
public class JourneyInTransitServiceHelper implements InitializingBean {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneyInTransitServiceHelper.class);

    public static final String ZHUGE_PERMANENT_CITY_LABEL = "lbs_permanent_city_id";

    @AppSwitch(des = "召回引导词的数量", level = Level.p4)
    public static Integer CUE_WORD_PAGE_SIZE = 5;

    @AppSwitch(des = "行中初始化页面兜底数据", level = Level.p4)
    public static Map<String, Object> IN_TRANSIT_DEFAULT_DATA_MAP = Maps.newHashMap();
    static {
        List<JSONObject> bottomTabList = Lists.newArrayList();
        JSONObject bottomTab = new JSONObject();
        bottomTab.put("showTitle", "");
        bottomTab.put("clickAfterQuery", "");
        bottomTab.put("iconUrl", "");
        bottomTabList.add(bottomTab);
        IN_TRANSIT_DEFAULT_DATA_MAP.put("bottomTab", bottomTabList);

        Map<String, List<JSONObject>> textPicListMap = Maps.newHashMap();
        List<JSONObject> picTextList = Lists.newArrayList();
        JSONObject picTextCueWord = new JSONObject();
        picTextCueWord.put("showTitle", "");
        picTextCueWord.put("picUrl", "");
        picTextCueWord.put("type", "pic");
        picTextList.add(picTextCueWord);
        textPicListMap.put("早上", picTextList);
        IN_TRANSIT_DEFAULT_DATA_MAP.put("textPicList", textPicListMap);

        Map<String, List<JSONObject>> textListMap = Maps.newHashMap();
        List<JSONObject> textList = Lists.newArrayList();
        JSONObject textCueWord = new JSONObject();
        picTextCueWord.put("showTitle", "");
        picTextList.add(picTextCueWord);
        textList.add(textCueWord);
        textListMap.put("早上", textList);
        IN_TRANSIT_DEFAULT_DATA_MAP.put("textList", textListMap);
    }

    @AppSwitch(des = "引导词缓存时间", level = Level.p4)
    public static Integer IN_TRANSIT_CUE_WORD_CACHE_TIME = 300;

    @AppSwitch(des = "行中态开关", level = Level.p4)
    public static Boolean IN_TRANSIT_OPEN_SWITCH = false;

    @Resource
    private TrdiDivisionReadServiceClient trdiDivisionReadServiceClient;

    @Resource
    private MdbTairCommonHelper mdbTairCommonHelper;

    @Resource
    private UserProfileManager userProfileManager;

    @Resource
    private AiCueWordFacade aiCueWordFacade;

    @Resource
    private AMapService aMapService;

    /**
     * 获取行中态信息
     */
    public InTransit queryInTransit(Long userId, String latitude, String longitude) {
        InTransit inTransit = new InTransit();
        long startTime = System.currentTimeMillis();
        try {
            if (BooleanUtils.isFalse(IN_TRANSIT_OPEN_SWITCH) || Objects.isNull(userId) || StringUtils.isEmpty(latitude) || StringUtils.isEmpty(longitude)) {
                inTransit.setHasMove(false);
                return inTransit;
            }

            // 通过lbs获取cityCode
            TrdiDivisionDO trdiDivisionDO = trdiDivisionReadServiceClient.getDivisionInfoByLatLng(Double.parseDouble(latitude), Double.parseDouble(longitude));
            if (Objects.isNull(trdiDivisionDO)) {
                inTransit.setHasMove(false);
                return inTransit;
            }

            inTransit.setCityCode(trdiDivisionDO.getTravelDivisionId());
            inTransit.setCityName(trdiDivisionDO.getName());
            inTransit.setCityNameAbbr(trdiDivisionDO.getNameAbbr());
            inTransit.setTimeZoneId(trdiDivisionDO.getTimezoneid());

            // 根据userid获取常驻地cityCode
            Map<String, ProfileValue> profileValueMap = userProfileManager.userProfile(userId, Arrays.asList(ZHUGE_PERMANENT_CITY_LABEL));
            if (MapUtils.isEmpty(profileValueMap)) {
                inTransit.setHasMove(false);
                return inTransit;
            }
            ProfileValue profileValue = profileValueMap.get(ZHUGE_PERMANENT_CITY_LABEL);
            if (Objects.isNull(profileValue) || StringUtils.isEmpty(profileValue.getValue())) {
                inTransit.setHasMove(false);
                return inTransit;
            }

            inTransit.setPermanentCityCode(profileValue.getValue());
            inTransit.setHasMove(!Objects.equals(inTransit.getPermanentCityCode(), inTransit.getCityCode()));
            return inTransit;
        } catch (Throwable e) {
            LOGGER.recordNormalException(new LogModel("queryInTransit").e(e).message("查询用户行中态信息包错"));
        } finally {
            LOGGER.recordOutput(new LogModel("queryInTransit")
                    .message("userId-{0},latitude-{1},longitude-{2},rt-{3}", userId, latitude, longitude, System.currentTimeMillis() - startTime)
                    .userId(userId)
                    .response(JSON.toJSONString(inTransit)));
        }
        return null;
    }

    /**
     * 处理行中态初始化页面信息
     */
    public void handleInitPageInfo(JourneyPlanAiInitRequest request, JourneyPlanAiInitResult result, InTransit inTransit) {
        long startTime = System.currentTimeMillis();
        try {
            result.setHasInTransit(true);
            result.setWelcomeType(AiJourneyInitPageWelcomeTypeEnum.JOURNEY_IN_TRANSIT.getCode());

            // 召回引导词
            JourneyPlanAiInitResult.InTransitInfo inTransitInfo = new JourneyPlanAiInitResult.InTransitInfo();
            result.setInTransit(inTransitInfo);

            String currentCityCode = inTransit.getCityCode();
            Integer currentMonthNum = getCurrentMonthNum();
            String currentTimePoint = getCurrentTimePointByTimeZoneId(inTransit.getTimeZoneId());

            // 调用高德获取poiTitle，拼接上附近
            buildPoiTitle(request, result, inTransitInfo, inTransit);

            // 构建底部bottom tab
            buildBottomTab(result, inTransit);

            String key = buildCueWordTairKey(currentCityCode, currentMonthNum, currentTimePoint);
            Object tairValue = mdbTairCommonHelper.getValue(key);
            if (Objects.nonNull(tairValue)) {
                JourneyPlanAiInitResult.CueWordList cueWordList = (JourneyPlanAiInitResult.CueWordList) tairValue;
                if (Objects.nonNull(cueWordList) && CollectionUtils.isNotEmpty(cueWordList.getTextPicList()) && CollectionUtils.isNotEmpty(cueWordList.getTextList())) {
                    // copy一份不要影响原始对象
                    List<JourneyPlanAiInitResult.CueWord> textPicList = new ArrayList<>(cueWordList.getTextPicList());
                    List<JourneyPlanAiInitResult.CueWord> textList = new ArrayList<>(cueWordList.getTextList());

                    // 打散
                    Collections.shuffle(textPicList);
                    Collections.shuffle(textList);

                    JourneyPlanAiInitResult.CueWordList newCueWordList = new JourneyPlanAiInitResult.CueWordList();
                    newCueWordList.setTextList(textList);
                    newCueWordList.setTextPicList(textPicList);
                    inTransitInfo.setCueWordList(newCueWordList);
                    return;
                }
            }

            // 获取带图文的引导词列表
            List<JourneyPlanAiInitResult.CueWord> aiCueWordDTOList = queryTextPicCueWordList(currentCityCode, currentTimePoint);
            if (CollectionUtils.isEmpty(aiCueWordDTOList)) {
                // 走兜底
                aiCueWordDTOList = buildDefaultTextPicCueWordList(currentTimePoint);
            }

            // 获取带文本的引导词列表
            List<JourneyPlanAiInitResult.CueWord> aiCueWordTextList = queryTextCueWordList(currentCityCode, currentTimePoint);
            if (CollectionUtils.isEmpty(aiCueWordTextList)) {
                // 走兜底
                aiCueWordTextList = buildDefaultTextCueWordList(currentTimePoint);
            }

            JourneyPlanAiInitResult.CueWordList cueWordList = new JourneyPlanAiInitResult.CueWordList();
            Collections.shuffle(aiCueWordDTOList);
            Collections.shuffle(aiCueWordTextList);
            cueWordList.setTextPicList(aiCueWordDTOList);
            cueWordList.setTextList(aiCueWordTextList);
            inTransitInfo.setCueWordList(cueWordList);

            // 添加到缓存
            mdbTairCommonHelper.putValue(key, cueWordList, IN_TRANSIT_CUE_WORD_CACHE_TIME);
        } catch (Exception e) {
            LOGGER.recordNormalException(new LogModel("queryInTransit").e(e).message("行中态构建页面信心报错"));
        } finally {
            LOGGER.recordOutput(new LogModel("handleInitPageInfo")
                    .message("request-{0},result-{1},inTransit-{2},rt-{3}", JSON.toJSONString(request), JSON.toJSONString(result), JSON.toJSONString(inTransit), System.currentTimeMillis() - startTime)
                    .userId(request.getUserId()));
        }
    }

    private List<JourneyPlanAiInitResult.CueWord> buildDefaultTextCueWordList(String currentTimePoint) {
        Object object = IN_TRANSIT_DEFAULT_DATA_MAP.get("textList");
        if (Objects.isNull(object)) {
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
        if (Objects.isNull(jsonObject)) {
            return null;
        }

        JSONArray currentTimePointJSONArray = jsonObject.getJSONArray(currentTimePoint);
        if (CollectionUtils.isEmpty(currentTimePointJSONArray)) {
            return null;
        }
        List<JourneyPlanAiInitResult.CueWord> cueWordList = JSON.parseArray(JSON.toJSONString(currentTimePointJSONArray), JourneyPlanAiInitResult.CueWord.class);
        return cueWordList;
    }

    private List<JourneyPlanAiInitResult.CueWord> buildDefaultTextPicCueWordList(String currentTimePoint) {
        Object object = IN_TRANSIT_DEFAULT_DATA_MAP.get("textPicList");
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
        if (Objects.isNull(jsonObject)) {
            return null;
        }

        JSONArray currentTimePointJSONArray = jsonObject.getJSONArray(currentTimePoint);
        if (CollectionUtils.isEmpty(currentTimePointJSONArray)) {
            return null;
        }
        List<JourneyPlanAiInitResult.CueWord> cueWordList = JSON.parseArray(JSON.toJSONString(currentTimePointJSONArray), JourneyPlanAiInitResult.CueWord.class);
        return cueWordList;
    }

    private List<JourneyPlanAiInitResult.CueWord> queryTextCueWordList(String currentCityCode, String currentTimePoint) {
        AiCueWordParameter parameter = new AiCueWordParameter();
        parameter.setCityCode(currentCityCode);
        parameter.setMonth(getCurrentMonthNum().longValue());
        parameter.setTimePoint(currentTimePoint);
        parameter.setStatus(Short.valueOf("2"));
        parameter.setShowPic(1); // 不展示图片
        parameter.setPageSize(CUE_WORD_PAGE_SIZE);
        List<AiCueWordDTO> aiCueWordDTOList = aiCueWordFacade.list(parameter);
        if (CollectionUtils.isEmpty(aiCueWordDTOList)) {
            return null;
        }
        List<JourneyPlanAiInitResult.CueWord> cueWordList = new ArrayList<>();
        for (AiCueWordDTO aiCueWordDTO : aiCueWordDTOList) {
            if (StringUtils.isEmpty(aiCueWordDTO.getText())) {
                continue;
            }
            JourneyPlanAiInitResult.CueWord cueWord = new JourneyPlanAiInitResult.CueWord();
            cueWord.setShowTitle(aiCueWordDTO.getText());
            cueWordList.add(cueWord);
        }
        return cueWordList;
    }

    private List<JourneyPlanAiInitResult.CueWord> queryTextPicCueWordList(String currentCityCode, String currentTimePoint) {
        AiCueWordParameter parameter = new AiCueWordParameter();
        parameter.setCityCode(currentCityCode);
        parameter.setMonth(getCurrentMonthNum().longValue());
        parameter.setTimePoint(currentTimePoint);
        parameter.setStatus(Short.valueOf("2"));
        parameter.setShowPic(2); // 展示图片
        parameter.setPageSize(CUE_WORD_PAGE_SIZE);
        List<AiCueWordDTO> aiCueWordDTOList = aiCueWordFacade.list(parameter);
        if (CollectionUtils.isEmpty(aiCueWordDTOList)) {
            return null;
        }
        List<JourneyPlanAiInitResult.CueWord> cueWordList = new ArrayList<>();
        for (AiCueWordDTO aiCueWordDTO : aiCueWordDTOList) {
            if (StringUtils.isEmpty(aiCueWordDTO.getPicUrl()) || StringUtils.isEmpty(aiCueWordDTO.getText())) {
                continue;
            }
            JourneyPlanAiInitResult.CueWord cueWord = new JourneyPlanAiInitResult.CueWord();
            cueWord.setShowTitle(aiCueWordDTO.getText());
            cueWord.setPicUrl(aiCueWordDTO.getPicUrl());
            cueWord.setVideoUrl(aiCueWordDTO.getVideoUrl());
            cueWord.setThumbPic(aiCueWordDTO.getThumbPic());
            cueWord.setType("pic");
            cueWordList.add(cueWord);
        }
        return cueWordList;
    }

    private void buildPoiTitle(JourneyPlanAiInitRequest request, JourneyPlanAiInitResult result, JourneyPlanAiInitResult.InTransitInfo inTransitInfo, InTransit inTransit) {
        if (StringUtils.isEmpty(request.getLongitude()) || StringUtils.isEmpty(request.getLatitude())) {
            inTransitInfo.setPoiTitle(inTransit.getCityName());
            return;
        }

        GaoDeSearchQueryRequest gaoDeRequest = new GaoDeSearchQueryRequest();
        gaoDeRequest.setLocation(String.format("%s,%s", request.getLongitude(), request.getLatitude()));
        List<Map<String, Object>> poiList = aMapService.queryGaoDeSearchAround(gaoDeRequest, true);
        if (CollectionUtils.isEmpty(poiList)) {
            inTransitInfo.setPoiTitle(inTransit.getCityName());
            return;
        }
        Map<String, Object> poiResMap = poiList.get(0);
        String poiTitle = MapUtils.getString(poiResMap, "name");
        if(StringUtils.isNotBlank(poiTitle)) {
            inTransitInfo.setPoiTitle(poiTitle + "附近");
            return;
        }
        inTransitInfo.setPoiTitle(inTransit.getCityName());
    }

    public void buildBottomTab(JourneyPlanAiInitResult result, InTransit inTransit) {
        if (MapUtils.isEmpty(IN_TRANSIT_DEFAULT_DATA_MAP)) {
            return;
        }
        String cityName = inTransit.getCityNameAbbr();
        Object bottomTab = IN_TRANSIT_DEFAULT_DATA_MAP.get("bottomTab");
        if (Objects.isNull(bottomTab)) {
            return;
        }
        List<BottomTab> bottomTabs = JSONObject.parseArray(JSON.toJSONString(bottomTab), BottomTab.class);
        if (CollectionUtils.isEmpty(bottomTabs)) {
            return;
        }
        for (BottomTab tab : bottomTabs) {
            tab.setClickAfterQuery(String.format(tab.getClickAfterQuery(), cityName, cityName));
        }
        result.setBottomTabList(bottomTabs);
        // 豆腐块从welcomeCardList中读取
        result.setWelcomeCardList(bottomTabs.stream().map(tab -> {
            JourneyPlanAiInitResult.WelcomeCard welcomeCard = new JourneyPlanAiInitResult.WelcomeCard();
            welcomeCard.setTitle(tab.getShowTitle());
            welcomeCard.setIcon(tab.getIconUrl());
            welcomeCard.setQuery(tab.getClickAfterQuery());
            return welcomeCard;
        }).collect(Collectors.toList()));
    }

    private String buildCueWordTairKey(String currentCityCode, Integer currentMonthNum, String currentTimePoint) {
        return String.format("%s_%s_%s_%s", EnvSwitch.isPre() ? "pre" : "online", currentCityCode, currentMonthNum, currentTimePoint);
    }

    /**
     * 获取当前时刻，早中晚
     * @return 返回的问候语
     */
    private String getCurrentTimePointByTimeZoneId(String timeZoneId) {
        // 使用指定的时区ID创建ZonedDateTime对象
        ZonedDateTime zonedDateTime = ZonedDateTime.now(ZoneId.of(timeZoneId));

        int hour = zonedDateTime.getHour();
        if (hour >= 6 && hour < 12) {
            return "早上";
        } else if (hour >= 12 && hour < 18) {
            return "下午";
        } else {
            return "晚上";
        }
    }

    /**
     * 获取当前时间
     *
     * @return
     */
    private Integer getCurrentMonthNum() {
        // 获取当前日期和时间
        LocalDateTime currentDateTime = LocalDateTime.now();
        // 获取当前月份（数字形式）
        int currentMonthAsInt = currentDateTime.getMonthValue();
        return currentMonthAsInt;
    }

    /**
     * 将列表按指定比例随机分成两部分
     *
     * @param list   待分割的列表
     * @param ratio1 第一部分的比例
     * @param ratio2 第二部分的比例
     * @return 包含两个分割后子列表的列表
     */
    public static List<List<AiCueWordDTO>> splitListInRatio(List<AiCueWordDTO> list, int ratio1, int ratio2) {
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }

        // 复制并打乱原列表
        List<AiCueWordDTO> shuffledList = new ArrayList<>(list);
        Collections.shuffle(shuffledList);

        // 计算分割点
        int firstPartSize = Math.min(list.size(), (list.size() * ratio1) / (ratio1 + ratio2));

        // 分割列表
        List<AiCueWordDTO> firstPart = new ArrayList<>(shuffledList.subList(0, firstPartSize));
        List<AiCueWordDTO> secondPart = new ArrayList<>(shuffledList.subList(firstPartSize, list.size()));

        List<List<AiCueWordDTO>> res =  new ArrayList<>();
        res.add(firstPart);
        res.add(secondPart);
        return res;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        SwitchManager.init(JourneyInTransitServiceHelper.class);
    }

    public void buildHistoryBottomTab(JourneyPlanAiHistoryRequest request, JourneyPlanAiAssistantHistoryWithCurrentChatResult result) {
        String lng = MtopContext.getLng();
        String lat = MtopContext.getLat();
        long userId = MtopContext.getUserId();

        InTransit inTransit = queryInTransit(userId, lat, lng);
        if (Objects.isNull(inTransit) || !Boolean.TRUE.equals(inTransit.getHasMove())) {
            return;
        }

        Object bottomTab = IN_TRANSIT_DEFAULT_DATA_MAP.get("bottomTab");
        if (Objects.isNull(bottomTab)) {
            return;
        }

        List<BottomTab> bottomTabs = JSONObject.parseArray(JSON.toJSONString(bottomTab), BottomTab.class);
        if (CollectionUtils.isEmpty(bottomTabs)) {
            return;
        }
        for (BottomTab tab : bottomTabs) {
            tab.setClickAfterQuery(String.format(tab.getClickAfterQuery(), inTransit.getCityNameAbbr(), inTransit.getCityNameAbbr()));
        }
        result.setBottomTabList(bottomTabs);
    }
}
