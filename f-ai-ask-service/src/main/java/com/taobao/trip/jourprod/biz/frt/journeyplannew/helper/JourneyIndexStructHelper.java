package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.trip.trippoi.domain.TrpoPoiDO;

import com.google.common.collect.Lists;
import com.taobao.trip.jourprod.common.sal.hsf.poi.PoiReadServiceClient;
import com.taobao.trip.tripjourneyop.base.index.model.JourneyOpCardIndex;
import com.taobao.trip.tripjourneyop.enums.JourneyTypeEnum;
import com.taobao.trip.tripjourneyop.journeyplatform.domain.ElementDO;
import com.taobao.trip.tripjourneyop.journeyplatform.domain.JourneyElementDO;
import com.taobao.trip.tripjourneyop.journeyplatform.domain.StructureElementModel;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

/**
 * @Description 行程结构化物料解析帮助类
 * <AUTHOR>
 * @Date 2025/2/12
 **/
@Component
public class JourneyIndexStructHelper {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneyIndexStructHelper.class);

    private static final List<String> TRAFFIC_PLAN_TYPES = Lists.newArrayList(
        "FLIGHT", "FLIGHT_SHARE", "CIVIL_FLIGHT_SHARE", "INTER_FLIGHT_SHARE", "FLIGHT_SUB",
        "TRAIN", "TRAIN_SHARE", "ENTRANCE", "ENTRANCE_SHARE", "TICKET", "TICKET_SHARE", "HOTEL",
        "HOTEL_SHARE", "BUS", "BUS_SHARE", "HOTEL_SUB"
    );

    @Resource
    private PoiReadServiceClient poiReadServiceClient;

    public String queryDestPoiName(JourneyOpCardIndex opCardIndex) {
        TrpoPoiDO trpoPoiDO = queryDestPoiDO(opCardIndex);
        if (trpoPoiDO == null) {
            return null;
        }
        return trpoPoiDO.getName();
    }

    private TrpoPoiDO queryDestPoiDO(JourneyOpCardIndex opCardIndex) {
        String journeyType = opCardIndex.getJourneyType();
        JourneyTypeEnum typeEnum = JourneyTypeEnum.getByName(journeyType);
        if (typeEnum == null || !TRAFFIC_PLAN_TYPES.contains(journeyType)) {
            return null;
        }
        try {
            String elementModelStr = MapUtils.getString(opCardIndex.getExtInfo(), com.taobao.trip.tripjourneyop.constant.ConstantElement.ELEMENT_MODELS);
            List<StructureElementModel> elementModelList = JSON.parseObject(elementModelStr, new TypeReference<List<StructureElementModel>>() {
            });
            if (CollectionUtils.isEmpty(elementModelList)) {
                return null;
            }
            StructureElementModel elementModel = elementModelList.get(0);
            JourneyElementDO elementDO = elementModel.getElementDO();
            if (elementDO == null || CollectionUtils.isEmpty(elementDO.getElementDOS())) {
                return null;
            }
            List<ElementDO> elementDOS = elementDO.getElementDOS();
            ElementDO element = elementDOS.get(0);
            String depart = "";
            switch (typeEnum) {
                case FLIGHT:
                case FLIGHT_SHARE:
                case CIVIL_FLIGHT_SHARE:
                case INTER_FLIGHT_SHARE:
                case FLIGHT_SUB:
                case TRAIN:
                case TRAIN_SUB:
                case TRAIN_SHARE:
                    depart = element.getDepart();
                    break;
                case TICKET:
                case TICKET_SUB:
                case TICKET_SHARE:
                case ENTRANCE:
                case ENTRANCE_SUB:
                case ENTRANCE_SHARE:
                case HOTEL:
                case HOTEL_SHARE:
                case HOTEL_SUB:
                    depart = element.getArrive();
                    break;
                default:
                    break;
            }
            if (StringUtils.isBlank(depart) || "null".equals(depart)) {
                return null;
            }
            return poiReadServiceClient.getPoiByPoiId(NumberUtils.toLong(depart));
        } catch (Throwable e) {
            LOGGER.recordDangerException(new LogModel("queryDestPoiDO")
                .e(e)
                .request(JSON.toJSONString(opCardIndex)));
        }
        return null;
    }

}
