package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.mtop3.invocation.MtopStream;
import com.fliggy.fic.dto.person.PoiInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanMessageStatusEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneyMessageContentDTO;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneyMessageDTO;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneyRecommendLineModel.Route;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.jourprod.common.sal.exception.TripJourneyException;
import com.taobao.trip.jourprod.common.sal.hsf.tair.MdbTairHelper;
import com.taobao.trip.jourprod.core.service.converter.AiJourneyChatConvert;
import com.taobao.trip.jourprod.metaq.MetaQService;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.JourneyPlanAiAssistantRoleEnum;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatRequest;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanMessageSummaryResult;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.Poi;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult.StreamMessageCardModel;
import com.taobao.trip.jourprod.service.impl.journeyplannew.JourneyAssistantDataOperateService;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.constant.JourneyPlanAiAssistantConstant.NEW_LINE;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.constant.JourneyPlanAiAssistantConstant.ROUTE_RESULT_KEY;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch.WAIT_COMPONENTS_ASYNC_TASK_TIMEOUT;

/**
 * @Description 聊天内容存储工具类
 * <AUTHOR>
 * @Date 2025/2/7
 **/
@Component
public class JourneyPlanAiAssistantContentHelper implements InitializingBean {

    //酒店的一级类目
    public static final Long HOTEL_FIRST_CATEGORY = 4L;
    //美食的一级类目
    public static final Long FOOD_FIRST_CATEGORY = 7L;

    private static final FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneyPlanAiAssistantContentHelper.class);

    @AppSwitch(des = "当前messageId对应的线路内容对应的key", level = Level.p4)
    public static final String CURRENT_MESSAGE_ID_ROUTE_RESULT_KEY = "ai:journey:messageId:routeResult:";

    @AppSwitch(des = "当前messageId对应的思考对应的key", level = Level.p4)
    public static final String CURRENT_MESSAGE_ID_THINKING_KEY = "ai:journey:messageId:thinking:";

    @AppSwitch(des = "当前messageId对应的线路内容对应的key的缓存时间", level = Level.p4)
    public static final Integer CURRENT_MESSAGE_ID_ROUTE_RESULT_TTL_S = 24 * 60 * 60;

    @AppSwitch(des = "历史总结内容对应的key", level = Level.p4)
    public static final String SUMMARY_RESULT_KEY = "ai:journey:summary:result:";

    @AppSwitch(des = "历史总结内容对应的key的缓存时间", level = Level.p4)
    public static final Integer SUMMARY_RESULT_TTL_S = 24 * 60 * 60;

    @AppSwitch(des = "要剔除的后缀", level = Level.p4)
    public static Set<String> REMOVE_SUFFIX = Sets.newHashSet();

    @AppSwitch(des = "消息缓存对应的key", level = Level.p4)
    public static final String CONTENT_MAP_KEY = "ai:journey:content:map:";

    @AppSwitch(des = "消息缓存对应的key的缓存时间", level = Level.p4)
    public static final Integer CONTENT_MAP_TTL_S = 24 * 60 * 60;

    @AppSwitch(des = "消息最终序列号对应的key", level = Level.p4)
    public static final String CONTENT_SEQ_KEY = "ai:journey:content:seq:";

    @AppSwitch(des = "消息最终序列号对应的key的缓存时间", level = Level.p4)
    public static final Integer CONTENT_SEQ_TTL_S = 24 * 60 * 60;

    @AppSwitch(des = "续接接口最多超时次数", level = Level.p4)
    public static Integer CONTINUE_CHAT_TIMEOUT_COUNT = 1500;

    @AppSwitch(des = "没有取到回答等待时间", level = Level.p4)
    public static Integer WAIT_TIME_MS = 20;

    @AppSwitch(des = "是否把序号+1的逻辑挪到续接", level = Level.p4)
    public static Boolean MOVE_SEQ_TO_CONTINUE_CHAT = false;

    private final static String MESSAGE_TOPIC = "F_AI_ASK_CHAT_MESSAGE_ADD";

    /**
     * 记录一次回答对应的序列号，一条消息所有信息都发完了就要清理掉
     */
    private final static Map<String, Integer> CONTENT_SEQ_MAP = Maps.newConcurrentMap();

    /**
     * 记录回答内容的上下文，在总结完之后要清理掉
     */
    private final static Map<String, Map<String, String>> MESSAGE_CONTENT_MAP = Maps.newConcurrentMap();

    @Resource
    private MdbTairHelper mdbTairHelper;
    @Resource
    private JourneyAssistantDataOperateService journeyAssistantDataOperateFacade;

    @Resource
    private MetaQService metaQService;

    @Resource
    private JourneyPlanAiAssistantMessageStatusHelper messageStatusHelper;
    @Resource
    private AiJourneyChatConvert aiJourneyChatConvert;

    /**
     * 插入用户对话记录
     */
    public void insertUserHistoryContent(JourneyPlanAiChatRequest request, ChatContext chatContext) {
        if (ObjectUtils.anyNull(chatContext.getUserMessageId(), request.getChat())) {
            throw new TripJourneyException("用户对话记录插入失败");
        }
        // 用户消息
        AiJourneyMessageDTO userAiJourneyMessageDTO = new AiJourneyMessageDTO();
        userAiJourneyMessageDTO.setMessageId(chatContext.getUserMessageId());
        userAiJourneyMessageDTO.setUserId(String.valueOf(request.getUserId()));
        userAiJourneyMessageDTO.setUtdid(request.getUtdid());
        userAiJourneyMessageDTO.setTtid(request.getTtid());
        userAiJourneyMessageDTO.setSessionId(chatContext.getSessionId());
        userAiJourneyMessageDTO.setSummarizedChat(chatContext.getSummarizedChat());
        userAiJourneyMessageDTO.setTimestamp(request.getRequestTime());
        userAiJourneyMessageDTO.setRole(JourneyPlanAiAssistantRoleEnum.USER.getCode());
        AiJourneyMessageContentDTO userContent = new AiJourneyMessageContentDTO();
        userContent.setContent(request.getChat());
        userAiJourneyMessageDTO.setContent(userContent);
        userAiJourneyMessageDTO.setUserChat(request.getChat());
        userAiJourneyMessageDTO.setSource(chatContext.getRequest().getSource());
        List<AiJourneyMessageDTO> aiJourneyMessageDTOList = Lists.newArrayList(userAiJourneyMessageDTO);
        LOGGER.recordOutput(new LogModel("insertUserContent").message("insertUserContent").request(JSONUtil.toJSONStringForLog(aiJourneyMessageDTOList)));
        journeyAssistantDataOperateFacade.insertHistoryMessage(aiJourneyMessageDTOList);
        chatContext.setUserAiJourneyMessageDTO(userAiJourneyMessageDTO);
    }


    /**
     * 插入历史记录，包含两部分，用户的问题和对应的回答
     */
    public void insertSystemHistoryContent(JourneyPlanAiChatRequest request, JourneyPlanMessageSummaryResult result, ChatContext chatContext) {
        if (ObjectUtils.anyNull(chatContext.getSystemMessageId(), result)) {
            throw new TripJourneyException("历史记录插入失败");
        }

        if (chatContext.isDisableHistoryMessage()) {
            return;
        }

        // 判断components数据任务是否都处理完
        waitComponentsAsyncTaskComplete(chatContext, request);

        // 系统消息
        // 判断是否停止
        // todo 状态收口
        // todo 区分异常停止和手动停止
        AiJourneyPlanMessageStatusEnum status = AiJourneyPlanMessageStatusEnum.NORMAL;
        if (messageStatusHelper.isStop(chatContext.getSystemMessageId())) {
            if (messageStatusHelper.isBlock(chatContext.getSystemMessageId())) {
                status = AiJourneyPlanMessageStatusEnum.BLOCK;
            } else {
                status = AiJourneyPlanMessageStatusEnum.CANCEL;
            }
        }
        AiJourneyMessageDTO systemAiJourneyMessageDTO = new AiJourneyMessageDTO();
        systemAiJourneyMessageDTO.setStatus(status.getStatusCode());
        systemAiJourneyMessageDTO.setType(result.getType());
        systemAiJourneyMessageDTO.setUserChat(request.getChat());
        systemAiJourneyMessageDTO.setMessageId(chatContext.getSystemMessageId());
        systemAiJourneyMessageDTO.setSummarizedChat(chatContext.getSummarizedChat());
        systemAiJourneyMessageDTO.setUserId(String.valueOf(request.getUserId()));
        systemAiJourneyMessageDTO.setUtdid(request.getUtdid());
        systemAiJourneyMessageDTO.setTtid(request.getTtid());
        systemAiJourneyMessageDTO.setSessionId(chatContext.getSessionId());
        systemAiJourneyMessageDTO.setRole(JourneyPlanAiAssistantRoleEnum.SYSTEM.getCode());
        systemAiJourneyMessageDTO.setTimestamp(new Date());
        long cost = (System.currentTimeMillis() - request.getRequestTime().getTime())/1000;
        systemAiJourneyMessageDTO.setCost(cost);
        systemAiJourneyMessageDTO.setRetry(request.getRetry());
        systemAiJourneyMessageDTO.setIsClearQuery(chatContext.getIsClearQuery());

        // 对话内容
        AiJourneyMessageContentDTO systemContentDTO = new AiJourneyMessageContentDTO();
        systemContentDTO.setContent(result.getInfo());
        systemContentDTO.setStructRoute(result.getStructRoute());
        systemContentDTO.setMore(result.getMoreDemands());
        systemContentDTO.setInspirations(result.getInspirations());
        systemContentDTO.setComponents(chatContext.getComponents());
        systemContentDTO.setThinkingThought(chatContext.getThinkingThought());
        systemAiJourneyMessageDTO.setContent(systemContentDTO);
        systemAiJourneyMessageDTO.setSource(chatContext.getRequest().getSource());
        systemAiJourneyMessageDTO.setAiaskChatRequest(chatContext.getAiaskChatRequest());

        // 批量插入
        List<AiJourneyMessageDTO> aiJourneyMessageDTOList = Lists.newArrayList(systemAiJourneyMessageDTO);
        LOGGER.recordOutput(new LogModel("insertHistoryContent").message("insertHistoryContent").request(JSONUtil.toJSONStringForLog(aiJourneyMessageDTOList)));
        journeyAssistantDataOperateFacade.insertHistoryMessage(aiJourneyMessageDTOList);
        systemAiJourneyMessageDTO.setContent(null);
        sendInsertSuccessMq(chatContext.getUserAiJourneyMessageDTO(), systemAiJourneyMessageDTO);
    }

    private void sendInsertSuccessMq(AiJourneyMessageDTO userMessage, AiJourneyMessageDTO systemMessage) {
        try {
            metaQService.send(MESSAGE_TOPIC, "*", systemMessage.getMessageId(), JSON.toJSONString(Lists.newArrayList(userMessage, systemMessage)).getBytes());
        } catch (Exception e) {
            LOGGER.recordNormalException(new LogModel("sendInsertSuccessMq").e(e)
                    .message("sendInsertSuccessMq").request(JSONUtil.toJSONStringForLog(Lists.newArrayList(userMessage, systemMessage))));
        }
    }


    /**
     * 获取当前消息关联的内容
     */
    @AteyeInvoker(description = "获取当前消息关联的内容", paraDesc = "currentMessageId")
    public Map<String, String> getContentMap(String currentMessageId) {
        String key = genContentMapKey(currentMessageId);
        return MapUtils.getObject(MESSAGE_CONTENT_MAP, key, Maps.newHashMap());
    }

    /**
     * 获取生成的线路结果
     */
    public String getRouteResult(String currentMessageId) {
        Map<String, String> contentMap = getContentMap(currentMessageId);
        String routeResult = MapUtils.getString(contentMap, ROUTE_RESULT_KEY, StringUtils.EMPTY);
        // 结尾新增换行符，方便结构解析
        if (StringUtils.isNotBlank(routeResult) && !routeResult.endsWith(NEW_LINE)) {
            return routeResult + NEW_LINE;
        }
        return routeResult;
    }

    public void getRoute(Map<Integer, List<PoiInfo>> poiListMap, List<Route> routeList) {
        for (Integer day : poiListMap.keySet()) {
            Route route = new Route();
            List<PoiInfo> poiList = poiListMap.get(day);
            if (CollectionUtils.isEmpty(poiList)) {
                continue;
            }
            List<Poi> pois = JSONObject.parseArray(JSON.toJSONString(poiList), Poi.class);
            route.setPoiList(pois);

            route.setDay(String.valueOf(day));
            route.setChineseDay(String.valueOf(day));
            routeList.add(route);
        }
    }

    private String genContentMapKey(String currentMessageId) {
        return CURRENT_MESSAGE_ID_ROUTE_RESULT_KEY + currentMessageId;
    }

    /**
     * 清理消息内容
     */
    public void clearContentMap(String currentMessageId) {
        String key = genContentMapKey(currentMessageId);
        MESSAGE_CONTENT_MAP.remove(key);
    }

    /**
     * 记录总结内容
     */
    public void recordSummaryResult(String messageId, JourneyPlanMessageSummaryResult result) {
        String key = genSummaryResultKey(messageId);
        mdbTairHelper.putValue(key, JSON.toJSONString(result), SUMMARY_RESULT_TTL_S);
    }

    private String genSummaryResultKey(String systemMessageId) {
        return SUMMARY_RESULT_KEY + systemMessageId;
    }

    @AteyeInvoker(description = "获取总结内容", paraDesc = "systemMessageId")
    public JourneyPlanMessageSummaryResult getSummaryResult(String systemMessageId) {
        String key = genSummaryResultKey(systemMessageId);
        Object value = mdbTairHelper.getValue(key);
        return JSONUtil.parseObject(String.valueOf(value), JourneyPlanMessageSummaryResult.class);
    }

    /**
     * 输出内容
     */
    public void outputContent(MtopStream mtopStream,
                              Map thinkingThought,
                              String message,
                              List<StreamMessageCardModel> cardModelList,
                              JourneyPlanAiChatRequest request,
                              ChatContext context) {
        String status = AiJourneyPlanMessageStatusEnum.NORMAL.getStatusCode();
        boolean stop = false;
        if (messageStatusHelper.isStop(context.getSystemMessageId())) {
            if (messageStatusHelper.isBlock(context.getSystemMessageId())) {
                status = AiJourneyPlanMessageStatusEnum.BLOCK.getStatusCode();
            } else {
                status = AiJourneyPlanMessageStatusEnum.CANCEL.getStatusCode();
            }
            stop = true;
        }
        // 获取流式输出内容
        // 流式输出
        StreamMessageResult result = new StreamMessageResult(context.getSystemMessageId(), context.getUserMessageId(), thinkingThought, message, cardModelList, status);
        result.setSessionId(context.getSessionId());
        // 记录信息到缓存
        recordStreamMessageResult(context.getSystemMessageId(), result);
        if (stop) {
            mtopStream.writeAndEnd(JSON.toJSONString(result));
        } else {
            mtopStream.write(JSON.toJSONString(result));
        }
    }

    /**
     * 输出内容
     */
    public void outputContent(MtopStream mtopStream,
                              String thinking,
                              String message,
                              List<StreamMessageCardModel> cardModelList,
                              JourneyPlanAiChatRequest request,
                              ChatContext context) {
        String status = AiJourneyPlanMessageStatusEnum.NORMAL.getStatusCode();
        boolean stop = false;
        if (messageStatusHelper.isStop(context.getSystemMessageId())) {
            if (messageStatusHelper.isBlock(context.getSystemMessageId())) {
                status = AiJourneyPlanMessageStatusEnum.BLOCK.getStatusCode();
            } else {
                status = AiJourneyPlanMessageStatusEnum.CANCEL.getStatusCode();
            }
            stop = true;
        }
        // 获取流式输出内容
        // 流式输出
        StreamMessageResult result = new StreamMessageResult(context.getSystemMessageId(), context.getUserMessageId(), thinking, message, cardModelList, status);
        // 记录信息到缓存
        recordStreamMessageResult(context.getSystemMessageId(), result);
        if (stop) {
            mtopStream.writeAndEnd(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
        } else {
            mtopStream.write(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
        }
    }

    /**
     * 把消息内容放到缓存中，方便后续读取
     */
    public void recordStreamMessageResult(String systemMessageId, StreamMessageResult result) {
        // 获取当前序列号
        Integer seq = MapUtils.getInteger(CONTENT_SEQ_MAP, systemMessageId, 0);
        // 生成当前消息对应的key
        String key = genContentKey(systemMessageId, seq);
        // 更新序列号
        if (!MOVE_SEQ_TO_CONTINUE_CHAT) {
            seq = seq + 1;
        }
        //设置当前消息seq
        result.setSeq(seq);
        // 记录到缓存
        mdbTairHelper.putValue(key, JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect), CONTENT_MAP_TTL_S);
        CONTENT_SEQ_MAP.put(systemMessageId, seq);
        // seq记录到缓存
        String msgSeqKey = genMessageSeqKey(systemMessageId);
        mdbTairHelper.putValue(msgSeqKey, String.valueOf(seq), CONTENT_SEQ_TTL_S);
    }

    private String genContentKey(String systemMessageId, Integer seq) {
        return CONTENT_MAP_KEY + systemMessageId + seq;
    }

    /**
     * 清理消息序列号
     */
    public void clearContentSeq(String systemMessageId) {
        Integer seq = CONTENT_SEQ_MAP.remove(systemMessageId);
        String key = genMessageSeqKey(systemMessageId);
        mdbTairHelper.putValue(key, String.valueOf(seq), CONTENT_SEQ_TTL_S);
    }

    private String genMessageSeqKey(String systemMessageId) {
        return CONTENT_SEQ_KEY + systemMessageId;
    }

    private Integer tryGetContentSeq(String systemMessageId) {
        String key = genMessageSeqKey(systemMessageId);
        Object value = mdbTairHelper.getValue(key);
        if (Objects.isNull(value)) {
            return 0;
        }
        return NumberUtils.toInt(String.valueOf(value));
    }



    /**
     * 输出缓存的内容
     * @param systemMessageId 系统消息ID
     * @param seq 起始序列号
     * @param mtopStream 输出流
     */
    public void outputCacheContent(String systemMessageId, Integer seq, MtopStream mtopStream) {
        // 参数校验
        if (StringUtils.isBlank(systemMessageId) || Objects.isNull(mtopStream)) {
            LOGGER.recordOutput(new LogModel("outputCacheContent")
                    .message("参数无效")
                    .request("systemMessageId:" + systemMessageId));
            return;
        }

        // 对序列号的兼容处理
        if (Objects.nonNull(seq) && MOVE_SEQ_TO_CONTINUE_CHAT) {
            // 返回传入seq对应的下一条消息
            seq += 1;
        }

        // 初始化当前序列号，如果为null则默认从0开始
        int curSeq = Objects.isNull(seq) ? 0 : seq;

        // 当seq=0时，批量查询已有消息并输出合并结果
        if (curSeq == 0) {
            // 从缓存中获取当前最大序列号
            int maxCachedSeq = tryGetContentSeq(systemMessageId);
            // 如果有缓存的消息，获取合并结果并输出
            StreamMessageResult mergedResult = aiJourneyChatConvert.getAlreadyOutputStreamMessageResult(systemMessageId, maxCachedSeq);
            // 输出合并结果
            if (Objects.nonNull(mergedResult)) {
                mtopStream.write(JSON.toJSONString(mergedResult));
                LOGGER.recordOutput(new LogModel("outputCacheContent")
                        .message("批量查询输出合并结果，maxSeq={0}", maxCachedSeq)
                        .request(systemMessageId)
                        .response(JSON.toJSONString(mergedResult)));

                // 更新当前序列号
                curSeq = maxCachedSeq;
            }
        }

        // 初始化等待时间和最大序列号
        int waitTime = 0;
        // 最大序列号（消息结束时的序列号）
        int maxSeq = 0;

        // 循环获取缓存内容
        while (true) {
            // 检查是否有最终序列号
            if (maxSeq == 0 && messageStatusHelper.isEnd(systemMessageId)) {
                // 消息已结束，获取最终序列号
                maxSeq = tryGetContentSeq(systemMessageId);
                LOGGER.recordOutput(new LogModel("outputCacheContent")
                        .message("消息已结束，获取最终序列号")
                        .request(systemMessageId)
                        .response("maxSeq:" + maxSeq));
            }

            // 如果已获取最终序列号且当前序列号已达到或超过最终序列号，结束循环
            if (maxSeq != 0 && curSeq >= maxSeq) {
                LOGGER.recordOutput(new LogModel("outputCacheContent")
                        .message("已到达最终序列号，结束输出")
                        .request(systemMessageId)
                        .response("curSeq:" + curSeq + ", maxSeq:" + maxSeq));
                break;
            }

            // 生成当前序列号对应的key
            String key = genContentKey(systemMessageId, curSeq);
            // 尝试获取缓存内容
            Object value = mdbTairHelper.getValue(key);

            if (Objects.isNull(value)) {
                // 未获取到内容，检查是否超时
                if (waitTime > CONTINUE_CHAT_TIMEOUT_COUNT) {
                    LOGGER.recordOutput(new LogModel("outputCacheContent")
                            .request(systemMessageId)
                            .message("超过最大等待时间，当前序列号:" + curSeq));
                    break;
                }

                try {
                    boolean stop = messageStatusHelper.isEnd(systemMessageId) || messageStatusHelper.isStop(systemMessageId);
                    if (stop) {
                        LOGGER.recordOutput(new LogModel("outputCacheContent")
                                .request(systemMessageId)
                                .message("消息已结束或已停止，当前序列号:" + curSeq));
                        break;
                    }
                    // 短暂等待后重试
                    Thread.sleep(WAIT_TIME_MS);
                    waitTime++;

                    // 每50次等待输出一次日志，避免日志过多
                    if (waitTime % 50 == 0) {
                        LOGGER.recordOutput(new LogModel("outputCacheContent")
                                .request(systemMessageId)
                                .message("等待中，次数:" + waitTime + ", curSeq:" + curSeq));
                    }
                } catch (InterruptedException e) {
                    LOGGER.recordNormalException(new LogModel("outputCacheContent")
                            .message("等待被中断")
                            .request(systemMessageId)
                            .e(e));
                    Thread.currentThread().interrupt();
                    break;
                }

                // 发送心跳包保持连接
                StreamMessageResult heartBeatResult = StreamMessageResult.heartBeat();
                mtopStream.write(JSON.toJSONString(heartBeatResult));
                continue;
            }

            // 重置等待时间
            waitTime = 0;

            // 输出获取到的内容
            mtopStream.write(String.valueOf(value));
            LOGGER.recordOutput(new LogModel("outputCacheContent")
                    .request(systemMessageId)
                    .response("输出数据:" + value)
                    .message("成功输出一条消息"));

            // 增加序列号，准备获取下一条
            curSeq++;
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        SwitchManager.init(JourneyPlanAiAssistantContentHelper.class);
    }

    /**
     * 等待components数据异步任务完成
     * @param chatContext
     * @param request
     */
    private static void waitComponentsAsyncTaskComplete(ChatContext chatContext, JourneyPlanAiChatRequest request) {

        List<CompletableFuture<Void>> futures = chatContext.getComponentsWaitMap().get(chatContext.getUserMessageId());
        if (CollectionUtils.isEmpty(futures)) {
            return;
        }
        LOGGER.recordOutput(new LogModel("waitComponentsAsyncTaskComplete").message("find futures").response("size:" + futures.size()));
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(WAIT_COMPONENTS_ASYNC_TASK_TIMEOUT, TimeUnit.SECONDS);
            LOGGER.recordOutput(new LogModel("waitComponentsAsyncTaskComplete").message("futures all complete"));
        } catch (Exception ex) {
            LOGGER.recordNormalException(new LogModel("waitComponentsAsyncTaskComplete").message("futures timeout").e(ex));
        } finally {
            // 移除当前等待器
            chatContext.getComponentsWaitMap().remove(chatContext.getUserMessageId());
        }
    }

}
