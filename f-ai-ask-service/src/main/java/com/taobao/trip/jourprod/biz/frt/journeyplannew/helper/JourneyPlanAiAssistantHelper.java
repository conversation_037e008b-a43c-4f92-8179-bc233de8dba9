package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;

import com.alibaba.dashscope.app.Application;
import com.alibaba.dashscope.app.ApplicationParam;
import com.alibaba.dashscope.app.ApplicationResult;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneySceneModel;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.biz.frt.llm.domain.MessageParam;
import com.taobao.trip.jourprod.biz.frt.llm.domain.MessageRole;
import com.taobao.trip.jourprod.common.lang.utils.HttpUtil;
import com.taobao.trip.jourprod.common.sal.annotation.RunLog;
import com.taobao.trip.jourprod.common.sal.exception.TripJourneyException;
import com.taobao.trip.jourprod.common.sal.hsf.common.EnvSwitch;
import com.taobao.trip.jourprod.common.sal.hsf.tair.MdbTairHelper;
import com.taobao.trip.jourprod.common.sal.tair.impl.LdbTairManager;
import com.taobao.trip.jourprod.common.sal.tair.impl.TairKeyFactory;
import com.taobao.trip.jourprod.core.service.converter.AiJourneySceneModelConvert;
import com.taobao.trip.jourprod.intent.IntentRecognitionService;
import com.taobao.trip.jourprod.intent.domain.IntentRecognitionRequest;
import com.taobao.trip.jourprod.intent.domain.IntentRecognitionResponse;
import com.taobao.trip.jourprod.intent.enums.IntentProviderTypeEnum;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatRequest;
import com.taobao.trip.jourprod.service.impl.journeyplannew.JourneyAssistantDataOperateService;
import com.taobao.trip.jourprod.utils.JSONUtil;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.taobao.trip.jourprod.common.sal.hsf.config.Switcher.AI_CHAT_BLACK_LIST;
import static com.taobao.trip.jourprod.common.sal.hsf.config.Switcher.AI_MESSAGE_CHAT_LIMITER_COUNT;

/**
 * @Description ai行程规划助手工具类
 * <AUTHOR>
 * @Date 2025/1/22
 **/
@Component
public class JourneyPlanAiAssistantHelper implements InitializingBean {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneyPlanAiAssistantHelper.class);

    @AppSwitch(des = "智能体返回的场景和具体的场景code之间的映射", level = Level.p4)
    public static Map<String, String> AI_JOURNEY_SCENE_NAME_2_CODE_MAP = Maps.newHashMap();

    static {
        AI_JOURNEY_SCENE_NAME_2_CODE_MAP.put("行程规划类问题", "READY_TO_PLAN");
        AI_JOURNEY_SCENE_NAME_2_CODE_MAP.put("交通类问题", "TRAFFIC");
        AI_JOURNEY_SCENE_NAME_2_CODE_MAP.put("酒店预订类问题", "HOTEL_BOOKING");
        AI_JOURNEY_SCENE_NAME_2_CODE_MAP.put("酒店套餐类问题", "HOTEL_PACKAGE_BOOKING");
        AI_JOURNEY_SCENE_NAME_2_CODE_MAP.put("景点推荐问题", "SCENIC_RECOMMEND");
        AI_JOURNEY_SCENE_NAME_2_CODE_MAP.put("周边景点推荐问题", "SCENIC_RECOMMEND");
        AI_JOURNEY_SCENE_NAME_2_CODE_MAP.put("美食特产餐厅推荐问题", "SCENIC_RECOMMEND");
        AI_JOURNEY_SCENE_NAME_2_CODE_MAP.put("周边美食特产餐厅推荐问题", "SCENIC_RECOMMEND");
        AI_JOURNEY_SCENE_NAME_2_CODE_MAP.put("政治相关问题", "NOT_SUPPORT_SCENE");
        AI_JOURNEY_SCENE_NAME_2_CODE_MAP.put("目的地相关问题", "DESTINATION_RECOMMEND");
        AI_JOURNEY_SCENE_NAME_2_CODE_MAP.put("签证相关问题", "VISA");
        AI_JOURNEY_SCENE_NAME_2_CODE_MAP.put("其他行程相关问题", "NOT_SUPPORT_SCENE");
        AI_JOURNEY_SCENE_NAME_2_CODE_MAP.put("自然灾害", "NOT_SUPPORT_SCENE");
        AI_JOURNEY_SCENE_NAME_2_CODE_MAP.put("实时的当地安全问题", "NOT_SUPPORT_SCENE");
        AI_JOURNEY_SCENE_NAME_2_CODE_MAP.put("闲聊场景", "NOT_SUPPORT_SCENE");

        //2025-04-12意图优化，映射修改https://alidocs.dingtalk.com/i/nodes/lyQod3RxJKe9QjOMi2kLGRpkWkb4Mw9r?iframeQuery=sheet_range%3Dkgqie6hm_17_2_1_1
        AI_JOURNEY_SCENE_NAME_2_CODE_MAP.put("行程规划", "READY_TO_PLAN");
        AI_JOURNEY_SCENE_NAME_2_CODE_MAP.put("交通预定", "TRAFFIC");
        AI_JOURNEY_SCENE_NAME_2_CODE_MAP.put("酒店预定", "HOTEL_BOOKING");
        AI_JOURNEY_SCENE_NAME_2_CODE_MAP.put("目的地", "DESTINATION_RECOMMEND");
        AI_JOURNEY_SCENE_NAME_2_CODE_MAP.put("POI", "SCENIC_RECOMMEND");
        AI_JOURNEY_SCENE_NAME_2_CODE_MAP.put("办理签证", "VISA");
    }

    @AppSwitch(des = "是否开启新场景分类", level = Level.p4)
    public static boolean USE_NEW_SCENE_TYPE = false;

    @AppSwitch(des = "按用户测试场景分类", level = Level.p4)
    public static ArrayList<Long> TEST_NEW_SCENE_UID = new ArrayList<>();
    static{
        TEST_NEW_SCENE_UID.add(2214122342290L);
    }

    @AppSwitch(des = "NLU返回的场景和具体的场景code之间的映射", level = Level.p4)
    public static Map<String, String> AI_JOURNEY_SCENE_NAME_NLU_CODE_MAP = Maps.newHashMap();

    static {
        AI_JOURNEY_SCENE_NAME_NLU_CODE_MAP.put("酒店预订类问题", "HOTEL_BOOKING");
        AI_JOURNEY_SCENE_NAME_NLU_CODE_MAP.put("行程规划类问题", "READY_TO_PLAN");
        AI_JOURNEY_SCENE_NAME_NLU_CODE_MAP.put("交通类问题", "TRAFFIC");
        AI_JOURNEY_SCENE_NAME_NLU_CODE_MAP.put("酒店套餐类问题", "HOTEL_PACKAGE");
        AI_JOURNEY_SCENE_NAME_NLU_CODE_MAP.put("景点推荐问题", "SCENIC_RECOMMEND");
    }

    @AppSwitch(des = "问一问类型和场景AI场景值的映射", level = Level.p4)
    public static Map<String, String> AI_JOURNEY_SCENE_CODE_AISEARCH_CODE_MAP = Maps.newHashMap();

    static {
        AI_JOURNEY_SCENE_CODE_AISEARCH_CODE_MAP.put("HOTEL_BOOKING", "scenic1");
        AI_JOURNEY_SCENE_CODE_AISEARCH_CODE_MAP.put("READY_TO_PLAN", "scenic3");
        AI_JOURNEY_SCENE_CODE_AISEARCH_CODE_MAP.put("TRAFFIC", "traffic");
        AI_JOURNEY_SCENE_CODE_AISEARCH_CODE_MAP.put("SCENIC_RECOMMEND", "poi");
    }

    @AppSwitch(des = "场景分类appid-新版", level = Level.p4)
    public static String AI_JOURNEY_SCENE_ANALYSIS_APP_ID_V2 = "51bdbdecbfc7428b88e88452f39af1a8";

    @AppSwitch(des = "百炼appkey", level = Level.p4)
    public static String AI_JOURNEY_PLAN_APP_KEY = "sk-d2f06bf127ae440a860fa51c50091348";

    @AppSwitch(des = "消息id对应的缓存key", level = Level.p4)
    public static String MESSAGE_ID_KEY = "ai:journey:messageId:";

    @AppSwitch(des = "消息id缓存时间", level = Level.p4)
    public static Integer MESSAGE_ID_TTL_S = 24 * 60 * 60;

    @AppSwitch(des = "消息id对应的用户指令缓存key", level = Level.p4)
    public static String MESSAGE_ID_CHAT_KEY = "ai:journey:messageId:chat:";

    @AppSwitch(des = "消息id对应的用户指令缓存时间", level = Level.p4)
    public static Integer MESSAGE_ID_CHAT_TTL_S = 24 * 60 * 60;

    @AppSwitch(des = "消息id首次发送时间的缓存key", level = Level.p4)
    public static String MESSAGE_ID_SEND_TIME_KEY = "ai:journey:messageId:sendTime:";

    @AppSwitch(des = "消息id首次发送时间的缓存时间", level = Level.p4)
    public static Integer MESSAGE_ID_SEND_TIME_TTL_S = 10 * 60;

    @AppSwitch(des = "ai问一问对话限流ttl", level = Level.p4)
    public static Integer AI_MESSAGE_CHAT_LIMITER_TTL_S = 60 * 60;


    @AppSwitch(des = "交通价格提示文案过滤", level = Level.p4)
    public static List<String> traffic_notice_text_filter = Lists.newArrayList("未来${价格可能上涨}，建议尽快预订","价格随时间波动，建议尽早预订","当前为${历史低价}，建议尽快预订");

    @AppSwitch(des = "是否使用nlu的意图解析能力", level = Level.p4)
    public static Boolean USE_NLU_INTENT_PARSE = false;
    @AppSwitch(des = "是否使用新的意图识别能力", level = Level.p4)
    public static Boolean USE_NEW_INTENT = Boolean.FALSE;
    @AppSwitch(des = "新的意图识别灰度比例", level = Level.p4)
    public static Integer USE_NEW_INTENT_GRAY_PERCENT = 0;

    private static final String SPLIT = "\\|";

    //ai问一问对话限流key
    private static final String AI_MESSAGE_CHAT_LIMITER_KEY = "tripOd:aiChat:limiter";

    @Resource
    private MdbTairHelper mdbTairHelper;

    @Resource
    private LdbTairManager ldbTairManager;

    @Resource
    private EnvSwitch envSwitch;

    @Resource
    private JourneyPlanAiAssistantSessionHelper journeyPlanAiAssistantSessionHelper;

    @Resource
    private JourneyPlanAiAssistantMessageStatusHelper messageStatusHelper;

    @AppSwitch(des = "ai主预发环境ip", level = Level.p4)
    public static String aiPreEnvUrl;
    @Resource
    private IntentRecognitionService intentRecognitionService;
    @Resource
    private JourneyPlanAiAssistantMessageHelper journeyPlanAiAssistantMessageHelper;

    /**
     * 分析ai行程规划场景信息
     */
    public AiJourneySceneModel analysisAiJourneySceneInfo(ChatContext context) {
        JourneyPlanAiChatRequest request = context.getRequest();
        // 生成prompt
        String prompt = generateSceneAnalysisPrompt(context);

        String errorMsg = "session-{1},prompt-{2}";
        Exception e = null;
        AiJourneySceneModel result = new AiJourneySceneModel(AiJourneyPlanSceneEnum.NOT_SUPPORT_SCENE);
        try {
            //是否黑名单用户
            if (AI_CHAT_BLACK_LIST.contains(request.getUserId())) {
                LOGGER.recordOutput(new LogModel("analysisAiJourneySceneInfo")
                        .message("hitBlackUserList")
                        .request(JSON.toJSONString(request))
                        .userId(request.getUserId()));
                return result;
            }

            //根据时间段限流
            String key = AI_MESSAGE_CHAT_LIMITER_KEY + request.getUserId();
            Object value = ldbTairManager.get(key);
            if (Objects.nonNull(value) && AI_MESSAGE_CHAT_LIMITER_COUNT < (Integer) value) {
                LOGGER.recordOutput(new LogModel("analysisAiJourneySceneInfo")
                        .message("limiter")
                        .request(JSON.toJSONString(request)));
                return result;
            }
            ldbTairManager.incr(key, 1, 0, AI_MESSAGE_CHAT_LIMITER_TTL_S);

            // 命中意图后，正常解析并返回
            Long userId = request.getUserId();
            String sceneInfo = null;
            if (BooleanUtils.isTrue(USE_NEW_INTENT) && request.getUserId() % 100 <= USE_NEW_INTENT_GRAY_PERCENT) {
                List<String> historyUserMessages = Lists.newArrayList();
                List<MessageParam> messageParams = journeyPlanAiAssistantMessageHelper.queryHistoryMessagesWithSession(userId, context.getSessionId());
                if (CollectionUtils.isNotEmpty(messageParams)) {
                    historyUserMessages = messageParams.stream()
                            .filter(messageParam -> Objects.equals(messageParam.getRole(), MessageRole.user))
                            .map(messageParam -> messageParam.getContent())
                            .collect(Collectors.toList());
                }
                IntentRecognitionRequest recognitionRequest = new IntentRecognitionRequest();
                recognitionRequest.setQuery(prompt);
                recognitionRequest.setHistoryMessageList(historyUserMessages);
                recognitionRequest.setProviderType(IntentProviderTypeEnum.FAI);
                IntentRecognitionResponse response = intentRecognitionService.recognize(recognitionRequest);
                LOGGER.recordOutput(new LogModel("syncCallAgent")
                        .response(JSONUtil.toJSONStringForLog(response))
                        .message("FAI意图识别返回结果")
                        .userId(userId));
                if (response.isSuccess()) {
                    sceneInfo = response.getResult();
                }
            } else {
                sceneInfo = syncCallAgent(userId, prompt, AI_JOURNEY_SCENE_ANALYSIS_APP_ID_V2, context);
            }
            if (StringUtils.isNotBlank(sceneInfo)) {
                result = JSON.parseObject(sceneInfo, AiJourneySceneModel.class);
                if (Objects.nonNull(result)) {
                    context.setSummarizedChat(result.getSummaryQuery());
                }
                AiJourneySceneModelConvert.extractAiJourneyScene(result);
                return result;
            }

            // 未命中意图需要返回不支持的场景给到用户界面，而不是报错导致界面不输出和卡住等异常
            LOGGER.recordNormalFailedRpc(new LogModel("analysisAiJourneySceneInfo.fail")
                    .message(errorMsg, context.getSessionId(), prompt));
        } catch (Exception ex) {
            e = ex;
            // 执行意图分析时报错，也返回不支持的场景
            LOGGER.recordNormalException(new LogModel("analysisAiJourneySceneInfo.error")
                    .message(errorMsg, context.getSessionId(), prompt).e(ex));
        } finally {
            context.setAiJourneySceneModel(result);
            LOGGER.recordOutput(new LogModel("analysisAiJourneySceneInfo")
                    .message(errorMsg, context.getSessionId(), prompt)
                    .request(JSONUtil.toJSONStringForLog(request))
                    .code(String.join("，", context.getSessionId(), context.getSystemMessageId(), context.getUserMessageId()))
                    .response(JSONUtil.toJSONStringForLog(result))
                    .e(e));
        }
        return result;
    }

    @AteyeInvoker(description = "分析ai行程规划场景信息", paraDesc = "query&userId")
    public String ateyeAnalysisAiJourneySceneInfo(String query, String userId) {
        JourneyPlanAiChatRequest journeyPlanAiChatRequest = new JourneyPlanAiChatRequest();
        journeyPlanAiChatRequest.setChat(query);
        journeyPlanAiChatRequest.setUserId(Long.valueOf(userId));
        ChatContext chatContext = new ChatContext();
        chatContext.setRequest(journeyPlanAiChatRequest);
        return JSON.toJSONString(analysisAiJourneySceneInfo(chatContext));
    }

    /**
     * 分析ai行程规划场景信息
     */
    public String syncCallAgent(Long userId, String prompt, String appId, ChatContext context) {
        if (StringUtils.isBlank(appId)) {
            throw new TripJourneyException("appId不能为空");
        }
        long startTime = System.currentTimeMillis();
        // 获取当前会话id
        String sessionId = context.getSessionId();
        // 请求大模型
        ApplicationParam param = ApplicationParam.builder()
                // 若没有配置环境变量，可用百炼API Key将下行替换为：.apiKey("sk-xxx")。但不建议在生产环境中直接将API Key硬编码到代码中，以减少API Key泄露风险。
                .apiKey(AI_JOURNEY_PLAN_APP_KEY)
                // 替换为实际的应用 ID
                .appId(appId)
                .prompt(prompt)
                .sessionId(sessionId)
                .build();
        Application application = new Application();
        try {
            if (envSwitch.inProjectEnv()) {
                // 项目环境拦截，解决频繁申请安全外联
                Map<String, String> requestParam = Maps.newHashMap();
                requestParam.put("apiKey", AI_JOURNEY_PLAN_APP_KEY);
                requestParam.put("appId", appId);
                requestParam.put("prompt", prompt);
                requestParam.put("sessionId", sessionId);
                String result = HttpUtil.doPost(aiPreEnvUrl + "/pre/ai/syncCallAgent", JSON.toJSONString(requestParam), String.class);
                Map<String, String> mapResult = JSON.parseObject(result, new TypeReference<>() {});
                LOGGER.recordOutput(new LogModel("syncCallAgent")
                    .message("appId-{0},requestId-{1},prompt-{2},response-{3}", appId, mapResult.get("requestId"), prompt, mapResult.get("text"))
                    .userId(userId));
                // 更新sessionid
                journeyPlanAiAssistantSessionHelper.updateSessionIdIfNeed(userId, appId, mapResult.get("sessionId"), sessionId);
                return mapResult.get("text");
            }

            ApplicationResult call = application.call(param);
            LOGGER.recordOutput(new LogModel("syncCallAgent")
                .message("appId-{0},requestId-{1},prompt-{2},response-{3}", appId, call.getRequestId(), prompt, call.getOutput().getText())
                .cost(System.currentTimeMillis() - startTime)
                .userId(userId));
            // 更新sessionid
            journeyPlanAiAssistantSessionHelper.updateSessionIdIfNeed(userId, appId, call.getOutput().getSessionId(), sessionId);
            return call.getOutput().getText();
        } catch (Throwable e) {
            LOGGER.recordNormalException(new LogModel("analysisAiJourneySceneInfo")
                    .e(e)
                    .cost(System.currentTimeMillis() - startTime)
                    .message("智能体请求失败"));
            return StringUtils.EMPTY;
        }
    }

    /**
     * 生成场景分类prompt，直接使用用户输入指令即可
     */
    private String generateSceneAnalysisPrompt(ChatContext context) {
        JourneyPlanAiChatRequest request = context.getRequest();
        if (StringUtils.isBlank(request.getChat())) {
            throw new TripJourneyException("用户指令为空");
        }
        // 初始化一个StringBuffer用于存储拼接的内容
        StringBuilder chat = new StringBuilder();

        // 获取原始消息列表
        List<MessageParam> originalMessageInfo = context.getOriginalMessageInfo();
        if (CollectionUtils.isEmpty(originalMessageInfo)) {
            chat.append(request.getChat());
            return chat.toString();
        }

        for (MessageParam messageParam : originalMessageInfo) {
            if (Objects.equals(messageParam.getRole(), MessageRole.system) || Objects.equals(messageParam.getRole(), MessageRole.assistant)) {
                if (StringUtils.isEmpty(messageParam.getContent()) || StringUtils.isEmpty(messageParam.getExtra())) {
                    continue;
                }
                JSONObject extraObject = JSON.parseObject(messageParam.getExtra());
                String chatQuery = extraObject.getString(JourneyAssistantDataOperateService.MESSAGE_SUMMARIZED_CHAT);
                if (StringUtils.isEmpty(chatQuery)) {
                    continue;
                }
                chat.append(chatQuery).append("\n");
            }
        }
        // 最后追加用户最新聊天输入
        chat.append(request.getChat());

        LOGGER.recordOutput(new

                LogModel("generateSceneAnalysisPrompt").

                response(chat.toString()));

        return chat.toString();
    }


    /**
     * 生成唯一id
     */
    public void generateMessageId(JourneyPlanAiChatRequest request, ChatContext chatContext) {
        // 系统消息id
        String systemMessageId = UUID.randomUUID().toString();
        chatContext.setSystemMessageId(systemMessageId);
        // 用户消息id
        String userMessageId = UUID.randomUUID().toString();
        chatContext.setUserMessageId(userMessageId);
        // 记录当前messageId
        recordMessageIdAndChat(request, chatContext);
    }

    /**
     * 记录当前messageId，以及用户指令
     */
    private void recordMessageIdAndChat(JourneyPlanAiChatRequest request, ChatContext chatContext) {
        // 生成key
        String key = genMessageIdKey(request.getUserId(), chatContext.getSessionId());
        // 存数据
        mdbTairHelper.putValue(key, chatContext.getSystemMessageId() + "|" + chatContext.getUserMessageId(), MESSAGE_ID_TTL_S);
        // 记录消息id对应的用户指令
        String chatKey = genMessageIdChatKey(chatContext.getSystemMessageId());
        // 存数据
        mdbTairHelper.putValue(chatKey, request.getChat(), MESSAGE_ID_CHAT_TTL_S);

        //存消息一开始发送的时间
        String idSendTimeKey = genMessageIdSendTimeKey(chatContext.getSystemMessageId());
        mdbTairHelper.putValue(idSendTimeKey, System.currentTimeMillis(), MESSAGE_ID_SEND_TIME_TTL_S);
    }

    /**
     * 生成消息对应用户指令的缓存key
     */
    private String genMessageIdChatKey(String messageId) {
        return MESSAGE_ID_CHAT_KEY + messageId;
    }

    /**
     * 生成消息首次发送时间的缓存key
     */
    private String genMessageIdSendTimeKey(String messageId) {
        return MESSAGE_ID_SEND_TIME_KEY + messageId;
    }

    /**
     * 生成消息对应的指令
     */
    public String getChat(String messageId) {
        String key = genMessageIdChatKey(messageId);
        Object value = mdbTairHelper.getValue(key);
        if (Objects.isNull(value)) {
            return StringUtils.EMPTY;
        }
        return value.toString();
    }

    /**
     * 当前没有结束回答的消息-系统消息
     */
    public String currentNotStoppedSystemMessageId(Long userId) {
        Pair<String, String> messageIdPair = currentNotStoppedMessageId(userId, StringUtils.EMPTY);
        if (Objects.nonNull(messageIdPair)) {
            return messageIdPair.getLeft();
        }
        return StringUtils.EMPTY;
    }

    /**
     * 当前没有结束回答的消息-用户消息
     */
    public String currentNotStoppedUserMessageId(Long userId) {
        Pair<String, String> messageIdPair = currentNotStoppedMessageId(userId, StringUtils.EMPTY);
        if (Objects.nonNull(messageIdPair)) {
            return messageIdPair.getRight();
        }
        return StringUtils.EMPTY;
    }

    /**
     * 当前没有结束回答的消息
     * todo 逻辑jieou
     */
    @RunLog
    @Nullable
    public Pair<String, String> currentNotStoppedMessageId(Long userId, String sessionId) {
        if (StringUtils.isBlank(sessionId)) {
            // 如果会话id为空，就查询用户的最新的消息id
            sessionId = journeyPlanAiAssistantSessionHelper.currentSession(userId);
        }
        String key = genMessageIdKey(userId, sessionId);
        Object value = mdbTairHelper.getValue(key);
        if (Objects.isNull(value)) {
            LOGGER.recordNormalException(new LogModel("currentNotStoppedMessageId_MessageId_null")
                    .userId(userId)
                    .message("sessionId={0}", sessionId));
            return null;
        }

        String systemAndUserMessageId = value.toString();
        String[] messageIdArray = systemAndUserMessageId.split(SPLIT);
        if (messageIdArray.length != 2) {
            LOGGER.recordNormalException(new LogModel("currentNotStoppedMessageId_MessageId_length_error")
                    .userId(userId)
                    .message("sessionId={0}, systemAndUserMessageId={1}", sessionId, systemAndUserMessageId)
                    .response(String.valueOf(messageIdArray.length)));
            return null;
        }
        String systemMessageId = messageIdArray[0];
        String userMessageId = messageIdArray[1];

        //判断是否超过时间阈值了
        // todo 删除逻辑
        String sendTimeKey = genMessageIdSendTimeKey(systemMessageId);
        Object sendTimeValue = mdbTairHelper.getValue(sendTimeKey);
        if (Objects.isNull(sendTimeValue)) {
            LOGGER.recordNormalException(new LogModel("currentNotStoppedMessageId_sendTimeValue")
                    .userId(userId)
                    .message("sessionId={0}, sysMessageId={1},userMessageId={2}", sessionId, systemMessageId, userMessageId));
            return null;
        }

        // 判断消息是否被暂停了
        String messageStopKey = TairKeyFactory.getSysMessageIdKey(systemMessageId);
        Object messageStop = mdbTairHelper.getValue(messageStopKey);
        if (Objects.nonNull(messageStop)) {
            LOGGER.recordNormalException(new LogModel("currentNotStoppedMessageId_messageStop")
                    .userId(userId)
                    .message("sessionId={0}, sysMessageId={1},userMessageId={2}", sessionId, systemMessageId, userMessageId)
                    .response("value=" + messageStop));
            // 消息被暂停了，直接返回空
            return null;
        }
        boolean end = messageStatusHelper.isEnd(systemMessageId);
        boolean stop = messageStatusHelper.isStop(systemMessageId);
        if (end || stop) {
            LOGGER.recordNormalException(new LogModel("currentNotStoppedMessageId_end_or_stop").userId(userId).response(end + "_" + stop));
            return null;
        }

        return Pair.of(systemMessageId, userMessageId);
    }

    /**
     * 消息key只和userid绑定
     */
    private String genMessageIdKey(Long userId, String sessionId) {
        if (Objects.isNull(userId)) {
            throw new TripJourneyException("messageId生成失败");
        }

        return MESSAGE_ID_KEY + userId + sessionId;
    }

    /**
     * Invoked by a BeanFactory after it has set all bean properties supplied
     * (and satisfied BeanFactoryAware and ApplicationContextAware).
     * <p>This method allows the bean instance to perform initialization only
     * possible when all bean properties have been set and to throw an
     * exception in the event of misconfiguration.
     *
     * @throws Exception in the event of misconfiguration (such
     *                   as failure to set an essential property) or if initialization fails.
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        SwitchManager.init(JourneyPlanAiAssistantHelper.class);
    }


}
