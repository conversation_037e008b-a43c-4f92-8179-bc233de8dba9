package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.trip.tripdivision.domain.TrdiDivisionDO;
import com.alitrip.recommend.domain.homepage.DestinationInfo;
import com.fliggy.crowd.service.domain.profile.ProfileValue;
import com.google.common.collect.Lists;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.trip.jourdprod.core.model.jourplan.bizmodel.JourneyChannelBizV3DO;
import com.taobao.trip.jourprod.biz.common.ThreadPoolMonitor;
import com.taobao.trip.jourprod.biz.frt.enums.UserTypeEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplan.JourneyChannelQueryV3Frt;
import com.taobao.trip.jourprod.biz.frt.journeyplan.helper.JourneyPlanHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.CompletableUtils.CompletableInstance;
import com.taobao.trip.jourprod.biz.frt.recommend.HomeRecommendServiceClient;
import com.taobao.trip.jourprod.biz.frt.routemap.RouteMapService;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.jourprod.common.lang.utils.MD5Util;
import com.taobao.trip.jourprod.common.sal.annotation.RunLog;
import com.taobao.trip.jourprod.common.sal.hsf.tripdivision.TrdiDivisionReadServiceClient;
import com.taobao.trip.jourprod.common.sal.hsf.userRelated.UserProfileManager;
import com.taobao.trip.jourprod.common.sal.tair.impl.MdbTairCommonHelper;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiInitRequest;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.AiJourneyMessageModel;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanAiInitResult.RecommendCard;
import com.taobao.trip.tripjourneyop.domain.JourneyIndexInfo;
import com.taobao.tripca.facade.request.route.JourneyPlanVO;
import com.taobao.tripjourneyop.domain.result.CityAttractionModel;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.taobao.trip.jourprod.biz.frt.enums.UserTypeEnum.*;
import static com.taobao.trip.jourprod.common.sal.hsf.config.JourneyChannelPlanNewSwitch.AI_CHANNEL_GUESS_CITY_RECOMMEND_PAGE_SIZE;

/**
 * @Description 问一问初始化接口
 * <AUTHOR>
 * @Date 2025/2/14
 **/
@Component
public class JourneyPlanAiAssistantInitHelper implements InitializingBean {

    private static final FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneyPlanAiAssistantInitHelper.class);

    /**
     * 线程池
     */
    public static final ThreadPoolExecutor userMessageQueryExecutor = new ThreadPoolExecutor(50, 50, 0, TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<Runnable>(100),
        new CustomizableThreadFactory("userMessageQueryExecutor"));

    static {
        new Thread(new ThreadPoolMonitor(userMessageQueryExecutor, "userMessageQueryExecutor")).start();
    }

    @AppSwitch(des = "城市提示语对应的key", level = Level.p4)
    public static String CITY_MESSAGE_KEY = "city_message";

    @AppSwitch(des = "默认热门搜索城市", level = Level.p4)
    public static List<Pair<Long, String>> DEFAULT_HOT_CITY = Lists.newArrayList(Pair.of(110000L, "北京"), Pair.of(330100L, "杭州"));

    @AppSwitch(des = "推荐文案对应的appid", level = Level.p4)
    public static String RECOMMEND_MESSAGE_APP_ID = "22d78a245d904d09a6ca7525802c3d86";

    @AppSwitch(des = "用户对应的推荐文案对应的key", level = Level.p4)
    public static String USER_RECOMMEND_MESSAGE_KEY = "user_recommend_message";

    @AppSwitch(des = "用户推荐文案缓存时间", level = Level.p4)
    public static Integer USER_RECOMMEND_MESSAGE_TTL_S = 7 * 24 * 60 * 60;

    @AppSwitch(des = "推荐目的地卡片数量", level = Level.p4)
    public static Integer recommendCardSize = 4;

    @AppSwitch(des = "城市卡片对应提示语逻辑", level = Level.p4)
    public static String CITY_MESSAGE_LOGIC = "规划%s旅行路线";

    /**
     * 所有需要获取的消息数量
     */
    public static Integer allMessage = 3;

    /**
     * 一轮找到的消息数量
     */
    public static Integer oneRoundMessage = 2;

    /**
     * 用户搜索历史城市key
     */
    private static final String USER_SEARCH_HOT_CITY_KEY = "user_search_city_top_10:";

    /**
     * 所有搜索历史城市key
     */
    private static final String ALL_SEARCH_HOT_CITY_KEY = "search_hot_city";

    /**
     * 诸葛高频位移key
     */
    private static final String HIGH_FRE_MOVE_KEY = "high_fre_move";

    /**
     * 诸葛认证学生key
     */
    private static final String RT_IS_VERIFIED_STUDENT_KEY = "rt_is_verified_student";

    /**
     * 诸葛亲子游key
     */
    private static final String D2QZY_KEY = "d2qzy";

    /**
     * 常住地
     */
    public static final String STAY_CITY_NAME_NEW = "lbs_permanent_city";

    /**
     * 常住地
     */
    public static final String STAY_CITY_ID_NEW = "lbs_permanent_city_id";

    /**
     * 城市景点探索类型
     */
    public static final String cityAttractionsExplorationType = "cityAttractionsExploration";

    /**
     * 城市周边线路类型
     */
    public static final String peripheralLinesType = "peripheralLines";

    /**
     * 本地城市周边线路类型
     */
    public static final String peripheralLinesLocal = "peripheralLinesLocal";

    /**
     * 城市路线引导类型
     */
    public static final String routeGuidanceType = "routeGuidance";

    @Resource
    private MdbTairCommonHelper mdbTairCommonHelper;

    @Resource
    private TrdiDivisionReadServiceClient trdiDivisionReadServiceClient;

    @Resource
    private JourneyChannelQueryV3Frt journeyChannelQueryV3Frt;

    @Resource
    private RouteMapService routeMapService;

    @Resource
    private JourneyPlanHelper journeyPlanHelper;

    @Resource
    private UserProfileManager userProfileManager;

    @Resource
    private JourneyPlanAiAssistantHelper journeyPlanAiAssistantHelper;

    @Resource
    private HomeRecommendServiceClient homeRecommendServiceClient;


    /**
     * 查询用户的搜索城市
     */
    public List<Pair<Long, String>> getSearchCityList(Long userId) {
        List<Long> cityIdList = Lists.newArrayList();
        // 用户搜索城市
        String userSearchKey = USER_SEARCH_HOT_CITY_KEY + userId;
        Object value = mdbTairCommonHelper.getValue(userSearchKey);
        if (value != null) {
            String result = (String) value;
            cityIdList = Arrays.stream(result.split(",")).map(NumberUtils::toLong).filter(id -> id > 0).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(cityIdList)) {
            Map<Long, TrdiDivisionDO> divisionMapInfoByOldId = trdiDivisionReadServiceClient.getDivisionMapInfoByOldId(cityIdList);
            if (MapUtils.isNotEmpty(divisionMapInfoByOldId)) {
                return divisionMapInfoByOldId.values().stream().filter(Objects::nonNull).map(item -> Pair.of(item.getId(), item.getName())).collect(Collectors.toList());
            }
        }
        return Lists.newArrayList();
    }

    /**
     * 查询热门搜索城市
     */
    public List<Pair<Long, String>> getCommonSearchCityList() {
        List<Long> cityIdList = Lists.newArrayList();
        // 热门搜索城市
        Object allValue = mdbTairCommonHelper.getValue(ALL_SEARCH_HOT_CITY_KEY);
        if (allValue != null) {
            String result = (String) allValue;
            cityIdList = Arrays.stream(result.split(",")).map(NumberUtils::toLong).filter(id -> id > 0).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(cityIdList)) {
            Map<Long, TrdiDivisionDO> divisionMapInfoByOldId = trdiDivisionReadServiceClient.getDivisionMapInfoByOldId(cityIdList);
            if (MapUtils.isNotEmpty(divisionMapInfoByOldId)) {
                return divisionMapInfoByOldId.values().stream().filter(Objects::nonNull).map(item -> Pair.of(item.getId(), item.getName())).collect(Collectors.toList());
            }
        }
        return DEFAULT_HOT_CITY;
    }

    /**
     * 查询行程城市
     */
    public List<Pair<Long, String>> getUserJourneyCityList(Long userId) {
        // 查询近期行程
        JourneyChannelBizV3DO userIndex = journeyChannelQueryV3Frt.fetchUserIndex(userId, 10);
        if (Objects.isNull(userIndex) || CollectionUtils.isEmpty(userIndex.getIndexInfoList())) {
            return Lists.newArrayList();
        }
        // 解析出城市
        List<JourneyIndexInfo> indexInfoList = userIndex.getIndexInfoList();
        return indexInfoList
            .stream()
            .map(item -> Pair.of(item.getMaterielIndex().getDestCityId(), item.getMaterielIndex().getDestCityName()))
            .filter(item -> Objects.nonNull(item.getLeft()) && StringUtils.isNotBlank(item.getRight()))
            .collect(Collectors.toList());
    }

    /**
     * 查询用户路线关联城市
     */
    public List<Pair<Long, String>> getUserJourneyPlanCityList(Long userId) {
        //先查 行程规划列表接口
        List<JourneyPlanVO> planDataList = routeMapService.queryJourneyPlanList(userId);
        if (CollectionUtils.isEmpty(planDataList)) {
            return Lists.newArrayList();
        }
        // 解析出城市
        return planDataList
            .stream()
            .filter(item -> BooleanUtils.isTrue(item.getIsRecentPlan()))
            .map(item -> Pair.of(journeyPlanHelper.dealGetCityIdByPlan(item), journeyPlanHelper.dealGetCityNameByPlan(item)))
            .filter(item -> Objects.nonNull(item.getLeft()) && StringUtils.isNotBlank(item.getRight()))
            .collect(Collectors.toList());
    }

    /**
     * 查询用户类型 是否认证学生 是否高频位移 是否亲子类型
     */
    public List<UserTypeEnum> getUserTypeList(Long userId, String longitude, String latitude) {
        // 查询用户标签
        Map<String, ProfileValue> userProfile = userProfileManager.userProfile(userId, Lists.newArrayList(HIGH_FRE_MOVE_KEY, RT_IS_VERIFIED_STUDENT_KEY, D2QZY_KEY));
        if (MapUtils.isEmpty(userProfile)) {
            return Lists.newArrayList();
        }
        // 解析出用户类型
        List<UserTypeEnum> userTypeList = Lists.newArrayList();
        Optional.of(userProfile).map(item -> item.get(HIGH_FRE_MOVE_KEY)).map(ProfileValue::getValue).filter("1"::equals).ifPresent(item -> userTypeList.add(MOVE_REPEATEDLY));
        Optional.of(userProfile).map(item -> item.get(RT_IS_VERIFIED_STUDENT_KEY)).map(ProfileValue::getValue).filter("1"::equals).ifPresent(item -> userTypeList.add(STUDENT));
        Optional.of(userProfile).map(item -> item.get(D2QZY_KEY)).map(ProfileValue::getValue).filter("1"::equals).ifPresent(item -> userTypeList.add(PARENTING));
        return userTypeList;
    }

    /**
     * 获取用户lbs
     */
    public List<Pair<Long, String>> getUserMoveCityList(String longitude, String latitude) {
        if (StringUtils.isAnyBlank(longitude, latitude)) {
            return Lists.newArrayList();
        }
        TrdiDivisionDO divisionInfoByLatLng = trdiDivisionReadServiceClient.getDivisionInfoByLatLng(NumberUtils.toDouble(latitude), NumberUtils.toDouble(longitude));
        if (Objects.isNull(divisionInfoByLatLng)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(Pair.of(divisionInfoByLatLng.getId(), divisionInfoByLatLng.getName()));
    }

    /**
     * 获取用户的基本信息
     */
    @RunLog
    public Pair<Long, String> genUserBasicInfo(String longitude, String latitude) {
        // 用户位移信息
        List<Pair<Long, String>> result = getUserMoveCityList(longitude, latitude);
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        return result.get(0);
    }

    /**
     * Invoked by a BeanFactory after it has set all bean properties supplied
     * (and satisfied BeanFactoryAware and ApplicationContextAware).
     * <p>This method allows the bean instance to perform initialization only
     * possible when all bean properties have been set and to throw an
     * exception in the event of misconfiguration.
     *
     * @throws Exception in the event of misconfiguration (such
     * as failure to set an essential property) or if initialization fails.
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        SwitchManager.init(JourneyPlanAiAssistantInitHelper.class);
    }

    /**
     * 并行获取用户信息
     *
     * @param request               请求信息
     * @param userTypeList          诸葛标签
     * @param journeyCityList       用户行程目的地
     * @param journeyPlanCityList   用户行程规划目的地
     * @param searchCityList        用户搜索信息
     * @param moveCityList          用户位移信息
     * @param regularStayCityList   用户常住地信息
     * @param commonSearchCityList  公共搜索信息
     */
    public void threadFillUserMessageWithCityName(JourneyPlanAiInitRequest request, List<UserTypeEnum> userTypeList, List<String> journeyCityList, List<String> journeyPlanCityList, List<String> searchCityList, List<String> moveCityList, List<String> regularStayCityList, List<String> commonSearchCityList) {
        // 用户城市信息
        List<Pair<Long, String>> journeyCityPairList = new ArrayList<>();
        List<Pair<Long, String>> journeyPlanCityPairList = new ArrayList<>();
        List<Pair<Long, String>> searchCityPairList = new ArrayList<>();
        // 通用热门城市
        List<Pair<Long, String>> commonSearchCityPairList = new ArrayList<>();
        List<Pair<Long, String>> moveCityPairList = new ArrayList<>();
        List<Pair<Long, String>> regularStayCityPairList = new ArrayList<>();
        threadFillUserMessage(request, userTypeList, journeyCityPairList, journeyPlanCityPairList, searchCityPairList, moveCityPairList, regularStayCityPairList, commonSearchCityPairList);
        journeyCityPairList.forEach(item -> journeyCityList.add(item.getRight()));
        journeyPlanCityPairList.forEach(item -> journeyPlanCityList.add(item.getRight()));
        searchCityPairList.forEach(item -> searchCityList.add(item.getRight()));
        commonSearchCityPairList.forEach(item -> commonSearchCityList.add(item.getRight()));
        moveCityPairList.forEach(item -> moveCityList.add(item.getRight()));
        regularStayCityPairList.forEach(item -> regularStayCityList.add(item.getRight()));
    }

    /**
     * 并行获取用户信息
     *
     * @param request               请求信息
     * @param userTypeList          诸葛标签
     * @param journeyCityList       用户行程目的地
     * @param journeyPlanCityList   用户行程规划目的地
     * @param searchCityList        用户搜索信息
     * @param moveCityList          用户位移信息
     * @param regularStayCityList   用户常住地信息
     * @param commonSearchCityList  公共搜索信息
     */
    public void threadFillUserMessageWithCityId(JourneyPlanAiInitRequest request, List<UserTypeEnum> userTypeList, List<Long> journeyCityList, List<Long> journeyPlanCityList, List<Long> searchCityList, List<Long> moveCityList, List<Long> regularStayCityList, List<Long> commonSearchCityList) {
        // 用户城市信息
        List<Pair<Long, String>> journeyCityPairList = new ArrayList<>();
        List<Pair<Long, String>> journeyPlanCityPairList = new ArrayList<>();
        List<Pair<Long, String>> searchCityPairList = new ArrayList<>();
        // 通用热门城市
        List<Pair<Long, String>> commonSearchCityPairList = new ArrayList<>();
        List<Pair<Long, String>> moveCityPairList = new ArrayList<>();
        List<Pair<Long, String>> regularStayCityPairList = new ArrayList<>();
        threadFillUserMessage(request, userTypeList, journeyCityPairList, journeyPlanCityPairList, searchCityPairList, moveCityPairList, regularStayCityPairList, commonSearchCityPairList);
        journeyCityPairList.forEach(item -> journeyCityList.add(item.getLeft()));
        journeyPlanCityPairList.forEach(item -> journeyPlanCityList.add(item.getLeft()));
        searchCityPairList.forEach(item -> searchCityList.add(item.getLeft()));
        commonSearchCityPairList.forEach(item -> commonSearchCityList.add(item.getLeft()));
        moveCityPairList.forEach(item -> moveCityList.add(item.getLeft()));
        regularStayCityPairList.forEach(item -> regularStayCityList.add(item.getLeft()));
    }


    /**
     * 并行获取用户信息
     *
     * @param request               请求信息
     * @param userTypeList          诸葛标签
     * @param journeyCityList       用户行程目的地
     * @param journeyPlanCityList   用户行程规划目的地
     * @param searchCityList        用户搜索信息
     * @param moveCityList          用户位移信息
     * @param regularStayCityList   用户常住地信息
     * @param commonSearchCityList  公共搜索信息
     */
    public void threadFillUserMessage(JourneyPlanAiInitRequest request, List<UserTypeEnum> userTypeList,
        List<Pair<Long, String>> journeyCityList, List<Pair<Long, String>> journeyPlanCityList,
        List<Pair<Long, String>> searchCityList, List<Pair<Long, String>> moveCityList,
        List<Pair<Long, String>> regularStayCityList, List<Pair<Long, String>> commonSearchCityList) {
        CompletableInstance completableInstance = CompletableUtils.getInstance(userMessageQueryExecutor, "userMessageQueryExecutor");
        completableInstance.executeCompletableFuture(() -> {
            // 用户类型
            List<UserTypeEnum> result = getUserTypeList(request.getUserId(), request.getLongitude(), request.getLatitude());
            userTypeList.addAll(result);
            return null;
        });
        completableInstance.executeCompletableFuture(() -> {
            // 用户行程目的地
            List<Pair<Long, String>> result = getUserJourneyCityList(request.getUserId());
            journeyCityList.addAll(result);
            return null;
        });
        completableInstance.executeCompletableFuture(() -> {
            // 线路目的地
            List<Pair<Long, String>> result = getUserJourneyPlanCityList(request.getUserId());
            journeyPlanCityList.addAll(result);
            return null;
        });
        completableInstance.executeCompletableFuture(() -> {
            // 用户搜索信息
            List<Pair<Long, String>> result = getSearchCityList(request.getUserId());
            searchCityList.addAll(result);
            return null;
        });
        completableInstance.executeCompletableFuture(() -> {
            // 全局热门搜索信息
            List<Pair<Long, String>> result = getCommonSearchCityList();
            commonSearchCityList.addAll(result);
            return null;
        });
        completableInstance.executeCompletableFuture(() -> {
            // 用户位移信息
            List<Pair<Long, String>> result = getUserMoveCityList(request.getLongitude(), request.getLatitude());
            moveCityList.addAll(result);
            return null;
        });
        // 等待所有任务执行完
        completableInstance.end();
    }

    /**
     * 决策出要出引导语的城市
     <产品逻辑>
     无位移：获取城市为订单城市或路线城市（有订单优先取订单、没有订单再取路线），订单城市（2）>路线城市（1）
     兜底：当前位置（2）>热门城市（1）
     有位移：优先获取位移城市描述2个，订单城市取一个，无订单时均取位移3个。
     位移2个>订单1个
     位移时优先推周边游路线
     兜底：当前位置城市（2）>热门城市

     订单城市只取-优先出行的目的地城市。
     */
    public Pair<Boolean, List<Long>> decideCityList(Long userId,
                                                    List<Long> journeyCityList,
                                                    List<Long> journeyPlanCityList,
                                                    List<Long> searchCityList,
                                                    List<Long> moveCityList,
                                                    List<Long> regularStayCityList,
                                                    List<Long> commonSearchCityList) {
        // 判断是否有位移
        boolean isMove = false;
        if (CollectionUtils.isNotEmpty(regularStayCityList) && CollectionUtils.isNotEmpty(moveCityList)) {
            Long regularCity = regularStayCityList.get(0);
            Long moveCity = moveCityList.get(0);
            if (!Objects.equals(regularCity, moveCity)) {
                isMove = true;
            }
        }

        List<Long> cityList = new ArrayList<>();
        Long lbsCityId = null;
        if (CollectionUtils.isNotEmpty(moveCityList)) {
            lbsCityId = moveCityList.get(0);
        }
        Long orderCityId = null;
        if (CollectionUtils.isNotEmpty(journeyCityList)) {
            orderCityId = journeyCityList.get(0);
        }
        List<DestinationInfo> featuredDestinationList = homeRecommendServiceClient.getFeaturedDestinationList(userId, lbsCityId, orderCityId, AI_CHANNEL_GUESS_CITY_RECOMMEND_PAGE_SIZE);
        if (CollectionUtils.isNotEmpty(featuredDestinationList)) {
            cityList = featuredDestinationList.stream().map(info -> Long.parseLong(info.getDestinationCode())).collect(Collectors.toList());
        }
        return Pair.of(isMove, cityList);
    }

    /**
     * 获取位移场景下对应的消息推荐
     * 优先取周边推荐
     * 如果在常住地，即查询本地周边玩法
     */
    public List<AiJourneyMessageModel> getRelatedCityMessage(List<Long> cityList, Long regularCity, String type) {
        int curMessageSize = 0;
        int curCity = 0;
        Map<Long, CityAttractionModel> cityMessageMap = new HashMap<>();
        List<AiJourneyMessageModel> result = Lists.newArrayList();
        while (curMessageSize < allMessage && CollectionUtils.isNotEmpty(cityList)) {
            Long cityId = cityList.get(curCity);
            CityAttractionModel cityMessage = getCityMessage(cityId, cityMessageMap);
            if (Objects.isNull(cityMessage) ||
                (CollectionUtils.isEmpty(cityMessage.getPeripheralLines()) &&
                    CollectionUtils.isEmpty(cityMessage.getCityAttractionsExploration()) &&
                    CollectionUtils.isEmpty(cityMessage.getRouteGuidance()))) {
                // 城市下没有消息 或者 消息都用完了，就移除该城市
                cityList.remove(curCity);
            }
            // 如果在常住地，即查询本地周边玩法
            if (cityId.equals(regularCity)) {
                type = peripheralLinesLocal;
            }
            // 从城市对应的导语中获取消息，位移的话优先取周边线路
            // 本轮取的消息数量
            int messageSize = Math.min(oneRoundMessage, allMessage - curMessageSize);
            List<AiJourneyMessageModel> message = getRandomMessage(cityMessage, type, messageSize);
            result.addAll(message);
            curMessageSize += message.size();
        }
        return result;
    }

    /**
     * 按照一定策略随机获取周边线路
     */
    private List<AiJourneyMessageModel> getRandomMessage(CityAttractionModel cityMessage, String type, int messageSize) {
        if (Objects.isNull(cityMessage)) {
            return Lists.newArrayList();
        }
        // 所有需要获取的线路
        List<AiJourneyMessageModel> allLine = Lists.newArrayList();
        // 按照策略纬度取
        if (StringUtils.isNotBlank(type)) {
            switch (type) {
                case peripheralLinesType:
                    // 周边线路
                    allLine.addAll(genMessageModel(cityMessage.getPeripheralLines(), peripheralLinesType));
                    break;
                case cityAttractionsExplorationType:
                    // 城市景点探索类型
                    allLine.addAll(genMessageModel(cityMessage.getCityAttractionsExploration(), cityAttractionsExplorationType));
                    break;
                case routeGuidanceType:
                    // 城市路线引导类型
                    allLine.addAll(genMessageModel(cityMessage.getRouteGuidance(), routeGuidanceType));
                    break;
                default:
                    // 默认用全部的线路
                    allLine.addAll(genMessageModel(cityMessage.getPeripheralLines(), peripheralLinesType));
                    allLine.addAll(genMessageModel(cityMessage.getCityAttractionsExploration(), cityAttractionsExplorationType));
                    allLine.addAll(genMessageModel(cityMessage.getRouteGuidance(), routeGuidanceType));
                    break;
            }
        } else {
            // 默认用全部的线路
            allLine.addAll(genMessageModel(cityMessage.getPeripheralLines(), peripheralLinesType));
            allLine.addAll(genMessageModel(cityMessage.getCityAttractionsExploration(), cityAttractionsExplorationType));
            allLine.addAll(genMessageModel(cityMessage.getRouteGuidance(), routeGuidanceType));
        }
        // 获取要输出的消息
        List<AiJourneyMessageModel> returnMessage = Lists.newArrayList();
        if (allLine.size() < messageSize) {
            returnMessage = allLine;
        } else {
            Collections.shuffle(allLine);
            returnMessage = allLine.subList(0, messageSize);
        }
        // 删除已经输出的消息
        List<String> resultMessage = returnMessage.stream().map(AiJourneyMessageModel::getContent).collect(Collectors.toList());
        cityMessage.getPeripheralLines().removeAll(resultMessage);
        cityMessage.getCityAttractionsExploration().removeAll(resultMessage);
        cityMessage.getRouteGuidance().removeAll(resultMessage);
        return returnMessage;
    }

    private List<AiJourneyMessageModel>  genMessageModel(List<String> routeGuidance, String routeGuidanceType) {
        List<AiJourneyMessageModel> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(routeGuidance)) {
            return result;
        }
        return routeGuidance.stream().map(item -> {
            AiJourneyMessageModel model = new AiJourneyMessageModel();
            model.setContent(item);
            model.setType(routeGuidanceType);
            return model;
        }).collect(Collectors.toList());
    }

    private CityAttractionModel getCityMessage(Long cityId, Map<Long, CityAttractionModel> cityMessageMap) {
        if (cityMessageMap.containsKey(cityId)) {
            return cityMessageMap.get(cityId);
        }
        String key = CITY_MESSAGE_KEY + cityId;
        Object value = mdbTairCommonHelper.getValue(key);
        CityAttractionModel cityAttractionModel = JSONUtil.parseObject(value, CityAttractionModel.class);
        if (Objects.nonNull(cityAttractionModel)) {
            cityMessageMap.put(cityId, cityAttractionModel);
        }
        return cityAttractionModel;
    }

    /**
     * 记录给用户推荐的消息列表
     */
    public void recordMessageList(Long userId, List<AiJourneyMessageModel> messageInfoList) {
        String md5 = MD5Util.md5(JSON.toJSONString(messageInfoList));
        String key = USER_RECOMMEND_MESSAGE_KEY + userId + md5;
        mdbTairCommonHelper.putValue(key, JSON.toJSONString(messageInfoList), USER_RECOMMEND_MESSAGE_TTL_S);
    }

    /**
     * 获取用户推荐消息 并放到缓存
     */
    public List<AiJourneyMessageModel> getUserMessageList(Long userId,
                                           List<Long> journeyCityList,
                                           List<Long> journeyPlanCityList,
                                           List<Long> searchCityList,
                                           List<Long> moveCityList,
                                           List<Long> regularStayCityList,
                                           List<Long> commonSearchCityList) {
        Pair<Boolean, List<Long>> userCity = decideCityList(userId, journeyCityList, journeyPlanCityList, searchCityList, moveCityList, regularStayCityList, commonSearchCityList);
        List<Long> cityList = userCity.getRight();
        // 记录用户推荐的消息
        LOGGER.recordOutput(new LogModel("getUserMessageList")
            .userId(userId)
            .message(JSON.toJSONString(cityList))
            .success(true));
        Boolean isMove = userCity.getLeft();
        List<AiJourneyMessageModel> messageInfoList;
        Long regularCity = null;
        if (CollectionUtils.isNotEmpty(regularStayCityList)) {
            regularCity = regularStayCityList.get(0);
        }
        if (isMove) {
            messageInfoList = getRelatedCityMessage(cityList, regularCity, peripheralLinesType);
        } else {
            messageInfoList = getRelatedCityMessage(cityList, regularCity, StringUtils.EMPTY);
        }
        recordMessageList(userId, messageInfoList);
        return messageInfoList;
    }

    /**
     * 获取缓存中的用户推荐消息，如果没有就再算一遍
     */
    public List<AiJourneyMessageModel> getUserMessageListWithCache(Long userId,
                                                    String token,
                                                    List<Long> journeyCityList,
                                                    List<Long> journeyPlanCityList,
                                                    List<Long> searchCityList,
                                                    List<Long> moveCityList,
                                                    List<Long> regularStayCityList,
                                                    List<Long> commonSearchCityList) {
        if (StringUtils.isNotBlank(token)) {
            String key = USER_RECOMMEND_MESSAGE_KEY + userId + token;
            Object value = mdbTairCommonHelper.getValue(key);
            if (Objects.nonNull(value)) {
                return JSONUtil.parseArray(value.toString(), AiJourneyMessageModel.class);
            }
        }
        return getUserMessageList(userId, journeyCityList, journeyPlanCityList, searchCityList, moveCityList, regularStayCityList, commonSearchCityList);
    }

    /**
     * 生成推荐目的地卡片
     */
    public List<RecommendCard> getRecommendCard(Long userId, List<Long> journeyCityList, List<Long> moveCityList) {
        Long lbsCityId = null;
        if (CollectionUtils.isNotEmpty(moveCityList)) {
            lbsCityId = moveCityList.get(0);
        }
        Long orderCityId = null;
        if (CollectionUtils.isNotEmpty(journeyCityList)) {
            orderCityId = journeyCityList.get(0);
        }
        List<DestinationInfo> featuredDestinationList = homeRecommendServiceClient.getFeaturedDestinationList(userId, lbsCityId, orderCityId, AI_CHANNEL_GUESS_CITY_RECOMMEND_PAGE_SIZE);
        if (CollectionUtils.isNotEmpty(featuredDestinationList)) {
            List<Long> cityList = featuredDestinationList.stream().map(info -> Long.parseLong(info.getDestinationCode())).collect(Collectors.toList());
            return generateRecommendCard(cityList);
        }
        return Lists.newArrayList();
    }

    /**
     * 生成推荐卡片
     */
    private List<RecommendCard> generateRecommendCard(List<Long> cityList) {
        // 查询城市信息
        Map<Long, TrdiDivisionDO> divisionMapInfoByOldId = trdiDivisionReadServiceClient.getDivisionMapInfoByOldId(cityList);
        List<RecommendCard> recommendCard = Lists.newArrayList();
        for (Long cityId : cityList) {
            if (Objects.isNull(divisionMapInfoByOldId.get(cityId))) {
                continue;
            }
            TrdiDivisionDO trdiDivisionDO = divisionMapInfoByOldId.get(cityId);
            RecommendCard card = new RecommendCard();
            String cityName = StringUtils.firstNonBlank(trdiDivisionDO.getName(), trdiDivisionDO.getNameEn());
            if (StringUtils.isNotBlank(cityName) && cityName.endsWith("市")) {
                cityName = cityName.substring(0, cityName.length() - 1);
            }
            card.setDestinationName(cityName);
            String message = String.format(CITY_MESSAGE_LOGIC, cityName);
            card.setMessage(message);
            recommendCard.add(card);
        }
        return recommendCard;
    }

}
