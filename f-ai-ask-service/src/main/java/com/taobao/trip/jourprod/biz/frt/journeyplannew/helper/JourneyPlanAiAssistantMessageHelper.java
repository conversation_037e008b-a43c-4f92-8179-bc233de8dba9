package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;

import com.alibaba.boot.switchcenter.annotation.SwitchGroup;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fliggy.ffa.touch.prod.customize.client.common.param.MemberDTO;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch;
import com.taobao.trip.jourprod.biz.frt.llm.domain.MessageParam;
import com.taobao.trip.jourprod.biz.frt.llm.domain.MessageRole;
import com.taobao.trip.jourprod.common.lang.utils.DateUtil;
import com.taobao.trip.jourprod.common.sal.hsf.tair.MdbTairHelper;
import com.taobao.trip.jourprod.member.MemberServiceHelper;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.JourneyPlanAiAssistantRoleEnum;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiAssistantHistoryMessageRequest;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanAiAssistantHistoryResult;
import com.taobao.trip.jourprod.service.impl.journeyplannew.JourneyAssistantDataOperateService;
import com.taobao.trip.jourprod.service.impl.journeyplannew.JourneyAssistantSessionOperateService;
import com.taobao.trip.request.AiJourneyQueryMessageRequest;
import com.taobao.trip.request.AiJourneySessionListRequest;
import com.taobao.trip.response.AiJourneyMessageSearchResponse;
import com.taobao.trip.response.AiJourneySessionListResponse;
import com.taobao.trip.response.AiJourneySessionListResponse.UserInfo;
import fliggy.content.common.logger.LogRunnable;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch.HISTORY_MESSAGE_SIZE;

/**
 * @Description 消息查询工具类
 * <AUTHOR>
 * @Date 2025/3/22
 **/
@Component
@SwitchGroup
public class JourneyPlanAiAssistantMessageHelper {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneyPlanAiAssistantMessageHelper.class);

    /**
     * 替换链接的正则
     */
    private static final String LINK_REGEX = "\\[\\s*(.*?)\\s*\\]\\([^)]*\\)";

    /**
     * 手绘地图旧字符串
     */
    private final static String OLD_MAP_URL_FORMAT = "\"drawPicForReplace\":\"%s_%s_draw_pic_value\"";

    /**
     * 手绘地图新字符串
     */
    private final static String NEW_MAP_URL_FORMAT = "\"routeMapImg\":\"%s\"";

    // 手绘地图缓存key
    public static final String MAP_URL_KEY = "ai:journey:map:url:";

    // 交通数据缓存key
    public static final String AI_JOURNEY_TRAFFIC_PRE = "ai:journey:traffic:";

    // 是否手动停止过
    public static final String MANUALLY_STOP_KEY = "ai:journey:manually_stop:";

    // 手绘地图缓存时间
    private static final Integer MAP_URL_TTL_S = 24 * 60 * 60;
    public static final Integer MANUALLY_STOP_TTL_S = 20 * 60;

    // 延迟任务执行器
    private static final ScheduledExecutorService scheduledExecutor =
            Executors.newScheduledThreadPool(2, new CustomizableThreadFactory("MapUrlDelayExecutor"));

    @AppSwitch(des = "欢迎文案标题列表", level = Level.p4)
    public static String WELCOME_TITLE = "你和 AI 问一问的对话将展示在这里";

    @AppSwitch(des = "不同等级会员等级对应的图标", level = Level.p4)
    public static Map<String, String> LEVEL_PIC_MAP_STR = new HashMap<>();

    static {
        LEVEL_PIC_MAP_STR.put("F1", "https://gw.alicdn.com/imgextra/i3/O1CN01vHrdME1YUuCNvAbYs_!!6000000003063-2-tps-102-102.png");
        LEVEL_PIC_MAP_STR.put("F2", "https://gw.alicdn.com/imgextra/i2/O1CN01fCaYOn1LISQn4suw5_!!6000000001276-2-tps-102-102.png");
        LEVEL_PIC_MAP_STR.put("F3", "https://gw.alicdn.com/imgextra/i3/O1CN01Oq2ct91k4apsQRZys_!!6000000004630-2-tps-102-102.png");
        LEVEL_PIC_MAP_STR.put("F4", "https://gw.alicdn.com/imgextra/i1/O1CN01EoI1Wq1HbSOiCsz7h_!!6000000000776-2-tps-102-102.png");
        LEVEL_PIC_MAP_STR.put("F5", "https://gw.alicdn.com/imgextra/i2/O1CN01ZPEOE01a0nOUxUXCV_!!6000000003268-2-tps-102-102.png");
        LEVEL_PIC_MAP_STR.put("F6", "https://gw.alicdn.com/imgextra/i4/O1CN01irUF2A1USpwpq0M66_!!6000000002517-2-tps-102-102.png");
    }

    @AppSwitch(des = "个人中心跳转链接", level = Level.p4)
    public static String JUMP_URL = "https://outfliggys.m.taobao.com/app/trip/rx-member2024/pages/home?disableNav=YES&titleBarHidden=2&urlReferer=member2023&_projVer=1.0.";

    // 可配置的延迟任务执行时间（分钟）
    @AppSwitch(des = "地图链接延迟任务执行时间（分钟）", level = Level.p4)
    public static Integer MAP_URL_DELAY_TASK_MINUTES = 10;

    @Resource
    private JourneyAssistantDataOperateService journeyAssistantDataOperateFacade;

    @Resource
    private MdbTairHelper mdbTairHelper;

    @Resource
    private JourneyAssistantDataOperateService journeyAssistantDataOperateService;

    @Resource
    private JourneyAssistantSessionOperateService journeyAssistantSessionOperateService;

    @Resource
    private MemberServiceHelper memberServiceHelper;

    /**
     * 带着session请求历史消息
     */
    public List<MessageParam> queryHistoryMessagesWithSession(Long userId, String sessionId) {
        try {
            JourneyPlanAiAssistantHistoryMessageRequest request = new JourneyPlanAiAssistantHistoryMessageRequest();
            request.setUserId(String.valueOf(userId));
            request.setSessionId(sessionId);
            Date now = new Date();
            request.setLeftTime(DateUtil.addHours(now, -AiJourneySwitch.QUERY_HISTORY_MESSAGE_BETWEEN_HOUR_NUM));
            request.setRightTime(now);
            request.setPageSize(HISTORY_MESSAGE_SIZE);
            JourneyPlanAiAssistantHistoryResult result = journeyAssistantDataOperateFacade.queryHistoryMessage(request);
            if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getMessageList())) {
                return Lists.newArrayList();
            }
            List<JourneyPlanAiAssistantHistoryResult.MessageInfo> messageList = result.getMessageList();
            return this.convertToMessageParams(messageList);
        } catch (Throwable throwable) {
            LOGGER.recordNormalException(new LogModel("queryHistoryMessagesWithSession")
                    .message("查询历史消息失败-{0}", sessionId)
                    .e(throwable)
                    .userId(userId));
            return Lists.newArrayList();
        }
    }

    /**
     * 带着session请求历史消息
     */
    public List<MessageParam> queryHistoryMessageWithLimitSize(Long userId, String sessionId,Integer limitSize) {
        try {
            JourneyPlanAiAssistantHistoryMessageRequest request = new JourneyPlanAiAssistantHistoryMessageRequest();
            request.setUserId(String.valueOf(userId));
            request.setSessionId(sessionId);
            request.setPageSize(limitSize);
            JourneyPlanAiAssistantHistoryResult result = journeyAssistantDataOperateFacade.queryHistoryMessage(request);
            if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getMessageList())) {
                return Lists.newArrayList();
            }
            List<JourneyPlanAiAssistantHistoryResult.MessageInfo> messageList = result.getMessageList();
            return this.convertToMessageParams(messageList);
        } catch (Throwable throwable) {
            LOGGER.recordNormalException(new LogModel("queryHistoryMessagesWithSession")
                .message("查询历史消息失败-{0}", sessionId)
                .e(throwable)
                .userId(userId));
            return Lists.newArrayList();
        }
    }

    public List<MessageParam> getOriginalMessageInfo(List<String> originalMessageIdList) {
        try {
            List<JourneyPlanAiAssistantHistoryResult.MessageInfo> originalMessageInfoList =
                    journeyAssistantDataOperateFacade.getMessageInfo(originalMessageIdList, null);
            return this.convertToMessageParams(originalMessageInfoList);
        } catch (Throwable throwable) {
            LOGGER.recordNormalException(new LogModel("getOriginalMessageInfo")
                    .e(throwable).request(JSON.toJSONString(originalMessageIdList)));
            return Collections.emptyList();
        }
    }

    public List<JourneyPlanAiAssistantHistoryResult.MessageInfo> getMessageInfo(List<String> originalMessageIdList, Long userId) {
        try {
            return journeyAssistantDataOperateFacade.getMessageInfo(originalMessageIdList ,userId);
        } catch (Throwable throwable) {
            LOGGER.recordNormalException(new LogModel("getOriginalMessageInfo")
                    .e(throwable).request(JSON.toJSONString(originalMessageIdList)));
            return Collections.emptyList();
        }
    }

    @AteyeInvoker
    public String testOriginalMessageInfo(String originalMessageIds) {
        List<String> messageIdList = Arrays.asList(originalMessageIds.split(","));
        List<MessageParam> originalMessageInfo = getOriginalMessageInfo(Lists.newArrayList(messageIdList));
        return JSON.toJSONString(originalMessageInfo);
    }

    private List<MessageParam> convertToMessageParams(List<JourneyPlanAiAssistantHistoryResult.MessageInfo> messages) {
        return messages.stream()
                .map(this::convertToMessageParam)
                .filter(Objects::nonNull)
                .peek(this::removeLink)
                .collect(Collectors.toList());
    }

    private MessageParam convertToMessageParam(JourneyPlanAiAssistantHistoryResult.MessageInfo item) {
        MessageParam messageParam = new MessageParam();
        JourneyPlanAiAssistantRoleEnum roleEnum = JourneyPlanAiAssistantRoleEnum.codeOf(item.getRole());
        if (roleEnum == null) {
            return null;
        }

        switch (roleEnum) {
            case USER:
                messageParam.setRole(MessageRole.user);
                messageParam.setContent(item.getInfo());
                messageParam.setExtra(item.getExtra());
                break;
            case SYSTEM:
                messageParam.setRole(MessageRole.assistant);
                if (item.getStructRoute() != null) {
                    messageParam.setContent(item.getStructRoute().getGuide());
                } else {
                    messageParam.setContent(item.getInfo());
                }
                messageParam.setExtra(item.getExtra());
                break;
            default:
                return null;
        }

        if (messageParam.getContent() == null) {
            messageParam.setContent(StringUtils.EMPTY);
        }

        return messageParam;
    }


    /**
     * 移除消息中的链接
     */
    private void removeLink(MessageParam messageParam) {
        if (StringUtils.isBlank(messageParam.getContent())) {
            return;
        }
        String content = messageParam.getContent();
        if (StringUtils.isBlank(content)) {
            return;
        }
        // 使用 replaceAll 替换匹配的内容
        content = content.replaceAll(LINK_REGEX, "「$1」");
        messageParam.setContent(content);
    }

    /**
     * 更新地图链接
     */
    public void updateMapUrl2Tai(String messageId, String cardId, String mapUrl) {
        if (StringUtils.isBlank(mapUrl)) {
            return;
        }
        String key = genMapUrlKey(messageId, cardId);
        mdbTairHelper.putValue(key, mapUrl, MAP_URL_TTL_S);
    }

    /**
     * 获取地图链接
     */
    public String getMapUrl(String messageId, String cardId) {
        String key = genMapUrlKey(messageId, cardId);
        Object value = mdbTairHelper.getValue(key);
        if (Objects.isNull(value)) {
            return StringUtils.EMPTY;
        }
        return String.valueOf(value);
    }

    private String genMapUrlKey(String messageId, String cardId) {
        return MAP_URL_KEY + messageId + "_" + cardId;
    }

    /**
     * 注册地图链接延时任务
     *
     * @param messageId 消息ID
     * @param url       地图URL
     */
    public void registerMapUrlDelayTask(String messageId, String cardId, String url) {
        if (StringUtils.isBlank(messageId) || StringUtils.isBlank(url)) {
            LOGGER.recordNormalException(new LogModel("registerMapUrlDelayTask")
                    .message("注册地图链接延时任务参数不合法，messageId={0}, url={1}", messageId, url));
            return;
        }

        try {
            scheduledExecutor.schedule(new LogRunnable() {
                @Override
                public void logRun() {
                    try {
                        // 更新消息
                        String oldValue = genOldValue(messageId, cardId);
                        String newValue = genNewValue(url);
                        journeyAssistantDataOperateService.updateContent(messageId, oldValue, newValue);
                    } catch (Throwable throwable) {
                        LOGGER.recordNormalException(new LogModel("executeMapUrlDelayTask")
                                .message("执行地图链接延时任务异常-{0}", messageId)
                                .e(throwable));
                    }
                }
            }, MAP_URL_DELAY_TASK_MINUTES, TimeUnit.MINUTES);

            LOGGER.recordKeyPath(new LogModel("registerMapUrlDelayTask")
                    .message("注册地图链接延时任务成功，将在{0}分钟后执行，messageId={1}, url={2}",
                            MAP_URL_DELAY_TASK_MINUTES, messageId, url));
        } catch (Throwable throwable) {
            LOGGER.recordNormalException(new LogModel("registerMapUrlDelayTask")
                    .message("注册地图链接延时任务异常-{0}", messageId)
                    .e(throwable));
        }
    }

    /**
     * 查询会话列表
     */
    public AiJourneySessionListResponse querySessionList(AiJourneySessionListRequest request) {
        LOGGER.recordEntry(new LogModel("querySessionList_helper"));

        try {
            // 调用会话服务查询会话列表
            AiJourneySessionListResponse response = journeyAssistantSessionOperateService.querySessionList(request);
            // 添加欢迎标题
            response.setWelcomeTitle(WELCOME_TITLE);
            // 添加用户信息
            response.setUserInfo(createUserInfo(request.getUserId().toString()));
            return response;
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("querySessionList_helper_exception").e(e));
            throw new RuntimeException("查询会话列表异常", e);
        }
    }

    // 创建用户信息
    private UserInfo createUserInfo(String userId) {
        try {
            MemberDTO memberDTO = memberServiceHelper.getByUserIdV2(NumberUtils.toLong(userId));
            UserInfo userInfo = new UserInfo();
            userInfo.setUserNick(memberDTO.getNick());
            userInfo.setHeadPic(memberDTO.getAvatar());
            if (Objects.nonNull(memberDTO.getLevelName())) {
                userInfo.setLevelPic(LEVEL_PIC_MAP_STR.get(memberDTO.getLevelName()));
            }
            userInfo.setJumpUrl(JUMP_URL);
            return userInfo;
        } catch (Throwable throwable) {
            LOGGER.recordDangerException(new LogModel("createUserInfo_helper_exception")
                .e(throwable)
                .userId(userId));
        }
        return null;
    }

    /**
     * 删除会话
     */
    public Boolean deleteSession(AiJourneySessionListRequest request) {
        LOGGER.recordEntry(new LogModel("deleteSession_helper"));

        try {
            // 调用会话服务删除会话
            Boolean deleteSession = journeyAssistantSessionOperateService.deleteSession(request);
            if (BooleanUtils.isNotTrue(deleteSession)) {
                LOGGER.recordNormalException(new LogModel("deleteSession_helper_exception")
                    .message("删除会话失败，deleteSession-{0}", deleteSession)
                    .userId(request.getUserId())
                    .request(JSON.toJSONString(request)));
                return false;
            }
            return true;
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("deleteSession_helper_exception").e(e));
            throw new RuntimeException("删除会话异常", e);
        }
    }

    /**
     * 搜索历史消息
     */
    public AiJourneyMessageSearchResponse queryMessageList(AiJourneyQueryMessageRequest request) {
        LOGGER.recordEntry(new LogModel("queryMessageList_helper")
            .request(JSON.toJSONString(request)));

        try {
            // 验证请求参数
            if (request == null || request.getUserId() == null) {
                return new AiJourneyMessageSearchResponse();
            }

            // 使用JSON处理内部类的问题
            // 创建完整的JSON对象
            JSONObject jsonResponse = new JSONObject();

            // 添加mock数据
            JSONArray searchMessageItems = createMockSearchMessages(request.getKeyWords());

            // 把数据添加到JSON对象
            jsonResponse.put("searchMessageList", searchMessageItems);

            // 将JSON转换为最终对象
            AiJourneyMessageSearchResponse response = JSON.parseObject(jsonResponse.toJSONString(), AiJourneyMessageSearchResponse.class);

            LOGGER.recordOutput(new LogModel("queryMessageList_helper")
                .response(JSON.toJSONString(response)));
            return response;
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("queryMessageList_helper_exception").e(e));
            throw new RuntimeException("搜索历史消息异常", e);
        }
    }

    /**
     * 创建模拟搜索消息数据
     */
    private JSONArray createMockSearchMessages(String keyWords) {
        JSONArray mockMessages = new JSONArray();

        // 如果关键词为空，返回一些通用的mock数据
        if (StringUtils.isBlank(keyWords)) {
            // Mock数据1
            JSONObject item1 = new JSONObject();
            item1.put("userMessage", "推荐杭州的旅游景点");
            item1.put("assistantMessage", "杭州最著名的景点是西湖，此外还有灵隐寺、千岛湖、西溪湿地等景点值得一游。");
            item1.put("timeStamp", String.valueOf(System.currentTimeMillis() - 86400000)); // 1天前
            item1.put("sessionId", UUID.randomUUID().toString());
            mockMessages.add(item1);

            // Mock数据2
            JSONObject item2 = new JSONObject();
            item2.put("userMessage", "北京有什么好玩的地方");
            item2.put("assistantMessage", "北京的著名景点包括故宫、长城、颐和园、天坛等，是中国历史文化的重要代表。");
            item2.put("timeStamp", String.valueOf(System.currentTimeMillis() - 172800000)); // 2天前
            item2.put("sessionId", UUID.randomUUID().toString());
            mockMessages.add(item2);

            // Mock数据3
            JSONObject item3 = new JSONObject();
            item3.put("userMessage", "上海迪士尼一日游攻略");
            item3.put("assistantMessage", "上海迪士尼游玩建议：提前购票，早上早点到，先玩热门项目，下载APP查看排队情况，带好防晒和水。");
            item3.put("timeStamp", String.valueOf(System.currentTimeMillis() - 259200000)); // 3天前
            item3.put("sessionId", UUID.randomUUID().toString());
            mockMessages.add(item3);

        } else {
            // 根据关键词过滤的mock数据
            if (keyWords.contains("北京") || keyWords.contains("故宫") || keyWords.contains("长城")) {
                JSONObject item = new JSONObject();
                item.put("userMessage", "北京有什么好玩的地方");
                item.put("assistantMessage", "北京的著名景点包括故宫、长城、颐和园、天坛等，是中国历史文化的重要代表。");
                item.put("timeStamp", String.valueOf(System.currentTimeMillis() - 172800000)); // 2天前
                item.put("sessionId", UUID.randomUUID().toString());
                mockMessages.add(item);
            }

            if (keyWords.contains("杭州") || keyWords.contains("西湖")) {
                JSONObject item = new JSONObject();
                item.put("userMessage", "推荐杭州的旅游景点");
                item.put("assistantMessage", "杭州最著名的景点是西湖，此外还有灵隐寺、千岛湖、西溪湿地等景点值得一游。");
                item.put("timeStamp", String.valueOf(System.currentTimeMillis() - 86400000)); // 1天前
                item.put("sessionId", UUID.randomUUID().toString());
                mockMessages.add(item);
            }

            if (keyWords.contains("上海") || keyWords.contains("迪士尼")) {
                JSONObject item = new JSONObject();
                item.put("userMessage", "上海迪士尼一日游攻略");
                item.put("assistantMessage", "上海迪士尼游玩建议：提前购票，早上早点到，先玩热门项目，下载APP查看排队情况，带好防晒和水。");
                item.put("timeStamp", String.valueOf(System.currentTimeMillis() - 259200000)); // 3天前
                item.put("sessionId", UUID.randomUUID().toString());
                mockMessages.add(item);
            }
        }

        return mockMessages;
    }

    /**
     * 生成带url的字符串
     */
    private String genNewValue(String url) {
        return String.format(NEW_MAP_URL_FORMAT, url);
    }

    /**
     * 生成要被替换的字符串
     */
    private String genOldValue(String messageId, String cardId) {
        return String.format(OLD_MAP_URL_FORMAT, messageId, cardId);
    }

    public String genAiStrategyTrafficKey(String bizId) {
        return AI_JOURNEY_TRAFFIC_PRE + bizId;
    }
}
