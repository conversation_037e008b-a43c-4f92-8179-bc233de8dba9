package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanStatusEnum;
import com.taobao.trip.jourprod.common.sal.exception.TripJourneyException;
import com.taobao.trip.jourprod.common.sal.hsf.tair.MdbTairHelper;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanStatusEnum.*;

/**
 * @Description 大模型消息状态工具类
 * <AUTHOR>
 * @Date 2025/2/12
 **/
@Component
public class JourneyPlanAiAssistantMessageStatusHelper implements InitializingBean {
    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneyPlanAiAssistantMessageStatusHelper.class);


    @AppSwitch(des = "消息id对应的状态的缓存key", level = Level.p4)
    public static String MESSAGE_ID_STATUS_KEY = "ai:journey:messageId:status:";

    @AppSwitch(des = "消息id对应的状态的缓存时间", level = Level.p4)
    public static Integer MESSAGE_ID_STATUS_TTL_S = 24 * 60 * 60;

    @Resource
    private MdbTairHelper mdbTairHelper;

    /**
     * 判断对话是否已停止
     * @param messageId
     * @return
     */
    public boolean isStop(String messageId) {
        return checkMessageStatus(messageId, STOP) || checkMessageStatus(messageId, BLOCK);
    }
    /**
     * 判断对话是否已结束
     * @param messageId
     * @return
     */
    public boolean isEnd(String messageId) {
        return checkMessageStatus(messageId, END);
    }
    /**
     * 判断对话是否已被绿网拦截
     * @param messageId
     * @return
     */
    public boolean isBlock(String messageId) {
        return checkMessageStatus(messageId, BLOCK);
    }

    /**
     * 更新当前消息状态，和messageId绑定
     */
    public boolean updateStatus(String messageId, AiJourneyPlanStatusEnum aiJourneyPlanStatusEnum) {
        if (ObjectUtils.anyNull(messageId, aiJourneyPlanStatusEnum)) {
            throw new TripJourneyException("messageId更新失败");
        }

        String key = genMessageIdStatusKey(messageId);
        Object value = mdbTairHelper.getValue(key);
        List<String> endStatus = Lists.newArrayList(STOP.getStatusCode(), END.getStatusCode(), BLOCK.getStatusCode());
        if (Objects.nonNull(value) && endStatus.contains(String.valueOf(value))) {
            LOGGER.recordOutput(new LogModel("updateStatus")
                    .request("消息ID为" + messageId)
                    .code("core")
                    .message("发现状态已经为" + value + "，不更新"));
            return true;
        }
        boolean result = mdbTairHelper.putValue(key, aiJourneyPlanStatusEnum.getStatusCode(), MESSAGE_ID_STATUS_TTL_S);
        LOGGER.recordOutput(new LogModel("updateStatus")
                .request("消息ID为" + messageId)
                .code("core")
                .message("消息状态更改为" + aiJourneyPlanStatusEnum.name()));
        return result;
    }

    /**
     * 判断对话是否是对应的状态
     */
    public boolean checkMessageStatus(String messageId, Set<AiJourneyPlanStatusEnum> statusEnumList) {
        if (ObjectUtils.anyNull(messageId) || CollectionUtils.isEmpty(statusEnumList)) {
            throw new TripJourneyException("参数为空");
        }
        String key = genMessageIdStatusKey(messageId);
        Object value = mdbTairHelper.getValue(key);
        AiJourneyPlanStatusEnum aiJourneyPlanStatusEnum = AiJourneyPlanStatusEnum.codeOf(String.valueOf(value));
        if (Objects.isNull(aiJourneyPlanStatusEnum)) {
            return false;
        }
        return statusEnumList.contains(aiJourneyPlanStatusEnum);
    }

    /**
     * 判断对话是否是对应的状态
     */
    public boolean checkMessageStatus(String messageId, AiJourneyPlanStatusEnum statusEnum) {
        if (ObjectUtils.anyNull(messageId, statusEnum)) {
            throw new TripJourneyException("参数为空");
        }
        return checkMessageStatus(messageId, Sets.newHashSet(statusEnum));
    }

    /**
     * 生成消息对应状态的key
     */
    private String genMessageIdStatusKey(String messageId) {
        return MESSAGE_ID_STATUS_KEY + messageId;
    }

    /**
     * Invoked by a BeanFactory after it has set all bean properties supplied
     * (and satisfied BeanFactoryAware and ApplicationContextAware).
     * <p>This method allows the bean instance to perform initialization only
     * possible when all bean properties have been set and to throw an
     * exception in the event of misconfiguration.
     *
     * @throws Exception in the event of misconfiguration (such
     * as failure to set an essential property) or if initialization fails.
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        SwitchManager.init(JourneyPlanAiAssistantMessageStatusHelper.class);
    }
}
