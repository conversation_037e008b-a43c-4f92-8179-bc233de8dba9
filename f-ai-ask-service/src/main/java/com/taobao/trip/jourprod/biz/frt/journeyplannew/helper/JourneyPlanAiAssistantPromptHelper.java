package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;

import com.taobao.trip.jourprod.common.sal.annotation.RunLog;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.utils.LogUtil;
import java.util.Map;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description Prompt工具类
 **/
@Component
public class JourneyPlanAiAssistantPromptHelper {

    private static final FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneyPlanAiAssistantPromptHelper.class);

    /**
     * 替换prompt里面的占位符
     * 使用key的方式，而不是String.format是为了防止替换参数多了之后导致的顺序错乱问题
     */
    @RunLog
    public static String replacePromptPlaceholder(String oriPrompt, Map<String, String> placeholderMap) {
        if (placeholderMap == null || placeholderMap.isEmpty()) {
            return oriPrompt;
        }
        for (Map.Entry<String, String> entry : placeholderMap.entrySet()) {
            oriPrompt = oriPrompt.replace("#{" + entry.getKey() + "}#", entry.getValue());
        }
        return oriPrompt;
    }

}
