package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.mtop3.invocation.MtopStream;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanStatusEnum;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult;
import fliggy.content.common.logger.LogRunnable;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Component;

/**
 * @Description 消息刷新帮助类
 * <AUTHOR>
 * @Date 2025/2/26
 **/
@Component
public class JourneyPlanAiAssistantRefreshHelper implements InitializingBean {

    private static final FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneyPlanAiAssistantRefreshHelper.class);

    /**
     * 线程池
     */
    private static final ThreadPoolExecutor refreshMessageExecutor = new ThreadPoolExecutor(1, 1, 0, TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<Runnable>(1),
        new CustomizableThreadFactory("refreshMessageExecutor"));

    /**
     * 流式对象对应的map，如果消息没结束，就刷新心跳
     */
    private static final Map<String, MtopStream> MTOP_STREAM_MAP = Maps.newConcurrentMap();

    private static final String SPLIT_CHAR = "_";

    @AppSwitch(des = "是否要刷新心跳", level = Level.p4)
    public static Boolean REFRESH_HEARTBEAT = false;

    @AppSwitch(des = "刷新心跳的间隔", level = Level.p4)
    public static Integer REFRESH_HEARTBEAT_INTERVAL_MS = 100;

    @Resource
    private JourneyPlanAiAssistantMessageStatusHelper journeyPlanAiAssistantMessageStatusHelper;

    /**
     * 刷新流式心跳
     */
    public void refreshHeartbeat(String systemMessageId, String userMessageId, MtopStream mtopStream) {
        // 如果已经停止了 就不再刷新
        if (journeyPlanAiAssistantMessageStatusHelper.checkMessageStatus(systemMessageId, Sets.newHashSet(AiJourneyPlanStatusEnum.STOP, AiJourneyPlanStatusEnum.END))) {
            return;
        }
        String messageKey = systemMessageId + SPLIT_CHAR + userMessageId;
        MTOP_STREAM_MAP.put(messageKey, mtopStream);
    }

    /**
     * Invoked by a BeanFactory after it has set all bean properties supplied
     * (and satisfied BeanFactoryAware and ApplicationContextAware).
     * <p>This method allows the bean instance to perform initialization only
     * possible when all bean properties have been set and to throw an
     * exception in the event of misconfiguration.
     *
     * @throws Exception in the event of misconfiguration (such
     * as failure to set an essential property) or if initialization fails.
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        SwitchManager.init(JourneyPlanAiAssistantRefreshHelper.class);
        // 启动一个线程去刷新心跳
        refreshMessageExecutor.execute(new LogRunnable() {
            @Override
            public void logRun() {
                while (REFRESH_HEARTBEAT) {
                    try {
                        if (MapUtils.isEmpty(MTOP_STREAM_MAP)) {
                            continue;
                        }
                        Set<String> messageIdSet = MTOP_STREAM_MAP.keySet();
                        for (String messageId : messageIdSet) {
                            MtopStream mtopStream = MTOP_STREAM_MAP.get(messageId);
                            if (mtopStream != null) {
                                String[] messageIdArray = messageId.split(SPLIT_CHAR);
                                if (messageIdArray.length != 2) {
                                    MTOP_STREAM_MAP.remove(messageId);
                                    continue;
                                }
                                String systemMessageId = messageIdArray[0];
                                // 如果已经停止了 就不再刷新
                                if (journeyPlanAiAssistantMessageStatusHelper.checkMessageStatus(systemMessageId, Sets.newHashSet(AiJourneyPlanStatusEnum.STOP, AiJourneyPlanStatusEnum.END))) {
                                    MTOP_STREAM_MAP.remove(messageId);
                                    continue;
                                }
                                StreamMessageResult result = StreamMessageResult.heartBeat();
                                mtopStream.write(JSON.toJSONString(result));
                                try {
                                    Thread.sleep(REFRESH_HEARTBEAT_INTERVAL_MS);
                                } catch (Exception e) {
                                    // ignore
                                }
                            }
                        }
                    } catch (Throwable throwable) {
                        LOGGER.recordNormalException(new LogModel("refreshHeartBeat")
                            .message("刷新心跳异常")
                            .e(throwable));
                    }
                }
            }
        });
    }
}
