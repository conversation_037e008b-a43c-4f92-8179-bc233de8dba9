package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;

import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.trip.jourprod.common.sal.annotation.RunLog;
import com.taobao.trip.jourprod.common.sal.tair.impl.LdbTairManager;
import com.taobao.trip.jourprod.domain.JourneyPlanAiChatSessionDO;
import com.taobao.trip.jourprod.domain.JourneyPlanAiChatSessionParam;
import com.taobao.trip.jourprod.mapper.JourneyPlanAiChatSessionDAO;
import com.taobao.trip.jourprod.service.impl.journeyplannew.JourneyAssistantSessionOperateService;
import com.taobao.trip.request.AiJourneySessionListRequest;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantHelper.AI_JOURNEY_SCENE_ANALYSIS_APP_ID_V2;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch.USE_BAILIAN_SESSION_ID;

/**
 * @Description ai行程规划调用大模型 session工具类
 * <AUTHOR>
 * @Date 2025/1/31
 **/
@Component
public class JourneyPlanAiAssistantSessionHelper implements InitializingBean {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneyPlanAiAssistantSessionHelper.class);

    @AppSwitch(des = "ai行程规划调用大模型 session的缓存key", level = Level.p4)
    public static String AI_JOURNEY_PLAN_SESSION_KEY = "ai:journey:plan:session:";

    @AppSwitch(des = "ai行程规划调用大模型 session的缓存时间", level = Level.p4)
    public static Integer AI_JOURNEY_PLAN_SESSION_TTL_S = 24 * 60 * 60;

    @Resource
    private LdbTairManager ldbTairManager;
    @Resource
    private JourneyPlanAiChatSessionDAO sessionDAO;
    @Resource
    private JourneyAssistantSessionOperateService journeyAssistantSessionOperateService;

    /**
     * 获取当前请求大模型的sessionId，使用用户 + appId 生成
     *
     */
    public String currentSession(Long userId) {
        String sessionKey = genSessionKey(userId, AI_JOURNEY_SCENE_ANALYSIS_APP_ID_V2);
        Object value = ldbTairManager.get(sessionKey);
        if (value == null) {
            // 生成随机sessionid
            String randomSession = UUID.randomUUID().toString();
            ldbTairManager.put(sessionKey, randomSession, AI_JOURNEY_PLAN_SESSION_TTL_S);
            return randomSession;
        }
        return String.valueOf(value);
    }

    /**
     * 必要时更新会话id
     */
    public void updateSessionIdIfNeed(Long userId, String generateJourneyPlanAppId, String curSessionId, String sessionId) {
        if (USE_BAILIAN_SESSION_ID) {
            return;
        }
        if (!StringUtils.equals(curSessionId, sessionId)) {
            String sessionKey = genSessionKey(userId, generateJourneyPlanAppId);
            ldbTairManager.put(sessionKey, curSessionId, AI_JOURNEY_PLAN_SESSION_TTL_S);
        }
    }

    /**
     * 清空会话信息
     */
    public boolean clearSession(Long userId) {
        String sessionKey = genSessionKey(userId, AI_JOURNEY_SCENE_ANALYSIS_APP_ID_V2);
        return ldbTairManager.delete(sessionKey);
    }

    /**
     * 生成sessionKey
     */
    private String genSessionKey(Long userId, String generateJourneyPlanAppId) {
        return AI_JOURNEY_PLAN_SESSION_KEY + userId + ":" + generateJourneyPlanAppId;
    }

    /**
     * 校验当前sessionId是否存在
     * todo 缓存逻辑可以考虑清掉
     */
    @RunLog
    public boolean checkSessionIsExist(Long userId, String sessionId, String sessionName) {
        JourneyPlanAiChatSessionParam selectParam = new JourneyPlanAiChatSessionParam();
        selectParam.createCriteria()
                .andUserIdEqualTo(String.valueOf(userId))
                .andSessionIdEqualTo(sessionId);
        List<JourneyPlanAiChatSessionDO> data = sessionDAO.selectByParam(selectParam);
        // 默认当前session有效，除非发现当前session有记录且是被删除的状态
        boolean rs = true;
        if (CollectionUtils.isNotEmpty(data)) {
            JourneyPlanAiChatSessionDO chatSessionDO = data.stream().filter(t -> "1".equals(t.getDeleteFlag())).findFirst().orElse(null);
            if (chatSessionDO != null) {
                rs = false;
            }
        } else {
            // 记录到session表里
            saveSession(userId, sessionId, sessionName);
        }
        return rs;
    }

    /**
     * 校验当前sessionId是否在数据库里有记录
     */
    @RunLog
    public boolean checkSessionInDb(Long userId, String sessionId) {
        JourneyPlanAiChatSessionParam selectParam = new JourneyPlanAiChatSessionParam();
        selectParam.createCriteria()
                .andUserIdEqualTo(String.valueOf(userId))
                .andSessionIdEqualTo(sessionId);
        List<JourneyPlanAiChatSessionDO> data = sessionDAO.selectByParam(selectParam);
        return CollectionUtils.isNotEmpty(data);
    }

    /**
     * 保存会话信息
     */
    public void saveSession(Long userId, String sessionId, String query) {
        AiJourneySessionListRequest sessionListRequest = new AiJourneySessionListRequest();
        sessionListRequest.setUserId(userId);
        sessionListRequest.setSessionListStr(sessionId);
        boolean exist = journeyAssistantSessionOperateService.exist(userId, sessionId);
        if (exist) {
            LOGGER.recordOutput(new LogModel("saveSession")
                    .request("userId:" + userId + ",sessionId:" + sessionId + ",query:" + query)
                    .response("会话已存在"));
            return;
        }
        JourneyPlanAiChatSessionDO record = new JourneyPlanAiChatSessionDO();
        Date now = new Date();
        record.setGmtCreate(now);
        record.setGmtModified(now);
        record.setUserId(String.valueOf(userId));
        record.setSessionId(sessionId);
        record.setSessionName(query);
        record.setDeleteFlag("0");
        int rs = sessionDAO.insert(record);
        if (rs > 0) {
            LOGGER.recordOutput(new LogModel("saveSession")
                    .request("userId:" + userId + ",sessionId:" + sessionId + ",query:" + query)
                    .response("保存会话信息成功")
                    .success(true));
        } else {
            LOGGER.recordDangerException(new LogModel("saveSession")
                    .request("userId:" + userId + ",sessionId:" + sessionId + ",query:" + query)
                    .response("保存会话信息失败")
                    .success(false));
        }
    }

    /**
     * 更新会话最近更新时间
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @return 是否更新成功
     */
    @RunLog
    public boolean updateSessionLastModifiedTime(Long userId, String sessionId) {
        if (userId == null || StringUtils.isBlank(sessionId)) {
            return false;
        }
        return journeyAssistantSessionOperateService.updateSessionLastModifiedTime(userId, sessionId);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        SwitchManager.init(JourneyPlanAiAssistantSessionHelper.class);
    }
}
