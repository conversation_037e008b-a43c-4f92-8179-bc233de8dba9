package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;

import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.trip.jourprod.common.lang.enums.BizErrorCodeEnum;
import com.taobao.trip.jourprod.common.sal.exception.TripJourneyException;
import com.taobao.trip.jourprod.common.sal.tair.impl.LdbTairManager;
import com.taobao.trip.jourprod.common.sal.tair.impl.MdbTairCommonHelper;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @Description ai问一问助手分享帮助类
 * <AUTHOR>
 * @Date 2025/2/10
 **/
@Component
public class JourneyPlanAiAssistantShareHelper implements InitializingBean {

    @AppSwitch(des = "ai行程规划分享token的缓存时间", level = Level.p4)
    public static Integer AI_JOURNEY_PLAN_SHARE_TOKEN_TTL_S = 30 * 24 * 60 * 60;

    @Resource
    private LdbTairManager ldbTairManager;

    @Resource
    private MdbTairCommonHelper mdbTairCommonHelper;

    /**
     * 生成分享token
     */
    public String genShareToken(String messageIdList) {
        String token = UUID.randomUUID().toString();
        ldbTairManager.put(token, messageIdList, AI_JOURNEY_PLAN_SHARE_TOKEN_TTL_S);
        return token;
    }

    /**
     * 获取分享的消息列表
     */
    public List<String> getMessageIdList(String token) {
        Object value = ldbTairManager.get(token);
        if (Objects.isNull(value)) {
            Object valueFromOD = mdbTairCommonHelper.getValueFromOD(token);
            if (Objects.isNull(valueFromOD)){
                throw new TripJourneyException(BizErrorCodeEnum.SHARE_TOKEN_EXPIRE.getCode(), BizErrorCodeEnum.SHARE_TOKEN_EXPIRE.getMsg());
            }
        }
        String messageIdList = String.valueOf(value);
        return Arrays.stream(messageIdList.split(",")).collect(Collectors.toList());
    }

    /**
     * Invoked by a BeanFactory after it has set all bean properties supplied
     * (and satisfied BeanFactoryAware and ApplicationContextAware).
     * <p>This method allows the bean instance to perform initialization only
     * possible when all bean properties have been set and to throw an
     * exception in the event of misconfiguration.
     *
     * @throws Exception in the event of misconfiguration (such
     * as failure to set an essential property) or if initialization fails.
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        SwitchManager.init(JourneyPlanAiAssistantShareHelper.class);
    }
}
