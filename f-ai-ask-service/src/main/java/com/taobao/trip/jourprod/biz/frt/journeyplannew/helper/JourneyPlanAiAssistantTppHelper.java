package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.hsf.util.RequestCtxUtil;
import com.taobao.recommendplatform.client.RecommendClient;
import com.taobao.recommendplatform.client.RecommendException;
import com.taobao.trip.jourdprod.core.model.common.ConstantElement;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Description ai行程规划tpp接口请求帮助类
 * <AUTHOR>
 * @Date 2025/2/15
 **/
@Component
public class JourneyPlanAiAssistantTppHelper implements InitializingBean {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneyPlanAiAssistantTppHelper.class);

    @AppSwitch(des = "线路推荐关联appid", level = Level.p4)
    public static Long RECORD_RECOMMEND_LINE_APP_ID = 47711L;

    @AppSwitch(des = "猪搜qpappid", level = Level.p4)
    public static Long QP_APP_ID = 48617L;

    @AppSwitch(des = "查询qp超时时间", level = Level.p4)
    public static Integer QUERY_QP_TIME_OUT_MS = 5000;

    @Resource
    private RecommendClient platformRecommendService;

    /**
     * 查询qp
     */
    @AteyeInvoker(description = "测试查询qp", paraDesc = "userId&query")
    public String queryQP(Long userId, String query) {
        if (Objects.isNull(userId) || StringUtils.isBlank(query)) {
            return null;
        }

        // 构建参数
        Map<String, String> param = getTppQiParam(userId, query);
        // 超时时间
        RequestCtxUtil.setRequestTimeout(QUERY_QP_TIME_OUT_MS);
        long t1 = System.currentTimeMillis();
        boolean success=true;
        try {
            // 查询qp
            return platformRecommendService.recommend(QP_APP_ID, userId, param, ConstantElement.APP_NAME);
        } catch (RecommendException e) {
            success=false;
            LOGGER.recordNormalException(new LogModel("queryQP").e(e));
            return null;
        } finally {
            LOGGER.recordCost(new LogModel("queryQP").success(success).cost(System.currentTimeMillis() - t1));
        }
    }


    private Map<String, String> getTppQiParam(Long userId, String query) {
        Map<String, String> param = Maps.newHashMap();
        param.put("query", query);
        param.put("searchType", "query_intelligence");
        return param;
    }


    @AteyeInvoker(description = "测试tpp", paraDesc = "param")
    public void testTpp(String param) throws RecommendException {
        Map<String, String> paramMap = JSON.parseObject(param, new TypeReference<Map<String, String>>() {
        });
        String recommend = platformRecommendService.recommend(RECORD_RECOMMEND_LINE_APP_ID, 1212313L, paramMap, ConstantElement.APP_NAME);
        Ateye.out.println(recommend);
    }

    /**
     * Invoked by a BeanFactory after it has set all bean properties supplied
     * (and satisfied BeanFactoryAware and ApplicationContextAware).
     * <p>This method allows the bean instance to perform initialization only
     * possible when all bean properties have been set and to throw an
     * exception in the event of misconfiguration.
     *
     * @throws Exception in the event of misconfiguration (such
     * as failure to set an essential property) or if initialization fails.
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        SwitchManager.init(JourneyPlanAiAssistantTppHelper.class);
    }
}
