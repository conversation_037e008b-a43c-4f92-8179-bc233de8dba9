package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;

import javax.annotation.Resource;

import com.alibaba.trip.tripdivision.domain.TrdiDivisionDO;

import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.trip.jourprod.biz.common.FlowCutHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplan.helper.JourneyCommonHelper;
import com.taobao.trip.jourprod.common.sal.hsf.tripdivision.TrdiDivisionReadServiceClient;
import com.taobao.trip.jourprod.common.sal.tair.impl.LdbTairManager;
import com.taobao.trip.jourprod.common.sal.tair.impl.MdbTairCommonHelper;
import com.taobao.trip.jourprod.domain.JourneyPlanAiChatMessageDO;
import com.taobao.trip.jourprod.domain.JourneyPlanAiChatMessageParam;
import com.taobao.trip.jourprod.mapper.JourneyPlanAiChatMessageDAO;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

/**
 * @Description 问一问白名单工具类
 * <AUTHOR>
 * @Date 2025/2/27
 **/
@Component
public class JourneyPlanAiAssistantWhiteHelper implements InitializingBean {

    private static final FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneyPlanAiAssistantWhiteHelper.class);

    @AppSwitch(des = "白名单缓存key", level = Level.p4)
    public static String WHITE_LIST_CACHE_KEY = "aiJourneyWhiteList";

    @AppSwitch(des = "问一问版本", level = Level.p4)
    public static String AI_JOURNEY_VERSION = "9.10.15";

    @AppSwitch(des = "问一问灰度比例", level = Level.p4)
    public static Integer AI_JOURNEY_GRAY_RATE = 0;

    @AppSwitch(des = "问一问灰度人群", level = Level.p4)
    public static Long AI_JOURNEY_GRAY_CROWD = -1L;

    @Resource
    private LdbTairManager ldbTairManager;

    @Resource
    private MdbTairCommonHelper mdbTairCommonHelper;

    @Resource
    private FlowCutHelper flowCutHelper;
    @Resource
    private TrdiDivisionReadServiceClient trdiDivisionReadServiceClient;
    @Resource
    private JourneyCommonHelper journeyCommonHelper;
    @Resource
    private JourneyPlanAiChatMessageDAO journeyPlanAiChatMessageDAO;

    /**
     * 问一问动态白名单
     */
    @AteyeInvoker(description = "问一问动态白名单", paraDesc = "userId")
    public boolean isInDynamicWhiteList(Long userId) {
        String key = getDyamicWhiteListKey(userId);
        Object value = mdbTairCommonHelper.getValueFromOD(key);
        Object ldbValue = ldbTairManager.get(key);
        if (Objects.nonNull(value) && Objects.isNull(ldbValue)) {
            ldbTairManager.put(key, userId, 0);
        }
        return Objects.nonNull(value) || Objects.nonNull(ldbValue);
    }

    /**
     * 问一问动态白名单新增
     */
    @AteyeInvoker(description = "问一问动态白名单新增", paraDesc = "userId&forceAdd")
    public boolean addDynamicWhiteList(Long userId) {
        String key = getDyamicWhiteListKey(userId);
        ldbTairManager.put(key, userId, 0);
        return true;
    }

    private String getDyamicWhiteListKey(Long userId) {
        return WHITE_LIST_CACHE_KEY + userId;
    }

    /**
     * 是否是海外流量
     */
    @AteyeInvoker(description = "是否是海外流量", paraDesc = "longitude&latitude")
    public boolean isOutFlow(String longitude, String latitude) {
        try {
            TrdiDivisionDO divisionInfoByLatLng = trdiDivisionReadServiceClient.getDivisionInfoByLatLng(NumberUtils.toDouble(latitude), NumberUtils.toDouble(longitude));

            // 如果未查询到地区信息，则默认是海外流量
            if (divisionInfoByLatLng == null) {
                return false;
            }
            return journeyCommonHelper.isOutCity(divisionInfoByLatLng.getId());
        } catch (Exception ex) {
            LOGGER.recordNormalException(new LogModel("isOutFlow").message("检查境外流量报错").e(ex));
        }
        return false;
    }

    /**
     * 扫描所有用户消息并批量添加白名单
     */
    @AteyeInvoker(description = "扫描所有用户消息并批量添加白名单", paraDesc = "batchSize")
    public String scanAllUsersAndAddToWhiteList(Integer batchSize, Long startId, Long endId) {
        if (batchSize == null || batchSize <= 0) {
            batchSize = 10; // 默认每批1000条
        }

        Set<String> allUserIds = new HashSet<>();
        long currentMinId = startId; // 从ID=1开始
        int totalProcessed = 0;
        int totalAdded = 0;

        try {
            while (currentMinId < endId) {
                long currentMaxId = currentMinId + batchSize - 1;

                // 创建查询参数，查询指定ID范围
                JourneyPlanAiChatMessageParam param = new JourneyPlanAiChatMessageParam();

                // 设置ID范围条件
                JourneyPlanAiChatMessageParam.Criteria criteria = param.createCriteria();
                criteria.andIdGreaterThanOrEqualTo(currentMinId);
                criteria.andIdLessThanOrEqualTo(currentMaxId);

                // 查询消息列表
                List<JourneyPlanAiChatMessageDO> messages = journeyPlanAiChatMessageDAO.selectByParamWithoutContent(param);

                if (messages == null || messages.isEmpty()) {
                    // 更新下一轮的起始ID
                    currentMinId = currentMaxId + 1;
                    continue;
                }

                // 提取userId
                for (JourneyPlanAiChatMessageDO message : messages) {
                    if (StringUtils.isNotBlank(message.getUserId())) {
                        allUserIds.add(message.getUserId());
                    }
                }

                // 批量添加白名单
                for (String userIdStr : allUserIds) {
                    try {
                        Long userId = Long.valueOf(userIdStr);
                        boolean added = addDynamicWhiteList(userId);
                        if (added) {
                            totalAdded++;
                        }
                    } catch (NumberFormatException e) {
                        LOGGER.recordNormalException(new LogModel("scanAllUsersAndAddToWhiteList")
                            .message("用户ID格式错误: " + userIdStr).e(e));
                    }
                }
                allUserIds.clear();

                Thread.sleep(100);

                totalProcessed += messages.size();

                LOGGER.recordOutput(new LogModel("scanAllUsersAndAddToWhiteList")
                    .message("已处理消息数: " + totalProcessed + ", ID范围: " + currentMinId + "-" + currentMaxId +
                            ", 当前批次消息数: " + messages.size()));

                // 更新下一轮的起始ID
                currentMinId = currentMaxId + 1;
            }

            String result = String.format("扫描完成！总共处理消息数: %d, 成功添加白名单用户数: %d",
                totalProcessed, totalAdded);

            LOGGER.recordOutput(new LogModel("scanAllUsersAndAddToWhiteList").message(result));

            return result;

        } catch (Exception e) {
            String errorMsg = String.format("扫描过程中发生异常！已处理消息数: %d, 已发现用户数: %d, 已添加白名单用户数: %d",
                totalProcessed, allUserIds.size(), totalAdded);
            LOGGER.recordNormalException(new LogModel("scanAllUsersAndAddToWhiteList")
                .message(errorMsg).e(e));
            return errorMsg + ", 异常信息: " + e.getMessage();
        }
    }


    /**
     * Invoked by a BeanFactory after it has set all bean properties supplied
     * (and satisfied BeanFactoryAware and ApplicationContextAware).
     * <p>This method allows the bean instance to perform initialization only
     * possible when all bean properties have been set and to throw an
     * exception in the event of misconfiguration.
     *
     * @throws Exception in the event of misconfiguration (such
     * as failure to set an essential property) or if initialization fails.
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        SwitchManager.init(JourneyPlanAiAssistantWhiteHelper.class);
    }
}
