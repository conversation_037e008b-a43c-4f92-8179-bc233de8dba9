package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;

import com.alibaba.boot.switchcenter.annotation.SwitchGroup;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.eagleeye.EagleEye;
import com.taobao.eagleeye.RpcContext_inner;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.JourneyPlanAiAssistantFrt;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanMessageStatusEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanStatusEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.csi.CsiClient;
import com.taobao.trip.jourprod.mapper.JourneyPlanAiChatMessageDAO;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatRequest;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult;
import com.taobao.trip.jourprod.utils.JSONUtil;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch.MOCK_CSI_CHECK_RESULT;

/**
 * <AUTHOR>
 * @date 2025/4/11
 */
@Component
@SwitchGroup
public class JourneyPlanAiCsiHelper {

    @Resource
    @Lazy
    private JourneyPlanAiAssistantFrt journeyPlanAiAssistantFrt;

    @Resource
    private CsiClient csiClient;

    @Resource
    private JourneyPlanAiChatMessageDAO journeyPlanAiChatMessageDAO;

    @Resource
    private JourneyPlanAiAssistantContentHelper journeyPlanAiAssistantContentHelper;


    private final static FliggyNewLogger logger = LogUtil.getFliggyNewLogger(JourneyPlanAiCsiHelper.class);

    @AppSwitch(des = "绿网检测mock白名单（强制让用户截断输出）", level = Level.p4)
    public static Set<Long> CSI_MOCK_USER_IDS = Sets.newHashSet();

    private static final ThreadPoolExecutor EXECUTOR = new ThreadPoolExecutor(10, 300, 60, TimeUnit.SECONDS,
        new LinkedBlockingQueue<>(100),
        new CustomizableThreadFactory("csiCheckProcessor"),
        new ThreadPoolExecutor.CallerRunsPolicy());

    public void asyncCheckBlocked(String currentMessageId, String content, ChatContext chatContext) {
        try {
            List<CompletableFuture<Void>> futures = chatContext.getOrCreateFutures(chatContext.getUserMessageId());
            RpcContext_inner rpcContext = EagleEye.getRpcContext();
            CompletableFuture<Void> blockFuture = CompletableFuture.runAsync(() -> {
                try {
                    EagleEye.setRpcContext(rpcContext);
                    syncCheckBlocked(currentMessageId, content, chatContext);
                } catch (Exception e) {
                    logger.recordDangerException(new LogModel("JourneyPlanAiCsiHelper").message("execute_error").e(e));
                } finally {
                    EagleEye.clearRpcContext();
                }
            }, EXECUTOR);
            futures.add(blockFuture);
        } catch (Exception e) {
            logger.recordDangerException(new LogModel("JourneyPlanAiCsiHelper").message("execute_error").e(e));
        }
    }

    public boolean syncCheckBlocked(String currentMessageId, String content, ChatContext chatContext) {
        if (MOCK_CSI_CHECK_RESULT) {
            return false;
        }
        JourneyPlanAiChatRequest request = chatContext.getRequest();
        Long userId = request.getUserId();
        Boolean passCsiCheck = csiClient.passCsiCheck(currentMessageId, chatContext.getSessionId(), content, userId);
        if (BooleanUtils.isFalse(passCsiCheck) || CSI_MOCK_USER_IDS.contains(userId)) {
            // 设置状态
            request.setStatus(AiJourneyPlanStatusEnum.BLOCK.getStatusCode());
            //中断流
            StreamMessageResult result = new StreamMessageResult(chatContext.getSystemMessageId(), chatContext.getUserMessageId(), StringUtils.EMPTY, StringUtils.EMPTY, AiJourneyPlanMessageStatusEnum.BLOCK.getStatusCode(), 0);
            chatContext.getMtopStream().writeAndEnd(JSON.toJSONString(result));
            // 记录缓存
            journeyPlanAiAssistantContentHelper.recordStreamMessageResult(chatContext.getSystemMessageId(), result);

            // 关闭当前会话的消息
            journeyPlanAiAssistantFrt.stopChat(request);
            journeyPlanAiAssistantFrt.resetChat(request);
            // 停止记录消息，防止用户分享
            chatContext.setDisableHistoryMessage(true);
            logger.recordOutput(new LogModel("JourneyPlanAiCsiHelper")
                    .request(currentMessageId)
                    .userId(userId)
                    .response(JSONUtil.removeVerticalBar(content))
                    .message("blocked"));
            return false;
        } else {
            return true;
        }
    }
}
