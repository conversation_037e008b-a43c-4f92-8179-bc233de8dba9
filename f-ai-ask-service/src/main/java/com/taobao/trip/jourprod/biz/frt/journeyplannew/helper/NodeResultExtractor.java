package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.taobao.ateye.annotation.Switch;
import com.taobao.trip.jourprod.biz.frt.impl.switchconfig.BizSwitch;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class NodeResultExtractor {
    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(NodeResultExtractor.class);


    public static void main(String[] args) {
        // 示例：将多个JSON对象收集到一个列表中
        String jsonInput = "{\"output\":{\"thoughts\":[{\"response\":\"{\\\"nodeName\\\":\\\"开始\\\",\\\"nodeType\\\":\\\"Start\\\",\\\"nodeStatus\\\":\\\"success\\\",\\\"nodeId\\\":\\\"Start_bYxoRU\\\",\\\"nodeExecTime\\\":\\\"0ms\\\"}\"},{\"response\":\"{\\\"nodeName\\\":\\\"大模型_1\\\",\\\"nodeResult\\\":\\\"{\\\\\\\"result\\\\\\\":\\\\\\\"\\\\\\\"}\\\",\\\"nodeType\\\":\\\"LLM\\\",\\\"nodeStatus\\\":\\\"success\\\",\\\"nodeId\\\":\\\"LLM_XKdu\\\",\\\"nodeExecTime\\\":\\\"599ms\\\"}\"},{\"response\":\"{\\\"nodeName\\\":\\\"条件判断1\\\",\\\"nodeResult\\\":\\\"{\\\\\\\"result\\\\\\\":\\\\\\\"\\\\\\\"}\\\",\\\"nodeType\\\":\\\"Judge\\\",\\\"nodeStatus\\\":\\\"success\\\",\\\"nodeId\\\":\\\"Judge_5xvz\\\",\\\"nodeExecTime\\\":\\\"0ms\\\"}\"},{\"response\":\"{\\\"nodeName\\\":\\\"无法识别\\\",\\\"nodeResult\\\":\\\"{\\\\\\\"result\\\\\\\":\\\\\\\"天气的建议。\\\\\\\"}\\\",\\\"nodeType\\\":\\\"LLM\\\",\\\"nodeStatus\\\":\\\"success\\\",\\\"nodeId\\\":\\\"LLM_QKo4\\\",\\\"nodeExecTime\\\":\\\"1602ms\\\"}\"},{\"response\":\"{\\\"nodeName\\\":\\\"脚本转换1\\\",\\\"nodeResult\\\":\\\"{\\\\\\\"result\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"answer\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"您好，我无法直接获取实时天气信息。不过，您可以通过飞猪App查看目的地的天气预报，或者告诉我您所在的城市，我可以为您提供一些查看天气的建议。\\\\\\\\\\\\\\\"}\\\\\\\"}\\\",\\\"nodeType\\\":\\\"Script\\\",\\\"nodeStatus\\\":\\\"success\\\",\\\"nodeId\\\":\\\"Script_QTZX\\\",\\\"nodeExecTime\\\":\\\"54ms\\\"}\"},{\"response\":\"{\\\"nodeName\\\":\\\"结束\\\",\\\"nodeResult\\\":\\\"{\\\\\\\"result\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"answer\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"您好，我无法直接获取实时天气信息。不过，您可以通过飞猪App查看目的地的天气预报，或者告诉我您所在的城市，我可以为您提供一些查看天气的建议。\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"test\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"1\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"userPrompt\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"今天天气怎么样\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"summaryQuery\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"今天天气怎么样？\\\\\\\\\\\\\\\"}\\\\\\\"}\\\",\\\"nodeType\\\":\\\"End\\\",\\\"nodeStatus\\\":\\\"success\\\",\\\"nodeId\\\":\\\"End_DrQn7F\\\",\\\"nodeExecTime\\\":\\\"4ms\\\"}\"}],\"session_id\":\"09833de11b4b43639c74e8d0f1763835\",\"finish_reason\":\"stop\",\"text\":\"{\\\"answer\\\":\\\"您好，我无法直接获取实时天气信息。不过，您可以通过飞猪App查看目的地的天气预报，或者告诉我您所在的城市，我可以为您提供一些查看天气的建议。\\\",\\\"test\\\":\\\"1\\\",\\\"userPrompt\\\":\\\"今天天气怎么样\\\",\\\"summaryQuery\\\":\\\"今天天气怎么样？\\\"}\"},\"usage\":{\"models\":[{\"input_tokens\":275,\"output_tokens\":4,\"model_id\":\"qwen-max\"},{\"input_tokens\":323,\"output_tokens\":36,\"model_id\":\"deepseek-v3\"}]},\"request_id\":\"15748f4d-1533-95e2-8161-a14e816e429d\"}";
        String extractedText = extractNodeResults(jsonInput);
        System.out.print(extractedText);
    }

    // 处理单个JSON对象的方法
    public static String extractNodeResults(String jsonInput) {
        // 检查输入是否为空
        if (jsonInput == null || jsonInput.trim().isEmpty()) {
            return "";
        }

        Set<String> FILTER_NODE_IDS = new HashSet<>(Arrays.asList(BizSwitch.IGNORE_INTENT_NODE.split(",")));

        StringBuilder resultBuilder = new StringBuilder();
        ObjectMapper mapper = new ObjectMapper();

        try {
            // 解析JSON
            JsonNode rootNode = mapper.readTree(jsonInput);

            // 检查output节点是否存在
            if (rootNode == null || !rootNode.has("output") || rootNode.get("output").isNull()) {
                return "";
            }

            JsonNode outputNode = rootNode.get("output");

            // 检查thoughts节点是否存在
            if (!outputNode.has("thoughts") || outputNode.get("thoughts").isNull()
                    || !outputNode.get("thoughts").isArray()) {
                return "";
            }

            JsonNode thoughtsNode = outputNode.get("thoughts");

            // 遍历所有的thoughts节点
            for (JsonNode thought : thoughtsNode) {
                // 检查thought节点是否为空或不包含response字段
                if (thought == null || !thought.has("response") || thought.get("response").isNull()) {
                    continue;
                }

                // 获取response字段的值，它是一个JSON字符串
                String responseJson = thought.get("response").asText();

                // 检查responseJson是否为空
                if (responseJson == null || responseJson.trim().isEmpty()) {
                    continue;
                }

                // 解析response中的JSON
                JsonNode responseNode = mapper.readTree(responseJson);

                // 检查nodeId字段是否存在
                if (responseNode == null || !responseNode.has("nodeId") || responseNode.get("nodeId").isNull()) {
                    continue;
                }

                String nodeId = responseNode.get("nodeId").asText();

                // 检查nodeId是否为空
                if (nodeId == null || nodeId.trim().isEmpty()) {
                    continue;
                }

                // 如果节点ID不在过滤列表中
                if (!FILTER_NODE_IDS.contains(nodeId)) {
                    // 检查nodeResult字段是否存在
                    if (!responseNode.has("nodeResult") || responseNode.get("nodeResult").isNull()) {
                        continue;
                    }

                    // 获取nodeResult字段，它也是一个JSON字符串
                    String nodeResultJson = responseNode.get("nodeResult").asText();

                    // 检查nodeResultJson是否为空
                    if (nodeResultJson == null || nodeResultJson.trim().isEmpty()) {
                        continue;
                    }

                    // 解析nodeResult中的JSON
                    JsonNode resultNode = mapper.readTree(nodeResultJson);

                    // 检查result字段是否存在
                    if (resultNode == null || !resultNode.has("result") || resultNode.get("result").isNull()) {
                        continue;
                    }

                    String result = resultNode.get("result").asText();

                    // 检查result是否为空（允许空字符串，因为有些节点可能没有输出）
                    if (result != null) {
                        // 将结果添加到StringBuilder
                        resultBuilder.append(result);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.recordNormalException(new LogModel("extractNodeResults")
                    .e(e).request(jsonInput));
        }

        String ret =  resultBuilder.toString();
        if(ret.startsWith("{\"answer")){
            return "";
        }else{
            return ret;
        }
    }
}