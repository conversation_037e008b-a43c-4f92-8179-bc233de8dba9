package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.PathNotFoundException;
import com.jayway.jsonpath.ReadContext;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import net.minidev.json.JSONArray; // 来自 json-smart 库

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch.PAGE_ID_VALIDATE_MAP;

/**
 * @Description 思考链校验工具类
 * <AUTHOR>
 * @Date 2025/4/15
 **/
public class ThinkingThoughtValidatorHelper {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(ThinkingThoughtValidatorHelper.class);

    /**
     * 校验 JSON 字符串中的 pageData 数据
     *
     * @param jsonString 待校验的 JSON 字符串
     * @return 如果所有 pageData 都通过校验则返回 true，否则返回 false
     */
    public static boolean validateJson(String jsonString) {
        try {
            Object document = Configuration.defaultConfiguration().jsonProvider().parse(jsonString);

            // 1. 提取所有 process 对象
            // $.result.cards[*].processList[*] 会找到所有 card 下的所有 processList 里的元素
            List<Map<String, Object>> processList = JsonPath.read(document, "$.result.cards[*].processList[*]");

            if (processList == null || processList.isEmpty()) {
                // 根据需求决定这种情况算不算校验通过，这里假设算通过
                return true;
            }

            boolean overallValidation = true;

            // 2. 遍历 process 对象
            for (Map<String, Object> process : processList) {
                String pageId = (String) process.get("pageId");
                Object pageData = process.get("pageData"); // 保持为 Object 类型，以便传递给 JsonPath

                if (pageId == null || pageData == null) {
                    return false;
                }

                // 3. 根据 pageId 对 pageData 进行校验
                boolean currentPageValidation = validatePageData(pageId, pageData);

                if (!currentPageValidation) {
                    return false;
                }
            }

            return overallValidation;

        } catch (Exception e) {
            LOGGER.recordNormalException(new LogModel("validateJson").message("校验 JSON 字符串失败").e(e));
            return true; // 解析或路径读取失败也算校验失败
        }
    }

    /**
     * 根据 pageId 校验 pageData
     *
     * @param pageId   页面 ID
     * @param pageData 页面数据对象
     * @return 如果校验通过返回 true，否则返回 false
     */
    private static boolean validatePageData(String pageId, Object pageData) {
        try {
            // 使用 ReadContext 来在 pageData 这个子文档上执行 JsonPath 查询
            ReadContext pageDataContext = JsonPath.parse(pageData);
            if (!PAGE_ID_VALIDATE_MAP.containsKey(pageId)) {
                return true;
            }
            String validatePath = PAGE_ID_VALIDATE_MAP.get(pageId);
            Object listDataObj = pageDataContext.read(validatePath);
            if (listDataObj == null) {
                LOGGER.recordNormalException(new LogModel("validatePageData").message("校验 pageData 失败，数据为空"));
                return false;
            }
            // 检查是否为 JSONArray 类型以及是否为空
            if (listDataObj instanceof JSONArray) {
                if (((JSONArray) listDataObj).isEmpty()) {
                    LOGGER.recordNormalException(new LogModel("validatePageData").message("校验 pageData 失败，数据为空"));
                    return false;
                }
            }

            // 如果当前 pageId 的所有校验都通过了
            return true;
        } catch (Exception e) {
            LOGGER.recordNormalException(new LogModel("validatePageData").message("校验 pageData 失败").e(e));
            return false;
        }
    }

}
