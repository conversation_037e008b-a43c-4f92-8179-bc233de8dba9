package com.taobao.trip.jourprod.biz.frt.journeyplannew.helper;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjsonfordrm.JSON;
import com.alibaba.fliggypoi.client.domain.*;
import com.alibaba.fliggypoi.client.service.TripPoiReadService;
import com.alibaba.trip.tripdivision.domain.TrdiDivisionDO;
import com.alibaba.trip.trippoi.domain.enumerate.TrpoPoiSourceType;
import com.alitrip.aisearch.model.search.hotel.entity.HotelInfo;
import com.alitrip.aisearch.model.search.poi.entity.PoiInfo;
import com.alitrip.aisearch.model.search.traffic.entity.TrafficJourneyInfo;
import com.alitrip.aisearch.model.search.traffic.entity.TrafficSegmentInfo;
import com.fliggy.train.banama.client.util.StringCommon;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.trip.jourprod.biz.common.CommonString;
import com.taobao.trip.jourprod.biz.common.ContextInnerKeyCommon;
import com.taobao.trip.jourprod.biz.common.StringCommonUtils;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl.HotelNodeProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl.JourneyPlanNodeProcessor.VacationInfo;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.constant.JourneyPlanAiAssistantConstant;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.diamond.PoiIdMappingConfigManager;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiTrafficCardVO;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.PoiInfoDTO;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.jourprod.common.sal.hsf.config.Switcher;
import com.taobao.trip.jourprod.common.sal.hsf.poi.PoiReadServiceClient;
import com.taobao.trip.jourprod.common.sal.hsf.tripdivision.TrdiDivisionReadServiceClient;
import com.taobao.trip.jourprod.core.service.gaode.AMapService;
import com.taobao.trip.jourprod.core.service.gaode.TrafficPlanQueryRequest;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageCardActionEnum;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageCardStatusEnum;
import com.taobao.trip.jourprod.service.facade.model.request.gaode.GaoDeSearchQueryRequest;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.ComponentDataResponse;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.HotelRecommendCardVO;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.RateTagVO;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult.StreamMessageCardModel;
import com.taobao.trip.rsp.collect.TitleCollectDTO;
import com.taobao.trip.wireless.hotel.domain.SearchHotelListVO;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.taobao.trip.jourprod.biz.common.ContextInnerKeyCommon.*;
import static com.taobao.trip.jourprod.core.service.gaode.AMapService.DefaultShowFields;

/**
 * @description: 行程规划工具类
 * @author: huiyi
 * @create: 2025-03-27 16:16
 **/
@Service
public class TripPlanNodeProcessorHelper implements InitializingBean {
    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(TripPlanNodeProcessorHelper.class);

    @Resource
    private TripPoiReadService tripPoiReadService;

    @Resource
    private HotelNodeProcessor hotelNodeProcessor;

    @AppSwitch(des = "线路跳转-前缀", level = Switch.Level.p3)
    public static String SQUARE_LINE_URL_PRE = "https://outfliggys.m.taobao.com/wow/pone/pcraft/common/fupr?isCollectionMap=true&params=%7B%22mapStage%22%3A%22previewPlan%22%2C%22planType%22%3A%222%22%2C%22planId%22%3A%22";

    private static final String AI_ROUTE_PRE_STR = "aiRoute_";

    private static final String ASK_MASK = "ask";

    @AppSwitch(des = "行程规划-频道页下方-我的行程规划-模块-详情跳转后缀2", level = Switch.Level.p4)
    public static String CHANNEL_PLAN_NEW_MODEL_PLAN_DETAIL_URL_AFTER_TWO = "%22";

    @AppSwitch(des = "行程规划-频道页下方-我的行程规划-模块-详情跳转后缀3", level = Switch.Level.p4)
    public static String CHANNEL_PLAN_NEW_MODEL_PLAN_DETAIL_URL_AFTER_THREE = "%7D";

    @Resource
    private MapStructUtil mapStructUtil;

    @Resource
    private PoiReadServiceClient poiReadServiceClient;

    @Resource
    private AMapService aMapService;

    @Resource
    private PoiIdMappingConfigManager poiIdMappingConfigManager;

    @AppSwitch(des = "使用poiName填充手绘地图开关，默认为开启", level = Switch.Level.p4)
    public static Boolean FillDrawMapByPoiName = true;

    @Resource
    private TrdiDivisionReadServiceClient trdiDivisionReadServiceClient;

    public Map<String, PoiInfo> getPoiMap(ChatContext chatContext) {
        Object routeObj = chatContext.getInternalData().get("poi");
        if (Objects.isNull(routeObj)) {
            return null;
        }
        return JSONObject.parseObject(JSON.toJSONString(routeObj), new TypeReference<Map<String, PoiInfo>>() {});
        // return (Map<String, Poi>)routeObj;
    }

    public void buildBaseCardModel(StreamMessageResult.StreamMessageCardModel cardModel, ComponentDataResponse response, String itemType) {
        cardModel.setId(response.getId());
        cardModel.setStatus(AiJourneyMessageCardStatusEnum.FINISH.getCode());
        cardModel.setAction(AiJourneyMessageCardActionEnum.REPLACE.getCode());
        cardModel.setItemType(itemType);
    }

    public void buildCurretPoiContext(Integer day, PoiInfo poi, ChatContext chatContext) {
        Map<Integer, List<PoiInfo>> dailyToPoiListMap = new ConcurrentHashMap<>();
        try {
            if (ObjectUtils.anyNull(day, poi, chatContext)) {
                return;
            }
            // 这是方法定义，chatContext中有一个internalData，internalData是一个Map结构，
            // 其中的一个key是day，value是List<AiJourneyPoiGroupByDailyDTO>
            // 要求每次传入的数据，累计到对应的day中
            Map<String, Object> internalData = chatContext.getInternalData();
            dailyToPoiListMap = (Map<Integer, List<PoiInfo>>) internalData.getOrDefault(ContextInnerKeyCommon.DAILY_POI_LIST, new ConcurrentHashMap<>());
            if (Objects.isNull(dailyToPoiListMap)) {
                dailyToPoiListMap = new ConcurrentHashMap<>();
            }
            List<PoiInfo> poiList = dailyToPoiListMap.get(day);
            if (Objects.isNull(poiList)) {
                poiList = Lists.newArrayList();
                dailyToPoiListMap.put(day, poiList);
            }
            long count = poiList.stream().filter(p -> Objects.equals(p.getPoiId(), poi.getPoiId())).findFirst().stream().count();
            if (count > 0) {
                return;
            }
            poiList.add(poi);
            // 2. 使用 computeIfAbsent 确保列表被正确存入 Map
            internalData.put(ContextInnerKeyCommon.DAILY_POI_LIST, dailyToPoiListMap);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("JourneyPlanNodeProcessor.buildCurretPoiContext")
                .e(e).message("buildCurretPoiContext_exception, day={0}, poi={1}, dailyToPoiListMap={2}", day, poi, dailyToPoiListMap));
        } finally {
            LOGGER.recordNormalException(new LogModel("JourneyPlanNodeProcessor.buildCurretPoiContext")
                .message("buildCurretPoiContext, day={0}, poi={1}, dailyToPoiListMap={2}", day, poi, dailyToPoiListMap));
        }
    }

    public void buildHotelContext(HotelRecommendCardVO hotelRecommendCardVO, ChatContext chatContext){
        try {
            Map<String, Object> internalData = chatContext.getInternalData();
            List<HotelRecommendCardVO> hotelList;
            if(internalData.containsKey(ContextInnerKeyCommon.JOURNEY_HOTEL)) {
                hotelList = (List<HotelRecommendCardVO>)internalData.get(ContextInnerKeyCommon.JOURNEY_HOTEL);
            }else {
                hotelList = new ArrayList<>();
            }
            long count = hotelList.stream().filter(p -> Objects.equals(p.getShid(), hotelRecommendCardVO.getShid()))
                .findFirst().stream().count();
            if (count > 0) {
                return;
            }
            hotelList.add(hotelRecommendCardVO);
            internalData.put(ContextInnerKeyCommon.JOURNEY_HOTEL, hotelList);
        }  catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("JourneyPlanNodeProcessor.buildCurretPoiContext")
                .e(e).message("buildHotelContext_exception"));
        }
    }

    public void buildTrafficContext(AiTrafficCardVO aiTrafficCardVO, ChatContext chatContext) {
        try {
            if(Objects.isNull(aiTrafficCardVO)) {
                return;
            }
            Map<String, Object> internalData = chatContext.getInternalData();
            List<AiTrafficCardVO> trafficCardVOList;
            if(internalData.containsKey(ContextInnerKeyCommon.JOURNEY_TRAFFIC)) {
                trafficCardVOList = (List<AiTrafficCardVO>)internalData.get(ContextInnerKeyCommon.JOURNEY_TRAFFIC);
            }else {
                trafficCardVOList = new ArrayList<>();
            }
            long count = trafficCardVOList.stream().filter(Objects::nonNull)
                    .filter(p -> Objects.nonNull(p.getTrafficGroupItem()))
                    .filter(p -> Objects.equals(p.getTrafficGroupItem().getId(), aiTrafficCardVO.getTrafficGroupItem().getId()))
                .findFirst().stream().count();
            if (count > 0) {
                return;
            }
            trafficCardVOList.add(aiTrafficCardVO);
            internalData.put(ContextInnerKeyCommon.JOURNEY_TRAFFIC, trafficCardVOList);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("JourneyPlanNodeProcessor.buildTrafficContext")
                .e(e).message("buildTrafficContext_exception"));
        }
    }


    public boolean isMultiTraffic(AiTrafficCardVO aiTrafficCardVO) {
        if (aiTrafficCardVO==null||aiTrafficCardVO.getTrafficGroupItem()==null){
            return false;
        }
        List<TrafficJourneyInfo> journeyInfos = aiTrafficCardVO.getTrafficGroupItem().getJourneyInfos();
        if (CollectionUtils.isEmpty(journeyInfos)){
            return false;
        }
        // 如果只有一个旅程信息，则进一步判断其包含的交通段种类
        if (journeyInfos.size() == 1) {
            TrafficJourneyInfo singleJourneyInfo = journeyInfos.get(0);
            List<TrafficSegmentInfo> segments = singleJourneyInfo.getSegments();
            // 收集所有不同的交通方式
            Set<Integer> transportTypeList = segments.stream()
                    .map(TrafficSegmentInfo::getTransportType)
                    .collect(Collectors.toSet());
            //如果有多种交通方式，则认为是多模式交通卡
            return transportTypeList.size() > 1;
        }
        return false;
    }




    public void buildJourneyItemList(List<VacationInfo> vacationInfos, ChatContext chatContext) {
        try {
            if(CollectionUtils.isEmpty(vacationInfos)) {
                return;
            }
            Map<String, Object> internalData = chatContext.getInternalData();
            //由于只会在快捷预订的时候，推荐门票，所以直接添加即可。
            internalData.put(ContextInnerKeyCommon.JOURNEY_ITEM_TYPE, vacationInfos);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("JourneyPlanNodeProcessor.buildJourneyItemList")
                .e(e).message("buildJourneyItemListToContext_exception"));
        }

    }

    public String safeGetJourneyTitle( ChatContext chatContext){
        Map<String, Object> internalData = chatContext.getInternalData();
        if(!internalData.containsKey(ContextInnerKeyCommon.TITLE)) {
            return "行程规划";
        }
        Object o = internalData.get(ContextInnerKeyCommon.TITLE);
        StreamMessageCardModel<TitleCollectDTO> baseJourneyTitleCard = (StreamMessageCardModel<TitleCollectDTO>)o;
        if(Objects.isNull(baseJourneyTitleCard)){
            return "行程规划";
        }
        TitleCollectDTO data = baseJourneyTitleCard.getData();
        if(Objects.isNull(data)){
            return "行程规划";
        }
        String title = data.getTitle();
        if (StringUtils.isBlank(title)) {
            return "行程规划";
        }
        return title;
    }

    public void buildTitleAndRouteMapUrl(StreamMessageCardModel<TitleCollectDTO> cardModel, ChatContext chatContext) {
        String systemMessageId = chatContext.getSystemMessageId();
        chatContext.getInternalData().put(ContextInnerKeyCommon.MAP_JUMP_URL, genMapJumpUrl(systemMessageId));
        chatContext.getInternalData().put(ContextInnerKeyCommon.TITLE, cardModel);
    }

    /**
     * 生成地图跳转链接，并把数据格式化成地图能接受的结构
     */
    public String genMapJumpUrl(String systemMessageId) {
        return SQUARE_LINE_URL_PRE + AI_ROUTE_PRE_STR + ASK_MASK + systemMessageId +
            CHANNEL_PLAN_NEW_MODEL_PLAN_DETAIL_URL_AFTER_TWO + CHANNEL_PLAN_NEW_MODEL_PLAN_DETAIL_URL_AFTER_THREE;
    }

    /**
     * 生成地图跳转链接，并把数据格式化成地图能接受的结构
     */
    public String genMapJumpUrl(String systemMessageId,String cardId) {
        return SQUARE_LINE_URL_PRE + AI_ROUTE_PRE_STR + ASK_MASK + systemMessageId + CommonString.CARD_ID_SPLIT +cardId +
            CHANNEL_PLAN_NEW_MODEL_PLAN_DETAIL_URL_AFTER_TWO + CHANNEL_PLAN_NEW_MODEL_PLAN_DETAIL_URL_AFTER_THREE;
    }

    public void checkResponseParams(ComponentDataResponse response) {
        Assert.hasLength(response.getId(), "id_null");
        Assert.hasLength(response.getComponentType(), "componentType_null");
        Assert.isTrue(response.isComplete(), "not_complete");
    }

    /**
     * 解析字符串并生成 <day, List<poiId>> 的映射
     *
     * @param input 输入字符串，格式为 day#poiId|poiId&day#poiId|poiId
     * @return 映射结果
     */
    @AteyeInvoker(description = "测试查询高德数据")
    public Map<String, List<String>> parseDayPoi(String input) {
        // 结果存储
        Map<String, List<String>> resultMap = Maps.newHashMap();
        // 按 & 分割不同的 day 部分
        String[] dayParts = input.split("&");
        for (String dayPart : dayParts) {
            // 每个部分按 # 分割出 day 和 poiIds
            String[] dayAndPois = dayPart.split("#");
            // 这时候就只有天数，那么这时候也要算到map里面的
            if(dayAndPois.length == 1 && StringUtils.isNumeric(dayAndPois[0])){
                // 获取 day
                String day = dayAndPois[0];
                resultMap.put(day, new LinkedList<>());
            }
            if (dayAndPois.length < 2) {
                continue; // 格式不正确，跳过
            }

            String day = dayAndPois[0]; // 获取 day
            String poiIdsStr = dayAndPois[1]; // 获取 poiId 列表
            // 按 | 分割 poiIds
            String[] poiIds = poiIdsStr.split("\\|");
            if (poiIds.length == 0) {
                poiIds = new String[0];
            }
            // 翻译值
            List<String> poiIdList = Arrays.stream(poiIds).filter(StringUtils::isNotBlank).map(poiId -> convertPoiIdByMappingId(poiId)).distinct().collect(Collectors.toList());
            // 将 poiIds 添加到对应的 day 中
            resultMap.computeIfAbsent(day, k -> new ArrayList<>()).addAll(poiIdList);
        }
        return resultMap;
    }

    public void buildCurrentHotelContext(ChatContext chatContext) {
    }

    /**
     * @param originLonLat      出发点 lon，lat（经度，纬度）， “,”分割
     * @param destinationLonLat 目的地 lon，lat（经度，纬度）， “,”分割
     * @param city              出发地cityCode
     * @param cityd             目的地cityCode
     * @return
     */
    public Map<DistanceUtils.TransportMode, RouterPlanInfo> queryRoutePlanByGaode(String originLonLat, String destinationLonLat, String city, String cityd) {
        TrafficPlanQueryRequest request = new TrafficPlanQueryRequest();
        //出发点 lon，lat（经度，纬度）， “,”分割
        request.setOrigin(originLonLat);
        // 目的地 lon，lat（经度，纬度）， “,”分割
        request.setDestination(destinationLonLat);
        request.setCity(city);
        request.setCityd(cityd);

        List<Map<String, Object>> mapResultList = aMapService.batchQueryTrafficPlan(request);
        if (CollectionUtils.isEmpty(mapResultList)) {
            return null;
        }

        // 公交地铁方案
        RouterPlanInfo busRoutePlanInfo = buildBusRoutePlan(mapResultList.get(0));
        // 步行方案
        RouterPlanInfo walkRoutePlanInfo = buildWalkRoutePlan(mapResultList.get(1));
        // 驾车方案
        RouterPlanInfo carRoutePlanInfo = buildCarRoutePlan(mapResultList.get(2));

        Map<DistanceUtils.TransportMode, RouterPlanInfo> routerPlanInfoMap = Maps.newHashMap();
        routerPlanInfoMap.put(DistanceUtils.TransportMode.BUS, busRoutePlanInfo);
        routerPlanInfoMap.put(DistanceUtils.TransportMode.WALK, walkRoutePlanInfo);
        routerPlanInfoMap.put(DistanceUtils.TransportMode.CAR, carRoutePlanInfo);
        return routerPlanInfoMap;
    }

    private RouterPlanInfo buildBusRoutePlan(Map<String, Object> buslineMap) {
        if (MapUtils.isEmpty(buslineMap)) {
            return null;
        }

        String infoCode = MapUtils.getString(buslineMap, "infoCode");
        if (Objects.equals(infoCode, "10000")) {
            return null;
        }
        Object body = buslineMap.get("body");
        if (Objects.isNull(body)) {
            return null;
        }

        JSONObject bodyJSONObj = JSONObject.parseObject(JSON.toJSONString(body));
        if (MapUtils.isEmpty(bodyJSONObj)) {
            return null;
        }

        JSONObject route = JSONObject.parseObject(JSON.toJSONString(bodyJSONObj.get("route")));
        if (MapUtils.isEmpty(route)) {
            return null;
        }

        JSONObject transit = new JSONObject();
        Object transitsObj = route.get("transits");
        if (transitsObj instanceof JSONObject) {
            transit = (JSONObject) transitsObj;
            if (MapUtils.isEmpty(transit)) {
                return null;
            }
        }
        if (transitsObj instanceof JSONArray) {
            JSONArray transits = route.getJSONArray("transits");
            if (CollectionUtils.isEmpty(transits)) {
                return null;
            }
            transit = transits.getJSONObject(0);
        }

        // 取第一个方案
        RouterPlanInfo routerPlanInfo = new RouterPlanInfo();
        String cost = transit.getString("cost");
        Integer duration = transit.getInteger("duration");
        // 将小数格式改成整形格式
        cost = buildCost(cost);

        routerPlanInfo.setCost(cost);
        routerPlanInfo.setCostTimeText("全程" + convertSecondsToTimeText(duration));
        routerPlanInfo.setCostMoneyText("¥" + cost);
        routerPlanInfo.setDuration(duration);
        routerPlanInfo.setIconUrl("https://gw.alicdn.com/imgextra/i4/O1CN01JcxQF11OzuqydIIit_!!6000000001777-2-tps-60-60.png");
        routerPlanInfo.setDistance(transit.getInteger("distance"));

        List<String> routerTextList = Lists.newArrayList();
        routerPlanInfo.setRouterTextList(routerTextList);

        JSONArray segments = transit.getJSONArray("segments");
        for (int i = 0; i < segments.size(); i++) {
            JSONObject segment = segments.getJSONObject(i);
            JSONObject bus = segment.getJSONObject("bus");
            if (MapUtils.isEmpty(bus)) {
                continue;
            }
            Object busObj = bus.get("buslines");
            JSONObject buslinesJSONObj;
            if (busObj instanceof JSONObject) {
                buslinesJSONObj = (JSONObject) busObj;
            } else {
                JSONArray buslines = bus.getJSONArray("buslines");
                if (CollectionUtils.isEmpty(buslines)) {
                    continue;
                }
                buslinesJSONObj = buslines.getJSONObject(0);
            }
            if (MapUtils.isEmpty(buslinesJSONObj)) {
                continue;
            }

            String railwayName = buslinesJSONObj.getString("name");
            railwayName = simplifyName(railwayName);
            JSONObject arrivalStop = buslinesJSONObj.getJSONObject("arrival_stop");
            JSONObject departureStop = buslinesJSONObj.getJSONObject("departure_stop");

            String arrivalStopName = MapUtils.getString(arrivalStop, "name");
            String departureStopName = MapUtils.getString(departureStop, "name");
            if (StringUtils.isBlank(arrivalStopName) || StringUtils.isBlank(departureStopName)) {
                continue;
            }
            if (Objects.equals(i, 0)) {
                String routerText = String.format("乘坐 %s 到 %s", railwayName, arrivalStopName);
                routerTextList.add(routerText);
                continue;
            }
            String routerText = String.format("%s 转 %s 到 %s", departureStopName, railwayName, arrivalStopName);
            routerTextList.add(routerText);
        }
        return routerPlanInfo;
    }

    private RouterPlanInfo buildCarRoutePlan(Map<String, Object> walkMap) {
        String infoCode = MapUtils.getString(walkMap, "infoCode");
        if (Objects.equals(infoCode, "10000")) {
            return null;
        }
        Object body = walkMap.get("body");
        if (Objects.isNull(body)) {
            return null;
        }
        JSONObject bodyJSONObj = JSONObject.parseObject(JSON.toJSONString(body));
        if (MapUtils.isEmpty(bodyJSONObj)) {
            return null;
        }
        JSONObject route = JSONObject.parseObject(JSON.toJSONString(bodyJSONObj.get("route")));
        if (MapUtils.isEmpty(route)) {
            return null;
        }
        JSONArray paths = route.getJSONArray("paths");
        if (CollectionUtils.isEmpty(paths)) {
            return null;
        }
        JSONObject path = paths.getJSONObject(0);
        if (MapUtils.isEmpty(path)) {
            return null;
        }
        Integer duration = path.getInteger("duration");
        Integer distance = path.getInteger("distance");

        return RouterPlanInfo.builder().costTimeText("约" + convertSecondsToTimeText(duration))
                .duration(duration).distance(distance)
                .iconUrl("https://gw.alicdn.com/imgextra/i3/O1CN01JPPQJs1VET8IgGR78_!!6000000002621-2-tps-60-60.png")
                .distanceText(DistanceUtils.TransportMode.CAR.getName() + " " + DistanceUtils.formatDistanceM(distance)).build();
    }


    private RouterPlanInfo buildWalkRoutePlan(Map<String, Object> walkMap) {
        String infoCode = MapUtils.getString(walkMap, "infoCode");
        if (Objects.equals(infoCode, "10000")) {
            return null;
        }
        Object body = walkMap.get("body");
        if (Objects.isNull(body)) {
            return null;
        }
        JSONObject bodyJSONObj = JSONObject.parseObject(JSON.toJSONString(body));
        if (MapUtils.isEmpty(bodyJSONObj)) {
            return null;
        }
        JSONObject route = JSONObject.parseObject(JSON.toJSONString(bodyJSONObj.get("route")));
        if (MapUtils.isEmpty(route)) {
            return null;
        }
        JSONArray paths = route.getJSONArray("paths");
        if (CollectionUtils.isEmpty(paths)) {
            return null;
        }
        JSONObject path = paths.getJSONObject(0);
        if (MapUtils.isEmpty(path)) {
            return null;
        }
        Integer duration = path.getInteger("duration");
        Integer distance = path.getInteger("distance");

        return RouterPlanInfo.builder().costTimeText("约" + convertSecondsToTimeText(duration))
                .duration(duration).distance(distance)
                .iconUrl("https://gw.alicdn.com/imgextra/i3/O1CN01O8MFp11wj9I1v3hza_!!6000000006343-2-tps-60-60.png")
                .distanceText(DistanceUtils.TransportMode.WALK.getName() + " " + DistanceUtils.formatDistanceM(distance)).build();
    }

    private String simplifyName(String railwayName) {
        if (StringUtils.isBlank(railwayName)) {
            return railwayName;
        }
        return railwayName.replaceAll("\\（.*?\\）", "").replaceAll("\\(.*?\\)", "").trim();
    }

    private String buildCost(String cost) {
        if (StringUtils.isBlank(cost)) {
            return cost;
        }
        String[] split = cost.split("\\.");
        return split[0];
    }

    public static String convertSecondsToTimeText(int seconds) {
        // 计算小时和分钟
        int hours = seconds / 3600; // 每小时3600秒
        int remainingSeconds = seconds % 3600; // 剩余的秒数
        int minutes = remainingSeconds / 60; // 每分钟60秒

        // 根据计算结果生成输出字符串
        if (hours > 0 && minutes > 0) {
            return hours + "小时" + minutes + "分钟";
        } else if (hours > 0) {
            return hours + "小时";
        } else if (minutes > 0) {
            return minutes + "分钟";
        } else {
            return seconds + "秒"; // 如果不足1分钟，直接返回秒数
        }
    }

    public void buildBusCostContext(ChatContext chatContext, String cost) {
        if(StringUtils.isBlank(cost) || Objects.isNull(chatContext)) {
            return;
        }
        Map<String, Object> internalData = chatContext.getInternalData();
        Integer busCost = MapUtils.getInteger(internalData, JOURNEY_BUS_COST, 0);

        internalData.put(JOURNEY_BUS_COST, busCost + buildBusCost(cost));
    }

    private Integer buildBusCost(String cost) {
        if (StringUtils.isBlank(cost)) {
            return 0;
        }
        try {
           return Integer.parseInt(cost);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("buildBusCost").message("invaildCost").request("cost=" + cost).response(""));
        }
        return 0;
    }

    public void buildFastReserveTag(ChatContext chatContext) {
        if(Objects.isNull(chatContext)) {
            return;
        }
        Map<String, Object> internalData = chatContext.getInternalData();
        internalData.put(JOURNEY_FAST_RESERVE_TAG, true);
    }

    public void buildFastReserveCardId(String id, ChatContext chatContext) {
        if(StringUtils.isBlank(id) || Objects.isNull(chatContext)) {
            return;
        }
        Map<String, Object> internalData = chatContext.getInternalData();
        internalData.put(JOURNEY_FAST_RESERVE_CARD_ID, id);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        SwitchManager.init(TripPlanNodeProcessorHelper.class);
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RouterPlanInfo {
        private List<String> routerTextList;

        private String costTimeText;

        private String costMoneyText;

        /**
         * 方案时间 秒
         */
        private Integer duration;
        /**
         * 方案费用 单位元
         */
        private String cost;

        /**
         * 方案距离 米
         */
        private Integer distance;

        /**
         * 方案距离 文本
         */
        private String distanceText;

        /**
         * icon
         */
        private String iconUrl;
    }

    public PoiInfoDTO queryPoiInfo(ChatContext chatContext, String poiId) {
        PoiInfoDTO poiInfoResult = new PoiInfoDTO();
        TripPoiResult<FliggyPoi> poiResult;
        PoiInfo poiInfo = null;
        try {
            Map<String, PoiInfo> poiMap = getPoiMap(chatContext);
            if (MapUtils.isNotEmpty(poiMap) && poiMap.containsKey(poiId)) {
                poiInfo = poiMap.get(poiId);
                PoiInfoDTO poiInfoDTO = JSONObject.parseObject(JSON.toJSONString(poiInfo), PoiInfoDTO.class);
                return poiInfoDTO;
            }
            poiResult = tripPoiReadService.getPoi(Long.valueOf(poiId));
            if (Objects.isNull(poiResult) || !poiResult.isSuccess() || Objects.isNull(poiResult.getData())) {
                return null;
            }
            FliggyPoi fliggyPoi = poiResult.getData();
            PoiBase poiBase = fliggyPoi.getPoiBase();
            PoiDivision poiDivision = fliggyPoi.getPoiDivision();

            poiInfoResult.setPoiId(poiId);
            poiInfoResult.setPoiName(poiBase.getName());
            poiInfoResult.setPoiLat(String.valueOf(poiBase.getLatitude()));
            poiInfoResult.setPoiLng(String.valueOf(poiBase.getLongitude()));
            poiInfoResult.setCityName(poiDivision.getCityName());
            poiInfoResult.setCityCode(String.valueOf(poiDivision.getCityId()));

            PoiExtend poiExtend = fliggyPoi.getPoiExtend();
            if (Objects.nonNull(poiExtend)) {
                poiInfoResult.setPoiNameAbbr(poiExtend.getSimpleName());
            }

            return poiInfoResult;
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("TripPlanNodeProcessorHelper.queryPoiInfo").message("exception").e(e));
        } finally {
            LOGGER.recordOutput(new LogModel("TripPlanNodeProcessorHelper.queryPoiInfo")
                    .message("contextPoi-{0}, apiPoi-{1}", JSON.toJSONString(poiInfo), JSON.toJSONString(poiInfoResult))
                    .request("poiId=" + poiId));
        }
        return null;
    }


    @AteyeInvoker(description = "查询高德poi测试")
    public PoiInfoDTO queryPoiByGaodePoiId(String poiId) {
        PoiInfoDTO poiInfo = new PoiInfoDTO();
        try {
            List<Map<String, Object>> maps = aMapService.queryGaoDeSearchByIdWithVersion(poiId, true, DefaultShowFields);
            if (CollectionUtils.isEmpty(maps)) {
                return null;
            }
            Map<String, Object> map = maps.get(0);

            String id = MapUtils.getString(map, "id");
            String name = MapUtils.getString(map, "name");
            String location = MapUtils.getString(map, "location");
            String cityName = MapUtils.getString(map, "cityname");
            String photosString = MapUtils.getString(map, "photos");
            // 行政区化id，这里返回的是区域的code，不是城市code，这里还得转一层，转到城市code去，这里有bug的
            String divisionId = MapUtils.getString(map, "adcode");


            JSONArray photos = JSONArray.parseArray(photosString);
            if (CollectionUtils.isEmpty(photos)) {
                return null;
            }
            JSONObject photo = photos.getJSONObject(0);
            String photoUrl = photo.getString("url");

            if (StringUtils.isNotBlank(location)) {
                String[] split = location.split(",");
                poiInfo.setPoiLat(split[1]);
                poiInfo.setPoiLng(split[0]);
            }
            poiInfo.setPoiId(id);
            poiInfo.setPoiName(name);
            poiInfo.setCityName(cityName);
            poiInfo.setCityCode(divisionId);
            poiInfo.setDivisionId(divisionId);
            poiInfo.setPoiImg(photoUrl);
            poiInfo.setPoiType(JourneyPlanAiAssistantConstant.GAO_DE_TYPE);
            return poiInfo;
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("TripPlanNodeProcessorHelper.queryGaodePoiByApi").message("exception").e(e));
        } finally {
            LOGGER.recordOutput(new LogModel("TripPlanNodeProcessorHelper.queryGaodePoiByApi").request("poiId=" + poiId).response(JSON.toJSONString(poiInfo)));
        }
        return null;
    }

    @AteyeInvoker(description = "手绘地图测试单个poi/城市/城市-name")
    public String queryPoiInfoForTest(String poiName){
        return JSON.toJSONString(queryPoiInfoByHsf(poiName));
    }


    public PoiInfoDTO queryPoiInfoByHsf(String poiId) {
        PoiInfoDTO poiInfo = new PoiInfoDTO();
        String gaodePoiId = "";
        try {
            // gaode的poi  如果不包含中文，代表是高德id
            if (!StringUtils.isNumeric(poiId) && !StringCommonUtils.checkContainChineseChar(poiId) ) {
                //高德id转飞猪poiId。
                Long fliggyPoiId = poiReadServiceClient.getPoiIdBySourceId(TrpoPoiSourceType.GAODE_MAP, poiId);
                if (Objects.nonNull(fliggyPoiId)) {
                    poiId = String.valueOf(fliggyPoiId);
                } else {
                    return queryPoiByGaodePoiId(poiId);
                }
            }
            //如果是中文，代表是大模型自己填充name
            if (BooleanUtils.isTrue(FillDrawMapByPoiName) && !StringUtils.isNumeric(poiId) && StringCommonUtils.checkContainChineseChar(poiId) ) {
                LOGGER.recordOutput(new LogModel("TripPlanNodeProcessorHelper.queryPoiInfoByHsf").request("queryPoiByName poiName =  " + poiId));
                //通过中文搜索高德点位。
                GaoDeSearchQueryRequest request = new GaoDeSearchQueryRequest();
                // 北京-故宫
                String[] split = poiId.split("-");
                //如果格式不对，解析的有问题，直接返回空。
                if (split.length != 2) {
                    LOGGER.recordOutput(new LogModel("TripPlanNodeProcessorHelper.queryPoiInfoByHsf").request(" dataAnalysisError poiName =  " + poiId));
                    return null;
                }
                String cityName = split[0];
                String poiName = split[1];
                // poiName转cityCode的功能  内存中保存了高德的行政区,将cityName转换为搞的cityCode
                String cityCode = aMapService.queryCityCodeByCityName(getDealCityName(cityName));
                if (StringUtils.isBlank(cityCode)) {
                    LOGGER.recordOutput(new LogModel("TripPlanNodeProcessorHelper.queryPoiInfoByHsf").request(" cityNameToCityCode error  =  " + poiId));
                    return null;
                }
                request.setCity(cityCode);
                request.setKeywords(poiName);
                request.setCitylimit(Boolean.TRUE);
                request.setPageSize(1);
                List<PoiInfoDTO> poiInfoDTOS = queryGaoPoiByKeywords(request);
                //如果通过高德名称查不到，返回为空。
                if (CollectionUtils.isEmpty(poiInfoDTOS)) {
                    return null;
                }
                PoiInfoDTO poiInfoDTO = poiInfoDTOS.get(0);
                gaodePoiId = poiInfoDTO.getPoiId();
                Long fliggyPoiId = poiReadServiceClient.getPoiIdBySourceId(TrpoPoiSourceType.GAODE_MAP, gaodePoiId);
                if (Objects.nonNull(fliggyPoiId)) {
                    poiId = String.valueOf(fliggyPoiId);
                } else {
                    PoiInfoDTO gaodePoi = queryPoiByGaodePoiId(gaodePoiId);
                    // 高德返回的城市code不是六子码，上面已经转化了六子码了，直接用好了，里面代码用的是区域code，肯定有问题的
                    gaodePoi.setDivisionId(cityCode);
                    gaodePoi.setDivisionId(cityCode);
                    return gaodePoi;
                }
            }

            TripPoiResult<FliggyPoi> poiResult = tripPoiReadService.getPoi(Long.valueOf(poiId));
            if (Objects.isNull(poiResult) || !poiResult.isSuccess() || Objects.isNull(poiResult.getData())) {
                LOGGER.recordOutput(new LogModel("TripPlanNodeProcessorHelper.queryPoiInfoByHsf").request("getPoi =" + poiId).response(JSONUtil.toJSONString(poiResult)));
                if(StringUtils.isNotEmpty(gaodePoiId)){
                    // 如果飞猪poi不存在，且高德id存在，则通过高德poiId查询，
                    return queryPoiByGaodePoiId(gaodePoiId);
                }

                return null;
            }
            FliggyPoi fliggyPoi = poiResult.getData();
            PoiBase poiBase = fliggyPoi.getPoiBase();
            PoiDivision poiDivision = fliggyPoi.getPoiDivision();

            poiInfo.setPoiId(poiId);
            poiInfo.setPoiName(poiBase.getName());
            poiInfo.setPoiLat(String.valueOf(poiBase.getLatitude()));
            poiInfo.setPoiLng(String.valueOf(poiBase.getLongitude()));
            if (Objects.nonNull(poiDivision)) {
                poiInfo.setCityCode(String.valueOf(poiDivision.getCityId()));
                //Id即是行政区化id。
                poiInfo.setDivisionId(String.valueOf(poiDivision.getId()));
                //如果divisionId存在， 那么cityName也使用标准行政区化的名字。
                poiInfo.setCityName(getCityNameByPoiDivision(poiDivision));
                //poiInfo.setCityName(getCityNameByIdOrPoiDivision(String.valueOf(poiDivision.getId()),poiDivision));
            }
            poiInfo.setPoiImg(poiBase.getMainPic());
            poiInfo.setFirstCategory(poiBase.getFirstCategory());
            poiInfo.setFirstCategoryName(poiBase.getFirstCategoryName());
            poiInfo.setIsAbroad(poiBase.getIsAbroad());
            poiInfo.setPoiType(JourneyPlanAiAssistantConstant.POI_TYPE);

            return poiInfo;
        } catch (Exception e) {
            LOGGER.recordNormalException(new LogModel("TripPlanNodeProcessorHelper.queryPoiInfoByHsf").message("exception").e(e));
            return null;
        } finally {
            LOGGER.recordOutput(new LogModel("TripPlanNodeProcessorHelper.queryPoiInfoByHsf").request("poiId=" + poiId).response(JSON.toJSONString(poiInfo)));
        }
    }

    private String getCityNameByIdOrPoiDivision(String divisionId, PoiDivision poiDivision) {
        if(StringUtils.isEmpty(divisionId)){
            // 如果没有行政区化id，那么使用poi的名称。
           return  getCityNameByPoiDivision(poiDivision);
        }
        //查询行政区数据
        TrdiDivisionDO divisionById = trdiDivisionReadServiceClient.getDivisionById(Long.valueOf(divisionId));
        if(Objects.nonNull(divisionById)){
            return divisionById.getName();
        }
        //行政区化查不到，还是使用poi的。
        return getCityNameByPoiDivision(poiDivision);
    }

    private String getCityNameByPoiDivision(PoiDivision poiDivision) {
        // 兼容省管县逻辑， 有的没有城市， 取他的上一级。
        // 例如这个 文昌市（名字叫市，其实是个县级市，也可以理解为是个县城），向上提取就成为了 海南。
        return StringUtils.isEmpty(poiDivision.getCityName()) ? poiDivision.getProvName()
            : poiDivision.getCityName();
    }

    private String getDealCityName(String cityName) {
        if(StringUtils.isBlank(cityName)){
            return cityName;
        }
        if(MapUtils.isNotEmpty(Switcher.DRAW_CITY_DEAL)){
            String dealName = Switcher.DRAW_CITY_DEAL.get(cityName);
            if(StringUtils.isNotBlank(dealName)){
                return dealName;
            }
        }
        return cityName;

    }

    @AteyeInvoker(description = "查询高德测试")
    public String getGaoDe(String testJsonString){
        List<PoiInfoDTO> poiInfoDTOS = queryGaoPoiByKeywords(JSON.parseObject(testJsonString,GaoDeSearchQueryRequest.class));
        return JSONUtil.toJSONString(poiInfoDTOS);
    }

    public List<PoiInfoDTO> queryGaoPoiByKeywords(GaoDeSearchQueryRequest request) {
        List<PoiInfoDTO> poiInfoDTOList = null;

        try {
            List<Map<String, Object>> resultMaps = aMapService.queryGaoDeSearch(request);
            if (CollectionUtils.isEmpty(resultMaps)) {
                return null;
            }
            poiInfoDTOList = resultMaps.stream().filter(Objects::nonNull).map(map -> {
                PoiInfoDTO poiInfoDTO = new PoiInfoDTO();
                poiInfoDTO.setPoiId(MapUtils.getString(map, "id"));
                poiInfoDTO.setPoiName(MapUtils.getString(map, "name"));

                String location = MapUtils.getString(map, "location");
                poiInfoDTO.setLocation(location);

                if (StringUtils.isNotBlank(location)) {
                    String[] split = location.split(",");
                    if (split.length == 2) {
                        poiInfoDTO.setPoiLat(split[1]);
                        poiInfoDTO.setPoiLng(split[0]);
                    }
                }
                return poiInfoDTO;
            }).collect(Collectors.toList());
            return poiInfoDTOList;
        } catch (Throwable e) {
            LOGGER.recordDangerException(new LogModel("queryGaoPoiByKeywords")
                    .request("request = " + JSONUtil.toJSONString(request))
                    .message("exception").e(e));
        } finally {
            LOGGER.recordOutput(new LogModel("queryGaoPoiByKeywords")
                .request("request = " + JSONUtil.toJSONString(request))
                    .response(JSON.toJSONString(poiInfoDTOList)));
        }
        return null;
    }

    public List<HotelRecommendCardVO> batchQuerySearchHotel(String shids,String checkIn,String checkOut, ChatContext chatContext) {
        Map<String, HotelInfo> hotelInfoMap = (Map<String, HotelInfo>) chatContext.getInternalData().getOrDefault("hotel", Collections.EMPTY_MAP);
        List<SearchHotelListVO.HotelListInfo> hotelListInfos = hotelNodeProcessor.batchQueryHotelList(shids, checkIn,checkOut,chatContext);
        LOGGER.recordOutput(new LogModel("TripPlanNodeProcessorHelper.batchQuerySearchHotel").request("shids=" + shids).response(JSON.toJSONString(hotelListInfos)));
        if (Objects.isNull(hotelListInfos) && MapUtils.isNotEmpty(hotelInfoMap)) {
            HotelInfo hotelInfo = hotelInfoMap.get(shids);
            if (Objects.isNull(hotelInfo)) {
                return null;
            }
            HotelRecommendCardVO hotelRecommendCardVO = new HotelRecommendCardVO();
            hotelRecommendCardVO.setTitle(hotelInfo.getName());
            hotelRecommendCardVO.setPrice(String.valueOf(hotelInfo.getPrice()));
            hotelRecommendCardVO.setRateScore(hotelInfo.getRateNumberWithoutUnit());
            hotelRecommendCardVO.setStar(hotelInfo.getStar());
            hotelRecommendCardVO.setDistince(hotelInfo.getAttractionDistance());
            hotelRecommendCardVO.setPicUrls(hotelInfo.getImages());
            hotelRecommendCardVO.setShid(shids);
            hotelRecommendCardVO.setPriceSuffix("起");
            hotelRecommendCardVO.setBookingBtnJumpUrl(hotelInfo.getDetailUrl());
            hotelRecommendCardVO.setBookingBtnName("预定");
            hotelRecommendCardVO.setJumpUrl(hotelInfo.getDetailUrl());
            return Lists.newArrayList(hotelRecommendCardVO);
        }
        if (CollectionUtils.isEmpty(hotelListInfos)) {
            return null;
        }
        List<HotelRecommendCardVO> hotelRecommendCardVOList = Lists.newArrayList();
        for (SearchHotelListVO.HotelListInfo hotelInfo : hotelListInfos) {
            long shid = hotelInfo.getShid();
            HotelInfo hotelSearchInfo = hotelInfoMap.get(String.valueOf(shid));
            HotelRecommendCardVO hotelRecommendCardVO = new HotelRecommendCardVO();
            hotelRecommendCardVO.setTitle(hotelInfo.getName());
            hotelRecommendCardVO.setPrice(String.valueOf(hotelInfo.getYuanPrice()));
            if (hotelInfo.getYuanPrice() != null && hotelInfo.getYuanPrice() <= 0) {
                LOGGER.recordDangerException(new LogModel("hotelRecommend").message("invaildPrice").request("shid=" + shid).response("" + hotelInfo.getYuanPrice()));
            } else {
                LOGGER.recordOutput(new LogModel("hotelRecommend").message("vaildPrice").request("shid=" + shid).response("" + hotelInfo.getYuanPrice()));
            }
            hotelRecommendCardVO.setPriceSuffix("起");
            // TODO 临时mock数据，后面需要去掉
            hotelRecommendCardVO.setRecommendReason("我是推荐理由😈");
            hotelRecommendCardVO.setDistince("距离故宫1900km");
            if (hotelSearchInfo != null) {
                hotelRecommendCardVO.setPicUrls(hotelSearchInfo.getImages());
                // 推荐理由
                hotelRecommendCardVO.setRecommendReason(hotelSearchInfo.getConciseDescription());
                // 距离
                hotelRecommendCardVO.setDistince(hotelSearchInfo.getAttractionDistance());
                hotelRecommendCardVO.setJumpUrl(hotelSearchInfo.getDetailUrl());
            }
            hotelRecommendCardVO.setRateScore(hotelInfo.getRateCountWithoutUnit());
            hotelRecommendCardVO.setStar(hotelInfo.getStar());
            // 评价标签
            hotelRecommendCardVO.setRateTagList(HotelNodeProcessor.convertRateTagVO(shid, hotelSearchInfo));
            // 收藏状态
            hotelRecommendCardVO.setIsCollect(hotelInfo.getIsFavourite() == 0 ? false : true);
            hotelRecommendCardVO.setBookingBtnName("预定");
            hotelRecommendCardVO.setOriginalData(hotelInfo);
            hotelRecommendCardVO.setShid(String.valueOf(shid));
            List<RateTagVO> rateTagVOList = Lists.newArrayList();
            RateTagVO rateTagVO1 = new RateTagVO();
            rateTagVO1.setCount("21");
            rateTagVO1.setTagName("超赞");
            RateTagVO rateTagVO2 = new RateTagVO();
            rateTagVO2.setCount("300");
            rateTagVO2.setTagName("超级干净");
            hotelRecommendCardVO.setRateTagList(Lists.newArrayList(rateTagVO1, rateTagVO2));
            hotelRecommendCardVOList.add(hotelRecommendCardVO);
        }

        return hotelRecommendCardVOList;
    }

    public String covertPoiGrade(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        // 遍历每个字符检查是否为'A'
        for (int i = 0; i < str.length(); i++) {
            if (str.charAt(i) != 'A') {
                return null;
            }
        }
        // 全为A时返回数量+字符A
        return str.length() + "A";
    }

    public String getPoiType(String poiId) {
        if (StringUtils.isNumeric(poiId)) {
            return "poi";
        }
        return "gaode";
    }

    /**
     * poi 翻译值转换，解决64 tiananmen的case
     */
    public String convertPoiIdByMappingId(String mappingPoiId) {
        if (StringUtils.isBlank(mappingPoiId)) {
            return mappingPoiId;
        }
        String poiId = poiIdMappingConfigManager.getPoiId(mappingPoiId);
        if (StringUtils.isNotBlank(poiId)) {
            return poiId;
        }
        return mappingPoiId;
    }
}
