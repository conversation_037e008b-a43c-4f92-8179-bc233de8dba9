package com.taobao.trip.jourprod.biz.frt.journeyplannew.impl;

import com.alibaba.boot.switchcenter.annotation.SwitchGroup;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fliggypoi.client.domain.*;
import com.alibaba.mtop3.invocation.MtopStream;
import com.alibaba.trip.tripdivision.client.vo.TrdiDivisionExtendVO;
import com.alibaba.trip.tripdivision.domain.TrdiDivisionDO;
import com.alibaba.trippoi.utils.GeoUtils;
import com.fliggy.aiworks.client.ChatbotClientResponse;
import com.fliggy.aiworks.client.aiask.AiaskMessagingClientService;
import com.fliggy.aiworks.client.aiask.request.AiaskStopRequest;
import com.fliggy.aiworks.client.enums.StopCodeEnum;
import com.fliggy.crowd.service.domain.profile.ProfileValue;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.taobao.ateye.annotation.Switch;
import com.taobao.csi.common.core.domain.CsiGenerateContent;
import com.taobao.csi.common.core.domain.CsiResult;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.mtop.api.agent.MtopContext;
import com.taobao.trip.jourprod.biz.common.AskHistoryCommon;
import com.taobao.trip.jourprod.biz.common.CommonString;
import com.taobao.trip.jourprod.biz.common.ThreadPoolMonitor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.JourneyPlanAiAssistantFrt;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.content.ChatContentGenerator;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.*;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.BaseDependencyBean;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyInTransitServiceHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneyMessageContentDTO;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneyRecommendLineModel;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneySceneModel;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.biz.frt.llm.domain.MessageParam;
import com.taobao.trip.jourprod.common.lang.utils.CIDateUtil;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.jourprod.common.lang.utils.JourPlanUtil;
import com.taobao.trip.jourprod.common.sal.annotation.RunLog;
import com.taobao.trip.jourprod.common.sal.exception.AiAskCheckException;
import com.taobao.trip.jourprod.common.sal.exception.AiAskInterruptException;
import com.taobao.trip.jourprod.common.sal.exception.TripJourneyException;
import com.taobao.trip.jourprod.common.sal.hsf.config.Switcher;
import com.taobao.trip.jourprod.common.sal.hsf.poi.PoiReadServiceClient;
import com.taobao.trip.jourprod.common.sal.hsf.tripdivision.TrdiDivisionReadServiceClient;
import com.taobao.trip.jourprod.common.sal.hsf.userRelated.UserProfileManager;
import com.taobao.trip.jourprod.common.sal.tair.impl.TairKeyFactory;
import com.taobao.trip.jourprod.core.service.gaode.AMapService;
import com.taobao.trip.jourprod.csi.CsiClient;
import com.taobao.trip.jourprod.csi.CsiResultEnum;
import com.taobao.trip.jourprod.member.MemberServiceHelper;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.JourneyPlanAiAssistantRoleEnum;
import com.taobao.trip.jourprod.service.facade.googlemap.GoogleMapService;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.*;
import com.taobao.trip.jourprod.service.facade.model.response.JourPlanError;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.*;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult.StreamMessageCardModel;
import com.taobao.trip.jourprod.service.impl.journeyplannew.JourneyAssistantDataOperateService;
import com.taobao.trip.request.JourneyPlanAiAssistantWhiteUserRequest;
import com.taobao.trip.request.JourneyPlanAiUrlCallbackRequest;
import com.taobao.trip.request.QueryRecentMessageRequest;
import com.taobao.trip.rsp.Route;
import com.taobao.trip.rsp.StructRoute;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.taobao.trip.jourdprod.core.model.common.ConstantElement.CHINA_COUNTRY_NAME;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl.JourneyPlanNodeProcessor.FLIGGY_DEFAULT_AVATAR;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.constant.JourneyPlanAiAssistantConstant.*;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch.MAX_CHAT_NUM_IN_PROGRESS;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch.SESSION_LIST_MIN_VERSION;
import static com.taobao.trip.jourprod.common.sal.hsf.config.JourneyChannelPlanNewSwitch.POI_DETAIL_URL;
import static com.taobao.trip.jourprod.core.service.gaode.AMapService.DefaultShowFields;
import static com.taobao.trip.jourprod.csi.CsiClient.STANDARD_ANSWER;
import static com.taobao.trip.jourprod.service.facade.model.response.JourPlanError.AI_JOURNEY_CHAT_IN_PROGREE_NUMBER_MORE_ERROR;

/**
 * @Description 问一问具体逻辑实现接口
 * <AUTHOR>
 * @Date 2025/1/21
 **/
@Component
@SwitchGroup
public class JourneyPlanAiAssistantFrtImpl extends BaseDependencyBean implements JourneyPlanAiAssistantFrt, DisposableBean {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneyPlanAiAssistantFrtImpl.class);

    /**
     * 线程池
     */
    public static final ThreadPoolExecutor journeyPlanAiAssistantExecutor = new ThreadPoolExecutor(50, 50, 0, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<Runnable>(100),
            new CustomizableThreadFactory("JourneyPlanAiAssistantExecutor"));

    static {
        new Thread(new ThreadPoolMonitor(journeyPlanAiAssistantExecutor, "JourneyPlanAiAssistantExecutor")).start();
    }

    private static final String MESSAGE_ID_LIST_SPLIT = ",";

    private static final String ZHUGE_PERMANENT_CITY_LABEL = "permanent_city_id";

    @AppSwitch(des = " CSI 同步输入检测开启", level = Level.p4)
    public static boolean CSI_QUERY_OPEN = false;

    @AppSwitch(des = " CSI 同步输入检测的 mock 用户", level = Level.p4)
    public static Set<Long> CSI_QUERY_MOCK_USER = Sets.newHashSet();

    @AppSwitch(des = "记录回答是否被暂停的缓存时间", level = Level.p4)
    public static Integer STOP_MESSAGE_ID_TTL_S = 24 * 60 * 60;

    @AppSwitch(des = "问一问初始化页面基础信息", level = Level.p4)
    public static Map<String, Map<String, String>> INIT_PAGE_BASIC_INFO = new HashMap<>();

    @AppSwitch(des = "用户问一问来源类型缓存时间", level = Level.p4)
    public static Integer USER_LAST_ASK_SOURCE_TYPE_TTL_S = 30 * 60;


    static {
        Map<String, String> journeyInTransitMap = new HashMap<>();
        journeyInTransitMap.put("mainTitle", "Hi，%s");
        journeyInTransitMap.put("subTitle", "我精通当地吃喝玩乐，有问题随时问我哦");
        INIT_PAGE_BASIC_INFO.put("journeyInTransit", journeyInTransitMap);

        Map<String, String> journeyPlanMap = new HashMap<>();
        journeyPlanMap.put("mainTitle", "Hi，我是AI旅行助手");
        journeyPlanMap.put("subTitle", "我精通旅游资讯，一起来规划下一次旅行吧");
        INIT_PAGE_BASIC_INFO.put("journeyPlan", journeyPlanMap);
    }

    @AppSwitch(des = "poi相册页url", level = Level.p4)
    public static String POI_PHOTO_PAGE_URL = "https://market.m.taobao.com/app/trip/h5-poi/pages/photoes/index.html?spm=181.9659619.base_info.click_image&mddId=%s&mddLevel=3&pre_pageVersion=4.4.15&fpt=vac";

    @AppSwitch(des = "高德poi详情页url", level = Level.p4)
    public static String AMAP_POI_DETAIL_PAGE_URL = "https://uri.amap.com/marker?poiid=%s&src=mypage&callnative=0";

    @AppSwitch(des = "poi图片数量", level = Level.p4)
    public static Integer MAX_POI_PHOTO_NUM = 5;

    @AppSwitch(des = "使用gcj02的境外行政区划id", level = Level.p4)
    public static List<Long> USE_GCJ02_ABROAD_DIVISION_ID_LIST = Lists.newArrayList(810000L, 820000L);

    @AppSwitch(des = "使用wsg84的国内行政区划id", level = Level.p4)
    public static List<Long> USE_WSG84_CHINA_DIVISION_ID_LIST = Lists.newArrayList(710000L);

    @AppSwitch(des = "poi距离展示阈值", level = Level.p4)
    public static Double MAX_POI_DISTANCE_SHOW = 50.0;

    @AppSwitch(des = "poi距离米-展示阈值", level = Level.p4)
    public static Double MAX_POI_DISTANCE_M_SHOW = 1.0;
    @AppSwitch(des = "记录回答是否被暂停的key", level = Level.p4)
    public static String STOP_MESSAGE_ID_KEY = "ai:journey:stop:messageId:";


    @Switch(description = "平台poi默认兜底图", name = "POI_DEFAULT_IMG")
    public static String POI_DEFAULT_IMG = "https://gw.alicdn.com/tfs/TB19n9kjoY1gK0jSZFCXXcwqXXa-750-468.png";

    @AppSwitch(des = "首页AI问一问卡片样式配置", level = Level.p4)
    public static List<Map<String, String>> HOMEPAGE_CARD_CONFIG = Lists.newArrayList();
    @AppSwitch(des = "获取系统消息ID的重试次数", level = Level.p4)
    public static Integer GET_SYSMESSAGEID_RETRY_NUM = 15;
    @AppSwitch(des = "获取系统消息ID的重试等待的休眠时间", level = Level.p4)
    public static Integer GET_SYSMESSAGEID_RETRY_SLEEP_TIME = 100;

    static {
        Map<String, String> cardConfig = new HashMap<>();
        cardConfig.put("title", "酒店");
        cardConfig.put("icon", "https://gw.alicdn.com/tfs/TB19n9kjoY1gK0jSZFCXXcwqXXa-750-468.png");
        cardConfig.put("query", "");
        HOMEPAGE_CARD_CONFIG.add(cardConfig);

        Map<String, String> cardConfig1 = new HashMap<>();
        cardConfig1.put("title", "目的地");
        cardConfig1.put("icon", "https://gw.alicdn.com/tfs/TB19n9kjoY1gK0jSZFCXXcwqXXa-750-468.png");
        cardConfig1.put("query", "");
        HOMEPAGE_CARD_CONFIG.add(cardConfig1);

        Map<String, String> cardConfig2 = new HashMap<>();
        cardConfig2.put("title", "行程规划");
        cardConfig2.put("icon", "https://gw.alicdn.com/tfs/TB19n9kjoY1gK0jSZFCXXcwqXXa-750-468.png");
        cardConfig2.put("query", "");
        HOMEPAGE_CARD_CONFIG.add(cardConfig2);

        Map<String, String> cardConfig3 = new HashMap<>();
        cardConfig3.put("title", "交通");
        cardConfig3.put("icon", "https://gw.alicdn.com/tfs/TB19n9kjoY1gK0jSZFCXXcwqXXa-750-468.png");
        cardConfig3.put("query", "");
        HOMEPAGE_CARD_CONFIG.add(cardConfig3);
    }


    @Resource
    private TrdiDivisionReadServiceClient trdiDivisionReadServiceClient;

    @Resource
    private PoiReadServiceClient poiReadServiceClient;

    @Resource
    private GoogleMapService googleMapService;

    @Resource
    private AMapService aMapService;

    @Resource
    private ChatContentGenerator chatContentGenerator;

    @Resource
    private JourneyAssistantDataOperateService journeyAssistantDataOperateService;

    @Resource
    private AiaskMessagingClientService aiaskMessagingClientService;

    @Resource
    private CsiClient csiClient;

    @Resource
    private UserProfileManager userProfileManager;

    @Resource
    private MemberServiceHelper memberServiceHelper;

    // 用于存储正在处理的请求
    private final ConcurrentHashMap<String, ChatContext> processingRequests = new ConcurrentHashMap<>();

    @Override
    @RunLog
    public JourneyPlanAiInitResult init(JourneyPlanAiInitRequest request) {
        // 参数判空
        if (request == null || Objects.isNull(request.getUserId())) {
            throw new TripJourneyException("请求参数为空", "请求参数为空");
        }

        // 加白名单，如果用户后续降级了 仍然能访问
        journeyPlanAiAssistantWhiteHelper.addDynamicWhiteList(request.getUserId());

        JourneyPlanAiInitResult result = new JourneyPlanAiInitResult();
        // 按照版本决定是否有续接
        if (!JourPlanUtil.isNewVersion(request.getVersion(), SESSION_LIST_MIN_VERSION)) {
            // 查询是否有在回答的问题
            Pair<String, String> messageIdPair = journeyPlanAiAssistantHelper.currentNotStoppedMessageId(request.getUserId(), null);
            if (Objects.nonNull(messageIdPair)) {
                // 有问题的话 立刻结束回答
                // 获取指令
                String chat = journeyPlanAiAssistantHelper.getChat(messageIdPair.getLeft());
                if (StringUtils.isNotBlank(chat)) {
                    result.setCurrentChat(chat);
                    result.setCurrentSystemMessageId(messageIdPair.getLeft());
                    result.setCurrentUserMessageId(messageIdPair.getRight());
                    return result;
                }
            }
        }

        if (Objects.equals(request.getSource(), SourceEnum.HOMEPAGE.getCode())) {
            result.setWelcomeType(AiJourneyInitPageWelcomeTypeEnum.HOMEPAGE_COMMON.getCode());
        }

        JourneyPlanAiChatRequest journeyPlanAiChatRequest = new JourneyPlanAiChatRequest();
        journeyPlanAiChatRequest.setUserId(request.getUserId());
        // 重置前查询历史消息
        fillHistoryMessage(request, result);
        resetChat(journeyPlanAiChatRequest);
        // todo 语意更明确 创建。查询分开
        result.setSessionId(journeyPlanAiAssistantSessionHelper.currentSession(request.getUserId()));

        // 查询当前用户行中态
        InTransit inTransit = journeyInTransitServiceHelper.queryInTransit(request.getUserId(), request.getLatitude(), request.getLongitude());

        // 处理初始化页面基本信息（标题等）
        handleInitPageBasicInfo(request, result, inTransit);

        //原始消息
        if (StringUtils.isNotEmpty(request.getOriginalMessageIds())) {
            List<String> messageIdList = Arrays.asList(request.getOriginalMessageIds().split(","));
            fillOriginalMessage(request, result, messageIdList);
            return result;
        }

        // 行中态，处理逻辑
        if (Objects.nonNull(inTransit) && Boolean.TRUE.equals(inTransit.getHasMove())) {
            // 处理行中态页面信息
            buildInTransitInitPageInfo(request, result, inTransit);
        }

        return result;
    }


    private void fillOriginalMessage(JourneyPlanAiInitRequest request, JourneyPlanAiInitResult result, List<String> originalMessageIdList) {
        try {
            JourneyPlanAiAssistantHistoryMessageRequest historyMessageRequest = new JourneyPlanAiAssistantHistoryMessageRequest();
            historyMessageRequest.setUserId(String.valueOf(request.getUserId()));
            historyMessageRequest.setPageSize(originalMessageIdList.size());
            JourneyPlanAiAssistantHistoryResult journeyPlanAiAssistantHistoryResult = journeyAssistantDataOperateFacade.queryHistoryMessage(historyMessageRequest);
            List<JourneyPlanAiAssistantHistoryResult.MessageInfo> messageList = journeyPlanAiAssistantHistoryResult.getMessageList();
            //如果历史消息为空直接返回
            if (CollectionUtils.isEmpty(messageList)) {
                List<JourneyPlanAiAssistantHistoryResult.MessageInfo> messageInfos = journeyAssistantDataOperateFacade.insertOriginalMessage(request, originalMessageIdList);
                result.setMessageInfos(messageInfos);
                return;
            }
            //如果分享的消息，是自己的最新的历史消息，直接返回
            List<String> myselfMessageIdList = messageList.stream().map(JourneyPlanAiAssistantHistoryResult.MessageInfo::getMessageId).collect(Collectors.toList());
            boolean isShareAndMyselfEqual = areListsEqual(myselfMessageIdList, originalMessageIdList);
            if (isShareAndMyselfEqual) {
                result.setMessageInfos(messageList);
                return;
            }
            List<String> historyMessageIdList = messageList.stream().map(JourneyPlanAiAssistantHistoryResult.MessageInfo::getOriginalMessageId).collect(Collectors.toList());
            //判断historyMessageIdList和shareMessageIdList是否完全相同
            boolean isShareAndHistoryEqual = areListsEqual(historyMessageIdList, originalMessageIdList);
            //如果不同 需要插入历史消息
            if (!isShareAndHistoryEqual) {
                List<JourneyPlanAiAssistantHistoryResult.MessageInfo> messageInfos = journeyAssistantDataOperateFacade.insertOriginalMessage(request, originalMessageIdList);
                result.setMessageInfos(messageInfos);
            } else {
                result.setMessageInfos(messageList);
            }
        } catch (Exception e) {
            LOGGER.recordNormalException(new LogModel("fillShareMessage")
                    .e(e)
                    .response(JSON.toJSONString(request))
                    .message("初始化原始消息失败"));
        }
    }


    public static boolean areListsEqual(List<String> historyMessageIdList, List<String> originalMessageIdList) {
        boolean equal = true;
        for (int i = 0; i < originalMessageIdList.size(); i++) {
            String historyMsgId = historyMessageIdList.get(i);
            String shareMsgId = originalMessageIdList.get(i);
            if (!Objects.equals(historyMsgId, shareMsgId)) {
                equal = false;
                break;
            }
        }
        return equal;
    }

    private void buildInTransitInitPageInfo(JourneyPlanAiInitRequest request, JourneyPlanAiInitResult result, InTransit inTransit) {
        try {
            journeyInTransitServiceHelper.handleInitPageInfo(request, result, inTransit);
        } catch (Throwable e) {
            LOGGER.recordNormalException(new LogModel("buildInTransitInitPageInfo")
                    .e(e)
                    .message("构建行中态初始化信息报错"));
        }
    }

    private void handleInitPageBasicInfo(JourneyPlanAiInitRequest request, JourneyPlanAiInitResult result, InTransit inTransit) {
        // 卡片渲染
        List<JourneyPlanAiInitResult.WelcomeCard> welcomeCards = JSONObject.parseArray(JSON.toJSONString(HOMEPAGE_CARD_CONFIG), JourneyPlanAiInitResult.WelcomeCard.class);
        result.setWelcomeCardList(welcomeCards);

        // 行中态标题
        if (Objects.nonNull(inTransit) && Boolean.TRUE.equals(inTransit.getHasMove())) {
            Map<String, String> journeyInTransitMap = INIT_PAGE_BASIC_INFO.get(AiJourneyInitPageWelcomeTypeEnum.JOURNEY_IN_TRANSIT.getCode());

            result.setMainTitle(String.format(journeyInTransitMap.get("mainTitle"), getCurrentTimePointTextByTimeZoneId(inTransit.getTimeZoneId())));
            result.setSubTitle(journeyInTransitMap.get("subTitle"));
            return;
        }
        // 非行中态标题
        if (Objects.equals(request.getSource(), SourceEnum.HOMEPAGE.getCode())) {
            Map<String, String> homepageMap = INIT_PAGE_BASIC_INFO.get(AiJourneyInitPageWelcomeTypeEnum.HOMEPAGE_COMMON.getCode());
            result.setMainTitle(homepageMap.get("mainTitle"));
            result.setSubTitle(homepageMap.get("subTitle"));
            return;
        }
        Map<String, String> journeyPlanMap = INIT_PAGE_BASIC_INFO.get(AiJourneyInitPageWelcomeTypeEnum.JOURNEY_PLAN.getCode());
        result.setMainTitle(journeyPlanMap.get("mainTitle"));
        result.setSubTitle(journeyPlanMap.get("subTitle"));
    }

    /**
     * 获取当前时刻(区分时区)，早中晚
     *
     * @return 返回的问候语
     */
    private String getCurrentTimePointTextByTimeZoneId(String timeZoneId) {
        // 使用指定的时区ID创建ZonedDateTime对象
        ZonedDateTime zonedDateTime = ZonedDateTime.now(ZoneId.of(timeZoneId));

        int hour = zonedDateTime.getHour();
        if (hour >= 6 && hour < 12) {
            return "早上好";
        } else if (hour >= 12 && hour < 18) {
            return "下午好";
        } else {
            return "晚上好";
        }
    }

    /**
     * 对话接口
     * 流式输出内容，并记录当前场景，之后总结接口获取总结数据
     * 流式数据记录到缓存中，保证续接接口能取到数据
     * 流式返回结束之后，要启动总结任务，生成历史记录
     *
     * @param request
     */
    @Override
    @RunLog(success = "$.success")
    public StreamMessageResult streamChat(JourneyPlanAiChatRequest request) {
        ChatContext chatContext = new ChatContext();
        chatContext.setRequest(request);
        // 启动mtop流式输出
        MtopStream mtopStream = MtopContext.startStream();
        chatContext.setMtopStream(mtopStream);
        // 对话开始 同时对话计数器+1
        this.incrChatCount(request, chatContext);
        try {
            // 预检查和参数前置处理
            StreamMessageResult streamMessageResult = this.chatPreCheckAndProcess(mtopStream, chatContext);
            if (streamMessageResult != null) {
                mtopStream.end();
                return streamMessageResult;
            }

            // 前置校验结束，更新状态
            messageStatusHelper.updateStatus(chatContext.getSystemMessageId(), AiJourneyPlanStatusEnum.GENERATE_SCENE);

            // 生成对话内容
            chatContentGenerator.generateChatContent(chatContext, mtopStream);
            return StreamMessageResult.succeed();
        } catch (AiAskCheckException askCheckException) {
            // 校验异常
            this.printErrorLog(request, chatContext, askCheckException);
            StreamMessageResult failedResult = StreamMessageResult.failed(askCheckException.getPlanError());
            mtopStream.writeAndEnd(JSON.toJSONString(failedResult));
            return failedResult;
        } catch (AiAskInterruptException aiAskInterruptException) {
            // 输出中断异常
            this.printErrorLog(request, chatContext, aiAskInterruptException);
            mtopStream.endExceptionally(aiAskInterruptException);
            return StreamMessageResult.failed(new JourPlanError(String.valueOf(aiAskInterruptException.getErrorCode().code),aiAskInterruptException.getErrorMsg()));
        }  catch (Throwable throwable) {
            // 其他异常
            this.printErrorLog(request, chatContext, throwable);
            mtopStream.endExceptionally(throwable);
        } finally {
            // 对话结束后 同时对话计数器-1
            this.decrChatCount(request, chatContext);

            // 移除当前消息的上下文缓存以及更新消息状态
            // 判空是兼容check报错时未生成消息ID的场景
            String systemMessageId = chatContext.getSystemMessageId();
            if (StringUtils.isNotBlank(systemMessageId)){
                journeyPlanAiAssistantContentHelper.clearContentMap(systemMessageId);
                messageStatusHelper.updateStatus(systemMessageId, AiJourneyPlanStatusEnum.END);
            }

            // 请求处理完成后移除
            Optional.ofNullable(chatContext.getUserMessageId()).ifPresent(t -> processingRequests.remove(t));
        }

        return StreamMessageResult.failed(JourPlanError.CHAT_BIZ_ERROR);
    }

    /**
     * 同时聊天计数器+1
     * @param request
     * @param chatContext
     */
    private void incrChatCount(JourneyPlanAiChatRequest request, ChatContext chatContext) {
        String chatInProgressCacheKey = TairKeyFactory.getUserChatInProgressKey(request.getUserId());
        // 对话开始 同时对话计数器+1
        ldbTairManager.incr(chatInProgressCacheKey, 1, 0, 60 * 60);
        LOGGER.recordOutput(new LogModel("incrChatCount")
                .condition("count incr")
                .message("sessionId={0},sysMessageId={1}", chatContext.getSessionId(), chatContext.getSystemMessageId())
                .userId(request.getUserId()));
    }

    /**
     * 同时聊天计数器-1
     * @param request
     * @param chatContext
     */
    private void decrChatCount(JourneyPlanAiChatRequest request, ChatContext chatContext) {
        String chatInProgressCacheKey = TairKeyFactory.getUserChatInProgressKey(request.getUserId());
        // 对话结束后 同时对话计数器-1
        ldbTairManager.decr(chatInProgressCacheKey, 1, 1, 60 * 60);
        LOGGER.recordOutput(new LogModel("decrChatCount")
                .condition("count decr")
                .message("sessionId={0},sysMessageId={1}", chatContext.getSessionId(), chatContext.getSystemMessageId())
                .userId(request.getUserId()));
    }

    private void printErrorLog(JourneyPlanAiChatRequest request, ChatContext chatContext, Throwable exception) {
        String systemMessageId = chatContext.getSystemMessageId();
        String message = null;
        if (exception instanceof AiAskCheckException) {
            message = "对话接口校验异常";
        } else if (exception instanceof AiAskInterruptException) {
            message = "对话接口异常中断";
        } else {
            message = "对话接口异常";
        }
        LOGGER.recordNormalException(new LogModel("printErrorLog")
                .request(JSONUtil.toJSONStringForLog(request))
                .code(String.join("，", chatContext.getSessionId(), systemMessageId, chatContext.getUserMessageId()))
                .e(exception)
                .success(false)
                .message(message));
    }

    /**
     * 对话接口前置校验和处理
     * @param chatContext
     */
    private StreamMessageResult chatPreCheckAndProcess(MtopStream mtopStream, ChatContext chatContext) {
        JourneyPlanAiChatRequest request = chatContext.getRequest();
        Long userId = request.getUserId();

        // 限制进行中对话的数量
        // 登录才校验
        if (userId != null && userId > 0) {
            this.checkChatNumInProgress(userId);
        }

        // 记录当前session
        String sessionId = request.getSessionId();
        if (StringUtils.isNotBlank(sessionId)) {
            // todo 把写数据库的逻辑提出去
            // 校验sessionId是否有效
            boolean exist = journeyPlanAiAssistantSessionHelper.checkSessionIsExist(userId, sessionId, request.getChat());
            if (!exist) {
                throw new AiAskCheckException(JourPlanError.AI_JOURNEY_SESSION_DELETED_ERROR);
            }
        } else {
            // todo 日志打印
            // 获取当前会话id
            sessionId = journeyPlanAiAssistantSessionHelper.currentSession(userId);
            // 记录到session表里
            journeyPlanAiAssistantSessionHelper.saveSession(userId, sessionId, request.getChat());
        }
        chatContext.setSessionId(sessionId);
        LOGGER.recordOutput(new LogModel("chatPreCheckAndProcess")
                .code("core")
                .request("sessionId=" + sessionId)
                .message("设置sessionId到上下文中"));

        // [顺序不要变] start
        // 查询是否有在回答的问题
        Pair<String, String> messageIdPair = journeyPlanAiAssistantHelper.currentNotStoppedMessageId(userId, sessionId);
        if (Objects.nonNull(messageIdPair)) {
            LOGGER.recordOutput(new LogModel("chatPreCheckAndProcess")
                    .code("core")
                    .request("systemMessageId=" + messageIdPair.getLeft() + ", userMessageId=" + messageIdPair.getRight())
                    .message("发现有正在回答的问题，已停止"));
            JourneyPlanAiChatRequest journeyPlanAiChatRequest = new JourneyPlanAiChatRequest();
            journeyPlanAiChatRequest.setCurrentMessageId(messageIdPair.getLeft());
            journeyPlanAiChatRequest.setUserId(userId);
            journeyPlanAiAssistantFrt.stopChat(journeyPlanAiChatRequest);
        }

        // 生成消息id
        journeyPlanAiAssistantHelper.generateMessageId(request, chatContext);
        LOGGER.recordOutput(new LogModel("chatPreCheckAndProcess").code("core").message("生成消息ID"));
        // [顺序不要变] end

        // 写入空消息，把消息id告诉客户端
        writeEmptyMessage(mtopStream, chatContext);

        // 记录正在处理的请求，方便钩子函数处理
        processingRequests.put(chatContext.getUserMessageId(), chatContext);

        //处理原始消息（分享/收藏过来的）
        handlerOriginalMessageInfo(chatContext);
        // 填充用户当前城市cityCode
        setLbsInfo(chatContext, request);

        // todo 挪出来
        // 意图识别来进行场景分类
        journeyPlanAiAssistantHelper.analysisAiJourneySceneInfo(chatContext);

        // 更新会话的最近更新时间
        if (StringUtils.isNotBlank(sessionId)) {
            journeyPlanAiAssistantSessionHelper.updateSessionLastModifiedTime(userId, sessionId);
        }

        //用户最近一次询问来源
        mdbTairCommonHelper.putValue(AskHistoryCommon.getLastAskSourceRedisKey(String.valueOf(request.getUserId())), request.getSource(), USER_LAST_ASK_SOURCE_TYPE_TTL_S);

        // 同步对用户输入做风控拦截(改写后的query)
        if (!passQueryCsi(mtopStream, chatContext)) {
            LOGGER.recordOutput(new LogModel("chatPreCheckAndProcess")
                    .code("core")
                    .message("输入内容被风控拦截"));
            return StreamMessageResult.succeed();
        }

        return null;
    }

    /**
     * 检查当前用户进行中对话的数量
     * @param userId
     */
    private void checkChatNumInProgress(Long userId) {
        String chatInProgressCacheKey = TairKeyFactory.getUserChatInProgressKey(userId);
        int num = (int) ldbTairManager.get(chatInProgressCacheKey);
        if (num > MAX_CHAT_NUM_IN_PROGRESS) {
            throw new AiAskCheckException(AI_JOURNEY_CHAT_IN_PROGREE_NUMBER_MORE_ERROR);
        }
    }

    // 填充用户当前城市cityCode
    private void setLbsInfo(ChatContext chatContext, JourneyPlanAiChatRequest request) {
        try {
            // 当有经纬度时，通过坐标获取地理信息
            boolean execCoordinateSuccess = handleCoordinateInfo(chatContext, request.getLatitude(), request.getLongitude());
            if (execCoordinateSuccess) {
                return;
            }
            //经纬度处理失败,则使用 用户常驻城市信息
            handleUserProfileForCity(chatContext, request.getUserId());
        } catch (Throwable throwable) {
            LOGGER.recordNormalException(new LogModel("setLbsInfo")
                    .response(JSON.toJSONString(request))
                    .code(String.join("，", chatContext.getSessionId(), chatContext.getSystemMessageId(), chatContext.getUserMessageId()))
                    .e(throwable)
                    .success(false)
                    .message("填充用户当前城市cityCode异常"));
        }
    }

    /**
     * 处理用户常驻城市信息
     *
     * @param chatContext 聊天上下文对象
     * @param userId      用户ID
     */
    private void handleUserProfileForCity(ChatContext chatContext, Long userId) {
        Map<String, ProfileValue> profileValueMap = userProfileManager.userProfile(userId, Lists.newArrayList(JourneyInTransitServiceHelper.ZHUGE_PERMANENT_CITY_LABEL));

        if (MapUtils.isEmpty(profileValueMap)) {
            return;
        }
        ProfileValue profileValue = profileValueMap.get(JourneyInTransitServiceHelper.ZHUGE_PERMANENT_CITY_LABEL);

        if (profileValue == null) {
            return;
        }

        String cityId = profileValue.getValue();

        if (NumberUtils.isDigits(cityId)) {
            TrdiDivisionDO divisionDO = trdiDivisionReadServiceClient.getDivisionById(Long.valueOf(cityId));
            LOGGER.recordOutput(new LogModel("handleUserProfileForCity.getDivisionById").
                    request(String.valueOf(userId)).
                    response(JSON.toJSONString(divisionDO)));
            updateChatContextWithDivision(chatContext, divisionDO);
        }
    }

    /**
     * 处理坐标地理信息
     *
     * @param chatContext 聊天上下文对象
     * @param latitude    纬度
     * @param longitude   经度
     */
    private boolean handleCoordinateInfo(ChatContext chatContext, String latitude, String longitude) {
        try {
            if (StringUtils.isBlank(latitude) || StringUtils.isBlank(longitude)) {
                return false;
            }
            TrdiDivisionDO divisionDO = trdiDivisionReadServiceClient.getDivisionInfoByLatLng(
                    Double.parseDouble(latitude),
                    Double.parseDouble(longitude)
            );

            if (divisionDO != null) {
                updateChatContextWithDivision(chatContext, divisionDO);
                return true;
            }
            return false;
        } catch (Exception e) {
            // 记录日志或处理格式异常
            LOGGER.recordNormalException(new LogModel("handleCoordinateInfo").message("Invalid coordinate format error").e(e));
            return false;
        }
    }


    /**
     * 统一更新聊天上下文的地理信息
     *
     * @param chatContext 聊天上下文对象
     * @param divisionDO  地理分区数据对象
     */
    private void updateChatContextWithDivision(ChatContext chatContext, TrdiDivisionDO divisionDO) {
        if (divisionDO == null) {
            return;
        }
        chatContext.setCurrentLbsCityCode(divisionDO.getId().toString());
        chatContext.setCurrentLbsCityName(divisionDO.getNameAbbr());
        chatContext.setCurrentLbsProCode(divisionDO.getParentId());
    }


    private void handlerOriginalMessageInfo(ChatContext chatContext) {
        JourneyPlanAiChatRequest request = chatContext.getRequest();
        if (StringUtils.isEmpty(request.getOriginalMessageIds())) {
            return;
        }
        List<String> messageIdList = Arrays.asList(request.getOriginalMessageIds().split(","));
        List<MessageParam> originalMessageInfo = journeyPlanAiAssistantMessageHelper.getOriginalMessageInfo(messageIdList);
        chatContext.setOriginalMessageInfo(originalMessageInfo);
        LOGGER.recordOutput(new LogModel("handlerOriginalMessageInfo").response(JSON.toJSONString(originalMessageInfo)).request(request.getOriginalMessageIds()));
    }

    /**
     * processQI
     *
     * @param request
     * @param chatContext
     * @param aiJourneySceneModel
     */
    private void processQI(JourneyPlanAiChatRequest request, ChatContext chatContext, AiJourneySceneModel aiJourneySceneModel) {
        // 返回结果判空
        String qpResponse = journeyPlanAiAssistantTppHelper.queryQP(request.getUserId(), chatContext.getSummarizedChat());
        LOGGER.recordOutput(new LogModel("processQI_result").request(chatContext.getSummarizedChat()).response(qpResponse));
        if (StringUtils.isBlank(qpResponse)) {
            LOGGER.recordOutput(new LogModel("processQI").message("qpResponse is empty"));
            return;
        }

        // 结果请求成功校验
        JSONObject resObj = JSONObject.parseObject(qpResponse);
        if (!Objects.equals(resObj.getBoolean("success"), true)) {
            LOGGER.recordOutput(new LogModel("processQI").message("qpResponse result is not success"));
            return;
        }

        JSONArray resultArray = resObj.getJSONArray("result");
        if (CollectionUtils.isEmpty(resultArray)) {
            LOGGER.recordOutput(new LogModel("processQI").message("qpResponse result is empty"));
            return;
        }

        JSONObject result = (JSONObject) resultArray.get(0);
        JSONArray subQueryList = result.getJSONArray("subQueryList");
        if (CollectionUtils.isEmpty(subQueryList)) {
            LOGGER.recordOutput(new LogModel("processQI").message("qpResponse subQueryList is empty"));
            return;
        }

        aiJourneySceneModel.setQpQueryList(buildSubQueryList(subQueryList));

        JSONObject queryEntityInfo = result.getJSONObject("queryEntityInfo");
        aiJourneySceneModel.setEntities(queryEntityInfo);

        LOGGER.recordOutput(new LogModel("processQI").message("after processQI")
                .response(JSONUtil.toJSONString(aiJourneySceneModel)));
    }

    private static List<AiJourneySceneModel.QpQuery> buildSubQueryList(JSONArray subQueryList) {
        List<AiJourneySceneModel.QpQuery> qpQueryList = new ArrayList<>();
        // 解析QP query
        for (int i = 0; i < subQueryList.size(); i++) {
            JSONObject subQuery = subQueryList.getJSONObject(i);
            if (MapUtils.isEmpty(subQuery)) {
                continue;
            }
            JSONObject subQueryType = subQuery.getJSONObject("subQueryType");
            if (MapUtils.isEmpty(subQueryType)) {
                continue;
            }
            JSONArray queryTypesCodes = subQueryType.getJSONArray("queryTypesCodes");
            String queryTypeCode = queryTypesCodes.getString(0);
            LOGGER.recordOutput(new LogModel("queryTypeCode").message(queryTypeCode));
            String finalSubQuery = subQuery.getString("finalSubQuery");
            AiJourneySceneModel.QpQuery qpQuery = AiJourneySceneModel.QpQuery.builder().queryType(queryTypeCode).query(finalSubQuery).build();
            qpQueryList.add(qpQuery);
        }
        return qpQueryList;
    }

    /**
     * 写入空消息
     */
    private void writeEmptyMessage(MtopStream mtopStream, ChatContext chatContext) {
        StreamMessageResult result = new StreamMessageResult(chatContext.getSystemMessageId(), chatContext.getUserMessageId(), StringUtils.EMPTY, StringUtils.EMPTY, AiJourneyPlanMessageStatusEnum.NORMAL.getStatusCode(), 0);
        result.setSessionId(chatContext.getSessionId());
        mtopStream.write(JSON.toJSONString(result));
    }

    /**
     * 用户输入风控拦截，同步逻辑
     */
    private boolean passQueryCsi(MtopStream mtopStream, ChatContext chatContext) {
        if (!CSI_QUERY_OPEN) {
            return true;
        }
        String chat = Optional.ofNullable(chatContext.getSummarizedChat()).orElse(chatContext.getRequest().getChat());
        Long userId = chatContext.getRequest().getUserId();
        CsiResult csiResult = csiClient.passQueryCsiCheck(chatContext.getUserMessageId(), chatContext.getSessionId(), chat, userId);
        if (Objects.isNull(csiResult)) {
            return true;
        }
        Boolean pass = CsiResultEnum.isPass(csiResult.getResult());

        String message = StringUtils.EMPTY;
        String status = AiJourneyPlanMessageStatusEnum.BLOCK.getStatusCode();
        CsiGenerateContent generateContent = csiResult.getGenerateContent();
        //有值就认为是被风控
        if (Objects.nonNull(generateContent) && StringUtils.equals(generateContent.getActionSuggestion(), STANDARD_ANSWER)
                && StringUtils.isNotBlank(generateContent.getStandardAnswer())) {
            message = generateContent.getStandardAnswer();
            status = AiJourneyPlanMessageStatusEnum.NORMAL.getStatusCode();
        }

        //如果发现result=1，或generateContent.actionSuggestion=standardAnswer，返回standardAnswer的内容
        if (!pass || StringUtils.isNotBlank(message) || CSI_QUERY_MOCK_USER.contains(chatContext.getRequest().getUserId())) {
            StreamMessageResult result = new StreamMessageResult(chatContext.getSystemMessageId(), chatContext.getUserMessageId(), StringUtils.EMPTY, message, status, 0);
            mtopStream.writeAndEnd(JSON.toJSONString(result));
            LOGGER.recordOutput(new LogModel("JourneyPlanAiCsiHelper")
                    .request(chatContext.getUserMessageId())
                    .userId(userId)
                    .response(chat)
                    .message(status));
            return false;
        } else {
            return true;
        }
    }

    /**
     * 对话续接接口，调用接口前要确保currentMessageId是一定不为空的
     *
     * @param request
     */
    @Override
    @RunLog
    public StreamMessageResult continueChat(JourneyPlanAiChatRequest request) {
        final MtopStream mtopStream = MtopContext.startStream();
        try {
            journeyPlanAiAssistantContentHelper.outputCacheContent(request.getSystemMessageId(), request.getSeq(), mtopStream);
            mtopStream.writeAndEnd(JSON.toJSONString(StreamMessageResult.heartBeat()));
        } catch (Throwable throwable) {
            LOGGER.recordNormalException(new LogModel("continueChat")
                    .response(JSON.toJSONString(request))
                    .e(throwable)
                    .message("对话续接接口异常"));
            mtopStream.endExceptionally(throwable);
        }
        return StreamMessageResult.succeed();
    }

    /**
     * 停止回答
     *
     * @param request
     */
    @Override
    @RunLog
    public Boolean stopChat(JourneyPlanAiChatRequest request) {
        // 如果没有配置消息id，就查询用户的最新的消息id
        String currentSystemMessageId = request.getCurrentMessageId();
        if (StringUtils.isBlank(currentSystemMessageId)) {
            currentSystemMessageId = this.getSystemMessageIdWithRetry(request.getUserId());
        }
        // 如果没有找到消息id，返回false
        if (StringUtils.isAnyBlank(currentSystemMessageId)) {
            LOGGER.recordOutput(new LogModel("stopChat")
                    .response(currentSystemMessageId)
                    .message("仍然未找到当前systemMessageId，停止失败，返回false"));
            return false;
        }

        // 现根据缓存获取用户消息id；再存stop缓存；再调fai-stop接口
        // 更新状态
        // 优先使用指定的状态
        AiJourneyPlanStatusEnum status = AiJourneyPlanStatusEnum.codeOf(request.getStatus());
        String key = TairKeyFactory.getSysMessageIdKey(currentSystemMessageId);
        mdbTairCommonHelper.putValue(key, currentSystemMessageId, STOP_MESSAGE_ID_TTL_S);
        AiJourneyPlanStatusEnum updateStatus = Objects.isNull(status) ? AiJourneyPlanStatusEnum.STOP : status;
        boolean result = messageStatusHelper.updateStatus(currentSystemMessageId, updateStatus);
        LOGGER.recordOutput(new LogModel("stopChat")
                .request("系统消息ID为" + currentSystemMessageId)
                .message("已更新消息状态为" + updateStatus));
        //3、通知结束对话
        noticeStop(request.getUserId(), currentSystemMessageId, request.getStatus());
        return result;
    }

    /**
     * 通知fai结束对话
     *
     * @param userId
     * @param systemMessageId
     */
    private void noticeStop(Long userId, String systemMessageId, String status) {
        try {
            if (StringUtils.isBlank(systemMessageId)) {
                LOGGER.recordOutput(new LogModel("noticeStop")
                        .userId(userId)
                        .code(LOG_LEVEL_TYPE)
                        .message("系统消息id为空，不通知fai停止对话"));
                return;
            }
            String sessionId = journeyPlanAiAssistantSessionHelper.currentSession(userId);
            LOGGER.recordOutput(new LogModel("noticeStop")
                    .userId(userId)
                    .message("根据userId获取当前的sessionId为" + sessionId));

            AiaskStopRequest stopRequest = new AiaskStopRequest();
            StopCodeEnum faiStatus = dealFaiStatus(status);
            //要传用户的，和前面查询的时候传一样【buildAiaskChatRequest】方法
            stopRequest.setMsgId(systemMessageId);
            stopRequest.setSid(sessionId);
            stopRequest.setReason(faiStatus);
            ChatbotClientResponse<String> stop = aiaskMessagingClientService.stop(stopRequest);
            LOGGER.recordOutput(new LogModel("noticeStop")
                    .userId(userId)
                    .code(LOG_LEVEL_TYPE)
                    .request("sessionId=" + sessionId + ", systemMessageId=" + systemMessageId)
                    .message("已通知fai停止对话")
                    .response("调用结果:" + JSONUtil.toJSONStringForLog(stop)));
        } catch (Exception e) {
            LOGGER.recordNormalException(new LogModel("noticeStop")
                    .code(LOG_LEVEL_TYPE)
                    .response(systemMessageId)
                    .message("通知fai停止对话异常")
                    .userId(userId)
                    .e(e));
        }
    }

    private StopCodeEnum dealFaiStatus(String status) {
        if(StringUtils.isBlank(status)){
            return StopCodeEnum.UNACCEPTABLE;
        }
        if(MapUtils.isEmpty(Switcher.CALL_FAI_STATUS)){
            return StopCodeEnum.UNACCEPTABLE;
        }

        String faiStatus = Switcher.CALL_FAI_STATUS.get(status);
        if(StringUtils.isBlank(faiStatus)){
            return StopCodeEnum.UNACCEPTABLE;
        }
        switch (faiStatus){
            case "FORBIDDEN":
                return StopCodeEnum.FORBIDDEN;
            case "ERROR":
                return StopCodeEnum.ERROR;
            case "TIMEOUT":
                return StopCodeEnum.TIMEOUT;
            case "UNACCEPTABLE":
                return StopCodeEnum.UNACCEPTABLE;
            default:
                return StopCodeEnum.UNACCEPTABLE;
        }
    }

    private String getSystemMessageIdWithRetry(Long userId) {
        String currentSystemMessageId = null;
        // 重试逻辑：最多尝试3次，每次间隔500ms
        for (int i = 0; i < GET_SYSMESSAGEID_RETRY_NUM; i++) {
            currentSystemMessageId = journeyPlanAiAssistantHelper.currentNotStoppedSystemMessageId(userId);
            if (StringUtils.isNotBlank(currentSystemMessageId)) {
                // 获取到数据，跳出循环
                return currentSystemMessageId;
            }

            try {
                Thread.sleep(GET_SYSMESSAGEID_RETRY_SLEEP_TIME);
                LOGGER.recordOutput(new LogModel("stopChat")
                        .response("retry-" + (i + 1))
                        .message("未获取到systemMessageId，第" + (i + 1) + "次重试"));
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                LOGGER.recordNormalException(new LogModel("stopChat")
                        .e(e)
                        .message("重试等待被中断"));
            }
        }
        return null;
    }

    /**
     * 返回总结内容，到这一步，总结内容已经有了，要做的就是从缓存中取出来
     *
     * @param request
     */
    @Override
    @RunLog
    public JourneyPlanMessageSummaryResult chatSummary(JourneyPlanAiChatRequest request) {
        return journeyPlanAiAssistantContentHelper.getSummaryResult(request.getCurrentMessageId());
    }

    /**
     * 删除会话id，同时清空历史记录
     *
     * @param request
     */
    @Override
    @RunLog
    public Boolean resetChat(JourneyPlanAiChatRequest request) {
        //journeyPlanAiAssistantFrt.stopChat(request);
        return journeyPlanAiAssistantSessionHelper.clearSession(request.getUserId());
    }

    /**
     * 返回历史记录
     *
     * @param request
     */
    @Override
    @RunLog
    public JourneyPlanAiAssistantHistoryWithCurrentChatResult history(JourneyPlanAiHistoryRequest request) {
        // 生成请求参数
        JourneyPlanAiAssistantHistoryMessageRequest journeyPlanAiAssistantHistoryMessageRequest = new JourneyPlanAiAssistantHistoryMessageRequest();
        journeyPlanAiAssistantHistoryMessageRequest.setUserId(String.valueOf(request.getUserId()));
        journeyPlanAiAssistantHistoryMessageRequest.setPageSize(request.getPageSize());
        journeyPlanAiAssistantHistoryMessageRequest.setStartKey(request.getStartKey());
        journeyPlanAiAssistantHistoryMessageRequest.setLeftTime(CIDateUtil.parseDateNewFormat(request.getLeftTime()));
        journeyPlanAiAssistantHistoryMessageRequest.setRightTime(CIDateUtil.parseDateNewFormat(request.getRightTime()));
        journeyPlanAiAssistantHistoryMessageRequest.setSessionId(request.getSessionId());
        // 新版app查询默认会带上sessionId
        if (JourPlanUtil.isNewVersion(request.getVersion(), SESSION_LIST_MIN_VERSION) && StringUtils.isBlank(request.getSessionId())) {
            // todo 日志记录
            journeyPlanAiAssistantHistoryMessageRequest.setSessionId(journeyPlanAiAssistantSessionHelper.currentSession(request.getUserId()));
        }
        // 老版本app，没有多会话窗口，就不带sessionId查询了
        if (!JourPlanUtil.isNewVersion(request.getVersion(), SESSION_LIST_MIN_VERSION)) {
            journeyPlanAiAssistantHistoryMessageRequest.setSessionId(null);
        }
        // 查询数据
        JourneyPlanAiAssistantHistoryResult result = journeyAssistantDataOperateFacade.queryHistoryMessage(journeyPlanAiAssistantHistoryMessageRequest);
        JourneyPlanAiAssistantHistoryWithCurrentChatResult historyWithCurrentChatResult = new JourneyPlanAiAssistantHistoryWithCurrentChatResult(result);
        // 查询是否有在回答的问题
        Pair<String, String> messageIdPair = journeyPlanAiAssistantHelper.currentNotStoppedMessageId(request.getUserId(), request.getSessionId());
        if (Objects.nonNull(messageIdPair)) {
            // 有问题的话 立刻结束回答
            // 获取指令
            String chat = journeyPlanAiAssistantHelper.getChat(messageIdPair.getLeft());
            if (StringUtils.isNotBlank(chat)) {
                historyWithCurrentChatResult.setCurrentChat(chat);
                historyWithCurrentChatResult.setCurrentSystemMessageId(messageIdPair.getLeft());
                historyWithCurrentChatResult.setCurrentUserMessageId(messageIdPair.getRight());
            }
        }

        // 判断是否在行中，行中的给出bottomTab
        journeyInTransitServiceHelper.buildHistoryBottomTab(request, historyWithCurrentChatResult);
        historyWithCurrentChatResult.setSessionId(request.getSessionId());
        return historyWithCurrentChatResult;
    }


    /**
     * 评价消息
     *
     * @param request
     */
    @Override
    @RunLog
    public Boolean wrateMessage(JourneyPlanAiChatRequest request) {
        // 生成请求参数
        JourneyPlanAiAssistantWrateMessageRequest journeyPlanAiAssistantWrateMessageRequest = new JourneyPlanAiAssistantWrateMessageRequest();
        journeyPlanAiAssistantWrateMessageRequest.setUserId(String.valueOf(request.getUserId()));
        journeyPlanAiAssistantWrateMessageRequest.setMessageId(request.getCurrentMessageId());
        journeyPlanAiAssistantWrateMessageRequest.setWrate(request.getAction());
        // 调op接口
        return journeyAssistantDataOperateFacade.wrateMessage(journeyPlanAiAssistantWrateMessageRequest);
    }

    /**
     * 查看分享消息
     *
     * @param request
     */
    @Override
    @RunLog(throwException = true)
    public JourneyPlanAiAssistantHistoryResult viewShareMessage(JourneyPlanAiChatViewShareRequest request) {
        // 基于token获取分享的消息列表
        List<String> messageIdList = journeyPlanAiAssistantShareHelper.getMessageIdList(request.getToken());
        if (CollectionUtils.isEmpty(messageIdList)) {
            return JourneyPlanAiAssistantHistoryResult.empty();
        }
        JourneyPlanAiAssistantHistoryMessageRequest journeyPlanAiAssistantHistoryMessageRequest = new JourneyPlanAiAssistantHistoryMessageRequest();
        journeyPlanAiAssistantHistoryMessageRequest.setMessageIdList(messageIdList);
        JourneyPlanAiAssistantHistoryResult journeyPlanAiAssistantHistoryResult = journeyAssistantDataOperateFacade.querySpecifiedMessage(journeyPlanAiAssistantHistoryMessageRequest);
        //处理用户的头像
        if (CollectionUtils.isNotEmpty(journeyPlanAiAssistantHistoryResult.getMessageList())) {
            List<JourneyPlanAiAssistantHistoryResult.MessageInfo> messageList = journeyPlanAiAssistantHistoryResult.getMessageList();
            String userId = messageList.get(0).getUserId();
            String avatar = memberServiceHelper.getAvatarByUserIdV2(userId);
            journeyPlanAiAssistantHistoryResult.setUserAvatar(avatar==null?FLIGGY_DEFAULT_AVATAR:avatar);
        }
        return journeyPlanAiAssistantHistoryResult;

    }

    /**
     * 分享消息
     *
     * @param request
     */
    @Override
    @RunLog
    public JourneyPlanShareMessageResult shareMessage(JourneyPlanAiChatShareRequest request) {
        // 校验消息
        if (StringUtils.isBlank(request.getMessageIdList())) {
            throw new TripJourneyException("消息id为空");
        }
        List<String> messageIdList = Arrays.stream(request.getMessageIdList().split(MESSAGE_ID_LIST_SPLIT)).collect(Collectors.toList());
        JourneyPlanAiAssistantHistoryMessageRequest journeyPlanAiAssistantHistoryMessageRequest = new JourneyPlanAiAssistantHistoryMessageRequest();
        journeyPlanAiAssistantHistoryMessageRequest.setMessageIdList(messageIdList);
        JourneyPlanAiAssistantHistoryResult result = journeyAssistantDataOperateFacade.querySpecifiedMessage(journeyPlanAiAssistantHistoryMessageRequest);
        if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getMessageList())) {
            throw new TripJourneyException("消息id不存在");
        }
        //消息分享，进行打标处理
        journeyAssistantDataOperateFacade.updateExtraByShare(messageIdList);
        // 生成分享token
        String token = journeyPlanAiAssistantShareHelper.genShareToken(request.getMessageIdList());
        return createShareMessageResult(result, token);
    }

    /**
     * 创建分享消息结果
     */
    private JourneyPlanShareMessageResult createShareMessageResult(JourneyPlanAiAssistantHistoryResult result, String token) {
        JourneyPlanShareMessageResult shareMessageResult = new JourneyPlanShareMessageResult();
        shareMessageResult.setToken(token);

        List<JourneyPlanAiAssistantHistoryResult.MessageInfo> messageList = result.getMessageList();
        JourneyPlanAiAssistantHistoryResult.MessageInfo userMessage = getLastUserMessage(messageList);
        shareMessageResult.setTitle(userMessage.getInfo());

        JourneyPlanAiAssistantHistoryResult.MessageInfo systemMessage = getLastSystemMessage(messageList);
        String truncatedInfo = truncateInfo(systemMessage.getInfo());
        shareMessageResult.setSubTitle(truncatedInfo);

        return shareMessageResult;
    }


    @Switch
    private static volatile int truncateSize = 50;

    /**
     * 截断信息
     */
    private String truncateInfo(String info) {
        if (info == null || info.isEmpty()) {
            return info;
        }

        int endIndex = info.indexOf("<");
        // 如果没有找到 '<'，则截取前50个字符
        if (endIndex == -1) {
            endIndex = info.length();
        }

        // 找到\n的索引
        int newlineIndex = info.indexOf("\n");
        // 如果找到了\n，更新endIndex为min(endIndex, newlineIndex)
        if (newlineIndex != -1) {
            endIndex = Math.min(endIndex, newlineIndex);
        }

        // 确保不超过 truncateSize
        endIndex = Math.min(endIndex, truncateSize);

        String substring = info.substring(0, endIndex);
        return substring.trim(); // 使用trim()去除可能存在的前后空格
    }




    /**
     * 获取最后一条用户消息
     */
    private JourneyPlanAiAssistantHistoryResult.MessageInfo getLastUserMessage(List<JourneyPlanAiAssistantHistoryResult.MessageInfo> messageList) {
        return messageList.stream()
                .filter(message -> JourneyPlanAiAssistantRoleEnum.USER.getCode().equals(message.getRole()))
                .reduce((first, second) -> second)
                .orElseGet(() -> {
                    JourneyPlanAiAssistantHistoryResult.MessageInfo messageInfo = new JourneyPlanAiAssistantHistoryResult.MessageInfo();
                    messageInfo.setInfo("和飞猪AI问一问的对话");
                    return messageInfo;
                });
    }

    /**
     * 获取最后一条系统消息
     */
    private JourneyPlanAiAssistantHistoryResult.MessageInfo getLastSystemMessage(List<JourneyPlanAiAssistantHistoryResult.MessageInfo> messageList) {
        return messageList.stream()
                .filter(message -> JourneyPlanAiAssistantRoleEnum.SYSTEM.getCode().equals(message.getRole()))
                .reduce((first, second) -> second)
                .orElseGet(() -> {
                    JourneyPlanAiAssistantHistoryResult.MessageInfo messageInfo = new JourneyPlanAiAssistantHistoryResult.MessageInfo();
                    messageInfo.setInfo("飞猪AI问一问，你的7X24小时AI团队，将灵光乍现转化为完美旅程。");
                    return messageInfo;
                });
    }

    /**
     * 根据消息id查看结构化线路
     *
     * @param request
     */
    @Override
    public JourneyPlanAiStructRouteResult structRoute(JourneyPlanAiStructRouteRequest request) {
        String messageId = request.getMessageId();
        String[] split = messageId.split(CommonString.CARD_ID_SPLIT);
        if (split.length == 2) {
            // 可以分隔，使用新版
            return getResultByCard(split);
        }
        //老版本根据id查询。
        return getResultByMessageId(messageId);
    }

    private JourneyPlanAiStructRouteResult getResultByMessageId(String messageId) {
        JourneyPlanAiStructRouteResult journeyPlanAiStructRouteResult = new JourneyPlanAiStructRouteResult();
        JourneyPlanAiAssistantHistoryMessageRequest journeyPlanAiAssistantHistoryMessageRequest
            = new JourneyPlanAiAssistantHistoryMessageRequest();
        journeyPlanAiAssistantHistoryMessageRequest.setMessageIdList(Lists.newArrayList(messageId));
        journeyPlanAiAssistantHistoryMessageRequest.setOnlyStructRoute(true);
        JourneyPlanAiAssistantHistoryResult result = journeyAssistantDataOperateFacade.querySpecifiedMessage(
            journeyPlanAiAssistantHistoryMessageRequest);
        if (Objects.nonNull(result) && CollectionUtils.isNotEmpty(result.getMessageList())) {
            journeyPlanAiStructRouteResult.setStructRoute(result.getMessageList().get(0).getStructRoute());
        }
        return journeyPlanAiStructRouteResult;
    }

    private JourneyPlanAiStructRouteResult getResultByCard(String[] split) {
        JourneyPlanAiStructRouteResult journeyPlanAiStructRouteResult = new JourneyPlanAiStructRouteResult();
        JourneyPlanAiAssistantHistoryMessageRequest journeyPlanAiAssistantHistoryMessageRequest
            = new JourneyPlanAiAssistantHistoryMessageRequest();
        String splitMessageId = split[0];
        String splitCardId = split[1];
        journeyPlanAiAssistantHistoryMessageRequest.setMessageIdList(Lists.newArrayList(splitMessageId));
        journeyPlanAiAssistantHistoryMessageRequest.setOnlyStructRoute(true);
        JourneyPlanAiAssistantHistoryResult historyMessage = journeyAssistantDataOperateFacade.querySpecifiedMessage(
            journeyPlanAiAssistantHistoryMessageRequest);
        List<JourneyPlanAiAssistantHistoryResult.MessageInfo> messageList = historyMessage.getMessageList();
        if (CollectionUtils.isEmpty(messageList)){
            LOGGER.recordOutput(new LogModel("getResultByCard")
                .message("没有根据messageId找到消息")
                .request("messageId: " + splitMessageId + "cardId:" + splitCardId).response(JSONUtil.toJSONStringForLog(historyMessage)));
            return journeyPlanAiStructRouteResult;
        }
        JourneyPlanAiAssistantHistoryResult.MessageInfo messageInfo = messageList.get(0);
        List<StreamMessageCardModel> components = messageInfo.getComponents();
        for (StreamMessageCardModel component : components) {
            if (!Objects.equals(splitCardId, component.getId())) {
                continue;
            }
            Object data = component.getData();
            if (data == null) {
                continue;
            }
            Map map = JSON.parseObject(JSON.toJSONString(data), Map.class);
            Object o = map.get(CommonString.STRUCTROUTE);
            AiJourneyMessageContentDTO.StructRoute structRoute = JSON.parseObject(JSON.toJSONString(o),
                AiJourneyMessageContentDTO.StructRoute.class);
            journeyPlanAiStructRouteResult.setStructRoute(structRoute);
            return journeyPlanAiStructRouteResult;
        }
        //没有找到对应的卡片，查询失败。
        LOGGER.recordOutput(new LogModel("getResultByCard")
            .message("没有找到对应的卡片数据")
            .request("messageId: " + splitMessageId + "cardId:" + splitCardId).response(JSONUtil.toJSONStringForLog(historyMessage)));
        return journeyPlanAiStructRouteResult;
    }

    @Override
    @RunLog
    public JourneyPlanQueryPoiInfoByFloatResult queryPoiInfoByFloat(JourneyPlanQueryPoiInfoByFloatRequest request) {
        if (request.getPoiId() == null) {
            return null;
        }
        if (request.getLongitude() == null || request.getLatitude() == null) {
            request.setLongitude(MtopContext.getLng());
            request.setLatitude(MtopContext.getLat());
        }
        if (StringUtils.equals(POI_TYPE, request.getPoiType())) {
            FliggyPoi fliggyPoi = poiReadServiceClient.getFliggyPoiByPoiIdLanguage(Long.valueOf(request.getPoiId()), request.getLanguage());
            return buildFloatPoiInfoByFliggyPoi(fliggyPoi, request);
        } else if (StringUtils.equals(GAO_DE_TYPE, request.getPoiType())) {
            List<Map<String, Object>> maps = aMapService.queryGaoDeSearchByIdWithVersion(request.getPoiId(), true, DefaultShowFields);
            if (CollectionUtils.isNotEmpty(maps)) {
                return buildFloatPoiInfoByAmapPoi(maps.get(0), request);
            }
        } else if (StringUtils.equals(GOOGLE_TYPE, request.getPoiType())) {
            JSONObject googleMapPoi = googleMapService.getGoogleMapPoiById(request.getPoiId());
            return buildFloatPoiInfoByGooglePoi(googleMapPoi, request);
        }
        return null;
    }

    @Override
    public Boolean hasHistory(JourneyPlanAiChatRequest request) {
        return journeyAssistantDataOperateFacade.hasHistoryMessage(request.getUserId(), request.getSessionId());
    }

    /**
     * 是否在白名单
     */
    @Override
    public Boolean inWhiteList(JourneyPlanAiAssistantWhiteUserRequest request) {
        if (Objects.isNull(request.getUserId())) {
            return false;
        }
        return journeyPlanAiAssistantWhiteHelper.isInDynamicWhiteList(request.getUserId());
    }

    /**
     * 添加白名单
     */
    @Override
    public Boolean addWhiteList(JourneyPlanAiAssistantWhiteUserRequest request) {
        if (CollectionUtils.isEmpty(request.getUserIds())) {
            return false;
        }

        for (Long userId : request.getUserIds()) {
            journeyPlanAiAssistantWhiteHelper.addDynamicWhiteList(userId);
        }
        return true;
    }

    /**
     * 手绘地图回流逻辑
     */
    @Override
    public Boolean mapUrlCallBack(JourneyPlanAiUrlCallbackRequest request) {
        // 写缓存
        journeyPlanAiAssistantMessageHelper.updateMapUrl2Tai(request.getMessageId(), request.getCardId(), request.getUrl());
        // 注册延时任务
        journeyPlanAiAssistantMessageHelper.registerMapUrlDelayTask(request.getMessageId(), request.getCardId(), request.getUrl());
        return true;
    }

    @Override
    public List<JourneyPlanAiAssistantHistoryResult.MessageInfo> getUserRecentMessageBySource(QueryRecentMessageRequest request) {
        return journeyAssistantDataOperateFacade.getUserRecentMessageBySource(request);
    }

    private JourneyPlanQueryPoiInfoByFloatResult buildFloatPoiInfoByAmapPoi(Map<String, Object> amapPoi, JourneyPlanQueryPoiInfoByFloatRequest request) {
        if (MapUtils.isEmpty(amapPoi)) {
            return null;
        }
        JourneyPlanQueryPoiInfoByFloatResult res = new JourneyPlanQueryPoiInfoByFloatResult();
        res.setPoiId(request.getPoiId());
        res.setJumpUrl(String.format(AMAP_POI_DETAIL_PAGE_URL, res.getPoiId()));
        res.setPoiType(GAO_DE_TYPE);
        res.setName(MapUtils.getString(amapPoi, "name"));
        res.setAddress(MapUtils.getString(amapPoi, "address"));
        String type = MapUtils.getString(amapPoi, "type");
        res.setGeoType(GeoTypeEnum.GCJ_02.name());
        if (StringUtils.isNotBlank(type)) {
            res.setType(StringUtils.split(type, ";")[0]);
        }
        Object business = amapPoi.get("business");
        if (business != null) {
            JSONObject businessInfo = JSON.parseObject(JSON.toJSONString(business));
            String tel = businessInfo.getString("tel");
            if (StringUtils.isNotBlank(tel)) {
                res.setContactNumber(Arrays.stream(StringUtils.split(tel, ";")).collect(Collectors.toList()));
            }
            Double cost = businessInfo.getDouble("cost");
            if (cost != null && cost.equals(0.0)) {
                res.setFreeInfo("不收费");
            }
            res.setBusinessHours(businessInfo.getString("opentime_week"));
        }
        Object location = amapPoi.get("location");
        if (location != null) {
            String[] split = StringUtils.split(location.toString(), ",");
            if (split != null && split.length == 2 && NumberUtils.isCreatable(split[0]) && NumberUtils.isCreatable(split[1])) {
                res.setLongitude(split[0]);
                res.setLatitude(split[1]);
                res.setStraightDistance(dealDistance(request, split[0], split[1]));
            }
        }
        List<String> photoList = buildAmapPoiPhotos(amapPoi.get("photos"));
        if (CollectionUtils.isNotEmpty(photoList)) {
            res.setPhotoTotal(photoList.size());
            res.setImgList(photoList);
        }
        // 查询行政区划是否为境外
        res.setGeoType(GeoTypeEnum.GCJ_02.name());
        Object adcode = amapPoi.get("adcode");
        if (adcode != null) {
            TrdiDivisionExtendVO divisionExtendById = trdiDivisionReadServiceClient.getDivisionExtendById(Long.valueOf(adcode.toString()));
            Object pcode = amapPoi.get("pcode");
            // 国内adcode和行政区划基本一致，若查不到或查到为国外或省份为台湾，则返回火星坐标系
            if (divisionExtendById == null || !StringUtils.equals(divisionExtendById.getCountryName(), CHINA_COUNTRY_NAME) || (pcode != null && USE_WSG84_CHINA_DIVISION_ID_LIST.contains(Long.valueOf(pcode.toString())))) {
                res.setGeoType(GeoTypeEnum.WGS84.name());
            }
        }
        return res;
    }

    private JourneyPlanQueryPoiInfoByFloatResult buildFloatPoiInfoByGooglePoi(JSONObject googlePoi, JourneyPlanQueryPoiInfoByFloatRequest request) {
        if (MapUtils.isEmpty(googlePoi)) {
            return null;
        }
        JourneyPlanQueryPoiInfoByFloatResult res = new JourneyPlanQueryPoiInfoByFloatResult();
        res.setPoiId(request.getPoiId());
//        res.setJumpUrl(String.format(AMAP_POI_DETAIL_PAGE_URL, res.getPoiId()));
        res.setPoiType(GOOGLE_TYPE);
        if (googlePoi.containsKey("displayName")) {
            res.setName(googlePoi.getJSONObject("displayName").getString("text"));
        }
        res.setAddress(MapUtils.getString(googlePoi, "formattedAddress"));
        if (googlePoi.containsKey("internationalPhoneNumber")) {
            res.setContactNumber(Lists.newArrayList(googlePoi.getString("internationalPhoneNumber")));
        }
        if (googlePoi.containsKey("primaryTypeDisplayName")) {
            String type = MapUtils.getString(googlePoi.getJSONObject("primaryTypeDisplayName"), "text");
            res.setType(type);
        }
        res.setGeoType(GeoTypeEnum.WGS84.name());
        JSONObject location = googlePoi.getJSONObject("location");
        if (location != null) {
            res.setLongitude(location.getString("longitude"));
            res.setLatitude(location.getString("latitude"));
        }
        JSONObject regularOpeningHours = googlePoi.getJSONObject("regularOpeningHours");
        if (regularOpeningHours != null) {
            JSONArray weekdayDescriptions = regularOpeningHours.getJSONArray("weekdayDescriptions");
            if (CollectionUtils.isNotEmpty(weekdayDescriptions)) {
                res.setBusinessHours(StringUtils.join(weekdayDescriptions, "\n"));
            }
        }
        res.setPhotoTotal(0);
        return res;
    }

    private List<String> buildAmapPoiPhotos(Object photosObj) {
        if (photosObj == null) {
            return Lists.newArrayList();
        }
        List<String> res = Lists.newArrayList();
        try {
            JSONArray photos = JSONArray.parseArray(JSON.toJSONString(photosObj));
            for (int i = 0; i < photos.size(); i++) {
                res.add(photos.getJSONObject(i).getString("url"));
            }
        } catch (Exception ignore) {
        }
        return res;
    }

    private JourneyPlanQueryPoiInfoByFloatResult buildFloatPoiInfoByFliggyPoi(FliggyPoi fliggyPoi, JourneyPlanQueryPoiInfoByFloatRequest request) {
        if (Objects.isNull(fliggyPoi) || Objects.isNull(fliggyPoi.getPoiBase())) {
            return null;
        }
        JourneyPlanQueryPoiInfoByFloatResult res = new JourneyPlanQueryPoiInfoByFloatResult();
        res.setPoiId(String.valueOf(fliggyPoi.getPoiBase().getPoiId()));
        res.setPoiType(POI_TYPE);
        res.setAddress(fliggyPoi.getPoiBase().getAddress());
        res.setName(fliggyPoi.getPoiBase().getName());
        if (fliggyPoi.getPoiBase().getIsAbroad() == 1) {
            res.setAbroadName(fliggyPoi.getPoiBase().getNameEn());
        }
        res.setJumpUrl(String.format(POI_DETAIL_URL, res.getPoiId()));
        if (fliggyPoi.getPoiFluctuantProperties() != null && BooleanUtils.isTrue(fliggyPoi.getPoiFluctuantProperties().getHasPoiShelf())) {
            res.setBookTicketsUrl(res.getJumpUrl() + "&toTicketShelf=true");
        }
        res.setPhotoUrl(String.format(POI_PHOTO_PAGE_URL, res.getPoiId()));
        res.setType(FliggyPoiCategoryEnum.codeOf(fliggyPoi.getPoiBase().getFirstCategory()).getName());
        res.setLongitude(String.valueOf(fliggyPoi.getPoiBase().getLongitude()));
        res.setLatitude(String.valueOf(fliggyPoi.getPoiBase().getLatitude()));
        if (Objects.equals(fliggyPoi.getPoiBase().getIsAbroad(), 1) && Objects.nonNull(fliggyPoi.getPoiDivision()) && !USE_GCJ02_ABROAD_DIVISION_ID_LIST.contains(fliggyPoi.getPoiDivision().getProvId())) {
            res.setGeoType(GeoTypeEnum.WGS84.name());
        } else {
            res.setGeoType(GeoTypeEnum.GCJ_02.name());
        }
        // 查询图片
        TripPoiResult<List<FliggyPoiImage>> poiImages = poiReadServiceClient.getPoiImages(fliggyPoi.getPoiBase().getPoiId(), 1, MAX_POI_PHOTO_NUM);
        res.setPhotoTotal(poiImages.getTotalSize());
        res.setImgList(buildPoiImageList(poiImages.getData(), fliggyPoi.getPoiBase().getMainPic(), MAX_POI_PHOTO_NUM));
        String telephone = fliggyPoi.getPoiBase().getTelephone();
        if (StringUtils.isNotBlank(telephone)) {
            res.setContactNumber(Arrays.stream(StringUtils.split(telephone, ",")).collect(Collectors.toList()));
        }

        res.setStraightDistance(dealDistance(request, String.valueOf(fliggyPoi.getPoiBase().getLongitude()), String.valueOf(fliggyPoi.getPoiBase().getLatitude())));

        if (CollectionUtils.isNotEmpty(fliggyPoi.getCatProperties())) {
            List<PoiCatProperty> catProperties = fliggyPoi.getCatProperties();
            for (PoiCatProperty catProperty : catProperties) {
                if (Objects.equals(catProperty.getPropertyId(), 2L)) {
                    res.setBusinessHours(catProperty.getPropertyValue());
                }
                if (Objects.equals(catProperty.getPropertyId(), 42L)) {
                    res.setFreeInfo(getFreeFromPoiCatProperty(catProperty));
                }
            }
        }
        // 多语言场景
        if (CollectionUtils.isNotEmpty(fliggyPoi.getPoiI18nInfoList())) {
            PoiI18nInfo poiI18nInfo = fliggyPoi.getPoiI18nInfoList().get(0);
            if (StringUtils.isNotBlank(poiI18nInfo.getName())) {
                res.setName(poiI18nInfo.getName());
            }
            res.setType(FliggyPoiCategoryEnum.codeOf(fliggyPoi.getPoiBase().getFirstCategory()).getNameEn());
            if (StringUtils.isNotBlank(poiI18nInfo.getAddress())) {
                res.setAddress(poiI18nInfo.getAddress());
            }
            if (StringUtils.isNotBlank(res.getFreeInfo())) {
                if ("不收费".equals(res.getFreeInfo())) {
                    res.setFreeInfo("free");
                }
            }
            if (StringUtils.isNotBlank(poiI18nInfo.getCatProperties())) {
                try {
                    Map<Long, String> catMap = JSON.parseObject(poiI18nInfo.getCatProperties(), new TypeReference<Map<Long, String>>() {
                    });
                    if (catMap.containsKey(2L)) {
                        res.setBusinessHours(catMap.get(2L));
                    }
                } catch (Throwable t) {
                    LOGGER.recordNormalException(new LogModel("buildFloatPoiInfoByFliggyPoi")
                            .e(t)
                            .request(JSONUtil.toJSONStringForLog(fliggyPoi))
                            .message("解析poi多语言类目信息异常"));
                }
            }
        }
        return res;
    }

    /**
     * 处理距离
     *
     * @param request
     * @param destLon
     * @param destLat
     * @return
     */
    private String dealDistance(JourneyPlanQueryPoiInfoByFloatRequest request, String destLon, String destLat) {
        if (!(NumberUtils.isCreatable(request.getLatitude()) && NumberUtils.isCreatable(request.getLongitude()))) {
            return null;
        }
        Double distance = GeoUtils.distanceKm(Double.valueOf(request.getLatitude()), Double.valueOf(request.getLongitude()),
                Double.valueOf(destLat), Double.valueOf(destLon));
        if (Objects.isNull(distance)) {
            return null;
        }
        if (distance < MAX_POI_DISTANCE_M_SHOW) {
            Double distanceM = GeoUtils.distance(Double.valueOf(request.getLatitude()), Double.valueOf(request.getLongitude()),
                    Double.valueOf(destLat), Double.valueOf(destLon));
            return String.format("直线距你%sm", distanceM.intValue());
        } else if (distance < MAX_POI_DISTANCE_SHOW) {
            return String.format("直线距你%.2fkm", distance);
        }
        return null;
    }

    private List<String> buildPoiImageList(List<FliggyPoiImage> poiImages, String mainPic, int subSize) {
        if (CollectionUtils.isEmpty(poiImages) && StringUtils.isBlank(mainPic)) {
            return Lists.newArrayList();
        }
        List<String> res = Lists.newArrayList();
        if (StringUtils.isNotBlank(mainPic) && !StringUtils.equals(mainPic, POI_DEFAULT_IMG)) {
            res.add(mainPic);
        }
        if (CollectionUtils.isNotEmpty(poiImages)) {
            res.addAll(poiImages.stream()
                    .map(FliggyPoiImage::getUrl)
                    .filter(url -> !Objects.equals(url, mainPic) && !StringUtils.equals(mainPic, POI_DEFAULT_IMG))
                    .collect(Collectors.toList()));
        }
        return res.subList(0, Math.min(res.size(), subSize));
    }

    private String getFreeFromPoiCatProperty(PoiCatProperty catProperty) {
        if (!Objects.equals(catProperty.getPropertyId(), 42L)) {
            return null;
        }
        try {
            JSONArray ticketInfos = JSONArray.parseArray(catProperty.getPropertyValue());
            if (CollectionUtils.isNotEmpty(ticketInfos)) {
                JSONObject jsonObject = ticketInfos.getJSONObject(0);
                return jsonObject.getString("pay");
            }
        } catch (Exception ignore) {
        }
        return null;
    }

    /**
     * 填充历史信息
     *
     * @param result
     */
    private void fillHistoryMessage(JourneyPlanAiInitRequest request, JourneyPlanAiInitResult result) {
        // 调op接口
        boolean hasHistory = journeyAssistantDataOperateFacade.hasHistoryMessage(request.getUserId(),
            null);
        result.setHasHistoryMessage(hasHistory);
    }

    @Override
    public void destroy() {
        // Spring 容器关闭时会调用这个方法
        processingRequests.forEach((messageId, chatContext) -> {
            // 对话结束后 同时对话计数器-1
            String chatInProgressCacheKey = TairKeyFactory.getUserChatInProgressKey(chatContext.getRequest().getUserId());
            ldbTairManager.decr(chatInProgressCacheKey, 1, 1, 60 * 60);
            LOGGER.recordOutput(new LogModel("destroy")
                    .condition("count decr")
                    .message("sessionId={0},sysMessageId={1}", chatContext.getSessionId(), chatContext.getSystemMessageId())
                    .userId(chatContext.getRequest().getUserId()));
            LOGGER.recordNormalException(new LogModel("streamChat_interrupted")
                    .condition("container_shutdown")
                    //.response(JSON.toJSONString(chatContext))
                    .message("对话接口执行被容器关闭中断 - systemMessageId:{}, userMessageId:{}",
                            chatContext.getSystemMessageId(),
                            chatContext.getUserMessageId()));
        });
    }

}
