package com.taobao.trip.jourprod.biz.frt.journeyplannew.model;

import java.io.Serializable;
import lombok.Data;

/**
 * 算法返回的poi
 */
@Data
public class AIJourneyPoiModel implements Serializable {

    private static final long serialVersionUID = 6520233778258526166L;

    private String pk;

    private String id;

    private String name;

    private String desc;

    /**
     * 底层提及文章数（类似路线库）
     */
    private String noteNumber;

    /**
     * 近30天底层xhs提及文章数
     */
    private String noteNumber30Day;

    /**
     * 底层文章互动量均值（点赞+评论+收藏）
     */
    private String interaction;

    /**
     * 近30天底层文章互动量均值（点赞+评论+收藏）
     */
    private String interaction30Day;

}

