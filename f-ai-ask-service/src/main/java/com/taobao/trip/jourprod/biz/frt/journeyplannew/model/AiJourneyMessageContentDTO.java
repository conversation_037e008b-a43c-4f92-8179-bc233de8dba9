package com.taobao.trip.jourprod.biz.frt.journeyplannew.model;

import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneyRecommendLineModel.Route;
import com.taobao.trip.jourprod.service.facade.model.response.fliggyhome.common.ActionVO;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult.StreamMessageCardModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Description 消息体结构
 * <AUTHOR>
 * @Date 2025/2/8
 **/
@Data
public class AiJourneyMessageContentDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 思考内容
     */
    private String thinking;

    /**
     * 文本消息内容
     */
    private String content;

    /**
     * 组件列表
     */
    private List<StreamMessageCardModel> components;

    /**
     * 关联卡片内容
     */
    private List<AiJourneyMessageContentDTO.CardInfo> relatedCard;

    /**
     * 关联链接
     */
    private List<AiJourneyMessageContentDTO.LinkInfo> relatedLink;

    /**
     * 关联问题
     */
    private List<String> relatedMessage;

    /**
     * 关联酒店
     */
    private List<AiJourneyMessageContentDTO.RelatedHotel> relatedHotel;

    /**
     * 关联交通卡
     */
    private AiJourneyMessageContentDTO.TrafficCard trafficCard;

    /**
     * 结构化路线和文本格式的内容互斥
     */
    private AiJourneyMessageContentDTO.StructRoute structRoute;

    /**
     * 更多诉求
     */
    private String more;

    /**
     * 灵感词
     */
    private List<String> inspirations;

    /**
     * 思考链数据
     */
    private List<Map<String, Object>> thinkingThought;

    /**
     * 关联链接
     */
    @Data
    public static class LinkInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        private String title;

        private String url;

        private String type;

    }

    /**
     * 结构化路线，和content thinking互斥，存在的意义是为了能让前端方便展示结构
     */
    @Data
    public static class StructRoute implements Serializable {

        private static final long serialVersionUID = 1L;

        private String thinking;

        private String title;

        private String guide;

        private List<Route> routeList;

        private String reason;

        private String tripTips;

    }

    @Data
    public static class RouteDay implements Serializable {

        private static final long serialVersionUID = 1L;

        private String title;

        private List<AiJourneyMessageContentDTO.RouteItem> route;

    }

    @Data
    public static class RouteItem implements Serializable {

        private static final long serialVersionUID = 1L;

        private String content;

        private List<AiJourneyMessageContentDTO.POI> poiList;

    }

    @Data
    public static class POI implements Serializable {

        private static final long serialVersionUID = 1L;

        private String poiName;

        private String jumpUrl;

    }

    /**
     * 关联卡片信息
     */
    @Data
    public static class CardInfo implements Serializable {

        private static final long serialVersionUID = -5766876893210982812L;

        private String title;

        private String subTitle;

        private String tag;

        private ActionVO button;

        private List<AiJourneyMessageContentDTO.PictureInfo> poiInfoList;

    }

    /**
     * 图片展示信息
     */
    @Data
    public static class PictureInfo implements Serializable {

        private static final long serialVersionUID = -5766876893210982812L;

        private String url;

        private String title;

        private String picture;

    }

    /**
     * 航班卡片
     */
    @Data
    public static class TrafficCard implements Serializable {
        /**
         * 例如：杭州-北京的机票
         */
        private String title;

        /**
         * icon地址
         */
        private String iconUrl;

        /**
         * 查看更多url
         */
        private String seeMoreUrl;

        /**
         * 类型
         */
        private String type;

        /**
         * 具体的航班信息
         */
        private List<AiJourneyMessageContentDTO.Traffic> trafficList;
    }

    /**
     * 航班信息
     */
    @Data
    public static class Traffic implements Serializable {
        /**
         * 航班名称
         */
        private String name;

        /**
         * icon图标
         */
        private String iconUrl;

        /**
         * 价格
         */
        private String priceStr;

        /**
         * 出发地名称
         */
        private String originName;

        /**
         * 目的地名称
         */
        private String destName;

        /**
         * 出发地时间
         */
        private String originTime;

        /**
         * 到目的地时间
         */
        private String destTime;

        /**
         * 耗时总计
         */
        private String allTime;

        /**
         * 跳转地址
         */
        private String jumpUrl;
    }

    /**
     * 提及酒店
     */
    @Data
    public static class RelatedHotel implements Serializable {
        /**
         * 酒店名称
         */
        private String name;

        /**
         * 酒店主图链接
         */
        private String mainPicUrl;

        /**
         * 价格
         */
        private String priceStr;

        /**
         * 价格前缀，¥
         */
        private String priceStrPrefix;

        /**
         * 价格后缀，起
         */
        private String priceStrSuffix;

        /**
         * 评价数量
         */
        private String rateCountStr;

        /**
         * 跳转链接
         */
        private String jumpLink;

    }

}
