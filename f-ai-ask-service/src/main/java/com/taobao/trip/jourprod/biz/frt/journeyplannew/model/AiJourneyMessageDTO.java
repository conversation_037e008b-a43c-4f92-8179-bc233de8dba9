package com.taobao.trip.jourprod.biz.frt.journeyplannew.model;

import com.fliggy.aiworks.client.aiask.request.AiaskChatRequest;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 问一问聊天消息
 * <AUTHOR>
 * @Date 2025/2/8
 **/
@Data
public class AiJourneyMessageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Database Column Remarks:
     *   消息id
     *
     *
     * @mbg.generated
     */
    private String messageId;

    /**
     * Database Column Remarks:
     *   userId
     *
     *
     * @mbg.generated
     */
    private String userId;

    /**
     * Database Column Remarks:
     *   设备唯一识别id
     *
     *
     * @mbg.generated
     */
    private String utdid;

    /**
     * Database Column Remarks:
     *   渠道id
     *
     *
     * @mbg.generated
     */
    private String ttid;

    /**
     * Database Column Remarks:
     *   会话id
     *
     *
     * @mbg.generated
     */
    private String sessionId;

    /**
     * Database Column Remarks:
     *   消息发送角色
     *
     *
     * @mbg.generated
     */
    private String role;

    /**
     * Database Column Remarks:
     *   用户评价
     *
     *
     * @mbg.generated
     */
    private String wrate;

    /**
     * Database Column Remarks:
     *   消息内容
     *
     *
     * @mbg.generated
     */
    private AiJourneyMessageContentDTO content;

    /**
     * 来源
     */
    private String source;

    /**
     * Database Column Remarks:
     *   扩展字段
     *
     *
     * @mbg.generated
     */
    private String extra;

    /**
     * 对话时间
     */
    private Date timestamp;

    /**
     * 是否是重试的消息
     */
    private Boolean retry;

    /**
     * 消息类型
     */
    private String type;

    /**
     * 消息状态
     */
    private String status;

    /**
     * 用户指令
     */
    private String userChat;

    /**
     * 改写用户后指令
     */
    private String summarizedChat;
    /**
     * 耗时，秒
     */
    private long cost;

    /**
     * 是否是澄清query
     */
    private Boolean isClearQuery;

    private AiaskChatRequest aiaskChatRequest;

}

