package com.taobao.trip.jourprod.biz.frt.journeyplannew.model;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * @Description ai行程规划算法 poi 返回结果
 **/
@Data
public class AiJourneyPoiResult implements Serializable {

    private static final long serialVersionUID = -5003862067516481770L;

    private List<AIJourneyPoiModel> result;

    private String __trip_xexpose_main_info;

    private String solutionCluster;

    private String pvid;

    private String debuginfo;

    private int solutionid;

    private String solutionHost;

    private String scm;

    private int version;

    private String tpp_trace;

    private int time_used;

}
