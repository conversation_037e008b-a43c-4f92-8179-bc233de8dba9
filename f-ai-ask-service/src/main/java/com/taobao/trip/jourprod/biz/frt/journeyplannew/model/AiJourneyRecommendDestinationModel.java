package com.taobao.trip.jourprod.biz.frt.journeyplannew.model;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2025/2/15
 **/
@Data
public class AiJourneyRecommendDestinationModel implements Serializable {

    private static final long serialVersionUID = -5766876893210982812L;

    private String destinationId;

    private SuggestDay suggestDay;

    private List<RelationDestination> relationDestinationList;

    @Data
    public static class SuggestDay implements Serializable {

        private static final long serialVersionUID = -5766876893210982812L;

        private String minDay;

        private String maxDay;

    }

    @Data
    public static class RelationDestination implements Serializable {

        private static final long serialVersionUID = -5766876893210982812L;

        private String id;

        private String rank;

    }

}
