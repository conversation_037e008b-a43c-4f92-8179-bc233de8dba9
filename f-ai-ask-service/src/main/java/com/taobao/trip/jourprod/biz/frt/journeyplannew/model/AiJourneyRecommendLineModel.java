package com.taobao.trip.jourprod.biz.frt.journeyplannew.model;

import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.Poi;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * ai行程规划推荐线路模型
 */
@Data
public class AiJourneyRecommendLineModel implements Serializable {

    private static final long serialVersionUID = -5766876893210982812L;

    private String planId;

    private String title;

    private String picture;

    private List<Route> routeList;

    @Data
    public static class Route implements Serializable {

        private static final long serialVersionUID = -5766876893210982812L;

        /**
         * 天数 1 2 3
         */
        private String day;

        /**
         * 中文天数 一 二 三
         */
        private String chineseDay;

        private String title;

        private List<Poi> poiList;

        private String listDestId;

        private String listDestName;
    }
}

