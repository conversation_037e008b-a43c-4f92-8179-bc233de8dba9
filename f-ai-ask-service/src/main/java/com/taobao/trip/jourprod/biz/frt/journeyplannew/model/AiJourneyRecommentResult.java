package com.taobao.trip.jourprod.biz.frt.journeyplannew.model;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * @Description ai行程规划算法推荐路线返回结果
 * <AUTHOR>
 * @Date 2025/2/11
 **/
@Data
public class AiJourneyRecommentResult implements Serializable {

    private static final long serialVersionUID = -5766876893210982812L;

    private List<AiJourneyRecommendLineModel> result;

    private String __trip_xexpose_main_info;

    private String solutionCluster;

    private String pvid;

    private String debuginfo;

    private int solutionid;

    private String solutionHost;

    private String scm;

    private int version;

    private String tpp_trace;

    private int time_used;

}
