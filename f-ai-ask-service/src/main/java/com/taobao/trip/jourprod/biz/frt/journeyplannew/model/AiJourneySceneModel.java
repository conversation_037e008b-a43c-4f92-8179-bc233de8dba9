package com.taobao.trip.jourprod.biz.frt.journeyplannew.model;

import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description ai行程规划场景信息，目前已经被当作上下文使用
 * <AUTHOR>
 * @Date 2025/1/30
 **/
@NoArgsConstructor
@Data
public class AiJourneySceneModel {

    /**
     * 问题类型
     */
    private String type;

    /**
     * 场景枚举
     */
    private AiJourneyPlanSceneEnum sceneEnum;

    /**
     * 提示信息，包括行程关联问题的回答以及关键参数的提问
     */
    private String tip;

    /**
     * 行程规划目的地信息，城市
     */
    private String destination;

    /**
     * 行程规划涉及的城市id
     */
    private Set<String> divisionIdSet;

    /**
     * 解析出来的大洲
     */
    private String continent;

    /**
     * 解析出来的国家
     */
    private String country;

    /**
     * 解析出来的区县
     */
    private String district;

    /**
     * 解析出来的省份
     */
    private String province;

    /**
     * 行程规划天数
     */
    private String day;

    /**
     * 出行季节
     */
    private String seasonal;

    /**
     * 出行偏好
     */
    private String preference;

    /**
     * 出行人群
     */
    private String crowd;

    /**
     * 推荐天数
     */
    private Pair<Integer, Integer> recommendDay;

    /**
     * 机票到达城市
     */
    private String flightArrCity;

    /**
     * 机票出发城市
     */
    private String flightDepCity;

    /**
     * 机票出发城市三字码
     */
    private String flightDepCityCode;

    /**
     * 机票到达城市三字码
     */
    private String flightArrCityCode;

    /**
     * 是否境外航班
     */
    private Boolean flightAbroad;

    /**
     * 是否有机票推荐数据
     */
    private Boolean hasFlightRecommendData;

    /**
     * 机票出发日期
     */
    private String flightDepDate;

    /**
     * 火车到达城市
     */
    private String trainArrCity;

    /**
     * 火车出发城市
     */
    private String trainDepCity;


    /**
     * 火车出发日期
     */
    private String trainDepDate;

    /**
     * 酒店入住城市
     */
    private String hotelCityName;

    /**
     * 酒店入住城市code
     */
    private String hotelCityCode;

    /**
     * 酒店入住日期
     */
    private String hotelCheckIn;

    /**
     * 酒店离店日期
     */
    private String hotelCheckOut;

    /**
     * 酒店搜索关键字
     */
    private String hotelKeywords;

    /**
     * 是否自身周边查询
     */
    private Boolean querySelfSurround;

    /**
     * 查询的poi类别
     */
    private String queryPoiType;

    /**
     * 需要推荐目的地
     */
    private boolean needRecommendDestination;

    /**
     * 出发日期
     */
    private String departureDate;

    /**
     * 返回日期
     */
    private String backDate;

    /**
     * 玩几天
     */
    private String playDays;


    /**
     * ======================== qp数据部分 start ==========================
     */
    /** 实体数据 **/
    private Map<String, Object> entities;

    /** qp查询数据 **/
    private List<QpQuery> qpQueryList;

    /**
     * ======================== qp数据部分 end ==========================
     */

    /**
     * 意图改写话术
     */
    private String summaryQuery;

    /**
     * ======================== 澄清模块 start ==========================
     */
    /**
     * 引导语
     */
    private String guide;

    /**
     * 是否缺少 目的地
     */
    private Boolean lackDestination;

    /**
     * 是否缺少 游玩天数
     */
    private Boolean lackDay;

    /**
     * 是否缺少 出行类型
     */
    private Boolean lackOutType;
    
    /**
     * 特价机票 场景
     */
    private Boolean cheapFlightScene;

    /**
     * 是否需求澄清
     */
    private Boolean needClarify;

    /**
     * ======================== 澄清模块 end ==========================
     */


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QpQuery {
        private String query;
        private String queryType;
    }

    public AiJourneySceneModel(AiJourneyPlanSceneEnum sceneEnum) {
        this.sceneEnum = sceneEnum;
    }

    public AiJourneySceneModel(AiJourneyPlanSceneEnum sceneEnum, String type) {
        this.sceneEnum = sceneEnum;
        this.type = type;
    }

}
