package com.taobao.trip.jourprod.biz.frt.journeyplannew.model;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/3/21 17:19
 * @desc
 */
@Data
public class AiJourneyTrafficSlideCard {

    /**
     * 交通方式 {@link com.fliggy.flyway.enums.TransportTypeEnum}
     */
    public List<Integer> transportType;

    /**
     * 出发城市名称
     */
    private String depCityName;

    /**
     * 到达城市名称
     */
    private String arrCityName;


    /**
     * 出发日期时间(yyyy-MM-dd HH:mm:ss)
     */
    private String depDateTime;

    /**
     * 到达日期时间(yyyy-MM-dd HH:mm:ss)
     */
    private String arrDateTime;

    /**
     * 总价
     */
    public Double price;

    public String priceStr;

    /**
     * 跳转连接
     */
    public String jumpUrl;

    /**
     * 是否往返
     */
    public Boolean isGoAndBack;

    /**
     * 是否往返 go back goAndBack
     */
    public String type;

    private JSONObject collectData;
}
