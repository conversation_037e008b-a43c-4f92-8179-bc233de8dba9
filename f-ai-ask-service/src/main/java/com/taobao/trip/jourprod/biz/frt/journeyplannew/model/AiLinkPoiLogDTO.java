package com.taobao.trip.jourprod.biz.frt.journeyplannew.model;

import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.Poi;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.Data;

/**
 * 链接poi的日志过程信息
 */
@Data
public class AiLinkPoiLogDTO {

    /**
     * 大模型返回的poi，链接到了哪个来源
     */
    private Map<String, String> aiPoiBySourceMap;

    /**
     * 处理前 链接的poi
     */
    private Map<String, Poi> linkPoiMapPre;

    /**
     * 小模型获取的行政区划
     */
    private List<String> predictDivisionIdList;

    /**
     * 场景解析的行政区划
     */
    private Set<String> divisionSet;

    /**
     * 酒店场景 过滤的poi
     */
    private List<Long> hotelFilterPoi;

    /**
     * 美食场景 过滤的poi
     */
    private List<Long> foodFilterPoi;

    /**
     * 不可分发 过滤的poi
     */
    private List<Long> distributableFilterPoi;

    /**
     * 最终链接poi
     */
    private Map<String, Poi> result;
}
