package com.taobao.trip.jourprod.biz.frt.journeyplannew.model;

import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class AiPoiIconDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *   主键
     */
    @Getter
    @Setter
    private Long id;

    /**
     *   创建时间
     */
    @Getter
    @Setter
    private Date gmtCreate;

    /**
     *   修改时间
     */
    @Getter
    @Setter
    private Date gmtModified;

    /**
     *   poi id
     */
    @Getter
    @Setter
    private Long poiId;

    /**
     *   poi名称
     */
    @Getter
    @Setter
    private String poiName;

    /**
     *   icon图标
     */
    @Getter
    @Setter
    private String icon;

    /**
     *   状态 1 不可用 2 可用
     */
    @Getter
    @Setter
    private Short status;

    @Getter
    @Setter
    private String cityCode;

    @Getter
    @Setter
    private String cityName;

    @Getter
    @Setter
    private String llm;
}