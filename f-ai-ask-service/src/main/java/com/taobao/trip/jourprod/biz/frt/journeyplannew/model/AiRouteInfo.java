package com.taobao.trip.jourprod.biz.frt.journeyplannew.model;

import lombok.Data;

import java.util.List;

@Data
public class AiRouteInfo {

    private String bizId;
    private Info info;

    @Data
    public static class Info {
        private String routeId;
        private String title;
        private List<Route> routeList;
        private String score;
    }

    @Data
    public static class Route {
        private String destIds;
        private List<Poi> poiList;
        private String day;
        private String destNames;
    }

    @Data
    public static class Poi {
        private String poiDestTreeName;
        private String address;
        private String poiName;
        private String latitude;
        private String poiDestTreeId;
        private String poiId;
        private String type;
        private String longitude;
        private String mention;
        private String desc;
    }
}
