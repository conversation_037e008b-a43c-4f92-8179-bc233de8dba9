package com.taobao.trip.jourprod.biz.frt.journeyplannew.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alitrip.aisearch.model.search.traffic.entity.TrafficJourneyInfo;
import com.alitrip.aisearch.model.search.traffic.entity.TrafficSegmentInfo;
import com.alitrip.aisearch.model.search.traffic.entity.TrafficSolutionItem;
import com.fliggy.flyway.multitransport.model.res.MtSegmentInfo;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageItemTypeEnum;
import com.taobao.trip.wireless.common.utils.TrainBizConvertUtils;
import com.taobao.trip.wireless.shopping.domain.response.TrainBizBO;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025/3/21 16:32
 * @desc
 */
@Data
public class AiTrafficCardVO implements Serializable {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(AiTrafficCardVO.class);

    /**
     * 交通方案信息
     */
    public TrafficSolutionItem trafficGroupItem;
    /**
     * 交通收藏信息
     */
    public JSONObject collectData;

    /**
     * 场景
     */
    public String scene;

    /**
     * 类型
     */
    private String type;

    public AiTrafficCardVO() {
    }

    public AiTrafficCardVO(TrafficSolutionItem trafficGroupItem) {
        this.trafficGroupItem = trafficGroupItem;
    }

    public JSONObject getCollectData() {
        if (trafficGroupItem == null) {
            return null;
        }
        try {
            CollectInfo collectInfo = new CollectInfo();
            List<TrafficJourneyInfo> journeyInfos = trafficGroupItem.getJourneyInfos();
            List<Integer> transportTypeList = extractDistinctTransportTypes(journeyInfos);

            if (transportTypeList.size() > 1) {
                // 多种交通，本期不考虑收藏
                return null;
            }

            Integer transportType = transportTypeList.get(0);
            // 火车
            if (transportType == 1) {
                handleTrainCase(collectInfo, journeyInfos);
                collectInfo.setJumpUrl(trafficGroupItem.getJumpUrl());
                // 飞机
            } else if (transportType == 0) {
                handleFlightCase(collectInfo, trafficGroupItem);
            }

            return JSON.parseObject(JSON.toJSONString(collectInfo));
        } catch (Exception e) {
            LOGGER.recordNormalException(new LogModel("AiTrafficCardVO.getCollectInfo").e(e).message("获取收藏信息出错"));
        }
        return null;
    }

    private List<Integer> extractDistinctTransportTypes(List<TrafficJourneyInfo> journeyInfos) {
        return journeyInfos.stream()
                .flatMap(journeyInfo -> journeyInfo.getSegments().stream())
                .map(MtSegmentInfo::getTransportType)
                .distinct()
                .collect(Collectors.toList());
    }

    private void handleTrainCase(CollectInfo collectInfo, List<TrafficJourneyInfo> journeyInfos) {
        collectInfo.setBizType("train");

        TrainBizBO trainBiz = new TrainBizBO();

        List<TrainBizBO.Segment> segmentList = buildSegmentList(journeyInfos);
        trainBiz.setSegmentList(segmentList);

        setTrainTypeBasedOnJourneyCount(trainBiz, journeyInfos);

        collectInfo.setTrainBiz(trainBiz);

        String trainBizIdMd5 = TrainBizConvertUtils.getTrainBizIdMd5(collectInfo.getTrainBiz());

        collectInfo.setBizId(trainBizIdMd5);
    }

    private List<TrainBizBO.Segment> buildSegmentList(List<TrafficJourneyInfo> journeyInfos) {
        List<TrainBizBO.Segment> segmentList = new ArrayList<>();
        for (TrafficJourneyInfo journeyInfo : journeyInfos) {
            for (TrafficSegmentInfo trafficSegmentInfo : journeyInfo.getSegments()) {
                TrainBizBO.Segment segment = new TrainBizBO.Segment();
                segment.setTrainNo(trafficSegmentInfo.getMarketingTransportNo());
                segment.setDepStation(trafficSegmentInfo.getDepStationName());
                segment.setArrStation(trafficSegmentInfo.getArrStationName());
                segment.setDepTime(trafficSegmentInfo.getDepDateTime().substring(0, 10));
                segmentList.add(segment);
            }
        }
        return segmentList;
    }

    private void setTrainTypeBasedOnJourneyCount(TrainBizBO trainBiz, List<TrafficJourneyInfo> journeyInfos) {
        if (journeyInfos.size() == 2) {
            trainBiz.setTrainType("round");
        } else if (journeyInfos.size() == 1) {
            int size = journeyInfos.get(0).getSegments().size();
            if (size == 1) {
                trainBiz.setTrainType("nostop");
            } else if (size > 1) {
                trainBiz.setTrainType("transfer");
            }
        }
    }

    private void handleFlightCase(CollectInfo collectInfo, TrafficSolutionItem trafficGroupItem) {
        collectInfo.setBizType("flight");
        collectInfo.setJumpUrl(trafficGroupItem.getJumpUrl());
        collectInfo.setBizId(trafficGroupItem.getEncryptRouteInfoKey());
    }



    /**
     * 根据旅程信息列表确定类型
     */
    public String getType() {
        if (trafficGroupItem == null || trafficGroupItem.getJourneyInfos() == null) {
            return null;
        }
        List<TrafficJourneyInfo> journeyInfos = trafficGroupItem.getJourneyInfos();
        // 如果只有一个旅程信息，则进一步判断其包含的交通段种类
        if (journeyInfos.size() == 1) {
            TrafficJourneyInfo singleJourneyInfo = journeyInfos.get(0);
            List<TrafficSegmentInfo> segments = singleJourneyInfo.getSegments();
            // 收集所有不同的交通方式
            Set<Integer> transportTypeList = segments.stream()
                    .map(TrafficSegmentInfo::getTransportType)
                    .collect(Collectors.toSet());
            //如果有多种交通方式，则认为是多模式交通卡
            if (transportTypeList.size() > 1) {
                return AiJourneyMessageItemTypeEnum.TRAFFIC_MULTI_CARD.getCode();
            }
            // 否则是单一交通方式的交通卡
            return AiJourneyMessageItemTypeEnum.TRAFFIC_CARD.getCode();
        }
        // 如果有两个旅程信息，则视为往返交通卡
        if (journeyInfos.size() == 2) {
            return AiJourneyMessageItemTypeEnum.TRAFFIC_ROUND_CARD.getCode();
        }
        // 其他情况不处理，返回null
        return null;
    }

    @Data
    public static class CollectInfo implements Serializable {
        private String bizId;
        private String bizType;
        private String jumpUrl;
        private TrainBizBO trainBiz;
    }

}
