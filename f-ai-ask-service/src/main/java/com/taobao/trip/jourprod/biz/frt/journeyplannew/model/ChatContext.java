package com.taobao.trip.jourprod.biz.frt.journeyplannew.model;

import com.alibaba.mtop3.invocation.MtopStream;
import com.fliggy.aiworks.client.aiask.request.AiaskChatRequest;
import com.google.common.collect.Lists;
import com.taobao.trip.jourprod.biz.frt.llm.domain.MessageParam;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatRequest;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult.StreamMessageCardModel;
import lombok.Data;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 对话上下文，存放输出的文本消息和卡片消息
 * <AUTHOR>
 * @date 2025/3/16
 */
@Data
public class ChatContext {

    /**
     * 改写后的用户指令
     */
    private String summarizedChat;

    /**
     * 系统消息id
     */
    private String systemMessageId;

    /**
     * 用户消息id
     */
    private String userMessageId;

    /**
     * 会话id
     */
    private String sessionId;

    /**
     * 文本消息列表
     *
     */
    private List<String> messageList = new CopyOnWriteArrayList<>();

    /**
     * 卡片列表
     */
    private List<StreamMessageCardModel> components = new CopyOnWriteArrayList<>();

    /**
     * 原始内容
     */
    private String originalContent;

    /**
     * 是否是澄清query
     */
    private Boolean isClearQuery;

    /**
     * 内部数据
     */
    private Map<String, Object> internalData = new ConcurrentHashMap<>();

    /**
     * 思维链数据
     */
    private List<Map<String, Object>> thinkingThought = Lists.newArrayList();

    /**
     * 用户落库数据
     */
    private AiJourneyMessageDTO userAiJourneyMessageDTO;

    /**
     * components异步执行同步等待管理器
     * @param list
     */
    private Map<String, List<CompletableFuture<Void>>> componentsWaitMap = new ConcurrentHashMap<>();

    public synchronized List<CompletableFuture<Void>> getOrCreateFutures(String userMessageId) {
        return componentsWaitMap.computeIfAbsent(userMessageId, k -> Collections.synchronizedList(new ArrayList<>()));
    }

    public void addComponents(List<StreamMessageCardModel> list) {
        components.addAll(list);
    }

    private JourneyPlanAiChatRequest request;

    private AiJourneySceneModel aiJourneySceneModel;

    /**
     * 大模型对话计数
     */
    private AtomicInteger streamCount = new AtomicInteger(0);


    /**
     * 原始消息内容
     */
    private List<MessageParam> originalMessageInfo;

    /**
     * 当前LBS所在城市code
     */
    private String currentLbsCityCode;

    /**
     * 当前LBS所在城市
     */
    private String currentLbsCityName;

    /**
     * 当前LBS所在省份
     */
    private Long currentLbsProCode;

    /**
     * 关闭历史记录写入，如果用户消息有问题，则不允许写历史
     */
    private boolean disableHistoryMessage = false;

    /*
     * 性能埋点相关
     */
    /**
     * 首次思考链token时间
     */
    private long firstThinkingTokenMs = 0L;
    /**
     * 首次内容token时间
     */
    private long firstContentTokenTimeMs = 0L;
    /**
     * 最后思考链token时间
     */
    private long lastThinkingTokenTimeMs = 0L;

    /**
     * 更多诉求对应的键盘展示的标签
     */
    private List<KeyboardTagDTO> keyboardTags;

    /**
     * 更多诉求对应的键盘展示的标签v2
     */
    private List<KeyboardTagDTOV2> keyboardTagsV2;

    /**
     * 灵感词v2
     */
    private List<KeyboardTagValueDTOV2> inspirationsV2;

    /**
     * mtop流
     */
    private MtopStream mtopStream;

    private AiaskChatRequest aiaskChatRequest;

}
