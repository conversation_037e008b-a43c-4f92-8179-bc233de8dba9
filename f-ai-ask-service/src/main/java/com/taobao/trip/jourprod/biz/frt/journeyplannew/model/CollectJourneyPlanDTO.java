package com.taobao.trip.jourprod.biz.frt.journeyplannew.model;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/03/30
 **/
public class CollectJourneyPlanDTO implements Serializable {

    private static final long serialVersionUID = 5846051092397169325L;

    /**
     * 物料数据， 区分类型，有商品、交通、Poi
     *
     */
    //private List<Item> items;

    /**
     * Long	收藏的线路id，覆盖原收藏线路时传
     */
    private Long collectRouteId;

    /**
     * 推荐的路线信息
     */
    private Object recommendRouteInfo;

}
