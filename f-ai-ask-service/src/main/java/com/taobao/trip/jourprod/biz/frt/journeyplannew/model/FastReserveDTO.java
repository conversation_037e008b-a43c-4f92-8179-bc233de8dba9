package com.taobao.trip.jourprod.biz.frt.journeyplannew.model;

import java.io.Serializable;
import java.util.List;

import com.taobao.trip.rsp.collect.JourneyCollectItemDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/05/06
 **/
@Data
public class FastReserveDTO implements Serializable {

    private static final long serialVersionUID = -1638196270758142079L;

    private List<JourneyAskItemDTO> itemList;

    private List<JourneyCollectItemDTO> collectData;

}
