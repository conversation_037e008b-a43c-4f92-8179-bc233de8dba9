package com.taobao.trip.jourprod.biz.frt.journeyplannew.model;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 高德poi点信息
 */
@Data
public class GaoDePoiModel implements Serializable {

    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 类型
     */
    private String type;

    /**
     * 地址
     */
    private String address;

    /**
     * 营业时间
     */
    private String openTime;

    /**
     * 图片列表
     */
    private List<String> imgList;

    /**
     * 人均消费
     */
    private String cost;

    /**
     * 评分
     */
    private String rating;

    /**
     * 特色内容
     */
    private String tag;

}

