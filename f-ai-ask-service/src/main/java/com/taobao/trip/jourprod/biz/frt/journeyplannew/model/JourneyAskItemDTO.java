package com.taobao.trip.jourprod.biz.frt.journeyplannew.model;

import java.io.Serializable;

import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.HotelRecommendCardVO;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/05/06
 **/
@Data
@Builder
public class JourneyAskItemDTO implements Serializable {

    private static final long serialVersionUID = 5701951958361924881L;

    /**
     * 行程规划中出现的可购买商品类型，hotel,traffic
     */
    private String type;

    private HotelRecommendCardVO hotelInfo;

    private AiTrafficCardVO trafficInfo;







}
