package com.taobao.trip.jourprod.biz.frt.journeyplannew.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 唤醒键盘后，展示的标签
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class KeyboardTagDTOV2 {

    /**
     * 标签标题
     */
    private String title;

    /**
     * 标签内容列表
     */
    private List<KeyboardTagValueDTOV2> keys;

}
