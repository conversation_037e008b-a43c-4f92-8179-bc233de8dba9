package com.taobao.trip.jourprod.biz.frt.journeyplannew.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 唤醒键盘后，展示的标签值 对象
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class KeyboardTagValueDTOV2 {

    /**
     * 内容
     */
    private String name;

    /**
     * 点击内容
     */
    private String clickText;

    /**
     * 图标
     */
    private String icon;

}
