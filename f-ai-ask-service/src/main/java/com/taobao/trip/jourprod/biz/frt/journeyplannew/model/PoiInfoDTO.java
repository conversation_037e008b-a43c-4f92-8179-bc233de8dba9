package com.taobao.trip.jourprod.biz.frt.journeyplannew.model;

import com.alitrip.aisearch.model.search.poi.entity.PoiInfo;
import lombok.Data;

/**
 * @description: 类描述
 * @author: huiyi
 * @create: 2025-03-30 18:47
 **/
@Data
public class PoiInfoDTO extends PoiInfo {

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 行政区划id
     */
    private String divisionId;

    /**
     * poi名称简称
     */
    private String poiNameAbbr;

    /**
     * icon地址
     */
    private String icon;

    /**
     * poi一级类型
     */
    private Long firstCategory;

    /**
     * poi一级类型名称
     */
    private String firstCategoryName;

    /**
     * 经纬度
     */
    private String location;

    /**
     * 是否海外 0-国内 1-海外
     */
    private Integer isAbroad;

    /**
     * poiType  gaode poi
     */
    private String poiType;
}
