package com.taobao.trip.jourprod.biz.frt.journeyplannew.model.drawmap;

import java.io.Serializable;
import java.util.List;

import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl.JourneyPlanNodeProcessor.RouteDay;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/05/26
 **/
@Data
public class DrawMapContextDTO implements Serializable {

    private static final long serialVersionUID = -1830696865165365726L;
    /**
     * 大模型返回的poi信息
     */
    private String dayPoiInfos;
    /**
     * 大模型返回的主题
     */
    private String theme;
    /**
     * 大模型返回的去程交通
     */
    private String goTraffic;
    /**
     * 大模型返回的回程交通
     */
    private String backTraffic;
    /**
     * 大模型返回的隐藏查看地图按钮，true为需要隐藏
     */
    private String hiddenBtn;
    /**
     * 大模型返回的行程标题
     */
    private String journeyTitle;

    /**
     * 只有poi的列表
     */
    private List<RouteDay> onlyPoiDayList;
}
