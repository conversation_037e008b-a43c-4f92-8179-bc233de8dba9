package com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher;

import com.alibaba.boot.switchcenter.annotation.SwitchGroup;

import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import org.springframework.stereotype.Component;

/**
 * @Description 问一问相关开关
 * <AUTHOR>
 * @Date 2025/3/1
 **/
@Component
@SwitchGroup
public class AiEntranceSwitch {

    @AppSwitch(des = "mock返回给荣耀的文本结构", level = Level.p4)
    public static String RY_TEXT_RESPONSE = "{\"requestId\":\"test-id\",\"errorCode\":\"0\",\"errorMessage\":\"success\",\"model\":\"your-model-name\",\"choices\":{\"message\":{\"role\":\"assistant\",\"contentType\":\"2\",\"hybridContent\":{\"commands\":{\"sn\":0,\"head\":{\"namespace\":\"rich_text\"},\"body\":{\"text\":\"为你推荐以下内容\"}}}},\"finishReason\":\"normal\"}}";

    @AppSwitch(des = "mock返回给荣耀的h5卡片结构", level = Level.p4)
    public static String RY_RESPONSE = "{\"requestId\":\"test-id\",\"errorCode\":\"0\",\"errorMessage\":\"success\",\"model\":\"your-model-name\",\"choices\":{\"message\":{\"role\":\"assistant\",\"contentType\":\"2\",\"hybridContent\":{\"commands\":{\"sn\":0,\"head\":{\"namespace\":\"card\"},\"body\":{\"cardType\":2,\"htmls\":[{\"url\":\"https://www.taobao.com\",\"mode\":0,\"height\":100,\"width\":100}]}}}},\"finishReason\":\"normal\"}}";
}
