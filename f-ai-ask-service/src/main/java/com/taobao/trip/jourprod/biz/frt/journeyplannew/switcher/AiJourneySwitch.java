package com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher;

import com.alibaba.boot.switchcenter.annotation.SwitchGroup;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.KeyboardTagDTO;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.KeyboardTagValueDTOV2;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @Description 问一问相关开关
 * <AUTHOR>
 * @Date 2025/3/1
 **/
@Component
@SwitchGroup
public class AiJourneySwitch {

    @AppSwitch(des = "返回结构里面的分隔符", level = Level.p4)
    public static String SPLITOR = "≌";

    @AppSwitch(des = "请求历史消息条数", level = Level.p4)
    public static Integer HISTORY_MESSAGE_SIZE = 6;

    @AppSwitch(des = "预算卡片历史查询条数", level = Level.p4)
    public static Integer BudgetCardHistorySize = 40;

    @AppSwitch(des = "大模型查询的历史消息控制在xx小时内", level = Level.p4)
    public static Integer QUERY_HISTORY_MESSAGE_BETWEEN_HOUR_NUM = 1;

    @AppSwitch(des = "ai流式上一个token和下一个token间隔告警时间", level = Level.p4)
    public static Integer AI_STREAM_PRE_AFTER_TOKEN_S = 5;

    @AppSwitch(des = "ai对话获取行政区划列表是否走qp", level = Level.p4)
    public static Boolean AI_CHAT_DIVISION_LIST_BY_QP = false;

    @AppSwitch(des = "ai搜高德周边半径距离", level = Level.p4)
    public static Integer AI_SEARCH_AROUND_RADIUS = 10000;

    @AppSwitch(des = "评价跳转链接", level = Level.p4)
    public static String RATE_JUMP_URL = "https://outfliggys.m.taobao.com/app/trip/rx-evaluate-list-pad/pages/home?disableNav=YES&titleBarHidden=2&_fli_background_transparent=true&_fli_anim_type=none&bizType=%s&itemId=%s&tabFilter=%s&enableLoadingView=false";

    @AppSwitch(des = "更多诉求对应的词库配置", level = Level.p4)
    public static Map<String, KeyboardTagDTO> MORE_QUESTION_KEYBORAD_MAP = Maps.newHashMap();
    @AppSwitch(des = "AI对话components数据任务完成等待时间", level = Level.p4)
    public static Integer WAIT_COMPONENTS_ASYNC_TASK_TIMEOUT = 5;
    @AppSwitch(des = "每个行业渲染卡片mock的用户列表", level = Level.p4)
    public static List<String> NODE_PROCESSOR_MOCK_USER_ID_LIST = Lists.newArrayList();
    @AppSwitch(des = "每个行业渲染卡片mock", level = Level.p4)
    public static Map<String, List<String>> NODE_PROCESSOR_MOCK_ID_MAP = Maps.newHashMap();

    @AppSwitch(des = "debug模式打印日志", level = Level.p4)
    public static Boolean IS_DEBUG_OUTPUT_LOGGING = Boolean.FALSE;

    @AppSwitch(des = "是否流式输出闲聊", level = Level.p4)
    public static boolean IS_STREAM_OUTPUT = true;

    @AppSwitch(des = "其他场景回答appId", level = Level.p4)
    public static String OTHER_SCENE_CHIT_CHAT_APP_ID = "6fb57e1f5c0f42b38be647a7ab55dfb6";

    @AppSwitch(des = "不同召回数据pageId对应的数据校验", level = Level.p4)
    public static Map<String, String> PAGE_ID_VALIDATE_MAP = Maps.newHashMap();

    static {
        PAGE_ID_VALIDATE_MAP.put("flightOnewayList", "$.listData");
    }
    @AppSwitch(des = "未识别到场景的兜底回复文案", level = Level.p4)
    public static String UNKNOWN_SCENE_CONTENT = "未识别到您的问题分类，请修改后重新提问";
    @AppSwitch(des = "支持用户同时对话中的数量", level = Level.p4)
    public static Integer MAX_CHAT_NUM_IN_PROGRESS = 3;
    @AppSwitch(des = "酒店卡片底部文案-通用场景", level = Level.p4)
    public static String HOTEL_BOTTOM_COMMON_TIPS = "价格可能随时间波动，建议尽早预定";
    @AppSwitch(des = "酒店卡片底部文案对应的图标-通用场景", level = Level.p4)
    public static String HOTEL_BOTTOM_COMMON_ICON = "";

    @AppSwitch(des = "会话列表最小版本号", level = Level.p4)
    public static String SESSION_LIST_MIN_VERSION = "9.10.20";
    @AppSwitch(des = "是否使用百炼返回的sessionId进行更新", level = Level.p4)
    public static boolean USE_BAILIAN_SESSION_ID = false;
    @AppSwitch(des = "是否mock安全审核的返回结果", level = Level.p4)
    public static boolean MOCK_CSI_CHECK_RESULT = false;

    @AppSwitch(des = "AI问一问-键盘标签-目的地标题", level = Level.p4)
    public static String AI_KEYBOARD_TAGS_TITLE_DEST = "目的地";

    @AppSwitch(des = "AI问一问-键盘标签-游玩天数标题", level = Level.p4)
    public static String AI_KEYBOARD_TAGS_TITLE_DAYS = "游玩天数";

    @AppSwitch(des = "AI问一问-键盘标签-游玩天数-对应列表", level = Level.p4)
    public static List<String> AI_KEYBOARD_TAGS_TITLE_DAYS_LIST = Lists.newArrayList("3日游", "4日游", "5日游");

    @AppSwitch(des = "AI问一问-键盘标签-出行类型标题", level = Level.p4)
    public static String AI_KEYBOARD_TAGS_TITLE_OUT_TYPE = "出行目的";

    @AppSwitch(des = "AI问一问-键盘标签-出行类型-对应列表", level = Level.p4)
    public static List<String> AI_KEYBOARD_TAGS_TITLE_OUT_TYPE_LIST = Lists.newArrayList("商务出差", "亲子游", "度假", "陪同父母");

    @AppSwitch(des = "AI问一问-键盘标签-出行时间标题", level = Level.p4)
    public static String AI_KEYBOARD_TAGS_TITLE_OUT_TIME = "出行时间";

    @AppSwitch(des = "AI问一问-特价机票快速返回的灵感显示文案", level = Level.p4)
    public static String AI_CHEAP_FLIGHT_INSPIRATIONS_V2_TEXT_SHOW = "跳过，直接推荐";

    @AppSwitch(des = "AI问一问-特价机票快速返回的灵感点击文案", level = Level.p4)
    public static String AI_CHEAP_FLIGHT_INSPIRATIONS_V2_TEXT_CLICK = "为我推荐任意地点便宜机票";

    @AppSwitch(des = "AI问一问-特价机票快速返回的灵感", level = Level.p4)
    public static String AI_CHEAP_FLIGHT_INSPIRATIONS_V2_LIST = "[{\"clickText\":\"为我推荐任意地点便宜机票\",\"name\":\"跳过，直接推荐\"}]";

    @AppSwitch(des = "AI问一问-键盘标签-出行人群标题", level = Level.p4)
    public static String AI_KEYBOARD_TAGS_TITLE_OUT_PEOPLE = "出行人群";

    @AppSwitch(des = "AI问一问-键盘标签-目的地范围标题", level = Level.p4)
    public static String AI_KEYBOARD_TAGS_TITLE_DEST_RANGE = "目的地范围";

    @AppSwitch(des = "历史会话列表一页数量", level = Level.p4)
    public static Integer SESSION_LIST_PAGE_SIZE = 14;
    @AppSwitch(des = "酒店优惠浮层跳转链接", level = Level.p4)
    public static String HOTEL_DISCOUNT_DETAIL_LAYER_URL = "https://market.{0}.taobao.com/app/trip/rx-hotel-ai2c-pages/pages/discount-detail?_fli_anim_type=none&_fli_background_transparent=true&disableNav=YES&titleBarHidden=2";

    @AppSwitch(des = "页面泰坦静态配置的configId", level = Level.p4)
    public static String PAGE_STATIC_RESOURCE_CONFIG_ID = "21078";

}
