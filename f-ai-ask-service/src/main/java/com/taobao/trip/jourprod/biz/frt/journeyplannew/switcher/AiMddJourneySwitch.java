package com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher;

import com.alibaba.boot.switchcenter.annotation.SwitchGroup;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


/**
 * @Description 问一问相关开关-目的地
 **/
@Component
@SwitchGroup
public class AiMddJourneySwitch {


    @AppSwitch(des = "ai问一问目的地搜索uv阈值", level = Level.p4)
    public static Integer AI_MDD_DEST_SEARCH_COUNT = 10000;

    @AppSwitch(des = "ai问一问目的地搜索量阈值", level = Level.p4)
    public static Integer AI_MDD_DEST_SEARCH_INCR = 20;

    @AppSwitch(des = "ai问一问目的地子玩法阈值", level = Level.p4)
    public static Integer AI_MDD_DEST_SUB_PLAY_TOP = 3;

    @AppSwitch(des = "ai问一问目的地签证-推荐理由1", level = Level.p4)
    public static String RECOMMEND_ITEM_ONE = "热度高：";

    @AppSwitch(des = "ai问一问目的地签证-推荐理由1", level = Level.p4)
    public static String RECOMMEND_ITEM_ONE_VALUE = "近期同类签证下销量第一，";

    @AppSwitch(des = "ai问一问目的地签证-推荐理由2", level = Level.p4)
    public static String RECOMMEND_ITEM_TWO = "服务好：";

    @AppSwitch(des = "ai问一问目的地签证-推荐理由标签取几个", level = Level.p4)
    public static Integer RECOMMEND_ITEM_SERVICE_TAG_COUNT = 2;

    @AppSwitch(des = "ai问一问目的地-hot标签mock", level = Level.p4)
    public static String AI_MDD_HOT_TAG_MOCK_DATA = "";

    @AppSwitch(des = "ai问一问目的地-关键词", level = Level.p4)
    public static List<String> AI_MDD_TITLE_KEYWORDS = Lists.newArrayList("景点推荐", "酒店推荐", "特色美食", "路线规划");

    @AppSwitch(des = "ai问一问目的地-跳转链接", level = Level.p4)
    public static String AI_MDD_JUMP_LINK = "https://outfliggys.m.taobao.com/app/trip/rx-dest2024-pop/pages/detail?_fli_use_manifest=true&destId={0}&disableNav=YES&enableLoadingView=true&fpt=dest_id(330100)abfpt.mddlenative(e3bf3fcd053982cf)abfpt.mddBangdan(62bc5fed68f1e3c6)home_user_status(local)dest_user_status(local)abfpt.bangdan(96be781a1d8e2b4b)abfpt.mddtab(dfb5613a50d4980f)ab2025BucketName(859112)page(dest)ftuid(pv2g3OM0373742.2)&play={1}&playPropInfoName={2}&pre_pageVersion=1.9.15&spm=181.29513995.card.jump&spmUrl=181.9476855.dest_world_month.mian_play_4_23008&themeId={3}&titleBarHidden=2&ttid={4}&type=PLAY&deviceid={5}&client_version=9.10.15&client_type=ios&frm=travel&_fli_router_spm=181.29334294.0.0&_fli_interceptor_start_time=1743477385798&_fli_from_cache=true&_fli_pressr=true&_projVer=1.0.0&_fli_background_transparent=true&animated=false&enableLoadingView=false";

    @AppSwitch(des = "ai问一问-是否线上", level = Level.p4)
    public static Boolean ENV_FLAG = true;

    @AppSwitch(des = "ai问一问目的地序号emoji的map", level = Level.p4)
    public static Map<String, String> AI_MDD_EMOJI_NUMBER_MAP = Maps.newHashMap();

    static {
        AI_MDD_EMOJI_NUMBER_MAP.put("1", "1️⃣");
        AI_MDD_EMOJI_NUMBER_MAP.put("2", "2️⃣");
        AI_MDD_EMOJI_NUMBER_MAP.put("3", "3️⃣");
        AI_MDD_EMOJI_NUMBER_MAP.put("4", "4️⃣");
        AI_MDD_EMOJI_NUMBER_MAP.put("5", "5️⃣");
        AI_MDD_EMOJI_NUMBER_MAP.put("6", "6️⃣");
        AI_MDD_EMOJI_NUMBER_MAP.put("7", "7️⃣");
        AI_MDD_EMOJI_NUMBER_MAP.put("8", "8️⃣");
        AI_MDD_EMOJI_NUMBER_MAP.put("9", "9️⃣");
        AI_MDD_EMOJI_NUMBER_MAP.put("10", "🔟");
    }

    @AppSwitch(des = "ai问一问目的地搜索量-日期阈值", level = Level.p4)
    public static Integer AI_MDD_DEST_SEARCH_DAY_LIMIT = 2;

    @AppSwitch(des = "ai问一问签证查询商品卡-大连code", level = Level.p4)
    public static String AI_VISA_QUERY_SKU_DALIAN_ID = "210200";

}
