package com.taobao.trip.jourprod.biz.frt.journeyplannew.template;

import freemarker.template.Configuration;
import freemarker.template.TemplateExceptionHandler;
import org.springframework.context.annotation.Bean;

/**
 * @Description Freemarker配置类
 * <AUTHOR>
 * @Date 2025/1/30
 **/
@org.springframework.context.annotation.Configuration
public class FreemarkerConfiguration {
    
    private String encoding = "UTF-8";
    
    private boolean cacheEnabled = true;
    
    private String templatePath = "classpath:/templates/";
    
    /**
     * 创建Freemarker配置Bean
     */
    @Bean
    public Configuration freemarkerConfig() {
        Configuration config = new Configuration(Configuration.VERSION_2_3_31);
        
        try {
            // 设置模板加载路径
            if (templatePath.startsWith("classpath:")) {
                String path = templatePath.substring("classpath:".length());
                config.setClassForTemplateLoading(this.getClass(), path);
            } else {
                config.setDirectoryForTemplateLoading(new java.io.File(templatePath));
            }
            
            // 设置编码
            config.setDefaultEncoding(encoding);
            
            // 设置异常处理器
            config.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
            
            // 设置日志模板异常
            config.setLogTemplateExceptions(false);
            
            // 设置包装未检查异常
            config.setWrapUncheckedExceptions(true);
            
            // 设置回退到内置模板
            config.setFallbackOnNullLoopVariable(false);
            
            // 设置缓存
            if (!cacheEnabled) {
                config.setCacheStorage(new freemarker.cache.NullCacheStorage());
            }
            
        } catch (Exception e) {
            throw new RuntimeException("Freemarker配置初始化失败", e);
        }
        
        return config;
    }
}
