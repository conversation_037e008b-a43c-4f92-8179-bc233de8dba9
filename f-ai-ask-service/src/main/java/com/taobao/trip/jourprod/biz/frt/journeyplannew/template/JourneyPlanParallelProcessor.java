package com.taobao.trip.jourprod.biz.frt.journeyplannew.template;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.utils.FreemarkerUtil;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Description 行程规划并行处理器
 * <AUTHOR>
 * @Date 2025/1/30
 **/
@Component
public class JourneyPlanParallelProcessor {
    
    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneyPlanParallelProcessor.class);
    
    private ThreadPoolExecutor templateProcessorExecutor = new ThreadPoolExecutor(50, 200, 30, TimeUnit.SECONDS,
        new LinkedBlockingQueue<>(10),
        new CustomizableThreadFactory("JourneyPlanParallelProcessor"),new ThreadPoolExecutor.CallerRunsPolicy());;
    
    /**
     * 并行处理行程规划各个模块
     */
    public CompletableFuture<String> processJourneyPlanParallel(JSONObject jsonData, ChatContext chatContext) {
        
        // 1. 分解为可并行处理的模块
        List<CompletableFuture<String>> futures = Lists.newArrayList();
        
        // 行程简介模块
        futures.add(CompletableFuture.supplyAsync(() -> 
            renderIntroSection(jsonData.getJSONObject("journeyIntro")), templateProcessorExecutor));
        
        // 路线概览模块
        futures.add(CompletableFuture.supplyAsync(() -> 
            renderOverviewSection(jsonData.getJSONObject("routeOverview")), templateProcessorExecutor));
        
        // 每日行程模块（可进一步并行）
        JSONArray dailyPlans = jsonData.getJSONArray("dailyPlans");
        for (int i = 0; i < dailyPlans.size(); i++) {
            final int dayIndex = i;
            futures.add(CompletableFuture.supplyAsync(() -> 
                renderDailyPlanSection(dailyPlans.getJSONObject(dayIndex)), templateProcessorExecutor));
        }
        
        // 注意事项模块
        futures.add(CompletableFuture.supplyAsync(() -> 
            renderNotesSection(jsonData.getJSONObject("notes")), templateProcessorExecutor));
        
        // 预算模块
        futures.add(CompletableFuture.supplyAsync(() -> 
            renderBudgetSection(jsonData.getJSONObject("budget")), templateProcessorExecutor));
        
        // 2. 等待所有模块完成并按顺序组装
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .thenApply(v -> {
                StringBuilder result = new StringBuilder();
                
                try {
                    // 按照预定义顺序组装结果
                    result.append(futures.get(0).join()); // 行程简介
                    result.append(futures.get(1).join()); // 路线概览
                    
                    // 每日行程按天数顺序
                    for (int i = 2; i < 2 + dailyPlans.size(); i++) {
                        result.append(futures.get(i).join());
                    }
                    
                    result.append(futures.get(futures.size() - 2).join()); // 注意事项
                    result.append(futures.get(futures.size() - 1).join()); // 预算
                    
                    LOGGER.recordOutput(new LogModel("processJourneyPlanParallel")
                        .message("并行处理完成，总长度: {0}", result.length()));
                    
                    return result.toString();
                    
                } catch (Exception e) {
                    LOGGER.recordDangerException(new LogModel("processJourneyPlanParallel")
                        .message("并行处理结果组装失败")
                        .e(e));
                    throw new RuntimeException("并行处理失败", e);
                }
            });
    }
    
    /**
     * 渲染行程简介部分
     */
    private String renderIntroSection(JSONObject introData) {
        try {
            HashMap<String, Object> data = Maps.newHashMap();
            data.put("data", introData);
            return FreemarkerUtil.processTemplate("intro_section.ftl", data);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("renderIntroSection")
                .message("行程简介渲染失败")
                .e(e));
            return "";
        }
    }
    
    /**
     * 渲染路线概览部分
     */
    private String renderOverviewSection(JSONObject overviewData) {
        try {
            HashMap<String, Object> data = Maps.newHashMap();
            data.put("data", overviewData);
            return FreemarkerUtil.processTemplate("overview_section.ftl", data);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("renderOverviewSection")
                .message("路线概览渲染失败")
                .e(e));
            return "";
        }
    }
    
    /**
     * 渲染单日行程部分
     */
    private String renderDailyPlanSection(JSONObject dailyPlanData) {
        try {
            HashMap<String, Object> data = Maps.newHashMap();
            data.put("dailyPlan", dailyPlanData);
            return FreemarkerUtil.processTemplate("daily_plan_template.ftl", data);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("renderDailyPlanSection")
                .message("每日行程渲染失败")
                .e(e));
            return "";
        }
    }
    
    /**
     * 渲染注意事项部分
     */
    private String renderNotesSection(JSONObject notesData) {
        try {
            HashMap<String, Object> data = Maps.newHashMap();
            data.put("data", notesData);
            return FreemarkerUtil.processTemplate("notes_section.ftl", data);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("renderNotesSection")
                .message("注意事项渲染失败")
                .e(e));
            return "";
        }
    }
    
    /**
     * 渲染预算部分
     */
    private String renderBudgetSection(JSONObject budgetData) {
        try {
            HashMap<String, Object> data = Maps.newHashMap();
            data.put("data", budgetData);
            return FreemarkerUtil.processTemplate("budget_section.ftl", data);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("renderBudgetSection")
                .message("预算参考渲染失败")
                .e(e));
            return "";
        }
    }
}
