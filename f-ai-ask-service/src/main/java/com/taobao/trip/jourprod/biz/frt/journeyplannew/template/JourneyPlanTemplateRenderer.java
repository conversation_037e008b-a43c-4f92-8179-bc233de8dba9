package com.taobao.trip.jourprod.biz.frt.journeyplannew.template;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.utils.JSONUtil;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import freemarker.template.Configuration;
import freemarker.template.Template;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.StringWriter;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Description 行程规划模板渲染引擎
 * <AUTHOR>
 * @Date 2025/1/30
 **/
@Component
public class JourneyPlanTemplateRenderer {
    
    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneyPlanTemplateRenderer.class);
    
    /**
     * 验证和处理ID连续性
     */
    public void validateAndProcessIds(JSONObject jsonData) {
        Set<Integer> usedIds = Sets.newHashSet();
        AtomicInteger maxId = new AtomicInteger(0);
        
        // 递归收集所有ID
        collectIds(jsonData, usedIds, maxId);
        
        // 验证ID连续性
        for (int i = 1; i <= maxId.get(); i++) {
            if (!usedIds.contains(i)) {
                throw new IllegalArgumentException("ID不连续，缺少ID: " + i);
            }
        }
        
        LOGGER.recordOutput(new LogModel("validateAndProcessIds")
            .message("ID验证通过，最大ID: {0}", maxId.get())
            .response("usedIds: " + usedIds.toString()));
    }
    
    /**
     * 递归收集JSON中的所有ID
     */
    private void collectIds(Object obj, Set<Integer> usedIds, AtomicInteger maxId) {
        if (obj instanceof JSONObject) {
            JSONObject jsonObj = (JSONObject) obj;
            if (jsonObj.containsKey("id")) {
                Integer id = jsonObj.getInteger("id");
                if (id != null) {
                    usedIds.add(id);
                    maxId.set(Math.max(maxId.get(), id));
                }
            }
            // 特殊处理cards数组
            if (jsonObj.containsKey("cards")) {
                JSONArray cardsArray = jsonObj.getJSONArray("cards");
                if (cardsArray != null) {
                    collectIds(cardsArray, usedIds, maxId);
                }
            }
            // 递归处理所有值
            for (Object value : jsonObj.values()) {
                collectIds(value, usedIds, maxId);
            }
        } else if (obj instanceof JSONArray) {
            JSONArray jsonArray = (JSONArray) obj;
            for (Object item : jsonArray) {
                collectIds(item, usedIds, maxId);
            }
        }
    }

}
