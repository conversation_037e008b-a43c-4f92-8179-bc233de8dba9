package com.taobao.trip.jourprod.biz.frt.llm;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

/**
 * LLM 网关自动配置
 */
@Configuration
public class LlmGatewayAutoConfiguration {
    @Autowired(required = false)
    private List<LlmProvider> providers;
    @Resource
    private LlmProviderRegistry registry;

    @PostConstruct
    public void init() {
        if (providers != null) {
            for (LlmProvider provider : providers) {
                registry.registerProvider(provider);
            }
        }
    }
}
