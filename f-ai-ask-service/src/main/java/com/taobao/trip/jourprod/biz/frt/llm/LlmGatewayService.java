package com.taobao.trip.jourprod.biz.frt.llm;

import javax.annotation.Resource;

import com.taobao.trip.jourprod.biz.frt.llm.domain.LlmProviderIdentityEnum;
import com.taobao.trip.jourprod.biz.frt.llm.domain.LlmRequest;
import com.taobao.trip.jourprod.biz.frt.llm.domain.LlmResponse;
import com.taobao.trip.jourprod.biz.frt.llm.domain.LlmStreamResponseCallback;
import org.springframework.stereotype.Component;

/**
 * LLM 网关服务
 * <AUTHOR>
 */
@Component
public class LlmGatewayService {
    @Resource
    private LlmProviderRegistry providerRegistry;

    /**
     * 执行模型调用
     * @param providerIdentity  模型服务提供者身份
     * @param request           请求对象
     * @return                  响应对象
     */
    public LlmResponse execute(LlmProviderIdentityEnum providerIdentity, LlmRequest request) {
        return this.getProvider(providerIdentity.getName()).execute(request);
    }

    /**
     * 执行流式调用
     * @param providerIdentity  模型服务提供者身份
     * @param request           请求对象
     * @param callback          响应回调，里面的逻辑代码就是对于每一次流式返回要做的操作
     */
    public void executeStream(LlmProviderIdentityEnum providerIdentity, LlmRequest request, LlmStreamResponseCallback callback) throws Throwable {
        this.getProvider(providerIdentity.getName()).executeStream(request, callback);
    }

    private LlmProvider getProvider(String providerName) {
        LlmProvider provider = providerRegistry.getProvider(providerName);
        if (provider == null) {
            throw new RuntimeException("Provider not found: " + providerName);
        }
        return provider;
    }
}
