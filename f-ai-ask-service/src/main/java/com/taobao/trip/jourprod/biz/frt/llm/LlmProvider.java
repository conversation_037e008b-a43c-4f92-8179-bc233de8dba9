package com.taobao.trip.jourprod.biz.frt.llm;

import java.util.Map;

import com.taobao.trip.jourprod.biz.frt.llm.domain.LlmRequest;
import com.taobao.trip.jourprod.biz.frt.llm.domain.LlmResponse;
import com.taobao.trip.jourprod.biz.frt.llm.domain.LlmStreamResponseCallback;
import org.apache.commons.collections4.MapUtils;
import org.jetbrains.annotations.Nullable;

/**
 * <AUTHOR>
 */
public interface LlmProvider {
    /**
     * 普通调用
     */
    LlmResponse execute(LlmRequest request);

    /**
     * 流式调用
     * @param request 请求对象
     * @param callback 流式响应回调
     */
    void executeStream(LlmRequest request, LlmStreamResponseCallback callback) throws Throwable;

    /**
     * 获取提供者名称
     */
    String getProviderName();

    @Nullable
    default Map<String, Object> convertThoughtData(Map<String, Object> thoughtData) {
        if (MapUtils.isEmpty(thoughtData) || !thoughtData.containsKey("result")) {
            return thoughtData;
        }
        Object result = thoughtData.get("result");
        if (result instanceof Map) {
            Map<String, Object> resultMap = (Map<String, Object>) result;
            if (MapUtils.isEmpty(resultMap) || !resultMap.containsKey("data")) {
                return thoughtData;
            }
            Object innerData = resultMap.get("data");
            if (innerData instanceof Map) {
                thoughtData = (Map<String, Object>)innerData;
            }
        }

        return thoughtData;
    }

}
