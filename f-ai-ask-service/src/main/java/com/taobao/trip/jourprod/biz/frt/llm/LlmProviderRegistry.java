package com.taobao.trip.jourprod.biz.frt.llm;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.stereotype.Component;

/**
 * LLM 提供者注册中心
 * <AUTHOR>
 */
@Component
public class LlmProviderRegistry {
    private final Map<String, LlmProvider> providers = new ConcurrentHashMap<>();

    /**
     * 注册提供者
     */
    public void registerProvider(LlmProvider provider) {
        providers.put(provider.getProviderName(), provider);
    }

    /**
     * 获取提供者
     */
    public LlmProvider getProvider(String providerName) {
        return providers.get(providerName);
    }
}
