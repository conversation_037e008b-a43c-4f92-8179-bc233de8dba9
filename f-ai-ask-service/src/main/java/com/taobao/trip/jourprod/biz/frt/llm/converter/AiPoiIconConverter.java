package com.taobao.trip.jourprod.biz.frt.llm.converter;

import com.taobao.trip.jourprod.service.facade.model.request.journeyplan.AiPoiIconParameter;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiPoiIconDTO;
import com.taobao.trip.jourprod.domain.AiPoiIconDO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@Service
public class AiPoiIconConverter {

    /**
     * DTO模型转换成DO模型
     *
     * @param aiPoiIconDTO
     */
    public AiPoiIconDO convertFromDTO(AiPoiIconDTO aiPoiIconDTO) {
        AiPoiIconDO aiPoiIconDO = new AiPoiIconDO();
        BeanUtils.copyProperties(aiPoiIconDTO, aiPoiIconDO);
        return aiPoiIconDO;
    }

    /**
     * DO模型转换成DTO模型
     *
     * @param aiPoiIconDO
     */
    public AiPoiIconDTO convertFromDO(AiPoiIconDO aiPoiIconDO) {
        AiPoiIconDTO aiPoiIconDTO = new AiPoiIconDTO();
        BeanUtils.copyProperties(aiPoiIconDO, aiPoiIconDTO);
        return aiPoiIconDTO;
    }

    /**
     * DO模型转换成DTO模型
     *
     * @param parameter
     */
    public AiPoiIconDO convertFromParam(AiPoiIconParameter parameter) {
        AiPoiIconDO aiPoiIconDO = new AiPoiIconDO();
        BeanUtils.copyProperties(parameter, aiPoiIconDO);
        return aiPoiIconDO;
    }
}