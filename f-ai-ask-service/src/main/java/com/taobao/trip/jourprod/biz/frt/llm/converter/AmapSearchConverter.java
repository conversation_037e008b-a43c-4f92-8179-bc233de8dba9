package com.taobao.trip.jourprod.biz.frt.llm.converter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alitrip.aisearch.model.search.poi.entity.PoiInfo;
import com.fliggy.fai.client.fsg.response.SearchDataStreamChunk;
import com.google.common.collect.Maps;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.PoiInfoExtend;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class AmapSearchConverter extends FusionSearchBaseConvert<Map<String, PoiInfoExtend>> {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(AmapSearchConverter.class);
    @Override
    Map<String, Map<String, PoiInfoExtend>> convert(SearchDataStreamChunk chunk) {
        Map<String, Map<String, PoiInfoExtend>> result = Maps.newHashMap();
        try {
            List<Map<String, Object>> recall = chunk.getRecall();
            if (Objects.isNull(recall)) {
                return null;
            }
            Map<String, PoiInfoExtend> collect = Maps.newHashMap();
            recall.stream().map(map -> {
                Object amapInfo = map.get("info");
                if (Objects.isNull(amapInfo)) {
                    return null;
                }
                JSONObject amapJson = JSON.parseObject(JSON.toJSONString(amapInfo));
                PoiInfoExtend poiInfo = new PoiInfoExtend();
                poiInfo.setSource(amapJson.getString("source"));
                poiInfo.setPoiId(amapJson.getString("id"));
                poiInfo.setPoiName(amapJson.getString("name"));
                if (StringUtils.isNotBlank(amapJson.getString("location"))) {
                    String[] location = amapJson.getString("location").split(",");
                    poiInfo.setPoiLng(location[0]);
                    poiInfo.setPoiLat(location[1]);
                }
                JSONObject business = amapJson.getJSONObject("business");
                if (business != null) {
                    poiInfo.setOpeningTime(business.getString("opentime_week"));
                    poiInfo.setPoiRating(business.getString("rating"));
                }
                JSONArray photos = amapJson.getJSONArray("photos");
                if (CollectionUtils.isNotEmpty(photos)) {
                    poiInfo.setPoiImg(photos.getJSONObject(0).getString("url"));
                    poiInfo.setPhotos(photos.stream().map(photo -> ((JSONObject) photo).getString("url")).collect(Collectors.toList()));
                }
                poiInfo.setDistance(amapJson.getString("distance"));
                poiInfo.setAddress(amapJson.getString("address"));
                return poiInfo;
            }).filter(Objects::nonNull).filter(poiInfo -> Objects.nonNull(poiInfo.getPoiId())).forEach(poiInfo -> collect.put(poiInfo.getPoiId(), poiInfo));
            result.put(chunk.getCategory(), collect);
        } catch (Throwable e) {
            LOGGER.recordNormalException(new LogModel("AmapSearchConverter.convert").e(e).message("解析高德poi数据失败"));
        } finally {
            LOGGER.recordOutput(new LogModel("AmapSearchConverter")
                    .request(JSONUtil.toJSONStringForLog(chunk))
                    .response(JSONUtil.toJSONStringForLog(result)));
        }
        return result;
    }

    @Override
    protected void register() {
        convertMap.put("amap", this);
    }

    public AmapSearchConverter() {
        register();
    }
}
