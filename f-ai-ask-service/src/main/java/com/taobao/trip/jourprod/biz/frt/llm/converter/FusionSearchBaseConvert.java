package com.taobao.trip.jourprod.biz.frt.llm.converter;

import com.fliggy.fai.client.fsg.response.AISearchData;
import com.fliggy.fai.client.fsg.response.SearchDataStreamChunk;
import com.google.common.collect.Maps;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantHelper;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;

import java.util.Map;

public abstract class FusionSearchBaseConvert<T> {
    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(FusionSearchBaseConvert.class);
    protected static Map<String, FusionSearchBaseConvert> convertMap = Maps.newHashMap();

    /**
     * 对外调用
     *
     * @param sceneType
     * @param chunk
     * @return
     */
    public static Map<String, Object> convert(String sceneType, SearchDataStreamChunk chunk) {
        if (convertMap.get(sceneType) == null) {
            LOGGER.recordNormalException(new LogModel("FusionSearchBaseConvert.convert").message("场景转换器不存在").request(sceneType));
            return null;
        }
        return convertMap.get(sceneType).convert(chunk);
    }

    public static Map<String, Object> convert(String sceneType, AISearchData aiSearchData) {
        if (convertMap.get(sceneType) == null) {
            LOGGER.recordNormalException(new LogModel("FusionSearchBaseConvert.convert2").message("场景转换器不存在").request(sceneType));
            return null;
        }
        SearchDataStreamChunk searchDataStreamChunk = new SearchDataStreamChunk();
        searchDataStreamChunk.setCategory(aiSearchData.getCategory());
        searchDataStreamChunk.setRecall(aiSearchData.getRecall());
        searchDataStreamChunk.setSummary(aiSearchData.getSummary());
        return convertMap.get(sceneType).convert(searchDataStreamChunk);
    }

    /**
     * 子类需要实现转换逻辑
     *
     * @param chunk
     * @return
     */
    abstract Map<String, T> convert(SearchDataStreamChunk chunk);

    /**
     * 注册场景转换器
     *
     */
    protected abstract void register();


}
