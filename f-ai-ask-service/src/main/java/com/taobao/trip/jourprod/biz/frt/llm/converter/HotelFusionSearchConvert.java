package com.taobao.trip.jourprod.biz.frt.llm.converter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alitrip.aisearch.model.search.hotel.entity.HotelInfo;
import com.fliggy.fai.client.fsg.response.SearchDataStreamChunk;
import com.google.common.collect.Maps;
import com.taobao.trip.jourprod.utils.JSONUtil;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 酒店召回结果转换器
 * <AUTHOR>
 */
@Component
public class HotelFusionSearchConvert extends FusionSearchBaseConvert<Map<String, HotelInfo>> {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(HotelFusionSearchConvert.class);

    @Override
    Map<String, Map<String, HotelInfo>> convert(SearchDataStreamChunk chunk) {
        Map<String, HotelInfo> hotelInfoMap = Maps.newHashMap();
        Map<String, Map<String, HotelInfo>> result = Maps.newHashMap();
        try {
            if (Objects.isNull(chunk) || Objects.isNull(chunk.getSummary())) {
                return null;
            }
            hotelInfoMap = chunk.getSummary().stream()
                    .filter(Objects::nonNull)
                    .filter(t -> t.get("bizId") != null)
                    .filter(t -> t.get("info") != null)
                    .collect(Collectors.toMap(t -> t.get("bizId").toString(), t -> JSONObject.parseObject(JSON.toJSONString(t.get("info")), HotelInfo.class), (existing, replacement) -> existing));
            result.put(chunk.getCategory(), hotelInfoMap);
            return result;
        } catch (Throwable e) {
            LOGGER.recordNormalException(new LogModel("HotelFusionSearchConvert").e(e).message("解析酒店数据失败"));
        } finally {
            LOGGER.recordOutput(new LogModel("HotelFusionSearchConvert")
                    .message("酒店-召回数量-{0}", hotelInfoMap.size())
                    .request(JSONUtil.toJSONStringForLog(chunk))
                    .response(JSONUtil.toJSONStringForLog(result)));
        }
        return null;
    }

    public HotelFusionSearchConvert() {
        register();
    }

    @Override
    protected void register() {
        convertMap.put("hotel", this);
    }
}
