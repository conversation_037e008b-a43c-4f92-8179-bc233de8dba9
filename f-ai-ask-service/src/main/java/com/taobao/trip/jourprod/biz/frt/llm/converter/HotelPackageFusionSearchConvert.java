package com.taobao.trip.jourprod.biz.frt.llm.converter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alitrip.aisearch.model.search.hotel_pkg.entity.HotelPkgInfo;
import com.fliggy.fai.client.fsg.response.SearchDataStreamChunk;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 酒店召回结果转换器
 * <AUTHOR>
 */
@Component
public class HotelPackageFusionSearchConvert extends FusionSearchBaseConvert<Map<String, HotelPkgInfo>> {

    @Override
    Map<String, Map<String, HotelPkgInfo>> convert(SearchDataStreamChunk chunk) {
        if (CollectionUtils.isEmpty(chunk.getSummary())) {
            return null;
        }
        Map<String, HotelPkgInfo> hotelPkgInfoMap = chunk.getSummary().stream()
                .filter(Objects::nonNull)
                .filter(t -> t.get("bizId") != null)
                .collect(Collectors.toMap(t -> t.get("bizId").toString(), t -> JSONObject.parseObject(JSON.toJSONString(t.get("info")), HotelPkgInfo.class), (existing, replacement) -> existing));

        Map<String, Map<String, HotelPkgInfo>> result = Maps.newHashMap();
        result.put(chunk.getCategory(), hotelPkgInfoMap);
        return result;
    }

    public HotelPackageFusionSearchConvert() {
        register();
    }

    @Override
    protected void register() {
        convertMap.put("hotel_package", this);
    }
}
