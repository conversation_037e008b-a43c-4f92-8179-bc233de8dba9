package com.taobao.trip.jourprod.biz.frt.llm.converter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alitrip.aisearch.model.search.dest.entity.DestInfo;
import com.alitrip.aisearch.model.search.hotel.entity.HotelInfo;
import com.fliggy.fai.client.fsg.response.SearchDataStreamChunk;
import com.google.common.collect.Maps;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.mdd.MddDestInfo;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * 目的地 召回结果转换器
 */
@Component
public class MddFusionSearchConvert extends FusionSearchBaseConvert<Map<String, MddDestInfo>> {

    private final static FliggyNewLogger logger = LogUtil.getFliggyNewLogger("MddFusionSearchConvert");

    public static final String DEST_CATEGORY = "dest";
    private static final String SPLIT = "_";
    private static final String BIZ_ID = "bizId";
    private static final String INFO = "info";

    @Override
    public Map<String, Map<String, MddDestInfo>> convert(SearchDataStreamChunk chunk) {
        Map<String, Map<String, MddDestInfo>> result = Maps.newHashMap();
        try {
            Map<String, MddDestInfo> mddMap = chunk.getSummary().stream()
                    .filter(info -> info.get(BIZ_ID).toString().split(SPLIT).length == 2)
                    .collect(Collectors.toMap(t -> t.get(BIZ_ID).toString(), t -> JSONObject.parseObject(JSON.toJSONString(t.get(INFO)), MddDestInfo.class), (key1, key2) -> key1));

            result.put(chunk.getCategory(), mddMap);
        } catch (Exception e) {
            logger.recordDangerException(new LogModel("MddFusionSearchConvert.convert").e(e));
        }
        return result;
    }

    public MddFusionSearchConvert() {
        register();
    }

    @Override
    protected void register() {
        convertMap.put(DEST_CATEGORY, this);
    }
}
