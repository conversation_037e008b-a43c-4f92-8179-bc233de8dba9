package com.taobao.trip.jourprod.biz.frt.llm.converter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alitrip.aisearch.model.search.visa.entity.VisaInfo;
import com.fliggy.fai.client.fsg.response.SearchDataStreamChunk;
import com.google.common.collect.Maps;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * 目的地签证 召回结果转换器
 */
@Component
public class MddVisaFusionSearchConvert extends FusionSearchBaseConvert<Map<String, VisaInfo>> {

    private final static FliggyNewLogger logger = LogUtil.getFliggyNewLogger("MddVisaFusionSearchConvert");

    public static final String VISA_CATEGORY = "visa";
    private static final String BIZ_ID = "bizId";
    private static final String INFO = "info";

    @Override
    public Map<String, Map<String, VisaInfo>> convert(SearchDataStreamChunk chunk) {
        Map<String, Map<String, VisaInfo>> result = Maps.newHashMap();
        try {
            Map<String, VisaInfo> mddMap = chunk.getRecall().stream()
                    .collect(Collectors.toMap(t -> t.get(BIZ_ID).toString(), t -> JSONObject.parseObject(JSON.toJSONString(t.get(INFO)), VisaInfo.class), (key1, key2) -> key1));

            result.put(chunk.getCategory(), mddMap);
        } catch (Exception e) {
            logger.recordDangerException(new LogModel("MddVisaFusionSearchConvert.convert").e(e));
        }
        return result;
    }

    public MddVisaFusionSearchConvert() {
        register();
    }

    @Override
    protected void register() {
        convertMap.put(VISA_CATEGORY, this);
    }
}
