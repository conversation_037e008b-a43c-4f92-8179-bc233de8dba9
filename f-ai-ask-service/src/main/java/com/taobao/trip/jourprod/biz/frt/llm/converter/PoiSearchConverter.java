package com.taobao.trip.jourprod.biz.frt.llm.converter;

import com.ali.com.google.common.collect.Lists;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alitrip.aisearch.model.search.poi.entity.PoiInfo;
import com.fliggy.fai.client.fsg.response.SearchDataStreamChunk;
import com.google.common.collect.Maps;
import com.taobao.trip.jourprod.utils.JSONUtil;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class PoiSearchConverter extends FusionSearchBaseConvert<Map<String, PoiInfo>>{

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(PoiSearchConverter.class);

    @Override
    Map<String, Map<String, PoiInfo>> convert(SearchDataStreamChunk chunk) {
        Map<String, Map<String, PoiInfo>> result = Maps.newHashMap();
        List<PoiInfo> poiInfos = Lists.newArrayList();
        try {
            List<Map<String, Object>> summary = chunk.getSummary();
            if (Objects.isNull(summary)) {
                return null;
            }
            poiInfos = summary.stream().map(map -> {
                Object info = map.get("info");
                if (Objects.isNull(info)) {
                    return null;
                }
                return JSONObject.parseObject(JSON.toJSONString(info), PoiInfo.class);
            }).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(poiInfos)) {
                return null;
            }

            Map<String, PoiInfo> poiMap = Maps.newHashMap();
            for (PoiInfo poiInfo : poiInfos) {
                if (Objects.isNull(poiInfo) || Objects.isNull(poiInfo.getPoiId())) {
                    continue;
                }
                poiMap.put(poiInfo.getPoiId(), poiInfo);
            }

            result.put(chunk.getCategory(), poiMap);
            return result;
        } catch (Throwable e) {
            LOGGER.recordNormalException(new LogModel("PoiSearchConverter.convert").e(e).message("解析POI数据失败"));
        } finally {
            LOGGER.recordOutput(new LogModel("poi_recall_result")
                    .message("poi-召回数量-{0}", poiInfos.size())
                    .request(JSONUtil.toJSONStringForLog(chunk))
                    .response(JSONUtil.toJSONStringForLog(result)));
        }
        return null;
    }

    public PoiSearchConverter() {
        register();
    }

    @Override
    protected void register() {
        convertMap.put("poi", this);
    }
}
