package com.taobao.trip.jourprod.biz.frt.llm.converter;

import com.ali.com.google.common.collect.Lists;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fliggy.fai.client.fsg.response.SearchDataStreamChunk;
import com.google.common.collect.Maps;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiRouteInfo;
import com.taobao.trip.jourprod.utils.JSONUtil;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 酒店召回结果转换器
 *
 * <AUTHOR>
 */
@Component
public class RouteFusionSearchConvert extends FusionSearchBaseConvert<Map<String, AiRouteInfo.Poi>> {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(RouteFusionSearchConvert.class);

    @Override
    Map<String, Map<String, AiRouteInfo.Poi>> convert(SearchDataStreamChunk chunk) {
        Map<String, Map<String, AiRouteInfo.Poi>> result = Maps.newHashMap();
        List<AiRouteInfo> aiRouteInfos = Lists.newArrayList();
        try {
            List<Map<String, Object>> summary = chunk.getSummary();
            if (Objects.isNull(summary)) {
                return null;
            }
            aiRouteInfos = JSONObject.parseArray(JSON.toJSONString(summary), AiRouteInfo.class);
            if (CollectionUtils.isEmpty(aiRouteInfos)) {
                return null;
            }

            Map<String, AiRouteInfo.Poi> poiMap = Maps.newHashMap();
            for (AiRouteInfo aiRouteInfo : aiRouteInfos) {
                if (Objects.isNull(aiRouteInfo) || Objects.isNull(aiRouteInfo.getInfo())) {
                    continue;
                }
                List<AiRouteInfo.Route> routeList = aiRouteInfo.getInfo().getRouteList();
                if (CollectionUtils.isEmpty(routeList)) {
                    continue;
                }
                Map<String, AiRouteInfo.Poi> childPoiMap = routeList.stream().flatMap(route -> route.getPoiList().stream()).collect(Collectors.toMap(
                        AiRouteInfo.Poi::getPoiId,  // 提取 Poi 的 ID 作为 Key
                        poi -> poi,     // Poi 对象本身作为 Value
                        (existing, replacement) -> existing // 如果有重复 Key，保留现有值
                ));
                poiMap.putAll(childPoiMap);
            }

            result.put(chunk.getCategory(), poiMap);
            return result;
        } catch (Throwable e) {
            LOGGER.recordNormalException(new LogModel("RouteFusionSearchConvert").e(e).message("解析行程规划数据失败"));
        } finally {
            LOGGER.recordOutput(new LogModel("RouteFusionSearchConvert")
                    .message("路线-召回数量-{0}", aiRouteInfos.size())
                    .request(JSONUtil.toJSONStringForLog(chunk))
                    .response(JSONUtil.toJSONStringForLog(result)));
        }
        return null;
    }

    public RouteFusionSearchConvert() {
        register();
    }

    @Override
    protected void register() {
        convertMap.put("route", this);
    }
}
