package com.taobao.trip.jourprod.biz.frt.llm.converter;

import com.alibaba.fastjson.JSON;
import com.alitrip.aisearch.model.facade.DocSummary;
import com.alitrip.aisearch.model.search.traffic.entity.TrafficSolutionItem;
import com.fliggy.fai.client.fsg.response.SearchDataStreamChunk;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl.TrafficNodeProcessor;
import com.taobao.trip.jourprod.utils.JSONUtil;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025/3/26 16:52
 * @desc
 */
@Component
public class TrafficFusionSearchConvert extends FusionSearchBaseConvert<Map<String, TrafficSolutionItem>> {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(TrafficNodeProcessor.class);

    public TrafficFusionSearchConvert() {
        register();
    }

    public static final String TRAFFIC_CATEGORY = "traffic";

    @Override
    Map<String, Map<String, TrafficSolutionItem>> convert(SearchDataStreamChunk chunk) {
        Map<String, Map<String, TrafficSolutionItem>> result = Maps.newHashMap();
        try {
            if (chunk.getRecall() == null) {
                result.put(chunk.getCategory(), Maps.newHashMap());
                return result;
            }
            List<Map<String, Object>> recall = chunk.getRecall();
            List<DocSummary<TrafficSolutionItem>> trafficGroupItems = Lists.newArrayList();
            for (Map<String, Object> map : recall) {
                if (MapUtils.isEmpty(map) || StringUtils.isBlank(MapUtils.getString(map, "bizId"))) {
                    continue;
                }
                DocSummary<TrafficSolutionItem> trafficGroupItem = new DocSummary<>();
                trafficGroupItem.setBizId((String) map.get("bizId"));
                trafficGroupItem.setInfo(JSON.parseObject(JSON.toJSONString(map.get("info")), TrafficSolutionItem.class));
                trafficGroupItems.add(trafficGroupItem);
            }
            if (CollectionUtils.isEmpty(trafficGroupItems)) {
                result.put(chunk.getCategory(), Maps.newHashMap());
                return result;
            }
            Map<String, TrafficSolutionItem> trafficGroupItemMap = trafficGroupItems.stream().collect(Collectors.toMap(DocSummary::getBizId, DocSummary::getInfo, (v1, v2) -> v1));
            result.put(chunk.getCategory(), trafficGroupItemMap);
            return result;
        } catch (Exception e) {
            LOGGER.recordNormalException(new LogModel("TrafficFusionSearchConvert").e(e).message("TrafficFusionSearchConvert_exception"));
        } finally {
            LOGGER.recordOutput(new LogModel("TrafficFusionSearchConvert")
                    .message("交通-召回数量-{0}", 0)
                    .request(JSONUtil.toJSONStringForLog(chunk))
                    .response(JSON.toJSONString(result)));
        }
        return null;
    }

    @Override
    protected void register() {
        convertMap.put(TRAFFIC_CATEGORY, this);
    }
}
