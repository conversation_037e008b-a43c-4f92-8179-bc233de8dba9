package com.taobao.trip.jourprod.biz.frt.llm.converter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alitrip.aisearch.model.search.poi.entity.PoiInfo;
import com.alitrip.aisearch.model.search.vacation.entity.VacationInfo;
import com.fliggy.fai.client.fsg.response.SearchDataStreamChunk;
import com.google.common.collect.Maps;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class VacationSearchConverter extends FusionSearchBaseConvert<Map<String, VacationInfo>>{

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(VacationSearchConverter.class);

    @Override
    Map<String, Map<String, VacationInfo>> convert(SearchDataStreamChunk chunk) {
        Map<String, Map<String, VacationInfo>> result = Maps.newHashMap();
        try {
            List<Map<String, Object>> summary = chunk.getSummary();
            if (Objects.isNull(summary)) {
                return null;
            }
            Map<String, VacationInfo> vacationInfoMap = Maps.newHashMap();
            for (Map<String, Object> stringObjectMap : summary) {
                Object summaryInfo = stringObjectMap.get("info");
                if (Objects.isNull(summaryInfo)) {
                    continue;
                }
                VacationInfo vacationInfo = JSONObject.parseObject(JSON.toJSONString(summaryInfo), VacationInfo.class);
                if (Objects.isNull(vacationInfo) || Objects.isNull(vacationInfo.getVacationId())) {
                    continue;
                }
                vacationInfoMap.put(vacationInfo.getVacationId(), vacationInfo);
            }
            result.put(chunk.getCategory(), vacationInfoMap);
            return result;
        } catch (Throwable e) {
            LOGGER.recordNormalException(new LogModel("VacationSearchConverter.convert").e(e).message("解析度假数据失败"));
        } finally {
            LOGGER.recordOutput(new LogModel("VacationSearchConverter")
                    .request(JSON.toJSONString(chunk))
                    .response(JSON.toJSONString(result)));
        }
        return null;
    }

    @Override
    protected void register() {
        convertMap.put("vacation", this);
    }

    public VacationSearchConverter() {
        register();
    }
}
