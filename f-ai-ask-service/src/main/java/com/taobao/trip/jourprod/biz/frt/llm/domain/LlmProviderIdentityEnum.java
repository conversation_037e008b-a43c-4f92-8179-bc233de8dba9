package com.taobao.trip.jourprod.biz.frt.llm.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 大模型服务提供者身份
 */
@Getter
@AllArgsConstructor
public enum LlmProviderIdentityEnum {


    DOUYIN_DEEPSEEK("douyin_deepseek", "抖音的火山方舟deepseek模型", "https://www.volcengine.com/experience/ark"),
    FAI_DEEPSEE_WORKFLOW("fai_deepseek_workflow", "FAI的deepseek模型，走的是workflow调用", "https://fai.alibaba-inc.com/#/project/632/studio"),
    WHALE_DEEPSEEK("whale_deepseek", "whale的deepseek模型", "https://whale.online.alibaba-inc.com/service?tab=project"),
    FAI_GATEWAY("fai_gateway", "fai直连网关", ""),
    AI_SEARCH("ai_search", "ai搜索", ""),
    AI_STRATEGY("ai_strategy", "ai策略搜索", ""),
    CHIT_CHAT("chit_chat", "闲聊", "")
    ;

    /** 名称 **/
    private final String name;

    /** 描述 **/
    private final String desc;

    /** 访问链接 **/
    private final String url;

    /**
     * 根据名称获取
     * @param name
     * @return
     */
    public static LlmProviderIdentityEnum getByCode(String name) {
        for (LlmProviderIdentityEnum identityEnum : LlmProviderIdentityEnum.values()) {
            if (identityEnum.name().equals(name)) {
                return identityEnum;
            }
        }
        return null;
    }

}
