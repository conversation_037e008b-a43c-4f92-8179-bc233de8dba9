package com.taobao.trip.jourprod.biz.frt.llm.domain;

import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneySceneModel;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatRequest;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * LLM 请求对象
 *
 * <AUTHOR>
 */
@Data
public class LlmRequest {

    /* ========================= 通用模型使用 start ========================= */
    /**
     * 模型的key，可根据传入账号
     **/
    private String apiKey;

    /**
     * 如果是普通模型就传模型的推理接入点ID，如果是应用就传应用botId
     **/
    private String modelId;
    /* ========================= 通用模型使用  end  ========================= */

    /* ========================= FAI模型使用 start ========================= */
    /**
     * 项目应用ID
     **/
    private Long appId;

    /**
     * 流程ID
     **/
    private Long flowId;
    private Map<String, Object> variables;
    /* ========================= FAI模型使用  end  ========================= */

    /**
     * 会话SessionID
     **/
    private String sessionId;
    /**
     * 上下文列表
     **/
    private List<MessageParam> messages;

    /**
     * 参数
     **/
    private JourneyPlanAiChatRequest journeyPlanAiChatRequest;
    private AiJourneySceneModel aiJourneySceneModel;
    private ChatContext chatContext;
    /**
     * 是否完成上下文
     */
    public Boolean completedContext = false;
}
