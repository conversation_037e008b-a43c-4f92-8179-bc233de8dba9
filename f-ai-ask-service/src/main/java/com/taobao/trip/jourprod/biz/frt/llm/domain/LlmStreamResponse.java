package com.taobao.trip.jourprod.biz.frt.llm.domain;

import lombok.Data;

import java.util.Map;

/**
 * 大模型服务 流式调用响应对象
 */
@Data
public class LlmStreamResponse {
    /**
     * 会话id,目前抖音deepseek模型在对话场景中无session概念，暂时不传
     */
    private String sessionId;

    /**
     * 推理过程
     */
    private String thinking;

    /**
     * 响应内容
     */
    private String content;

    /**
     * 思考链数据
     */
    private Map<String, Object> thinkingThought;
}
