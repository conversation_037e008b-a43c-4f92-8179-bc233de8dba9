package com.taobao.trip.jourprod.biz.frt.llm.provider;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fliggy.fai.client.fsg.request.FsgTenant;
import com.fliggy.fai.client.fsg.request.FusionSearchGenerateRequest;
import com.fliggy.fai.client.fsg.request.SearchIntentDTO;
import com.fliggy.fai.client.fsg.response.ContentStreamChunk;
import com.fliggy.fai.client.fsg.response.FsgStreamChunk;
import com.fliggy.fai.client.fsg.response.SearchDataStreamChunk;
import com.fliggy.fai.client.fsg.response.ThinkingStreamChunk;
import com.fliggy.fai.client.fsg.service.FusionSearchGenerateClientService;
import com.google.common.collect.Lists;
import com.taobao.eagleeye.EagleEye;
import com.taobao.eagleeye.RpcContext_inner;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneySceneModel;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.biz.frt.llm.LlmProvider;
import com.taobao.trip.jourprod.biz.frt.llm.converter.FusionSearchBaseConvert;
import com.taobao.trip.jourprod.biz.frt.llm.domain.*;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatRequest;
import com.taobao.trip.wireless.Ssif;
import com.taobao.trip.wireless.domain.SsifContext;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.stream.StreamObserver;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl.PlayMethodNodeProcessor.*;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantHelper.AI_JOURNEY_SCENE_CODE_AISEARCH_CODE_MAP;

/**
 * @Description 融合搜索实现类
 * <AUTHOR>
 * @Date 2025/3/23
 **/
@Component
public class AiSearchLlmProvider implements LlmProvider {

    private final static FliggyNewLogger logger = LogUtil.getFliggyNewLogger("AiSearchLlmProvider");

    @Resource
    private FusionSearchGenerateClientService fusionSearchGenerateClientService;

    //用户当前lbs城市
    private static final String CUR_LBS_CITY_NAME = "curLbsCityName";

    /**
     * 普通调用
     *
     * @param request
     */
    @Override
    public LlmResponse execute(LlmRequest request) {
        return null;
    }

    /**
     * 流式调用
     *
     * @param request  请求对象
     * @param callback 流式响应回调
     */
    @Override
    public void executeStream(LlmRequest request, LlmStreamResponseCallback callback) throws Throwable {
        FusionSearchGenerateRequest fusionSearchGenerateRequest = buildFusionSearchGenerateRequest(request);
        logger.recordOutput(new LogModel("AiSearchLlmProvider.executeStream").message("AiSearch-请求入参").request(JSON.toJSONString(fusionSearchGenerateRequest)));
        CountDownLatch countDownLatch = new CountDownLatch(1);
        RpcContext_inner rpcContext = EagleEye.getRpcContext();
        Map<String, String> mdcContext = MDC.getCopyOfContextMap();
        SsifContext ssifContext = Ssif.get();
        fusionSearchGenerateClientService.stream(fusionSearchGenerateRequest, new StreamObserver<FsgStreamChunk>() {
            @Override
            public void onNext(FsgStreamChunk data) {
                try {
                    EagleEye.setRpcContext(rpcContext);
                    MDC.setContextMap(mdcContext);
                    Ssif.putSsifContext(ssifContext);
                    if (data instanceof SearchDataStreamChunk) {
                        //召回的数据
                        SearchDataStreamChunk chunk = (SearchDataStreamChunk) data;
                        // 日志埋点，排查问题需要
                        doTraceLog(request, chunk);
                        Map<String, Object> convertData = FusionSearchBaseConvert.convert(chunk.getCategory(), chunk);
                        if (MapUtils.isEmpty(convertData)) {
                            return;
                        }
                        Map<String, Object> internalData = request.getChatContext().getInternalData();
                        internalData.putAll(convertData);
                        logger.recordOutput(new LogModel("executeStream_onNext")
                                .request(JSONUtil.toJSONStringForLog(fusionSearchGenerateRequest))
                                .response(JSONUtil.toJSONStringForLog(data)));
                    } else if (data instanceof ThinkingStreamChunk) {
                        //大模型思考数据
                        LlmStreamResponse response = new LlmStreamResponse();
                        ThinkingStreamChunk chunk = (ThinkingStreamChunk) data;
                        if (chunk.getData() != null) {
                            response.setThinking(String.valueOf(chunk.getData()));
                            if (Objects.equals("ACTION", chunk.getMimeType())) {
                                try {
                                    JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(chunk.getData()));
                                    JSONObject thinkingThought = jsonObject.getJSONObject("action");
                                    thinkingThought = (JSONObject) convertThoughtData(thinkingThought);
                                    response.setThinkingThought(thinkingThought);
                                } catch (Exception e) {
                                    logger.recordNormalException(new LogModel("executeStream_ThinkingStreamChunk")
                                            .response(String.valueOf(chunk.getData()))
                                            .message("执行异常").e(e));
                                }
                            }
                            callback.onMessage(response);
                        }
                    } else if (data instanceof ContentStreamChunk) {
                        //正文数据
                        LlmStreamResponse response = new LlmStreamResponse();
                        ContentStreamChunk chunk = (ContentStreamChunk) data;
                        if (chunk.getData() != null) {
                            response.setContent(String.valueOf(chunk.getData()));
                            callback.onMessage(response);
                        }
                    }
                } catch (Throwable e) {
                    logger.recordNormalException(new LogModel("AiSearch_onNext")
                            .message("执行异常").e(e));
                } finally {
                    EagleEye.clearRpcContext();
                    MDC.clear();
                    Ssif.removeSsifContext();
                }

            }

            @SneakyThrows
            @Override
            public void onError(Throwable throwable) {
                try {
                    EagleEye.setRpcContext(rpcContext);
                    callback.onError(throwable);
                    countDownLatch.countDown();
                } finally {
                    EagleEye.clearRpcContext();
                    logger.recordNormalException(new LogModel("AiSearchLlmProvider.executeStream")
                            .message("AiSearch_Error-调用AiSearch执行异常").e(throwable));
                }
            }

            @Override
            public void onCompleted() {
                try {
                    EagleEye.setRpcContext(rpcContext);
                    callback.onComplete();
                    countDownLatch.countDown();
                } finally {
                    EagleEye.clearRpcContext();
                }
            }
        });

        logger.recordOutput(new LogModel("executeStream").message("await"));
        countDownLatch.await();
    }

    private void doTraceLog(LlmRequest request, SearchDataStreamChunk chunk) {
        String msg = StringUtils.EMPTY;
        String category = StringUtils.EMPTY;
        Integer summarySize = null;
        Integer recallSize = null;
        try {
            if (Objects.isNull(chunk) || Objects.isNull(chunk.getCategory())) {
                msg = "chunk is null";
                return;
            }
            category = chunk.getCategory();
            List<Map<String, Object>> summary = chunk.getSummary();
            if (CollectionUtils.isNotEmpty(summary)) {
                summarySize = summary.size();
            }
            List<Map<String, Object>> recall = chunk.getRecall();
            if (CollectionUtils.isNotEmpty(recall)) {
                recallSize = recall.size();
            }
        } finally {
            logger.recordOutput(new LogModel("AiSearchLlmProvider.doTraceLog").message(
                    "AiSearch召回数据集 - query-{0}, category-{1}, summarySize-{2}, recallSize-{3}, msg-{4}",
                    request.getJourneyPlanAiChatRequest().getChat(), category, summarySize, recallSize, msg
            ));
        }
    }


    /**
     * 构造融合搜素请求
     */
    private FusionSearchGenerateRequest buildFusionSearchGenerateRequest(LlmRequest request) {
        ChatContext chatContext = request.getChatContext();
        JourneyPlanAiChatRequest chatRequest = request.getJourneyPlanAiChatRequest();
        AiJourneySceneModel sceneModel = request.getAiJourneySceneModel();

        FusionSearchGenerateRequest fsgRequest = new FusionSearchGenerateRequest();

        fsgRequest.setTtid(chatRequest.getTtid());
        fsgRequest.setUserId(String.valueOf(chatRequest.getUserId()));
        fsgRequest.setUserInput(chatRequest.getChat());
        fsgRequest.setDeviceId(chatRequest.getDeviceId());
        fsgRequest.setLbsCityCode(chatContext.getCurrentLbsCityCode());
        fsgRequest.setLbsCityName(chatContext.getCurrentLbsCityName());
        fsgRequest.setLbsLatitude(chatRequest.getLatitude());
        fsgRequest.setLbsLongitude(chatRequest.getLongitude());
        // 测试期间强制设置为这个
        fsgRequest.setScenic(getAiSearchScenic(sceneModel));
        fsgRequest.setDebug(true);
        FsgTenant tennat = new FsgTenant();
        tennat.setTenantCode("fliggy");
        fsgRequest.setTenant(tennat);
        fsgRequest.setIntents(buildSearchIntentList(chatContext, sceneModel));
        // 构建自定义参数
        buildFeature(fsgRequest, chatContext, sceneModel);
        // 构建自定义query，临时方案
        buildFeatureQuery(fsgRequest, sceneModel);
        //构建历史消息
        buildHistory(fsgRequest, request);
        return fsgRequest;
    }

    /**
     * 构建QpQuery集合
     */
    private List<SearchIntentDTO> buildSearchIntentList(ChatContext chatContext, AiJourneySceneModel sceneModel) {
        // 默认会返回一个兜底的，保证一定会命中对应的召回场景
        List<SearchIntentDTO> defaultSearchIntentDTOS = Lists.newArrayList();
        SearchIntentDTO intentDTO = new SearchIntentDTO();
        intentDTO.setQpType(sceneModel.getSceneEnum().getSceneCode());
        intentDTO.setQuery(chatContext.getSummarizedChat());
        defaultSearchIntentDTOS.add(intentDTO);

        List<AiJourneySceneModel.QpQuery> qpQueryList = sceneModel.getQpQueryList();
        List<SearchIntentDTO> searchIntentDTOS = Optional.ofNullable(qpQueryList).orElse(Lists.newArrayList()).stream().filter(Objects::nonNull).map(qpQuery -> {
            SearchIntentDTO searchIntentDTO = new SearchIntentDTO();
            searchIntentDTO.setQpType(qpQuery.getQueryType());
            searchIntentDTO.setQuery(qpQuery.getQuery());
            return searchIntentDTO;
        }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(searchIntentDTOS)) {
            defaultSearchIntentDTOS.addAll(searchIntentDTOS);
        }

        return defaultSearchIntentDTOS;
    }

    /**
     * 构建历史消息
     *
     * @param fsgRequest
     */
    private void buildHistory(FusionSearchGenerateRequest fsgRequest, LlmRequest request) {
        //增加历史消息
        if (CollectionUtils.isNotEmpty(request.getMessages())) {
            if (fsgRequest.getFeatures() == null) {
                fsgRequest.setFeatures(new HashMap<>());
            }
            String historyMessages = JSONUtil.toJSONStringForLog(request.getMessages());
            logger.recordOutput(new LogModel("buildHistory")
                    .message("historyMessages").request(historyMessages));
            fsgRequest.getFeatures().put("historyMessages", JSONUtil.toJSONString(request.getMessages()));
        }
    }

    private void buildFeatureQuery(FusionSearchGenerateRequest fsgRequest, AiJourneySceneModel sceneModel) {
        try {
            if (Objects.isNull(sceneModel) || Objects.isNull(sceneModel.getSceneEnum())) {
                return;
            }
            // 只有行中规划场景才有这个
            if (AiJourneyPlanSceneEnum.READY_TO_PLAN.equals(sceneModel.getSceneEnum())) {
                Map<String, Object> entities = sceneModel.getEntities();
                if (MapUtils.isEmpty(entities)) {
                    return;
                }
                Object destinationCity = entities.get("destinationCity");
                if (Objects.isNull(destinationCity)) {
                    return;
                }
                JSONArray destinationCityMap = JSONObject.parseArray(JSON.toJSONString(destinationCity));
                if (CollectionUtils.isEmpty(destinationCityMap)) {
                    return;
                }
                Object departureCity = entities.get("departureCity");
                if (Objects.isNull(destinationCity)) {
                    return;
                }
                JSONArray departureCityMap = JSONObject.parseArray(JSON.toJSONString(departureCity));
                if (CollectionUtils.isEmpty(departureCityMap)) {
                    return;
                }

                // 获取出发地
                JSONObject departureCityJSONObject = departureCityMap.getJSONObject(0);
                JSONObject destinationCityJSONObject = destinationCityMap.getJSONObject(0);
                String departureName = MapUtils.getString(departureCityJSONObject, NAME);
                String destinationName = MapUtils.getString(destinationCityJSONObject, NAME);

                List<SearchIntentDTO> intents = fsgRequest.getIntents();
                // qp 已经有从出发地到目的地的query 手动加一个 从目的地到出发地的单程
                SearchIntentDTO goRoute = new SearchIntentDTO();
                String departureDate = sceneModel.getDepartureDate();
                if (StringUtils.isNotBlank(departureDate)) {
                    goRoute.setQuery(String.format("%s从%s到%s的交通推荐", departureDate, departureName, destinationName));
                } else {
                    goRoute.setQuery(String.format("从%s到%s的交通推荐", departureName, destinationName));
                }
                goRoute.setQpType("journey_traffic_go");
                intents.add(goRoute);

                // qp 已经有从出发地到目的地的query 手动加一个 从目的地到出发地的单程
                String backDate = sceneModel.getBackDate();
                SearchIntentDTO returnRoute = new SearchIntentDTO();
                if (StringUtils.isNotBlank(backDate)) {
                    returnRoute.setQuery(String.format("%s从%s到%s的交通推荐", backDate, destinationName, departureName));
                } else {
                    returnRoute.setQuery(String.format("从%s到%s的交通推荐", destinationName, departureName));
                }
                returnRoute.setQpType("journey_traffic_return");
                intents.add(returnRoute);
            }
        } catch (Exception e) {
            logger.recordNormalException(new LogModel("buildFeatureQuery").message("getInternalData error").e(e));
        }
    }

    private void buildFeature(FusionSearchGenerateRequest fsgRequest, ChatContext chatContext, AiJourneySceneModel sceneModel) {
        HashMap<String, Object> features = new HashMap<>();
        features.put(CUR_LBS_CITY_NAME, chatContext.getCurrentLbsCityName());

        buildFeatureSubByDestinationCity(features, sceneModel);

        if (MapUtils.isNotEmpty(features)) {
            fsgRequest.setFeatures(features);
        }
    }

    private void buildFeatureSubByDestinationCity(Map<String, Object> features, AiJourneySceneModel sceneModel) {
        if (Objects.isNull(features)) {
            return;
        }
        try {
            Map<String, Object> entities = sceneModel.getEntities();
            if (MapUtils.isEmpty(entities)) {
                return;
            }
            Object destinationCity = entities.get("destinationCity");
            if (Objects.isNull(destinationCity)) {
                return;
            }
            JSONArray destinationCityArray = JSONObject.parseArray(JSON.toJSONString(destinationCity));
            if (CollectionUtils.isEmpty(destinationCityArray)) {
                return;
            }

            features.put("destinationCityCount", destinationCityArray.size());
            features.put("departureDate", sceneModel.getDepartureDate());
            features.put("backDate", sceneModel.getBackDate());
            features.put("playDays", sceneModel.getPlayDays());

            //设置意图出发地
            List<Map<String, Object>> departureCityList = (List<Map<String, Object>>) MapUtils.getObject(entities, DEPARTURE_CITY);
            if (CollectionUtils.isNotEmpty(departureCityList)) {
                Map<String, Object> departureCity = departureCityList.get(0);
                String name = MapUtils.getString(departureCity, NAME);
                if (StringUtils.isNotBlank(name)) {
                    features.put(TRAFFIC_DEP_NAME, name);
                }
            }

        } catch (Exception e) {
            logger.recordNormalException(new LogModel("buildFusionSearchGenerateRequest").message("getInternalData error").e(e));
        }
    }

    private String getAiSearchScenic(AiJourneySceneModel sceneModel) {
        if (Objects.isNull(sceneModel) || Objects.isNull(sceneModel.getSceneEnum())) {
            return null;
        }
        return AI_JOURNEY_SCENE_CODE_AISEARCH_CODE_MAP.get(sceneModel.getSceneEnum().getSceneCode());
    }

    /**
     * 获取提供者名称
     */
    @Override
    public String getProviderName() {
        return LlmProviderIdentityEnum.AI_SEARCH.getName();
    }
}
