package com.taobao.trip.jourprod.biz.frt.llm.provider;

import com.alibaba.boot.switchcenter.annotation.SwitchGroup;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fliggy.aiworks.client.aiask.AiaskMessagingClientService;
import com.fliggy.aiworks.client.aiask.request.AiaskChatRequest;
import com.fliggy.aiworks.client.aiask.response.AiaskStreamChunk;
import com.fliggy.aiworks.client.aiask.response.chunk.ActionThinkingChunk;
import com.fliggy.aiworks.client.aiask.response.chunk.ContentStreamChunk;
import com.fliggy.fai.client.FaiResponse;
import com.fliggy.fai.client.fsg.response.AISearchData;
import com.fliggy.fai.client.fsg.service.FspAISearchService;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.eagleeye.EagleEye;
import com.taobao.eagleeye.RpcContext_inner;
import com.taobao.trip.jourprod.biz.common.FlowCutHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.DesensitizationUtil;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantMessageHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantMessageStatusHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.ThinkingThoughtValidatorHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneySceneModel;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.biz.frt.llm.LlmProvider;
import com.taobao.trip.jourprod.biz.frt.llm.converter.FusionSearchBaseConvert;
import com.taobao.trip.jourprod.biz.frt.llm.domain.*;
import com.taobao.trip.jourprod.common.sal.exception.AiAskInterruptException;
import com.taobao.trip.jourprod.common.sal.hsf.tair.MdbTairHelper;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatRequest;
import com.taobao.trip.jourprod.service.impl.journeyplannew.JourneyAssistantDataOperateService;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.stream.StreamObserver;
import org.apache.dubbo.rpc.StatusRpcException;
import org.apache.dubbo.rpc.TriRpcStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;

import static com.taobao.trip.jourprod.biz.frt.llm.domain.LlmProviderIdentityEnum.AI_STRATEGY;
import static org.apache.dubbo.rpc.TriRpcStatus.CANCELLED;

/**
 * @Description 编排工作流
 * <AUTHOR>
 * @Date 2025/3/29
 **/
@Component
@SwitchGroup
public class AiStrategyLlmProvider implements LlmProvider {

    private final static FliggyNewLogger logger = LogUtil.getFliggyNewLogger(AiStrategyLlmProvider.class);

    @Resource
    private AiaskMessagingClientService aiaskMessagingClientService;

    @Resource
    private FspAISearchService fspAISearchService;

    @Resource
    private MdbTairHelper mdbTairHelper;

    @Resource
    private JourneyPlanAiAssistantMessageHelper journeyPlanAiAssistantMessageHelper;

    @Resource
    protected JourneyPlanAiAssistantMessageStatusHelper messageStatusHelper;

    @Resource
    private FlowCutHelper flowCutHelper;

    @Switch(name = "TRAFFIC_DATA_CACHE_TIME", description = "交通数据缓存时间(s)")
    public static Integer TRAFFIC_DATA_CACHE_TIME = 30 * 60;

    @AppSwitch(des = "数据脱敏灰度比例", level = Level.p4)
    public static Integer DATA_DESENSITIZATION_GRAY = 0;

    @AppSwitch(des = "数据脱敏灰度白名单", level = Level.p4)
    public static List<String> DATA_DESENSITIZATION_GRAY_WHITE_LIST = Lists.newArrayList("**********");

    @Value("${fai.client.timeout}")
    private int faiClientTimeout;

    /**
     * 普通调用
     *
     * @param request
     */
    @Override
    public LlmResponse execute(LlmRequest request) {
        return null;
    }

    /**
     * 流式调用
     *
     * @param request  请求对象
     * @param callback 流式响应回调
     */
    @Override
    public void executeStream(LlmRequest request, LlmStreamResponseCallback callback) throws Throwable {
        // 记录开始时间，用于超时检测
        final long startTime = System.currentTimeMillis();

        ChatContext chatContext = request.getChatContext();
        String systemMessageId = chatContext.getSystemMessageId();
        String userMessageId = chatContext.getUserMessageId();
        String sessionId = chatContext.getSessionId();

        AiaskChatRequest aiAskChatRequest = buildAiaskChatRequest(request);
        logger.recordOutput(new LogModel("AiStrategyLlmProvider_executeStream").request(JSON.toJSONString(aiAskChatRequest)));
        //如果是手动停止，就不调stream
        if (messageStatusHelper.isStop(systemMessageId) || messageStatusHelper.isEnd(systemMessageId)) {
            logger.recordEntry(new LogModel("AiStrategyLlmProvider_executeStream")
                    .message("用户取消，不进行模型服务调用")
                    .request(String.join("，", sessionId, systemMessageId, userMessageId)));
            return;
        }
        CountDownLatch countDownLatch = new CountDownLatch(1);
        RpcContext_inner rpcContext = EagleEye.getRpcContext();
        aiaskMessagingClientService.stream(aiAskChatRequest, new StreamObserver<>() {
            @Override
            public void onNext(AiaskStreamChunk data) {
                executeWithContext(rpcContext, () -> {
                    LlmStreamResponse response = new LlmStreamResponse();
                    if (data instanceof ContentStreamChunk) {
                        //没有处理过上下文，查一遍塞入上下文
                        if (BooleanUtils.isFalse(request.getCompletedContext())) {
                            request.setCompletedContext(true);
                            handlerAiStrategyData(request);
                        }
                        ContentStreamChunk chunk = (ContentStreamChunk) data;
                        if (chunk.getData() != null) {
                            response.setContent(String.valueOf(chunk.getData()));
                            callback.onMessage(response);
                        }
                    } else if (data instanceof ActionThinkingChunk) {
                        ActionThinkingChunk chunk = (ActionThinkingChunk) data;
                        if (chunk.getData() != null) {
                            Map<String, Object> thoughtData = convertThoughtData(chunk.getData());
                            // 校验数据
                            if (!validateThoughtData(thoughtData)) {
                                logger.recordNormalException(new LogModel("onNext")
                                    .message("思考链数据校验不通过")
                                    .request(JSON.toJSONString(request)));
                                return;
                            }
                            response.setThinkingThought(thoughtData);
                            callback.onMessage(response);
                        }
                    }
                });
            }

            @SneakyThrows
            @Override
            public void onError(Throwable throwable) {
                executeWithContext(rpcContext, () -> {
                    // 获取当前时间，计算执行时间
                    long currentTime = System.currentTimeMillis();
                    long executionTime = currentTime - startTime;

                    TriRpcStatus workflowStatus = null;
                    if (throwable instanceof StatusRpcException) {
                        StatusRpcException statusRpcException = (StatusRpcException) throwable;
                        workflowStatus = statusRpcException.getStatus();
                    }

                    // 检查是否超时（超过600秒）
                    boolean isTimeout = executionTime > faiClientTimeout;

                    logger.recordNormalException(new LogModel("onError")
                            .message("AiAskMessagingClientService stream 执行异常" + (isTimeout ? "，执行超时" : ""))
                            .code("workflowStatus=" + JSON.toJSONString(workflowStatus))
                            .cost(executionTime)
                            .e(throwable));

                    // 如果执行时间超过300秒，强制设置为超时状态
                    if (isTimeout && workflowStatus == null) {
                        workflowStatus = TriRpcStatus.DEADLINE_EXCEEDED;
                        logger.recordNormalException(new LogModel("onError_timeout")
                                .message("执行时间超过300秒，强制设置为超时状态")
                                .code(String.valueOf(executionTime)));
                    }

                    if (workflowStatus == null) {
                        AiAskInterruptException exception = new AiAskInterruptException(throwable, TriRpcStatus.UNKNOWN, "未知异常");
                        callback.onError(exception);
                    } else {
                        // 区分错误类型
                        String errorMsg;
                        if (CANCELLED.code.equals(workflowStatus.code)) {
                            errorMsg = "客户端主动取消";
                        }
                        else if (TriRpcStatus.UNAVAILABLE.code.equals(workflowStatus.code)) {
                            errorMsg = "FAI触发风控停止";
                        }
                        else if (TriRpcStatus.DEADLINE_EXCEEDED.code.equals(workflowStatus.code)) {
                            errorMsg = "FAI超时停止";
                        }
                        else if (TriRpcStatus.INTERNAL.code.equals(workflowStatus.code)) {
                            errorMsg = "FAI调用方发生错误";
                        } else {
                            errorMsg = isTimeout ? "FAI执行超时" : "FAI服务方发生异常";
                        }
                        callback.onError(new AiAskInterruptException(throwable, workflowStatus, errorMsg));
                        logger.recordNormalException(new LogModel("onError").message(errorMsg).e(throwable));
                    }
                    countDownLatch.countDown();
                });
            }

            @Override
            public void onCompleted() {
                executeWithContext(rpcContext, () -> {
                    callback.onComplete();
                    countDownLatch.countDown();
                    logger.recordOutput(new LogModel("onCompleted").message("countDown"));
                });
            }
        });
        logger.recordOutput(new LogModel("executeStream").message("await"));
        countDownLatch.await();
    }

    /**
     * 构造请求
     */
    private AiaskChatRequest buildAiaskChatRequest(LlmRequest request) {
        ChatContext chatContext = request.getChatContext();
        JourneyPlanAiChatRequest chatRequest = request.getJourneyPlanAiChatRequest();
        AiJourneySceneModel sceneModel = request.getAiJourneySceneModel();
        AiaskChatRequest fsgRequest = new AiaskChatRequest();
        fsgRequest.setScenic(getAiStrategyScenic(sceneModel));
        fsgRequest.setUserId(String.valueOf(chatRequest.getUserId()));
        fsgRequest.setLbsCityCode(chatContext.getCurrentLbsCityCode());
        fsgRequest.setLbsCityName(chatContext.getCurrentLbsCityName());
        fsgRequest.setLbsLatitude(chatRequest.getLatitude());
        fsgRequest.setLbsLongitude(chatRequest.getLongitude());
        fsgRequest.setUtdId(chatRequest.getUtdid());
        fsgRequest.setTtid(chatRequest.getTtid());
        fsgRequest.setUserNick(chatRequest.getUserNick());
        fsgRequest.setDeviceId(chatRequest.getDeviceId());
        String userInput = constructFormattedMessage(chatRequest.getUserId(), chatContext.getOriginalMessageInfo(), chatRequest.getChat());
        fsgRequest.setUserInput(userInput);
        //构造用户消息。增加引用消息逻辑
        String message = constructFormattedMessage(chatRequest.getUserId(), chatContext.getOriginalMessageInfo(), chatContext.getSummarizedChat());
        fsgRequest.setMessage(message);
        fsgRequest.setSid(chatContext.getSessionId());
        fsgRequest.setMsgId(chatContext.getSystemMessageId());
        // 透传参数
        fsgRequest.setSlots(chatRequest.extractExtInfo());
        chatContext.setAiaskChatRequest(fsgRequest);
        return fsgRequest;
    }

    /**
     * 构造格式化消息
     */
    private String constructFormattedMessage(Long userId, List<MessageParam> originalMessageInfo, String summarizedChat) {
        StringBuilder sb = new StringBuilder();
        if (CollectionUtils.isEmpty(originalMessageInfo)) {
            sb.append(summarizedChat);
            return desensitizationMessage(userId, sb.toString());
        }
        sb.append("**引用消息**\n");
        for (MessageParam messageParam : originalMessageInfo) {
            if (Objects.equals(messageParam.getRole(), MessageRole.system) || Objects.equals(messageParam.getRole(), MessageRole.assistant)) {
                if (StringUtils.isEmpty(messageParam.getContent())||StringUtils.isEmpty(messageParam.getExtra())) {
                    continue;
                }
                JSONObject extraObject = JSON.parseObject(messageParam.getExtra());
                String chatQuery = extraObject.getString(JourneyAssistantDataOperateService.MESSAGE_SUMMARIZED_CHAT);
                if (StringUtils.isEmpty(chatQuery)) {
                    continue;
                }
                sb.append("用户输入:").append(chatQuery).append("\n");
                sb.append("系统回答:").append(messageParam.getContent()).append("\n");
            }
        }
        sb.append("**引用消息结束**\n");
        sb.append("**本次用户输入**:\n").append(summarizedChat).append("\n**用户输入结束**");
        return desensitizationMessage(userId, sb.toString());
    }

    /**
     * 数据脱敏
     */
    public String desensitizationMessage(Long userId, String chat) {
        if (flowCutHelper.isHitFlowCut(String.valueOf(userId), DATA_DESENSITIZATION_GRAY_WHITE_LIST, DATA_DESENSITIZATION_GRAY, -1L)) {
            String desensitization = DesensitizationUtil.desensitization(chat);
            logger.recordOutput(new LogModel("desensitization").request(chat).response(desensitization));
            return desensitization;
        }
        return chat;
    }


    private String getAiStrategyScenic(AiJourneySceneModel sceneModel) {
        if (Objects.isNull(sceneModel) || Objects.isNull(sceneModel.getSceneEnum())) {
            return null;
        }
        return sceneModel.getSceneEnum().getAgentType().getType();
    }

    /**
     * 获取提供者名称
     */
    @Override
    public String getProviderName() {
        return AI_STRATEGY.getName();
    }


    public void handlerAiStrategyData(LlmRequest request) {
        try {
            FaiResponse<List<AISearchData>> faiResponse = fspAISearchService.queryAiSearchV2Result(request.getChatContext().getSystemMessageId());
            logger.recordOutput(new LogModel("handlerAiStrategyData").request(request.getChatContext().getSystemMessageId()).response(JSON.toJSONString(faiResponse)));
            List<AISearchData> aiSearchDataList = faiResponse.getData();
            if (CollectionUtils.isEmpty(aiSearchDataList)) {
                return;
            }
            Map<String, Object> internalData = request.getChatContext().getInternalData();
            for (AISearchData aiSearchData : aiSearchDataList) {
                Map<String, Object> convertData = FusionSearchBaseConvert.convert(aiSearchData.getCategory(), aiSearchData);
                if (MapUtils.isEmpty(convertData)) {
                    continue;
                }
                internalData.putAll(convertData);
                // 交通类，将数据放到tair中，解决每次会话同一航班bizId都在变的问题，大模型会使用上一次的bizId，导致数据查询失败
                buildTrafficTair(aiSearchData.getCategory(), convertData);
            }
        } catch (Throwable throwable) {
            logger.recordNormalException(new LogModel("handlerAiStrategyData").message("处理数据上下文失败").e(throwable));
        }
    }

    private void buildTrafficTair(String category, Map<String, Object> convertData) {
        if (!Objects.equals(category, "traffic")) {
            return;
        }
        if (MapUtils.isEmpty(convertData)) {
            return;
        }
        try {
            for (String bizId : convertData.keySet()) {
                Object object = convertData.get(bizId);
                if (Objects.isNull(object)) {
                    continue;
                }
                mdbTairHelper.putValue(journeyPlanAiAssistantMessageHelper.genAiStrategyTrafficKey(bizId), (Serializable)object, TRAFFIC_DATA_CACHE_TIME);
            }
        } catch (Throwable throwable) {
            logger.recordNormalException(new LogModel("buildTrafficTair").message("处理数据上下文失败").e(throwable));
        }
    }

    @AteyeInvoker
    public String testAiSearchV2(String userMessageId) {
        FaiResponse<List<AISearchData>> faiResponse = fspAISearchService.queryAiSearchV2Result(userMessageId);
        String result = JSON.toJSONString(faiResponse);
        logger.recordOutput(new LogModel("testAiSearchV2").request(userMessageId).response(result));
        return result;
    }

    private void executeWithContext(RpcContext_inner rpcContext, Runnable action) {
        try {
            EagleEye.setRpcContext(rpcContext);
            action.run();
        } finally {
            EagleEye.clearRpcContext();
        }
    }

    /**
     * 校验数据
     * 分场景，按照jsonpath校验
     */
    private boolean validateThoughtData(Map<String, Object> thoughtData) {
        if (MapUtils.isEmpty(thoughtData)) {
            return false;
        }

        return ThinkingThoughtValidatorHelper.validateJson(JSON.toJSONString(thoughtData));
    }

}
