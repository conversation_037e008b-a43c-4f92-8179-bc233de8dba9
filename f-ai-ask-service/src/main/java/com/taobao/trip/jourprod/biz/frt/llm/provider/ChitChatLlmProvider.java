package com.taobao.trip.jourprod.biz.frt.llm.provider;

import com.alibaba.dashscope.app.Application;
import com.alibaba.dashscope.app.ApplicationParam;
import com.alibaba.dashscope.app.ApplicationResult;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import com.taobao.eagleeye.EagleEye;
import com.taobao.eagleeye.RpcContext_inner;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantSessionHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.NodeResultExtractor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneySceneModel;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch;
import com.taobao.trip.jourprod.biz.frt.llm.LlmProvider;
import com.taobao.trip.jourprod.biz.frt.llm.domain.LlmRequest;
import com.taobao.trip.jourprod.biz.frt.llm.domain.LlmResponse;
import com.taobao.trip.jourprod.biz.frt.llm.domain.LlmStreamResponse;
import com.taobao.trip.jourprod.biz.frt.llm.domain.LlmStreamResponseCallback;
import com.taobao.trip.jourprod.core.service.converter.AiJourneySceneModelConvert;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatRequest;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import io.reactivex.Flowable;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.taobao.trip.jourprod.biz.frt.llm.domain.LlmProviderIdentityEnum.CHIT_CHAT;

@Component
public class ChitChatLlmProvider implements LlmProvider {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(ChitChatLlmProvider.class);

    @Resource
    private JourneyPlanAiAssistantSessionHelper journeyPlanAiAssistantSessionHelper;
    @Resource
    private AiJourneySceneModelConvert aiJourneySceneModelConvert;

    @Override
    public LlmResponse execute(LlmRequest llmRequest) {
        JourneyPlanAiChatRequest request = llmRequest.getJourneyPlanAiChatRequest();
        ChatContext chatContext = llmRequest.getChatContext();
        AiJourneySceneModel aiJourneySceneModel = llmRequest.getAiJourneySceneModel();
        String appId = AiJourneySwitch.OTHER_SCENE_CHIT_CHAT_APP_ID;
        String userPrompt = request.getChat();
        String sessionId = chatContext.getSessionId();
        String type = chatContext.getAiJourneySceneModel().getType();
        // 未切流且不在白名单返回快速文案
        if (!JourneyPlanAiAssistantHelper.USE_NEW_SCENE_TYPE && !JourneyPlanAiAssistantHelper.TEST_NEW_SCENE_UID.contains(request.getUserId())) {
            String fastReturnContent = aiJourneySceneModelConvert.fastReturnWithoutRecommend(llmRequest.getChatContext(), null);
            LlmResponse llmResponse = new LlmResponse();
            llmResponse.setContent(fastReturnContent);
            llmResponse.setSessionId(chatContext.getSessionId());
            return llmResponse;
        }
        ApplicationParam param = buildParam(appId, userPrompt, sessionId, type);
        String answer = getAnswer(request.getUserId(), param);
        if (StringUtils.isBlank(answer)) {
            return null;
        }
        chatContext.setOriginalContent(answer);
        // 输出
        LlmResponse llmResponse = new LlmResponse();
        llmResponse.setContent(answer);
        llmResponse.setSessionId(chatContext.getSessionId());
        return llmResponse;
    }

    public String getAnswer(Long userId, ApplicationParam param) {
        Application application = new Application();
        try {
            ApplicationResult call = application.call(param);
            journeyPlanAiAssistantSessionHelper.updateSessionIdIfNeed(userId, param.getAppId(), call.getOutput().getSessionId(), param.getSessionId());
            return extractAnswer(call.getOutput().getText());
        } catch (Throwable e) {
            LOGGER.recordNormalException(new LogModel("chitChat").e(e).message("兜底闲聊失败"));
            return null;
        }
    }

    public String extractAnswer(String s) {
        if (StringUtils.isNotEmpty(s)) {
            JSONObject jsonObject = JSON.parseObject(s);
            return jsonObject.getString("answer");
        }
        return null;
    }

    @Override
    public void executeStream(LlmRequest llmRequest, LlmStreamResponseCallback callback) throws Throwable {
        JourneyPlanAiChatRequest request = llmRequest.getJourneyPlanAiChatRequest();
        ChatContext chatContext = llmRequest.getChatContext();
        AiJourneySceneModel aiJourneySceneModel = llmRequest.getAiJourneySceneModel();
        String appId = AiJourneySwitch.OTHER_SCENE_CHIT_CHAT_APP_ID;
        String userPrompt = request.getChat();
        String sessionId = chatContext.getSessionId();
        String type = chatContext.getAiJourneySceneModel().getType();
        // 未切流且不在白名单返回快速文案
        if (!JourneyPlanAiAssistantHelper.USE_NEW_SCENE_TYPE && !JourneyPlanAiAssistantHelper.TEST_NEW_SCENE_UID.contains(request.getUserId())) {
            String fastReturnContent = aiJourneySceneModelConvert.fastReturnWithoutRecommend(llmRequest.getChatContext(), null);
            LlmStreamResponse llmStreamResponse = new LlmStreamResponse();
            llmStreamResponse.setContent(fastReturnContent);
            llmStreamResponse.setSessionId(chatContext.getSessionId());
            callback.onMessage(llmStreamResponse);
            callback.onComplete();
            return;
        }
        ApplicationParam param = buildParam(appId, userPrompt, sessionId, type);
        param.setIncrementalOutput(true);
        param.setHasThoughts(true);
        Application application = new Application();
        Flowable<ApplicationResult> result = null;
        try {
            result = application.streamCall(param);
            result.blockingForEach(data -> {
                String dataStr = JSON.toJSONString(data);
                if (StringUtils.isNotEmpty(dataStr)) {
                    String ans = NodeResultExtractor.extractNodeResults(dataStr);
                    if (StringUtils.isNotBlank(ans)) {
                        LlmStreamResponse llmStreamResponse = new LlmStreamResponse();
                        llmStreamResponse.setContent(ans);
                        llmStreamResponse.setSessionId(chatContext.getSessionId());
                        callback.onMessage(llmStreamResponse);
                    }
                }
            });
        } catch (Exception e) {
            callback.onError(e);
            return;
        }
        callback.onComplete();
    }

    public ApplicationParam buildParam(String appId, String userInput, String sessionId, String type) {
        JsonObject bizParam = new JsonObject();
        bizParam.addProperty("type", type);
        return ApplicationParam.builder()
                //key
                .apiKey(JourneyPlanAiAssistantHelper.AI_JOURNEY_PLAN_APP_KEY)
                //id
                .appId(appId)
                //用户输入
                .prompt(userInput)
                //session
                .sessionId(sessionId).bizParams(bizParam)
                //参数
                .parameter("type", type).build();
    }

    @Override
    public String getProviderName() {
        return CHIT_CHAT.getName();
    }

    private void executeWithContext(RpcContext_inner rpcContext, Runnable action) {
        try {
            EagleEye.setRpcContext(rpcContext);
            action.run();
        } finally {
            EagleEye.clearRpcContext();
        }
    }
}
