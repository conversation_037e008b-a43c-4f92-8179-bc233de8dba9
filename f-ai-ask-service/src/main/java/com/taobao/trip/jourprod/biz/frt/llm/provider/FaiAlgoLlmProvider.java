package com.taobao.trip.jourprod.biz.frt.llm.provider;

import com.alitrip.ai.sdk.api.TripAlgoChatCompletionRequest;
import com.alitrip.ai.sdk.service.TripAlgoLlmClient;
import com.alitrip.ai.sdk.service.TripAlgoLlmService;
import com.google.common.collect.Lists;
import com.taobao.trip.jourprod.biz.frt.llm.LlmProvider;
import com.taobao.trip.jourprod.biz.frt.llm.domain.*;
import com.theokanning.openai.completion.chat.ChatMessage;
import com.theokanning.openai.completion.chat.ChatMessageRole;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 直连FAI网关的模型服务
 * <AUTHOR>
 */
@Component
public class FaiAlgoLlmProvider implements LlmProvider, DisposableBean, InitializingBean {
    private final static FliggyNewLogger logger = LogUtil.getFliggyNewLogger("FaiAlgoLlmProvider");
    TripAlgoLlmService service;

    static {
        logger.recordOutput(new LogModel("static init").response("WhaleDeepSeekProvider.init"));
    }
    @Override
    public LlmResponse execute(LlmRequest request) {
        // 实现普通调用逻辑
        return null;
    }

    @Override
    public void executeStream(LlmRequest request, LlmStreamResponseCallback callback) throws Throwable {
        TripAlgoChatCompletionRequest chatCompletionRequest = buildChatRequest(request);

        service.streamChatCompletion(chatCompletionRequest)
                .doOnError(Throwable::printStackTrace)
                .blockingForEach(chunk -> {
                    LlmStreamResponse chunkResponse = new LlmStreamResponse();
                    if (CollectionUtils.isNotEmpty(chunk.getChoices())) {
                        chunkResponse.setContent(chunk.getChoices().get(0).getMessage().getContent());
                        callback.onMessage(chunkResponse);
                    }
                });
    }

    @Override
    public String getProviderName() {
        return LlmProviderIdentityEnum.FAI_GATEWAY.getName();
    }

    @Override
    public void destroy() throws Exception {
        service.shutdownExecutor();
        logger.recordOutput(new LogModel("destroy").response("WhaleDeepSeekProvider.destroy"));
    }

    private TripAlgoChatCompletionRequest buildChatRequest(LlmRequest request) {
        TripAlgoChatCompletionRequest chatCompletionRequest = new TripAlgoChatCompletionRequest();
        chatCompletionRequest.setMaxTokens(10000000);
        chatCompletionRequest.setModel("openlm_qwq-32b_ai2c");
        // 消息列表
        if (CollectionUtils.isNotEmpty(request.getMessages())) {
            List<ChatMessage> messages = Lists.newArrayList();
            for (MessageParam message : request.getMessages()) {
                if (message.getRole() == MessageRole.user) {
                    messages.add(new ChatMessage(ChatMessageRole.USER.value(),message.getContent()));
                }
                if (message.getRole() == MessageRole.assistant) {
                    messages.add(new ChatMessage(ChatMessageRole.ASSISTANT.value(),message.getContent()));
                }
                if (message.getRole() == MessageRole.system) {
                    messages.add(new ChatMessage(ChatMessageRole.SYSTEM.value(),message.getContent()));
                }
            }
            chatCompletionRequest.setMessages(messages);
        }



        return chatCompletionRequest;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 初始化客户端
        String token = "fai-2-632-f20b438c8a82";
        service = new TripAlgoLlmClient(token).getService();
    }
}
