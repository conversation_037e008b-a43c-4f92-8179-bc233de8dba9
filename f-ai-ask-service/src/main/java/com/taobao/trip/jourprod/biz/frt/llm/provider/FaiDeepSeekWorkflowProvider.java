package com.taobao.trip.jourprod.biz.frt.llm.provider;

import com.alibaba.fastjson.JSON;
import com.fliggy.fai.client.workflow.cmd.WorkflowClientStreamStartCmd;
import com.fliggy.fai.client.workflow.enums.WorkflowEventType;
import com.fliggy.fai.client.workflow.model.WorkflowStreamChunk;
import com.fliggy.fai.client.workflow.model.payload.ActivityMessageDTO;
import com.fliggy.fai.client.workflow.service.FaiWorkflowClientService;
import com.google.common.collect.Maps;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.eagleeye.EagleEye;
import com.taobao.eagleeye.RpcContext_inner;
import com.taobao.trip.jourprod.biz.frt.llm.LlmProvider;
import com.taobao.trip.jourprod.biz.frt.llm.domain.*;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import lombok.SneakyThrows;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.common.stream.StreamObserver;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * FAI的deepseek模型服务
 * <AUTHOR>
 */
@Component
public class FaiDeepSeekWorkflowProvider implements LlmProvider, DisposableBean {
    private final static FliggyNewLogger logger = LogUtil.getFliggyNewLogger("FaiDeepSeekProvider");
    @Resource
    FaiWorkflowClientService faiWorkflowClientService;

    static {
        logger.recordOutput(new LogModel("static init").response("FaiDeepSeekProvider.init"));
    }
    @Override
    public LlmResponse execute(LlmRequest request) {
        // 实现普通调用逻辑
        return null;
    }

    @Override
    public void executeStream(LlmRequest request, LlmStreamResponseCallback callback) throws Throwable {
        WorkflowClientStreamStartCmd workflowClientStreamStartCmd = new WorkflowClientStreamStartCmd();
        workflowClientStreamStartCmd.setAppId(request.getAppId());
        workflowClientStreamStartCmd.setFlowId(request.getFlowId());
        workflowClientStreamStartCmd.setSessionId(request.getSessionId());
        workflowClientStreamStartCmd.setVariables(request.getVariables());

        CountDownLatch countDownLatch = new CountDownLatch(1);
        RpcContext_inner rpcContext = EagleEye.getRpcContext();
        faiWorkflowClientService.startWithStream(workflowClientStreamStartCmd, new StreamObserver<WorkflowStreamChunk>() {
            @Override
            public void onNext(WorkflowStreamChunk chunk) {
                EagleEye.setRpcContext(rpcContext);
                WorkflowEventType eventType = chunk.getEventType();
                if (eventType != WorkflowEventType.WT_MESSAGE && eventType != WorkflowEventType.WT_LLM_REASONING_MESSAGE) {
                    return;
                }
                ActivityMessageDTO activityMessageDTO = (ActivityMessageDTO) chunk.getPayload();
                LlmStreamResponse chunkResponse = new LlmStreamResponse();
                switch (eventType) {
                    case WT_LLM_REASONING_MESSAGE:
                        chunkResponse.setThinking(activityMessageDTO.getMessage());
                        break;
                    case WT_MESSAGE:
                        chunkResponse.setContent(activityMessageDTO.getMessage());
                        break;
                    default:
                        break;
                }
                callback.onMessage(chunkResponse);
            }

            @SneakyThrows
            @Override
            public void onError(Throwable throwable) {
                EagleEye.setRpcContext(rpcContext);
                logger.recordNormalException(new LogModel("onError")
                        .message("fai deepseek stream 执行异常")
                        .request(JSON.toJSONString(request))
                        .e(throwable));
                callback.onError(throwable);
                countDownLatch.countDown();
                logger.recordNormalException(new LogModel("onError").message("countDown").e(throwable));
            }

            @Override
            public void onCompleted() {
                EagleEye.setRpcContext(rpcContext);
                callback.onComplete();
                countDownLatch.countDown();
                logger.recordOutput(new LogModel("onCompleted").message("countDown"));
            }
        });
        logger.recordOutput(new LogModel("executeStream").message("await"));
        countDownLatch.await();
    }

    @Override
    public String getProviderName() {
        return LlmProviderIdentityEnum.FAI_DEEPSEE_WORKFLOW.getName();
    }

    @Override
    public void destroy() throws Exception {
        logger.recordOutput(new LogModel("destroy").response("FaiDeepSeekProvider.destroy"));
    }

    @AteyeInvoker(description = "测试FAI的deepseek模型服务", paraDesc = "request")
    public void testExecuteStream() throws Throwable {
        // Setup
        final LlmRequest request = new LlmRequest();
        request.setAppId(632L);
        request.setFlowId(1514L);
        request.setSessionId("1030");
        MessageParam messageParam1 = new MessageParam();
        messageParam1.setRole(MessageRole.user);
        messageParam1.setContent("我要去西湖你给我个建议？");
        MessageParam messageParam2 = new MessageParam();
        messageParam2.setRole(MessageRole.system);
        messageParam2.setContent("你是想玩多久呢");
        MessageParam messageParam3 = new MessageParam();
        messageParam3.setRole(MessageRole.user);
        messageParam3.setContent("我不去西湖了，我要去扬州的瘦西湖");

        List<MessageParam> messages = new ArrayList<>();
        request.setMessages(messages);

        // 第一轮会话
        messages.add(messageParam1);
        messages.add(messageParam2);
        // 第二轮会话
        messages.add(messageParam3);

        // Run the test
        Map<String, Object> variables = Maps.newHashMap();
        variables.put("chat", request.getMessages().get(0).getContent());
        request.setVariables(variables);
        executeStream(request, new LlmStreamResponseCallback() {
            @Override
            public void onMessage(LlmStreamResponse chunkResponse) {
                if (StringUtils.isNotEmpty(chunkResponse.getThinking())) {
                    System.out.print(chunkResponse.getThinking());
                    logger.recordOutput(new LogModel("think").response(chunkResponse.getThinking()));
                } else {
                    System.out.print(chunkResponse.getContent());
                    logger.recordOutput(new LogModel("content").response(chunkResponse.getContent()));
                }
            }

            @Override
            public void onError(Throwable throwable) {
                throw new RuntimeException(throwable);
            }

            @Override
            public void onComplete() {
                System.out.println("onComplete");
            }
        });
    }
}
