package com.taobao.trip.jourprod.biz.frt.llm.provider;

import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.trip.jourprod.biz.frt.llm.LlmProvider;
import com.taobao.trip.jourprod.biz.frt.llm.domain.*;
import com.taobao.trip.jourprod.common.sal.hsf.config.LLMSwitcher;
import com.volcengine.ark.runtime.model.bot.completion.chat.BotChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.service.ArkService;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 火山方舟的deepseek模型服务
 * <AUTHOR>
 */
@Component
public class VolcanoDeepSeekProvider implements LlmProvider, DisposableBean {
    private final static FliggyNewLogger logger = LogUtil.getFliggyNewLogger("VolcanoDeepSeekProvider");

    static {
        logger.recordOutput(new LogModel("static init").response("VolcanoDeepSeekProvider.init"));
    }
    @Override
    public LlmResponse execute(LlmRequest request) {
        // 实现普通调用逻辑
        return null;
    }

    @Override
    public void executeStream(LlmRequest request, LlmStreamResponseCallback callback) throws Throwable {
        ArkService service = ArkService.builder().apiKey(request.getApiKey()).build();
        try {
            // 传入对话消息列表
            final List<ChatMessage> streamMessages = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(request.getMessages())) {
                for (MessageParam param : request.getMessages()) {
                    ChatMessage message = ChatMessage.builder()
                            .role(convert2ChatMessageRole(param.getRole()))
                            .content(param.getContent()).build();
                    if (Objects.isNull(message.getRole())) {
                        continue;
                    }
                    streamMessages.add(message);
                }
            }
            BotChatCompletionRequest botChatCompletionRequest = BotChatCompletionRequest.builder()
                    .model(request.getModelId())
                    .messages(streamMessages)
                    .build();

            service.streamBotChatCompletion(botChatCompletionRequest)
                    .doOnError(Throwable::printStackTrace)
                    .blockingForEach(
                            delta -> {
                                if (CollectionUtils.isNotEmpty(delta.getChoices())) {
                                    ChatMessage message = delta.getChoices().get(0).getMessage();

                                    LlmStreamResponse chunkResponse = new LlmStreamResponse();
                                    chunkResponse.setThinking(message.getReasoningContent());
                                    if (message != null) {
                                        chunkResponse.setContent(String.valueOf(message.getContent()));
                                    }
                                    callback.onMessage(chunkResponse);
                                }
                            }
                    );
        } catch (Exception e) {
            callback.onError(e);
        } finally {
            service.shutdownExecutor();
        }
    }

    /**
     * 转化成服务内置的模型
     * @param messageRole
     * @return
     */
    private static ChatMessageRole convert2ChatMessageRole(MessageRole messageRole) {
        if (messageRole == MessageRole.system) {
            return ChatMessageRole.SYSTEM;
        }
        if (messageRole == MessageRole.user) {
            return ChatMessageRole.USER;
        }
        if (messageRole == MessageRole.assistant) {
            return ChatMessageRole.ASSISTANT;
        }
        return null;
    }

    @Override
    public String getProviderName() {
        return LlmProviderIdentityEnum.DOUYIN_DEEPSEEK.getName();
    }

    @Override
    public void destroy() throws Exception {
        logger.recordOutput(new LogModel("destroy").response("VolcanoDeepSeekProvider.destroy"));
    }

    @AteyeInvoker(description = "测试火山方舟的deepseek模型服务", paraDesc = "request")
    public void testExecuteStream() throws Throwable {
        // Setup
        final LlmRequest request = new LlmRequest();
        request.setApiKey(LLMSwitcher.TIKTOK_DEEPSEEK_API_KEY);
        request.setModelId(LLMSwitcher.TIKTOK_DEEPSEEK_MODEL_VERSION);
        MessageParam messageParam1 = new MessageParam();
        messageParam1.setRole(MessageRole.user);
        //messageParam1.setContent("我要去西湖你给我个建议？");
        MessageParam messageParam2 = new MessageParam();
        messageParam2.setRole(MessageRole.system);
        messageParam2.setContent("你是想玩多久呢");
        MessageParam messageParam3 = new MessageParam();
        messageParam3.setRole(MessageRole.user);
        messageParam3.setContent("我不去西湖了，我要去扬州的瘦西湖");

        List<MessageParam> messages = new ArrayList<>();
        request.setMessages(messages);

        // 第一轮会话
        messages.add(messageParam1);
//        messages.add(messageParam2);
        // 第二轮会话
       // messages.add(messageParam3);

        // Run the test

        new VolcanoDeepSeekProvider().executeStream(request, new LlmStreamResponseCallback() {
            @Override
            public void onMessage(LlmStreamResponse chunkResponse) {
                if (StringUtils.isNotEmpty(chunkResponse.getThinking())) {
                    System.out.print(chunkResponse.getThinking());
                } else {
                    System.out.print(chunkResponse.getContent());
                }
            }

            @Override
            public void onError(Throwable throwable) {
                throw new RuntimeException(throwable);
            }

            @Override
            public void onComplete() {
                System.out.println("onComplete");
            }
        });
    }
}
