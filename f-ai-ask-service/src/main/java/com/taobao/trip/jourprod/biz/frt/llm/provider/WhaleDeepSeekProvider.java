package com.taobao.trip.jourprod.biz.frt.llm.provider;

import com.alibaba.whale.client.core.SDKFactory;
import com.alibaba.whale.client.protocol.LMResponse;
import com.alibaba.whale.client.protocol.TextGeneration;
import com.alibaba.whale.client.protocol.chat.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.trip.jourprod.biz.frt.llm.LlmProvider;
import com.taobao.trip.jourprod.biz.frt.llm.domain.*;
import com.taobao.trip.jourprod.common.sal.hsf.config.Switcher;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * whale的deepseek模型服务
 * <AUTHOR>
 */
@Component
public class WhaleDeepSeekProvider implements LlmProvider, DisposableBean, InitializingBean {
    private final static FliggyNewLogger logger = LogUtil.getFliggyNewLogger("WhaleDeepSeekProvider");

    /**
     * 线程池
     */
    private static final ThreadPoolExecutor executor = new ThreadPoolExecutor(20, 100, 60, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(300),
            new CustomizableThreadFactory("WhaleDeepSeekProvider"));
    TextGeneration<LMResponse> client;

    static {
        logger.recordOutput(new LogModel("static init").response("WhaleDeepSeekProvider.init"));
    }
    @Override
    public LlmResponse execute(LlmRequest request) {
        // 实现普通调用逻辑
        return null;
    }

    @Override
    public void executeStream(LlmRequest request, LlmStreamResponseCallback callback) throws Throwable {
        // 构建请求参数
        ChatRequest chatRequest = buildChatRequest(request);

        // 请求LLM并打印结果
        ChatStreamResult streamResult = client.chatStream(chatRequest).toCompletableFuture().join();
        streamResult.blockingStream().forEach(chatResponse -> {
            if (chatResponse.getErrorCode() != null && !chatResponse.getErrorCode().equals(200)) {
                logger.recordOutput(new LogModel("stream.error").response(chatResponse.toString()));
                return;
            }
            LlmStreamResponse llmStreamResponse = new LlmStreamResponse();
//            llmStreamResponse.setThinking(chatResponse.getChoices().get(0).getDelta().getReasoningContent());
            llmStreamResponse.setContent(chatResponse.getChoices().get(0).getDelta().getContent());
            callback.onMessage(llmStreamResponse);
        });
    }

    @Override
    public String getProviderName() {
        return LlmProviderIdentityEnum.WHALE_DEEPSEEK.getName();
    }

    @Override
    public void destroy() throws Exception {
        executor.shutdown();
        logger.recordOutput(new LogModel("destroy").response("WhaleDeepSeekProvider.destroy"));
    }

    static TextGeneration<LMResponse> getClient() {
        // 初始化client
        SDKFactory.TextGenerationConfig config = new SDKFactory.TextGenerationConfig();
        config.setApiKey(Switcher.WHALE_API_KEY);
        config.setExecutorService(executor);

        // 显示指定后端服务endpoint，仅用于本地测试，线上可不用设置不建议使用，因为该方法会直接显式固定后端服务的domain，使得无法在处理容灾情况时动态切换后端服务的Domain
        // 本地测试无法访问Vipserver, 因此本地测试需要显式指定serviceLocator为 https://whale-wave.alibaba-inc.com
        // config.setServiceLocator(() -> "https://whale-wave.alibaba-inc.com");
        // 本地访问 查阅Whale API 服务文档确认域名 https://aliyuque.antfin.com/gdorir/ahrwo0/gcwgz78cmz15l8r2

        return SDKFactory.defaultTextGeneration(config);
    }

    private ChatRequest buildChatRequest(LlmRequest request) {
        // 构造request
        ChatRequest chatRequest = new ChatRequest();
        chatRequest.setModel("deepseek_r1_671b");
        chatRequest.setStream(true);

        // 控制参数
        chatRequest.setMaxTokens(4000);
        chatRequest.setTemperature(0.8);
        chatRequest.setTopP(1.0);
        chatRequest.setFrequencyPenalty(1.0);
        chatRequest.setPresencePenalty(1.0);

        // 设置超时时间
        // 参考文档：https://aliyuque.antfin.com/gdorir/ahrwo0/kch0a4r11vge0h1m?singleDoc# 《超时时间控制》
        chatRequest.setTimeoutMs(Switcher.WHALE_CALL_TIME_OUT);
        chatRequest.setPerEventTimeoutMs(Switcher.WHALE_PRE_EVENT_TIME_OUT);

        // 设置扩展字段
        // 支持设置用户自定义参数，记录在pvLog中
        // 支持将非标准的控制参数传递给后端服务，例如max_new_tokens，top_k, 参考文档：https://aliyuque.antfin.com/gdorir/ahrwo0/xgvt7ckyxqli6iq7?singleDoc#
        // 《generate_config》
        Map<String, Object> extendFields = new HashMap<String, Object>(4) {{
            put("top_k", 1);
        }};
        chatRequest.setExtendFields(extendFields);

        // 消息列表
        if (CollectionUtils.isNotEmpty(request.getMessages())) {
            List<ChatMessage> messages = Lists.newArrayList();
            for (MessageParam message : request.getMessages()) {
                if (message.getRole() == MessageRole.user) {
                    messages.add(new ChatUserTextMessage(message.getContent()));
                }
                if (message.getRole() == MessageRole.assistant) {
                    messages.add(new ChatAssistantMessage(message.getContent()));
                }
                if (message.getRole() == MessageRole.system) {
                    messages.add(new ChatSystemMessage(message.getContent()));
                }
            }
            chatRequest.setMessages(messages);
        }

        return chatRequest;
    }

    @AteyeInvoker(description = "测试whale的deepseek模型服务", paraDesc = "request")
    public void testExecuteStream() throws Throwable {
        // Setup
        final LlmRequest request = new LlmRequest();
        request.setApiKey("P8Z1UCM51Y");
        MessageParam messageParam1 = new MessageParam();
        messageParam1.setRole(MessageRole.user);
        messageParam1.setContent("我要去西湖你给我个建议？");
        MessageParam messageParam2 = new MessageParam();
        messageParam2.setRole(MessageRole.system);
        messageParam2.setContent("你是想玩多久呢");
        MessageParam messageParam3 = new MessageParam();
        messageParam3.setRole(MessageRole.user);
        messageParam3.setContent("我不去西湖了，我要去扬州的瘦西湖");

        List<MessageParam> messages = new ArrayList<>();
        request.setMessages(messages);

        // 第一轮会话
        messages.add(messageParam1);
        messages.add(messageParam2);
        // 第二轮会话
        messages.add(messageParam3);

        // Run the test
        Map<String, Object> variables = Maps.newHashMap();
        variables.put("chat", request.getMessages().get(0).getContent());
        request.setVariables(variables);
        executeStream(request, new LlmStreamResponseCallback() {
            @Override
            public void onMessage(LlmStreamResponse chunkResponse) {
                if (StringUtils.isNotEmpty(chunkResponse.getThinking())) {
                    System.out.print(chunkResponse.getThinking());
                    logger.recordOutput(new LogModel("think").response(chunkResponse.getThinking()));
                } else {
                    System.out.print(chunkResponse.getContent());
                    logger.recordOutput(new LogModel("content").response(chunkResponse.getContent()));
                }
            }

            @Override
            public void onError(Throwable throwable) {
                throw new RuntimeException(throwable);
            }

            @Override
            public void onComplete() {
                System.out.println("onComplete");
            }
        });
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 初始化客户端
        client = getClient();
    }
}
