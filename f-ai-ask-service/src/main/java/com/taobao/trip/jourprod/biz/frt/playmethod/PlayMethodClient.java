package com.taobao.trip.jourprod.biz.frt.playmethod;

import com.alibaba.trippoi.domain.admin.view.CommonResult;
import com.alibaba.trippoi.domain.dto.ChildPlayMethodDTO;
import com.alibaba.trippoi.domain.enums.PlayMethodEnvEnum;
import com.alibaba.trippoi.service.play.method.ChildPlayMethodService;
import com.taobao.trip.jourprod.common.sal.annotation.RunLog;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiMddJourneySwitch.ENV_FLAG;

@Service
public class PlayMethodClient {

    @Resource
    private ChildPlayMethodService childPlayMethodService;

    @RunLog
    public ChildPlayMethodDTO findById(String subPlayId) {
        if (StringUtils.isBlank(subPlayId)) {
            return null;
        }
        PlayMethodEnvEnum env = ENV_FLAG ? PlayMethodEnvEnum.PRODUCT : PlayMethodEnvEnum.PRE;
        CommonResult<ChildPlayMethodDTO> result = childPlayMethodService.findById(subPlayId, env);
        if (Objects.isNull(result) || Objects.isNull(result.getData())) {
            return null;
        }
        return result.getData();
    }
}
