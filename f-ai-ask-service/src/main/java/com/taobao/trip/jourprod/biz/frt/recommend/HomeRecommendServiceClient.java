package com.taobao.trip.jourprod.biz.frt.recommend;

import javax.annotation.Resource;

import com.alibaba.boot.switchcenter.annotation.SwitchGroup;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.alitrip.recommend.domain.common.RecmdResponse;
import com.alitrip.recommend.domain.homepage.DestinationInfo;
import com.alitrip.recommend.domain.homepage.HomePageFeedsRequest;
import com.alitrip.recommend.service.homepage.HomePageRecommendService;
import com.google.common.collect.Lists;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch;
import com.taobao.trip.jourprod.common.sal.annotation.RunLog;
import com.taobao.trip.jourprod.common.sal.tair.impl.LdbTairManager;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Service;

@Service
@SwitchGroup
public class HomeRecommendServiceClient {

    @Resource
    private HomePageRecommendService homePageRecommendService;
    @Resource
    private LdbTairManager ldbTairManager;

    @AppSwitch(des = "算法推荐城市缓存时间", level = Switch.Level.p4)
    public static Integer FEATURED_CITY_LIST_CACHE_TTL = 24 * 60 * 60;


    private static final String CITY_CODE_KEY = "cityCode";
    public static final String FEATURED_DESTINATION_CITY_CACHE_KEY = "tripOd:featuredCityList:";


    @RunLog(userId = "#userId")
    public List<DestinationInfo> getFeaturedDestinationList(Long userId, Long lbsCityId, Long orderCityId, Integer pageSize) {
        HomePageFeedsRequest request = new HomePageFeedsRequest();
        request.setUserId(userId);
        request.setPageSize(pageSize);
        if (Objects.nonNull(lbsCityId)) {
            JSONObject move = new JSONObject();
            move.put(CITY_CODE_KEY, String.valueOf(lbsCityId));
            request.setMove(move.toJSONString());
        }
        if (Objects.nonNull(orderCityId)) {
            JSONObject order = new JSONObject();
            order.put(CITY_CODE_KEY, String.valueOf(orderCityId));
            request.setOverlap(order.toJSONString());
        }
        RecmdResponse<List<DestinationInfo>> featuredDestinations = homePageRecommendService.getFeaturedDestinations(request);
        if (Objects.isNull(featuredDestinations) || !featuredDestinations.isSuccess()) {
            return Lists.newArrayList();
        }
        List<DestinationInfo> data = featuredDestinations.getData();
        String key = FEATURED_DESTINATION_CITY_CACHE_KEY + userId;
        ldbTairManager.put(key, JSON.toJSONString(data), FEATURED_CITY_LIST_CACHE_TTL);
        return data;
    }

}
