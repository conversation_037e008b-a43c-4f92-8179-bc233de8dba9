package com.taobao.trip.jourprod.biz.frt.routemap;

import javax.annotation.Resource;

import com.taobao.trip.jourprod.common.sal.annotation.RunLog;
import com.taobao.tripca.facade.JourneyRouteMapService;
import com.taobao.tripca.facade.request.route.JourneyPlanRequest;
import com.taobao.tripca.facade.request.route.JourneyPlanRouteResult;
import com.taobao.tripca.facade.request.route.JourneyPlanVO;
import com.taobao.tripvu.module.result.BaseResult;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.utils.LogUtil;
import java.util.List;
import java.util.Objects;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

@Component
public class RouteMapService {

    private final FliggyNewLogger newLogger = LogUtil.getFliggyNewLogger(RouteMapService.class);


    @Resource
    private JourneyRouteMapService journeyRouteMapService;


    @RunLog(userId = "#userId")
    public List<JourneyPlanVO> queryJourneyPlanList(Long userId) {
        JourneyPlanRequest journeyPlanRequest = new JourneyPlanRequest();
        journeyPlanRequest.setUserId(userId);
        BaseResult<List<JourneyPlanVO>> listBaseResult = journeyRouteMapService.queryJourneyPlanList(journeyPlanRequest);
        if (Objects.isNull(listBaseResult) || CollectionUtils.isEmpty(listBaseResult.getData())) {
            return null;
        }
        return listBaseResult.getData();
    }

    @RunLog(userId = "#userId")
    public JourneyPlanRouteResult queryJourneyPlanDetailNew(Long userId, Long planId) {
        JourneyPlanRequest planRequest = new JourneyPlanRequest();
        planRequest.setUserId(userId);
        planRequest.setId(planId);
        BaseResult<JourneyPlanRouteResult> journeyPlanDetail = journeyRouteMapService.queryJourneyPlanDetailNew(planRequest);
        if (Objects.isNull(journeyPlanDetail) || Objects.isNull(journeyPlanDetail.getData())) {
            return null;
        }
        return journeyPlanDetail.getData();
    }

}
