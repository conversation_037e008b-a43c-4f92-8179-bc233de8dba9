package com.taobao.trip.jourprod.biz.frt.search;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.trip.jourprod.common.sal.hsf.poi.PoiReadServiceClient;
import com.taobao.trip.jourprod.common.sal.hsf.tripdivision.TrdiDivisionReadServiceClient;
import com.taobao.trip.jourprod.core.service.gaode.AMapService;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.utils.LogUtil;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

/**
 * 高德 搜索接口
 */
@Service
public class GaoDeSearchService {

    private final FliggyNewLogger newLogger = LogUtil.getFliggyNewLogger(GaoDeSearchService.class);

    @PostConstruct
    public void init() {
        SwitchManager.init(GaoDeSearchService.class);
    }

    @AppSwitch(des = "高德搜索，城市白名单", level = Switch.Level.p4)
    public static Map<String, String> GAO_DE_SEARCH_CITY_CODE_WHITE_MAP = Maps.newHashMap();

    @AppSwitch(des = "高德搜索，判断国内省", level = Switch.Level.p4)
    public static List<String> GAO_DE_SEARCH_CHINA_PROVINCE = Lists.newArrayList("120000", "440000", "610000", "650000", "140000", "360000", "450000", "630000", "340000", "370000", "510000", "230000", "460000", "530000", "710000", "130000", "350000", "310000", "520000", "210000", "410000", "540000", "810000", "150000", "430000", "110000", "220000", "330000", "640000", "620000", "320000", "420000", "500000", "820000");

    @AppSwitch(des = "高德搜索，图片默认值", level = Switch.Level.p4)
    public static String GAO_DE_SEARCH_IMG_DEFAULT = "https://gw.alicdn.com/imgextra/i1/O1CN01kmVeHo1jOubpEx5jF_!!6000000004539-0-tps-420-420.jpg";

    @AppSwitch(des = "高德搜索，酒店类型code", level = Switch.Level.p4)
    public static List<String> GAO_DE_SEARCH_HOTEL_TYPE_CODE_LIST = Lists.newArrayList("100000", "100100", "100101", "100102", "100103", "100104", "100105", "100200", "100201");

    @AppSwitch(des = "平台poi要不要过滤掉低质量的点", level = Switch.Level.p4)
    public static Boolean POI_REMOVE_LEVEL_BAD = true;

    @AppSwitch(des = "高德poi详情链接前缀", level = Switch.Level.p4)
    public static String GAO_DE_POI_JUMP_URL_PRE = "https://outfliggys.m.taobao.com/wow/pone/pcraft/common/fupr?_fli_background_transparent=true&_fli_anim_type=none&isPoiDetailLayer=true&params=%7B%22poiIdStr%22%3A%22";
    @AppSwitch(des = "高德poi详情链接后缀", level = Switch.Level.p4)
    public static String GAO_DE_POI_JUMP_URL_AFTER = "%22%7D";



    @Resource
    private AMapService aMapService;
    @Resource
    private TrdiDivisionReadServiceClient trdiDivisionReadServiceClient;
    @Resource
    private PoiReadServiceClient poiReadServiceClient;



    public static final String ID = "id";
    public static final String NAME = "name";
    public static final String LOCATION = "location";
    public static final String SPLIT = ",";
    public static final String PHOTOS = "photos";
    public static final String BUSINESS = "business";
    private static final String URL = "url";
    public static final String TYPE = "type";
    private static final String P_NAME = "pname";
    private static final String P_CODE = "pcode";
    private static final String CITY_NAME = "cityname";
    private static final String AD_NAME = "adname";
    public static final String ADDRESS = "address";
    private static final String CITY_CODE = "citycode";
    private static final String AD_CODE = "adcode";
    private static final String BIZ_EXT = "biz_ext";
    //评分
    public static final String RATING = "rating";
    //人均消费
    public static final String COST = "cost";
    //特色内容
    public static final String TAG = "tag";
    //营业时间描述
    public static final String OPEN_TIME_WEEK = "opentime_week";

    //高德国外城市泛搜
    public static final String GAO_DE_SEARCH_CITY = "000000000";
    public static final String GAO_DE_SEARCH_CITY_DOMESTIC = "中国";
    public static final String GAO_DE_SEARCH_CITY_DOMESTIC_DEFAULT = "";

    //为空的值
    private static final String NULL_VALUE = "[]";
    //高德搜索的后缀
    private static final String GAO_DE_SEARCH_CITY_AFTER = "市";

    /**
     * 处理图片列表
     * @param photos
     * @return
     */
    public List<String> getDealImgList(List<Map<String, Object>> photos) {
        if (CollectionUtils.isEmpty(photos)) {
            return Lists.newArrayList();
        }
        return photos.stream().map(photo -> MapUtils.getString(photo, URL)).collect(Collectors.toList());
    }

}
