package com.taobao.trip.jourprod.biz.frt.train;

import com.taobao.trip.jourprod.service.facade.model.request.train.TrainListQueryRequest;
import com.taobao.trip.jourprod.service.facade.model.request.train.TrainNumberQueryRequest;
import com.taobao.trip.jourprod.service.facade.model.response.train.TrainNumberQueryResult;
import com.taobao.trip.wireless.train.domain.search.result.listsearch.NonstopVO;

/**
 * 执行火车票查询服务
 *
 * <AUTHOR>
 * @time 2023/12/1 11:27
 */
public interface TrainOperateFrt {

    /**
     * 根据出发地、目的地、出发日期查询关联车次原始信息
     *
     * @param trainListQueryRequest
     * @return
     */
    NonstopVO queryOirTrainList(TrainListQueryRequest trainListQueryRequest);

    /**
     * 查询火车票余量以及车次信息
     * @param trainNumberQueryRequest
     * @return
     */
    TrainNumberQueryResult queryTrainNumber(TrainNumberQueryRequest trainNumberQueryRequest);
}
