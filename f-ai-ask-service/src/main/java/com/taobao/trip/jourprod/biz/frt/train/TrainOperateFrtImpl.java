package com.taobao.trip.jourprod.biz.frt.train;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;

import com.google.common.collect.Maps;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import com.taobao.mtop.common.Result;
import com.taobao.trip.jourprod.common.sal.annotation.RunLog;
import com.taobao.trip.jourprod.core.service.converter.Converter;
import com.taobao.trip.jourprod.service.facade.model.request.train.TrainListQueryRequest;
import com.taobao.trip.jourprod.service.facade.model.request.train.TrainNumberQueryRequest;
import com.taobao.trip.jourprod.service.facade.model.response.JourPlanError;
import com.taobao.trip.jourprod.service.facade.model.response.train.TrainNumberQueryResult;
import com.taobao.trip.wireless.train.TrainItemService;
import com.taobao.trip.wireless.train.domain.search.param.ListSearchMtopParam;
import com.taobao.trip.wireless.train.domain.search.result.listsearch.ListSearchVO;
import com.taobao.trip.wireless.train.domain.search.result.listsearch.NonstopVO;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import java.util.Objects;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

/**
 * 火车票查询实现类
 *
 * <AUTHOR>
 * @time 2023/12/1 11:30
 */
@Component
public class TrainOperateFrtImpl implements TrainOperateFrt {

    private final static FliggyNewLogger SLS_LOGGER = LogUtil.getFliggyNewLogger(TrainOperateFrtImpl.class);

    @Resource
    private TrainItemService trainItemService;

    @Resource
    private Converter<TrainListQueryRequest, ListSearchMtopParam> trainListQueryConverter;

    @Resource
    private Converter<TrainNumberQueryRequest, ListSearchMtopParam> trainNumberQueryConverter;

    @Resource
    private Converter<NonstopVO, TrainNumberQueryResult> trainNumberQueryResultConverter;

    /**
     * 根据出发地、目的地、出发日期查询关联车次原始信息
     *
     * @param trainListQueryRequest
     * @return
     */
    @Override
    @RunLog
    public NonstopVO queryOirTrainList(TrainListQueryRequest trainListQueryRequest) {
        // 参数转换
        ListSearchMtopParam listSearchMtopParam = trainListQueryConverter.convert(trainListQueryRequest, Maps.newHashMap());
        if (Objects.isNull(listSearchMtopParam)) {
            return null;
        }

        // 请求火车列表
        Result<ListSearchVO> listSearchVOResult = trainItemService.listSearch(listSearchMtopParam);

        // 返回参数校验
        if (!listSearchVOResult.isSuccess() || Objects.isNull(listSearchVOResult.getModel()) || Objects.isNull(listSearchVOResult.getModel().getNonstopModule())) {
            SLS_LOGGER.recordNormalFailedRpc(new LogModel("listSearch")
                .message("火车票列表搜索失败")
                .request(JSON.toJSONString(listSearchMtopParam))
                .response(JSON.toJSONString(listSearchVOResult))
                .success(false));
            return null;
        }
        // 记录正常结果
        SLS_LOGGER.recordRpc(new LogModel("listSearch")
            .request(JSON.toJSONString(listSearchMtopParam))
            .response(JSON.toJSONString(listSearchVOResult)));
        return listSearchVOResult.getModel().getNonstopModule();
    }

    /**
     * 查询火车票余量以及车次信息
     * @param trainNumberQueryRequest
     * @return
     */
    @Override
    public TrainNumberQueryResult queryTrainNumber(TrainNumberQueryRequest trainNumberQueryRequest) {
        // 参数转换
        ListSearchMtopParam listSearchMtopParam = trainNumberQueryConverter.convert(trainNumberQueryRequest, Maps.newHashMap());
        if (Objects.isNull(trainNumberQueryRequest)) {
            return TrainNumberQueryResult.failed(JourPlanError.PARAM_INVALID);
        }

        // 请求火车列表
        Result<ListSearchVO> listSearchVOResult = trainItemService.listSearch(listSearchMtopParam);

        // 返回参数校验
        if (!listSearchVOResult.isSuccess() || Objects.isNull(listSearchVOResult.getModel()) || Objects.isNull(listSearchVOResult.getModel().getNonstopModule())) {
            SLS_LOGGER.recordNormalFailedRpc(new LogModel("queryTrainNumber")
                    .message("火车票列表搜索失败")
                    .request(JSON.toJSONString(listSearchMtopParam))
                    .response(JSON.toJSONString(listSearchVOResult))
                    .success(false));
            return TrainNumberQueryResult.failed(JourPlanError.QUERY_TRAIN_LIST_ERROR);
        }
        // 记录正常结果
        SLS_LOGGER.recordRpc(new LogModel("queryTrainNumber")
                .request(JSON.toJSONString(listSearchMtopParam))
                .response(JSON.toJSONString(listSearchVOResult)));


        // 返回结果转换
        return trainNumberQueryResultConverter.convert(listSearchVOResult.getModel().getNonstopModule(), Maps.newHashMap());
    }

    /**
     * 校验参数
     *
     * @param trainListQueryRequest 火车列表查询参数
     * @return  参数是否合法
     */
    private boolean checkParam(TrainListQueryRequest trainListQueryRequest) {
        return !Objects.isNull(trainListQueryRequest) && !ObjectUtils.anyNull(trainListQueryRequest.getArrAreaName(),
                trainListQueryRequest.getDepAreaName(), trainListQueryRequest.getDepDate());
    }

    @AteyeInvoker(description = "测试火车票数量")
    public void testSearchTrainNumber(String depDate, String fromCityName, String destCityName) {
        TrainNumberQueryRequest trainNumberQueryRequest = new TrainNumberQueryRequest();
        trainNumberQueryRequest.setDepDate(depDate);
        trainNumberQueryRequest.setFromCityName(fromCityName);
        trainNumberQueryRequest.setDestCityName(destCityName);
        TrainNumberQueryResult trainNumberQueryResult = queryTrainNumber(trainNumberQueryRequest);
        trainNumberQueryResult.setExtInfo(null);
        Ateye.out.println(trainNumberQueryResult);
    }

    @AteyeInvoker(description = "测试火车listing搜索接口", paraDesc = "depAreaName&arrAreaName&depDate&depLocation&arrLocation&depType&arrType")
    public void searchTrainList(String depAreaName, String arrAreaName, String depDate, String depLocation, String arrLocation, Integer depType, Integer arrType) {
        ListSearchMtopParam listSearchMtopParam = new ListSearchMtopParam();
        listSearchMtopParam.setDepAreaName(depAreaName);
        listSearchMtopParam.setDepLocation(depLocation);
        listSearchMtopParam.setDepType(depType);
        listSearchMtopParam.setArrAreaName(arrAreaName);
        listSearchMtopParam.setArrLocation(arrLocation);
        listSearchMtopParam.setArrType(arrType);
        listSearchMtopParam.setDepDate(depDate);
        listSearchMtopParam.setSearchSource("journey");
        listSearchMtopParam.setSearchType(1);
        Result<ListSearchVO> listSearchVOResult = trainItemService.listSearch(listSearchMtopParam);
        Ateye.out.println(JSON.toJSONString(listSearchVOResult));
    }
}
