package com.taobao.trip.jourprod.biz.frt.vipserver;

import javax.annotation.Resource;

import com.alibaba.boot.switchcenter.annotation.SwitchGroup;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fliggypoi.client.domain.TripPoiResult;
import com.alibaba.fliggypoi.client.service.TripPoiReadService;
import com.alibaba.trippoi.constants.TrpoPoiSourceType;

import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.DestinationInfo;
import com.taobao.trip.jourprod.common.sal.annotation.RunLog;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.Poi;
import com.taobao.vipserver.client.core.Host;
import com.taobao.vipserver.client.core.VIPClient;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import java.net.URLEncoder;
import java.util.List;
import java.util.Objects;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.constant.JourneyPlanAiAssistantConstant.POI_TYPE_HOTEL;

@Service
@SwitchGroup
public class VipServerClient {

    private final FliggyNewLogger newLogger = LogUtil.getFliggyNewLogger(VipServerClient.class);

    @Resource
    private TripPoiReadService tripPoiReadService;

    @AppSwitch(des = "poi搜索vipserver的key", level = Switch.Level.p4)
    public static final String POI_QUERY_VIPSERVER_KEY = "com.trip.search.qp.dolphin.vipserver";

    @AppSwitch(des = "ai场景-酒店", level = Switch.Level.p4)
    public static final List<String> AI_SCENE_HOTEL_LIST = Lists.newArrayList("HOTEL_BOOKING");

    @AppSwitch(des = "ai场景-美食", level = Switch.Level.p4)
    public static final List<String> AI_SCENE_FOOD_LIST = Lists.newArrayList("RESTAURANT_RECOMMEND");

    // 在类中定义静态HTTP客户端（使用连接池）
    private static final CloseableHttpClient HTTP_CLIENT = HttpClients.custom()
            .setMaxConnTotal(200) // 最大连接数
            .setMaxConnPerRoute(50) // 每个路由最大连接数
            .evictExpiredConnections() // 自动清理过期连接
            .build();

    private static final String MODULES = "modules";
    private static final String POIS_ALL = "pois_all";
    private static final String DESTINATION_CITY = "destination_city";
    private static final String MULTI_DESTINATION_TERM = "multi_destination_term";

    private static final String POI_TYPE = "poi";
    private static final String ID = "id";
    private static final String LON = "lon";
    private static final String LAT = "lat";
    private static final String NAME = "name";
    private static final String SOURCE = "source";
    private static final String LEVEL = "level";

    private static final String MULTI_DESTINATION_TERM_VALUE = "T";

    /**
     * 根据vipserver查询poi
     *
     * @param query
     * @param cityCode
     * @param sceneEnum
     * @return
     */
    @RunLog(userId = "#userId")
    public Poi getPoiByQpVipServer(Long userId, String query, String cityCode, String longitude, String latitude, AiJourneyPlanSceneEnum sceneEnum) {
        try {
            String newQuery = URLEncoder.encode(query, "UTF-8");
            String param = "user_lat=" + latitude + "&source=tripwjourprod&outfmt=json&q=" + newQuery + "&s=dolphin_trip_search_qp&user_id=" +
                    userId +"&user_lon=" + longitude + "5&user_city_code=" + cityCode;
            String res = getResultByQP(param);

            JSONObject jsonObject = JSON.parseObject(res);
            JSONObject modules = jsonObject.getJSONObject(MODULES);
            JSONArray poisAll = (JSONArray) modules.get(POIS_ALL);
            if (CollectionUtils.isEmpty(poisAll)) {
                return null;
            }
            String searchType = POI_TYPE;
            //判断当前查什么类型的
            if (AI_SCENE_HOTEL_LIST.contains(sceneEnum.getSceneCode())) {
                searchType = POI_TYPE_HOTEL;
            }

            for (Object value : poisAll) {
                JSONObject firstPoi = (JSONObject) value;
                String poiId = firstPoi.getString(ID);
                String name = firstPoi.getString(NAME);
                String source = firstPoi.getString(SOURCE);
                if (!StringUtils.equals(searchType, source)) {
                    continue;
                }
                double lon = firstPoi.getDouble(LON);
                double lat = firstPoi.getDouble(LAT);

                Poi poi = new Poi();
                poi.setPoiId(poiId);
                poi.setPoiName(name);
                poi.setLongitude(lon);
                poi.setLatitude(lat);
                poi.setType(POI_TYPE);
                poi.setPoiType(source);

                //如果是酒店
                if (StringUtils.equals(POI_TYPE_HOTEL, source)) {
                    poi.setShid(poiId);
                    TripPoiResult<Long> poiIdResult = tripPoiReadService.getPoiIdByRawPoiId(poiId, TrpoPoiSourceType.HOTEL_DB.getCode());
                    if (Objects.nonNull(poiIdResult)) {
                        poi.setPoiId(String.valueOf(poiIdResult.getData()));
                    }
                }
                return poi;
            }

        } catch (Exception e) {
            newLogger.recordDangerException(new LogModel("getPoiByQpVipServer").e(e));
        }
        return null;
    }

    /**
     * 根据vipserver查询poi
     *
     * @param query
     * @return
     */
    @RunLog(userId = "#userId")
    public List<DestinationInfo> getDivisionListByQpVipServer(Long userId, String query) {
        List<DestinationInfo> result = Lists.newArrayList();
        try {
            String newQuery = URLEncoder.encode(query, "UTF-8");
            String param = "source=tripwjourprod&outfmt=json&q=" + newQuery + "&s=dolphin_trip_search_qp&user_id=" + userId;
            String res = getResultByQP(param);
            JSONObject jsonObject = JSON.parseObject(res);
            JSONObject modules = jsonObject.getJSONObject(MODULES);
            JSONArray divisionAll = (JSONArray) modules.get(DESTINATION_CITY);
            if (CollectionUtils.isEmpty(divisionAll)) {
                return result;
            }
            String multiDestinationTerm = modules.getString(MULTI_DESTINATION_TERM);
            if (StringUtils.equals(multiDestinationTerm, MULTI_DESTINATION_TERM_VALUE)) {
                for (Object divisionStr : divisionAll) {
                    JSONObject division = (JSONObject) divisionStr;
                    getDivision(division, result);
                }
            } else {
                JSONObject division = (JSONObject) divisionAll.get(0);
                getDivision(division, result);
            }
        } catch (Exception e) {
            newLogger.recordDangerException(new LogModel("getDivisionListByQpVipServer").e(e));
        }
        return result;
    }

    private void getDivision(JSONObject division, List<DestinationInfo> result) {
        Integer level = division.getInteger(LEVEL);
        String id = division.getString(ID);
        result.add(new DestinationInfo(id, level));
    }

    /**
     * 发起调用
     * @return
     */
    private String getResultByQP(String param) throws Exception {
        Host host = VIPClient.srvHost(POI_QUERY_VIPSERVER_KEY);
        String url = "http://" + host.toInetAddr() + "/qp?" + param;
        HttpGet httpGet = new HttpGet(url);
        CloseableHttpResponse response = HTTP_CLIENT.execute(httpGet);
        int responseCode = response.getStatusLine().getStatusCode();
        if (responseCode != 200) {
            return null;
        }
        return EntityUtils.toString(response.getEntity(), "UTF-8");
    }


    @AteyeInvoker(description = "测试vipserver")
    public void testVipServer(String vipServer, String query, String cityCode) {
        HttpResponse response = null;
        try {
            Host host = VIPClient.srvHost(vipServer);
            query = URLEncoder.encode(query, "UTF-8");
            String param = "user_lat=22.830437&source=tripwjourprod&outfmt=json&q=" + query + "&s=dolphin_trip_search_qp&user_id=1887956101&user_lon=108.371875&user_city_code=" + cityCode;
            String url = "http://" + host.toInetAddr() + "/qp?" + param;
            HttpGet httpGet = new HttpGet(url);
            response = HTTP_CLIENT.execute(httpGet);
            int responseCode = response.getStatusLine().getStatusCode();
            if (responseCode != 200) {
                return ;
            }
            String res =  EntityUtils.toString(response.getEntity(), "UTF-8");
            Ateye.out.print(res);
        } catch (Exception e) {
            newLogger.recordDangerException(new LogModel("testVipServer").e(e));
        }
    }
}
