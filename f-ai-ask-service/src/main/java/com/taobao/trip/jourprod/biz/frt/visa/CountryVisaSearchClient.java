package com.taobao.trip.jourprod.biz.frt.visa;

import com.alibaba.boot.switchcenter.annotation.SwitchGroup;
import com.alibaba.fastjson.JSON;
import com.alitrip.travel.common.result.QuaResult;
import com.fliggy.travelvc.sbc.dilu.client.model.VisaHotSkuSearchItemDTO;
import com.fliggy.travelvc.sbc.dilu.client.model.VisaHotSkuSearchItemQueryRequest;
import com.fliggy.travelvc.sbc.dilu.client.service.CountryVisaSearchService;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch;
import com.taobao.trip.jourprod.common.sal.annotation.RunCache;
import com.taobao.trip.jourprod.common.sal.annotation.RunLog;
import com.taobao.trip.jourprod.common.sal.tair.impl.LdbTairManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
@SwitchGroup
public class CountryVisaSearchClient {

    @AppSwitch(des = "ai问一问-搜索签证热销sku缓存ttl", level = Switch.Level.p1)
    public static Integer SEARCH_VISA_ITEM_FOR_HOT_SKU_TTL = 60 * 60;

    @Resource
    private CountryVisaSearchService countryVisaSearchService;
    @Resource
    private LdbTairManager ldbTairManager;

    private static final String SEARCH_VISA_ITEM_FOR_HOT_SKU_KEY = "tripOd:searchVisaHotSku:";

    private static final Long USUAL_PLACE_PROV_CODE_BJ = 110000L;

    @RunLog
    public VisaHotSkuSearchItemDTO searchVisaItemForHotSku(VisaHotSkuSearchItemQueryRequest request) {
        String key = SEARCH_VISA_ITEM_FOR_HOT_SKU_KEY + request.getCountryVid() + "_" + request.getVisaTypeVid() + "_" + request.getUsualPlaceProvCode();
        Object value = ldbTairManager.get(key);
        if (Objects.nonNull(value)) {
            return JSON.parseObject(value.toString(), VisaHotSkuSearchItemDTO.class);
        }
        //如果取不到lbs，默认北京
        if (Objects.isNull(request.getUsualPlaceProvCode())) {
            request.setUsualPlaceProvCode(USUAL_PLACE_PROV_CODE_BJ);
        }
        QuaResult<VisaHotSkuSearchItemDTO> quaResult = countryVisaSearchService.searchVisaItemForHotSku(request);
        if (Objects.isNull(quaResult) || Objects.isNull(quaResult.getModule())) {
            return null;
        }
        ldbTairManager.put(key, JSON.toJSONString(quaResult.getModule()), SEARCH_VISA_ITEM_FOR_HOT_SKU_TTL);
        return quaResult.getModule();
    }


    @AteyeInvoker(description = "测试查询签证商品")
    public void testSearchVisaItemForHotSku(String countryVid, String visaTypeVid, String usualPlaceProvCode, String buyerId) {
        VisaHotSkuSearchItemQueryRequest request = new VisaHotSkuSearchItemQueryRequest();
        request.setCountryVid(Long.valueOf(countryVid));
        request.setVisaTypeVid(StringUtils.isNotBlank(visaTypeVid) ? Long.valueOf(visaTypeVid) : null);
        request.setUsualPlaceProvCode(Long.valueOf(usualPlaceProvCode));
        request.setBuyerId(Long.valueOf(buyerId));
        VisaHotSkuSearchItemDTO visaHotSkuSearchItemDTO = searchVisaItemForHotSku(request);
        Ateye.out.print(JSON.toJSONString(visaHotSkuSearchItemDTO));
    }

}
