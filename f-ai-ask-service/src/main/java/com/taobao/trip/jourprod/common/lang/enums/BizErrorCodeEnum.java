/**   
* @Title: BizErrorCodeEnum.java 
* <AUTHOR>
* @date 2014年12月5日 下午2:40:26 
*/
package com.taobao.trip.jourprod.common.lang.enums;

import com.taobao.trip.jourprod.service.facade.enums.BaseEnum;
import org.apache.commons.lang.StringUtils;

public enum BizErrorCodeEnum implements BaseEnum {

    /**
     * 行程3.0相关错误码
     */
    NO_SCENIC_FOUND("NO_SCENIC_FOUND", "未搜索到门票数据"),
    SCENIC_SEARCH_EMPTY("SCENIC_SEARCH_EMPTY", "门票搜索结果为空"),
    SCENIC_SEARCH_FAILED("SCENIC_SEARCH_FAILED", "搜索门票失败"),

    /**************************************以下属于火车票的错误码信息***********************************************/
    NO_TRAIN_STATION("FAIL_BIZ_NO_TRAIN_STATION", "当前车次无站点信息"),

    NO_PUSH_HANDLER_CHECKIN("NO_PUSH_HANDLER_CHECKIN", "没有对应的值机推送handler"),

    NO_PULL_HANDLER_CHECKIN("NO_PUSH_HANDLER_CHEErrorCodeCKIN", "没有对应的值机拉取 handler"),

    JOURNEY_NOT_EXIST("FAIL_BIZ_JOURNEY_NOT_EXIST", "行程单不存在"),

    NO_MATCH_VOUCHER("FAIL_BIZ_NO_MATCH_VOUCHER", "无对应的票信息"),

    CHECKIN_QUERY_ERROR("FAIL_BIZ_CHECKIN_QUERY_ERROR", "值机查询异常"),

    NOT_CHECKIN("FAIL_BIZ_NOT_CHECKIN", "未值机"),

    SYSTEM_ERROR("FAIL_BIZ_FAIL_BIZ_SYSTEM_ERROR", "系统错误"),

    BLACK_USER("FAIL_BIZ_BLACK_USER", "您的账号存在异常"),

    HTTP_ERROR("FAIL_BIZ_HTTP_ERROR", "HTTP请求错误"),

    HSF_ERROR("FAIL_BIZ_HSF_ERROR", "HSF请求错误"),

    SYSTEM_BUSY("FAIL_BIZ_SYSTEM_BUSY", "系统繁忙，请稍后再试"),

    PARAM_ERROR("FAIL_BIZ_PARAM_ERROR", "参数错误"),

    NOT_USE_END("FAIL_BIZ_NOT_USE_END", "还有剩余,不可删除"),

    DB_OP_FAIL("FAIL_BIZ_DB_OP_FAIL", "数据库操作失败"),

    JOURNEY_ERROR("FAIL_BIZ_JOURNEY_ERROR", "行程服务异常"),

    JOURNEY_RECORD_NOT_EXIST("FAIL_BIZ_JOURNEY_RECORD_NOT_EXIST", "行程数据不存在"),

    NO_LOGIN_INFO("FAIL_BIZ_NO_LOGIN_INFO", "客户端登录异常"),

    OUT_BIZ_ERROR("FAIL_BIZ_OUT_BIZ_ERROR", "外部订单处理异常"),

    BAD_RESPONSE("FAIL_BIZ_BAD_RESPONSE", "错误的响应"),

    ADD_MEMBER("FAIL_BIZ_ADD_MEMBER", "添加群用户失败"),

    ADD_CONVERSATION_RULE("FAIL_BIZ_ADD_CONVERSATION_RULE", "添加规则群信息失败"),

    UPDATE_MAPPING("FAIL_BIZ_UPDATE_MAPPING", "更新mapping表失败"),

    APPEND_MAPPING("FAIL_BIZ_APPEND_MAPPING", "系统异常，无法加入"),

    QUERY_MAPPING("FAIL_BIZ_QUERY_MAPPING", "查询mapping表失败"),

    NO_VALID_MAPPING("FAIL_BIZ_NO_VALID_MAPPING", "无有效卡片"),

    QUIT_CONVERSATION("FAIL_BIZ_QUIT_CONVERSATION", "退群失败"),

    CREATE_CONVERSATION("FAIL_BIZ_CREATE_CONVERSATION", "创建群失败"),

    UNSUPPORTED_VOUCHER_TYPE("FAIL_BIZ_UNSUPPORTED_VOUCHER_TYPE", "不支持的卡片类型"),

    TAIR_CACHE_GET_ERROR("FAIL_BIZ_TAIR_CACHE_GET_ERROR", "TAIR缓存读取失败"),

    TAIR_CACHE_PUT_ERROR("FAIL_BIZ_TAIR_CACHE_PUT_ERROR", "TAIR缓存写入失败"),

    TAIR_CACHE_INCR_ERROR("FAIL_BIZ_TAIR_CACHE_INCR_ERROR", "TAIR缓存自增失败"),

    TAIR_CACHE_SETCOUNT_ERROR("TAIR_CACHE_SETCOUNT_ERROR", "TAIR设置数值失败"),

    TAIR_CACHE_DECR_ERROR("FAIL_BIZ_TAIR_CACHE_DECR_ERROR", "TAIR缓存自减失败"),

    TAIR_CACHE_DELETE_ERROR("FAIL_BIZ_TAIR_CACHE_DELETE_ERROR", "TAIR缓存删除失败"), EXCEED_LIMIT(
                                                                                            "FAIL_BIZ_EXCEED_LIMIT",
                                                                                            "待定行程已满"), ALREADY_EXIST(
                                                                                                                     "FAIL_BIZ_ALREADY_EXIST",
                                                                                                                     "该行程已添加"),

    SAL_INVOKE_EXCEPTION("SAL_INVOKE_EXCEPTION", "外部接口调用异常"),

    ANALYSIS_ERROR("FAIL_BIZ_ANALYSIS_ERROR", "不支持的编码格式"),

    TPP_RECOMMEND_ERROR("TPP_RECOMMEND_ERROR", "TPP推荐返回错误"),
    TPP_RECOMMEND_TIMEOUT("TPP_RECOMMEND_TIMEOUT", "TPP推荐超时错误"),

    FLIGGY_HOME_RECOMMEND_PLAN("FLIGGY_HOME_RECOMMEND_PLAN", "飞猪首页推荐行程错误"),
    FLIGGY_HOME_RECOMMEND_PLAN_DEFECT("FLIGGY_HOME_RECOMMEND_PLAN_DEFECT", "飞猪首页推荐行程模块缺失"),


    ALIPAY_NO_REAL_NAME_AUTHENTICATION("ALIPAY_NO_REAL_NAME_AUTHENTICATION", "授权失败，请确认支付宝身份证准确性"),

    NO_ID_CARD_REAL_NAME_AUTHENTICATION("NO_ID_CARD_REAL_NAME_AUTHENTICATION", "授权失败，需使用身份证号进行授权"),

    ALIPAY_ID_NOT_MATCH_TAOBAO_ID("ALIPAY_ID_NOT_MATCH_TAOBAO_ID", "授权失败，当前账户与实名认证信息不匹配"),

    LBS_STRATEGY_IS_CLOSED("LBS_STRATEGY_IS_CLOSED", "用户行中位移策略接口已经关闭"),
    LBS_STRATEGY_ERROR("LBS_STRATEGY_ERROR", "用户行中位移策略解析失败"),
    LBS_STRATEGY_REQUEST_IS_NULL("LBS_STRATEGY_REQUEST_IS_NULL", "用户行中位移策略请求参数为空"),
    MESSAGEID_NOT_EXIST("MESSAGEID_NOT_EXIST", "消息id不存在"),

    SHARE_TOKEN_EXPIRE("SHARE_TOKEN_EXPIRE", "分享token已过期"),



    ;

    /** 枚举码.*/
    private String code;

    /** 枚举描述.*/
    private String msg;

    /**
     * 构造方法.
     * @param code 枚举码
     * @param detail 枚举详情
     */
    BizErrorCodeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /** 
     * 
     * 
     * @return
     */
    public String getName() {
        return name();
    }

    /**
     * 构造方法
     * 
     * @param code
     * @return
     */
    public static BizErrorCodeEnum getEnum(String code) {
        for (BizErrorCodeEnum item : values()) {
            if (StringUtils.equals(code, item.getCode())) {
                return item;
            }
        }
        return null;
    }

    /**
     * Getter method for property <tt>code</tt>.
     * 
     * @return property value of code
     */
    public String getCode() {
        return code;
    }

    /**
     * Getter method for property <tt>msg</tt>.
     * 
     * @return property value of msg
     */
    public String getMsg() {
        return msg;
    }

    /** 
     * @see com.taobao.trip.jourprod.service.facade.enums.jourdyn.service.facade.enums.BaseEnum#getDesc()
     */
    @Override
    public String getDesc() {
        return msg;
    }

    /** 
     * @see com.taobao.trip.jourprod.service.facade.enums.jourdyn.service.facade.enums.BaseEnum#isEqual(String)
     */
    @Override
    public boolean isEqual(String exterCode) {
        return false;
    }
}
