/**
 * Alipay.com Inc.
 * Copyright (c) 2005-2013 All Rights Reserved.
 */
package com.taobao.trip.jourprod.common.lang.utils;

import com.alibaba.common.lang.StringUtil;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Objects;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 日期工具类
 * 
 * <AUTHOR>
 */
public final class CIDateUtil {

    /**logger  */
    private static final Logger logger               = LoggerFactory.getLogger(CIDateUtil.class);

    public final static String  dateFormat           = "MM月dd日";
    public final static String  dateFormat2          = "M月d日";
    public final static String  dateFormat3          = "d日";

    public final static String  newDateFormat        = "MM-dd";
    public final static String  newDateFormat2        = "MM.dd";

    public final static String  simpleDateFormat     = "MM-dd";

    public final static String  dateandtimeFormat    = "MM月dd日HH:mm";
    public final static String  dateandtimeFormat2    = "M月d日HH:mm";

    public final static String  longDateFormat       = "MM月dd日 HH:mm";
    public final static String  shortLongDateFormat  = "M月d日 H:mm";

    public final static String  MONTH_DAY_HOUR       = "MM月dd日 HH点";

    public final static String  longDateFormatV2      = "MM月dd号 HH:mm";

    public final static String  newLongDateFormat    = "yyyy年MM月dd日";
    public final static String  newLongDateFormatV2    = "yyyy年M月d日";

    public final static String  shoreTimeFormat      = "HH:mm";

    public final static String  detailTimeFormat     = "HH:mm:ss";

    public final static String  shortFormat          = "yyyyMMdd";

    public final static String  newFormat            = "yyyy-MM-dd HH:mm:ss";

    public final static String  newTimeFormat        = "yyyy-MM-dd HH:mm";

    public final static String  timeFormat           = "HHmmss";

    public final static String  webFormat            = "yyyy-MM-dd";
    public final static String  webFormat2            = "yyyy.MM.dd";

    public final static String  yearFormat           = "yyyy";
    public final static String  yearFormatV2           = "yyyy年";

    public final static String  monthFormat           = "MM";
    public final static String  shortMonthFormat           = "M月";
    public final static String  dayFormat           = "dd";
    public final static String  hourFormat           = "HH";

    public final static String  monthformat          = "MMddHHmmss";

    public final static String  slashFormat          = "yyyy/MM/dd";

    public final static String  simpleSlashFormat    = "MM/dd";
    public final static String  simpleSlashFormat3    = "MM/dd HH:mm";
    public final static String  simpleSlashFormat2    = "M/d";

    public final static String  monthDateFormat     =  "yyyy-MM";

    public final static String    HH_MM_SS_ONE   =  " 00:00:00";

    public final static String    HH_MM_SS_TWO   =  " 23:59:59";

    public final static long    ONE_DAY_MILL_SECONDS = 86400000;

    /**
     * 转换String类型日期格式
     * 如yyyy-MM-dd转换成yyyyMMdd
     * @return
     */
    public static String changeFormat(String date, String origin, String target) {
        DateFormat originFormat = new SimpleDateFormat(origin);
        DateFormat targetFormat = new SimpleDateFormat(target);
        String ret;
        try {
            Date originDate = originFormat.parse(date);
            ret = targetFormat.format(originDate);
        } catch (ParseException e) {
            return null;
        }
        return ret;
    }

    /**
     * 指定日期格式的字符串转化为Date对象
     * @param str
     * @param format
     * @return
     */
    public static Date stringToDate(String str, String format) {
        DateFormat dateFormat = new SimpleDateFormat(format);
        Date d = null;
        dateFormat.setLenient(false);
        if ((str != null) && (str.length() == format.length())) {
            try {
                d = dateFormat.parse(str);
            } catch (ParseException ex) {
                return null;
            }
        }
        return d;
    }

    /**
     * Date对象转化为指定格式的字符串
     * @param date
     * @param format
     * @return
     */
    public static String dateToString(Date date, String format) {
        try {
            if (date == null) {
                return null;
            }
            DateFormat df = new SimpleDateFormat(format);

            df.setLenient(true);

            return df.format(date);
        } catch (Exception e) {

        }
        return "";
    }

    /**
     * 获取时间 ，例如2020-02-01
     * @param strDate
     * @return
     */
    public static String getDayTime(String strDate) {

        Date date = parseDateNewFormat(strDate);
        if (date == null) {
            return "";
        }
        DateFormat df = new SimpleDateFormat(webFormat);
        df.setLenient(false);

        return df.format(date);
    }

    /**
     * 获取时间 ，例如2020.02.01
     * @param strDate
     * @return
     */
    public static String getDayTime2(String strDate) {

        Date date = parseDateNewFormat(strDate);
        if (date == null) {
            return "";
        }
        DateFormat df = new SimpleDateFormat(webFormat2);
        df.setLenient(false);

        return df.format(date);
    }

    /**
     * 获取时间 ，例如09:02:00
     * @param strDate yyyy-MM-dd HH:mm:ss格式
     * @return
     */
    public static String getDetailTime(String strDate) {

        Date date = parseDateNewFormat(strDate);

        DateFormat df = new SimpleDateFormat(detailTimeFormat);

        df.setLenient(false);

        return df.format(date);
    }

    /**
     * 获取时间 ，例如09:02:00
     * @param strDate yyyy-MM-dd HH:mm格式
     * @return
     */
    public static String getDetailTimeNoSec(String strDate) {

        Date date = parseDateNewTimeFormat(strDate);

        DateFormat df = new SimpleDateFormat(detailTimeFormat);

        df.setLenient(false);

        return df.format(date);
    }

    /**
     * 获取当前日期是星期几
     *
     * @param dt
     * @return 当前日期是星期几
     */
    public static String getWeekOfDate(Date dt) {
        if (dt == null) {
            return "";
        }
        String[] weekDays = { "周日", "周一", "周二", "周三", "周四", "周五", "周六" };
        Calendar cal = Calendar.getInstance();
        cal.setTime(dt);

        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0){
            w = 0;
        }
        return weekDays[w];
    }

    public static String getWeekOfDate(String dateStr) {
        Date date = parseWebDateFormat(dateStr);
        return getWeekOfDate(date);
    }

    public static int getMonthOfDate(Date date) {
        Calendar cale = Calendar.getInstance();
        return cale.get(Calendar.MONTH) + 1;
    }
    /**
     * 获取当前日期是星期几
     *
     * @param dt
     * @return 当前日期是星期几
     */
    public static String getWeekOfDate2(Date dt) {
        if (dt == null) {
            return "";
        }
        String[] weekDays = { "星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六" };
        Calendar cal = Calendar.getInstance();
        cal.setTime(dt);

        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0){
            w = 0;
        }
        return weekDays[w];
    }

    public static String getWeekOfDate2(String dateStr) {
        Date date = parseWebDateFormat(dateStr);
        return getWeekOfDate2(date);
    }

    /**
     * 判断是否今年 true今年
     * 跨年
     * @param date
     * @return
     */
    public static boolean isToYear(Date date) {
        DateFormat df = new SimpleDateFormat(yearFormat);
        df.setLenient(false);

        return StringUtil.equals(df.format(new Date()), df.format(date));
    }

    public static boolean isToYearV2(String time) {
        Date date = parseDateNewFormat(time);
        return isToYear(date);
    }

    public static String getYear(Date date) {
        try {
            if (date == null) {
                return "";
            }
            DateFormat df = new SimpleDateFormat(yearFormat);
            df.setLenient(false);
            return df.format(date);
        } catch (Exception e) {
            return "";
        }
    }

    public static String getShortMonth(Date date) {
        if (date == null) {
            return "";
        }
        DateFormat df = new SimpleDateFormat(shortMonthFormat);
        df.setLenient(false);
        return df.format(date);
    }

    public static String getMonth(Date date) {
        if (date == null) {
            return "";
        }
        DateFormat df = new SimpleDateFormat(monthFormat);
        df.setLenient(false);
        return df.format(date);
    }
    public static String getDay(Date date) {
        if (date == null) {
            return "";
        }
        DateFormat df = new SimpleDateFormat(dayFormat);
        df.setLenient(false);
        return df.format(date);
    }
    public static String getHour(Date date) {
        if (date == null) {
            return "";
        }
        DateFormat df = new SimpleDateFormat(hourFormat);
        df.setLenient(false);
        return df.format(date);
    }

    /**
     * yyyy年MM月dd日
     * @param date
     * @return
     */
    public static String getNewLongDate(Date date) {
        if (date == null) {
            return StringUtils.EMPTY;
        }
        DateFormat df = new SimpleDateFormat(newLongDateFormat);
        df.setLenient(false);
        return df.format(date);
    }

    /**
     * yyyy年M月d日
     * @param date
     * @return
     */
    public static String getNewLongDateV2(Date date) {
        DateFormat df = new SimpleDateFormat(newLongDateFormatV2);
        df.setLenient(false);
        return df.format(date);
    }


    /**
     * 判断是否今天
     */
    public static boolean isToday(Date date) {
        if (date == null) {
            return false;
        }

        DateFormat df = new SimpleDateFormat(shortFormat);
        df.setLenient(false);

        return StringUtil.equals(df.format(new Date()), df.format(date));

    }

    public static boolean isBeforeToday(Date date) {
        DateFormat df = new SimpleDateFormat(shortFormat);
        df.setLenient(false);

        Date date1 = new Date();
        try {
            date1 = df.parse(df.format(new Date()));
        } catch (ParseException e) {
            logger.error("解析时间出现错误。", e);
        }

        return date.before(date1);
    }

    public static String getNewDateString(Date date) {
        if (date == null) {
            return "";
        }
        DateFormat df = new SimpleDateFormat(newDateFormat);

        df.setLenient(false);

        return df.format(date);
    }

    public static String getNewDateString2(Date date) {
        if (date == null) {
            return "";
        }
        DateFormat df = new SimpleDateFormat(newDateFormat2);

        df.setLenient(false);

        return df.format(date);
    }

    public static String getMonthString(Date date) {
        DateFormat df = new SimpleDateFormat(monthDateFormat);

        df.setLenient(false);

        return df.format(date);
    }

    public static String getDateAndTime(Date date) {
        //保险点 虽然基本不会出错
        try {
            DateFormat df = new SimpleDateFormat(dateandtimeFormat);

            df.setLenient(false);
            return df.format(date);
        } catch (Exception e) {
            return "";
        }
    }

    public static String getDateAndTime2(Date date) {
        if (date == null) {
            return StringUtils.EMPTY;
        }
        DateFormat df = new SimpleDateFormat(dateandtimeFormat2);

        df.setLenient(false);

        return df.format(date);
    }

    /**
     * 获取日期， 例如10月06号
     * @param date
     * @return
     */
    public static String getLongDate(Date date) {
        if (date == null) {
            return StringUtils.EMPTY;
        }
        try {
            DateFormat df = new SimpleDateFormat(dateFormat);

            df.setLenient(false);

            return df.format(date);
        } catch (Exception e) {
            logger.error("getLongDate is error! date={}", date, e);
        }
        return StringUtils.EMPTY;
    }

    /**
     * 获取日期， 例如10月06日 12点
     * @param date
     * @return
     */
    public static String getMonthDayHour(Date date) {
        if (date == null) {
            return StringUtils.EMPTY;
        }
        try {
            DateFormat df = new SimpleDateFormat(MONTH_DAY_HOUR);

            df.setLenient(false);

            return df.format(date);
        } catch (Exception e) {
            logger.error("getMonthDayHour is error! date={}", date, e);
        }
        return StringUtils.EMPTY;
    }

    /**
     * 获取年，例如：2020
     * @param date
     * @return
     */
    public static String getYearStrV2(Date date) {
        if (date == null) {
            return null;
        }
        DateFormat df = new SimpleDateFormat(yearFormatV2);
        df.setLenient(false);
        return df.format(date);
    }

    /**
     * 获取日期， 例如1月6号
     * @param date
     * @return
     */
    public static String getLongDate3(Date date) {
        if (date == null) {
            return null;
        }

        DateFormat df = new SimpleDateFormat(dateFormat2);

        df.setLenient(false);

        return df.format(date);
    }

    /**
     * 获取日期， 例如6号
     * @param date
     * @return
     */
    public static String getLongDate4(Date date) {
        DateFormat df = new SimpleDateFormat(dateFormat3);
        df.setLenient(false);
        return df.format(date);
    }

    /**
     * 获取日期， 例如01/06
     * @param date
     * @return
     */
    public static String getSimpleSlashFormat(Date date) {
        if (date == null) {
            return StringUtils.EMPTY;
        }
        try {
            DateFormat df = new SimpleDateFormat(simpleSlashFormat);

            df.setLenient(false);

            return df.format(date);
        } catch (Exception e) {
            logger.error("getSimpleSlashFormat is error, date={}", date, e);
        }
        return StringUtils.EMPTY;
    }

    /**
     * 获取日期， 例如1月6号
     * @param date
     * @return
     */
    public static String getSimpleSlashFormat2(Date date) {
        if (date == null) {
            return null;
        }
        DateFormat df = new SimpleDateFormat(simpleSlashFormat2);

        df.setLenient(false);

        return df.format(date);
    }

    public static String getSimpleSlashFormat3(Date date) {
        if (date == null) {
            return null;
        }
        DateFormat df = new SimpleDateFormat(simpleSlashFormat3);

        df.setLenient(false);

        return df.format(date);
    }




    /**
     * 获取日期， 例如2018年10月6号
     * @param date
     * @return
     */
    public static String getLongDate2(Date date) {

        if (date == null) {
            return StringUtils.EMPTY;
        }

        DateFormat df = new SimpleDateFormat(newLongDateFormat);

        df.setLenient(false);

        return df.format(date);
    }

    /**
     * 获取日期， 例如10-06
     * @param date
     * @return
     */
    public static String getSimpleLongDate(Date date) {
        if (date == null) {
            return null;
        }
        DateFormat df = new SimpleDateFormat(simpleDateFormat);

        df.setLenient(false);

        return df.format(date);
    }

    public static Date getAfterDay(Date date, int days) {

        return new Date(date.getTime() + (ONE_DAY_MILL_SECONDS * days));

    }

    /**
     * 根据月份偏移量得到那一月的第一天.
     * @param monthOffset 月份偏移量
     * @return 月份的第一天
     */
    public static Date getFirstMonthDay(int monthOffset) {

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, monthOffset);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    /**
     * 根据月份偏移量得到那一月的最后一天(如果是当月，则取当前时间往后一天的0点).
     * @param monthOffset 月份偏移量
     * @return 月份的最后一天
     */
    public static Date getLastMonthDay(int monthOffset) {

        Calendar calendar = Calendar.getInstance();
        if (monthOffset == 0) {
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            return calendar.getTime();
        }
        calendar.add(Calendar.MONTH, monthOffset + 1);
        calendar.set(Calendar.DAY_OF_MONTH, 0);

        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);

        return calendar.getTime();
    }

    /**
     * 获取日期， 例如10月6号
     * @param date 日期
     * @return 字符串类型日期
     */
    public static String getDate(Date date) {

        if (date == null) {
            return null;
        }
        DateFormat df = new SimpleDateFormat(dateFormat);

        df.setLenient(false);

        return df.format(date);
    }

    /**
     * 获取时间， 例如09:02
     * @param date 日期
     * @return 字符串类型时间
     */
    public static String getTime(Date date) {
        if (date == null) {
            return null;
        }
        DateFormat df = new SimpleDateFormat(shoreTimeFormat);

        df.setLenient(false);

        return df.format(date);
    }

    /**
     * 获取间隔小时.
     * 
     * @param one
     * @param two
     * @return
     */
    public static long getDiffHours(Date one, Date two) {
        Calendar sysDate = new GregorianCalendar();

        sysDate.setTime(one);

        Calendar failDate = new GregorianCalendar();

        failDate.setTime(two);
        return (sysDate.getTimeInMillis() - failDate.getTimeInMillis()) / (60 * 60 * 1000);
    }

    /**
     * 获取间隔自然日天数
     * one - two
     * @param one
     * @param two
     * @return
     */
    public static long getDiffNatureDays(Date one, Date two) {
        if (one == null || two == null) {
            return 0L;
        }
        Date oneBeginDate = getDayBegin(one);
        Date twoBeginDate = getDayBegin(two);
        return getDiffDays(oneBeginDate, twoBeginDate);
    }

    /**
     * 获取间隔天数.
     * a - b
     * @param one a
     * @param two b
     * @return
     */
    public static long getDiffDays(Date one, Date two) {
        if (one == null || two == null) {
            return 0L;
        }
        Calendar sysDate = new GregorianCalendar();

        sysDate.setTime(one);

        Calendar failDate = new GregorianCalendar();

        failDate.setTime(two);
        return (sysDate.getTimeInMillis() - failDate.getTimeInMillis()) / (24 * 60 * 60 * 1000);
    }

    /**
     * 获取间隔分钟.
     * one - two
     * @param one
     * @param two
     * @return
     */
    public static long getDiffMinutes(Date one, Date two) {
        if (one == null || two == null) {
            return 0;
        }
        Calendar sysDate = new GregorianCalendar();

        sysDate.setTime(one);

        Calendar failDate = new GregorianCalendar();

        failDate.setTime(two);
        return (sysDate.getTimeInMillis() - failDate.getTimeInMillis()) / (60 * 1000);
    }

    /**
     * 获取当前时间和目标时间的间隔秒数
     * @param target 目标时间
     * @return 目标时间 - 当前时间
     */
    public static long getDiffSeconds(Date target) {
        Calendar failDate = new GregorianCalendar();

        failDate.setTime(target);

        return (failDate.getTimeInMillis() - System.currentTimeMillis()) / (1000);
    }

    /**
     * 获取间隔秒数.
     * one - two
     * @param one
     * @param two
     * @return
     */
    public static long getDiffSeconds(Date one, Date two) {
        Calendar sysDate = new GregorianCalendar();

        sysDate.setTime(one);

        Calendar failDate = new GregorianCalendar();

        failDate.setTime(two);

        return (sysDate.getTimeInMillis() - failDate.getTimeInMillis()) / (1000);
    }

    /**
     * 获取明天零点的日期
     * @return
     */
    public static Date getTomorrowStartDate() {
        try {
            Date tomorrow = addDays(new Date(), 1);
            String strTomorrow = dateToString(tomorrow, webFormat);
            return stringToDate(strTomorrow, webFormat);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取间隔分钟.
     *
     * @param one
     * @param two
     * @return
     */
    public static long getDiffMinutes(String one, String two) {
        Date oneDate = parseDateNewFormat(one);
        Date twoDate = parseDateNewFormat(two);
        return getDiffMinutes(oneDate, twoDate);
    }

    /**
     * 获取间隔时间
     * 格式 3小时10分
     * 
     * @param one
     * @param two
     * @return
     */
    public static String getDiffString(Date one, Date two) {
        return getDiffStringWithUnit(one, two, "小时", "分", 0L);
    }

    /**
     * 获取间隔时间
     * 格式 x小时x分钟
     *
     * @param one
     * @param two
     * @return
     */
    public static String getDiffStringWithFullMinute(Date one, Date two, long minMillis) {
        return getDiffStringWithUnit(one, two, "小时", "分钟", minMillis);
    }

    /**
     * 获取间隔时间
     * 格式 x小时x分钟
     *
     * @param one
     * @param two
     * @param hourUnit
     * @param minuteUnit
     * @param minMillis     最小展示时间，对于某些时间字符串计算逻辑，希望有一个最小值
     * @return
     */
    public static String getDiffStringWithUnit(Date one, Date two, String hourUnit, String minuteUnit, long minMillis) {
        if (one == null || two == null) {
            return StringUtils.EMPTY;
        }

        Calendar sysDate = new GregorianCalendar();

        sysDate.setTime(one);

        Calendar failDate = new GregorianCalendar();

        failDate.setTime(two);
        long differentMillis =  sysDate.getTimeInMillis() - failDate.getTimeInMillis();
        differentMillis = Math.max(differentMillis, minMillis);
        long i = (differentMillis) / (60 * 60 * 1000);

        long f = differentMillis - 60 * 60 * 1000 * i;

        String res = "";

        if (i != 0) {
            res = i + hourUnit;
        }
        if (f == 0) {
            return res;
        } else {
            long k = f / (60 * 1000);
            return res + k + minuteUnit;
        }
    }

    /**
     * yyyyMMdd
     * @param date
     * @return
     */
    public static String getDateString(Date date) {
        DateFormat df = getNewDateFormat(shortFormat);

        return df.format(date);
    }

    /**
     * 
     * @param pattern
     * @return
     */
    public static DateFormat getNewDateFormat(String pattern) {
        DateFormat df = new SimpleDateFormat(pattern);

        df.setLenient(false);
        return df;
    }

    /**
     * 日期格式 YYYY-MM-dd HH:mm:ss
     * @param sDate
     * @return
     */
    public static Date parseDateNewFormat(String sDate) {
        DateFormat dateFormat = new SimpleDateFormat(newFormat);
        Date d = null;
        dateFormat.setLenient(false);
        if ((sDate != null) && (sDate.length() == newFormat.length())) {
            try {
                d = dateFormat.parse(sDate);
            } catch (ParseException ex) {
                return null;
            }
        }
        return d;
    }

    public static Date getRandomDateTime(int year,int month,int day,int hour,int minute,int second) {

        try {
            Calendar calendar =  Calendar.getInstance();
            calendar.set(Calendar.YEAR, year);
            calendar.set(Calendar.MONTH, month-1);
            calendar.set(Calendar.DAY_OF_MONTH, day);

            calendar.set(Calendar.HOUR_OF_DAY, hour);
            calendar.set(Calendar.MINUTE, minute);
            calendar.set(Calendar.SECOND, second);
            Date date = calendar.getTime();
            return date;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 2014-12-19 12:23:45
     * 
     * @return
     */
    public static String getTimeToString(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(newFormat);
        String res = null;
        if (date != null) {
            try {
                res = sdf.format(date);
            } catch (Exception e) {
                return null;
            }
        }
        return res;
    }

    /**
     * 转换时间戳
     * @param timestamp
     * @return
     */
    public static String getTimestampToString(Long timestamp) {
        SimpleDateFormat sdf = new SimpleDateFormat(newFormat);
        String res = null;
        if (timestamp != null) {
            try {
                res = sdf.format(new Date(timestamp));
            } catch (Exception e) {
                return null;
            }
        }
        return res;
    }

    /**
     * 2014-12-19 12:23
     * 
     * @return
     */
    public static String getTimeToStringNotHaveSecond(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(newTimeFormat);
        String res = null;
        if (date != null) {
            try {
                res = sdf.format(date);
            } catch (Exception e) {
                return null;
            }
        }
        return res;
    }

    /**
     * 日期格式 YYYY-MM-dd HH
     * @param sDate
     * @return
     */
    public static Date parseDateNewTimeFormat(String sDate) {
        try {
            DateFormat dateFormat = new SimpleDateFormat(newTimeFormat);
            Date d;
            dateFormat.setLenient(false);
            try {
                d = dateFormat.parse(sDate);
            } catch (ParseException ex) {
                return null;
            }
            return d;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 
     * @param sDate
     * @return
     */
    public static Date parseDateShortFormat(String sDate) {
        DateFormat dateFormat = new SimpleDateFormat(shortFormat);
        Date d = null;
        dateFormat.setLenient(false);
        if ((sDate != null) && (sDate.length() == shortFormat.length())) {
            try {
                d = dateFormat.parse(sDate);
            } catch (ParseException ex) {
                return null;
            }
        }
        return d;
    }

    public static Date parseWebDateFormat(String date, Date defaultDate) {
        Date result = parseWebDateFormat(date);
        return Objects.isNull(result) ? defaultDate : result;
    }

    /**
     * "yyyy-MM-dd"转成date
     * @param date
     * @return
     */
    public static Date parseWebDateFormat(String date) {
        DateFormat dateFormat = new SimpleDateFormat(webFormat);
        Date d = null;
        dateFormat.setLenient(false);
        if ((date != null) && (date.length() == webFormat.length())) {
            try {
                d = dateFormat.parse(date);
            } catch (ParseException ex) {
                return null;
            }
        }
        return d;
    }

    public static Date parseWebDateFormatV2(String date) {
        DateFormat dateFormat = new SimpleDateFormat(webFormat2);
        Date d = null;
        dateFormat.setLenient(false);
        if ((date != null) && (date.length() == webFormat2.length())) {
            try {
                d = dateFormat.parse(date);
            } catch (ParseException ex) {
                return null;
            }
        }
        return d;
    }


    /**
     * 
     * @param date
     * @return
     */
    public static String getTimeString(Date date) {
        DateFormat dateFormat = getNewDateFormat(timeFormat);

        return getDateString(date, dateFormat);
    }

    /**
     * 
     * @param date
     * @param dateFormat
     * @return
     */
    public static String getDateString(Date date, DateFormat dateFormat) {
        if (date == null || dateFormat == null) {
            return null;
        }

        return dateFormat.format(date);
    }

    /**
     * 计算当前时间几天之后的时间
     * @param date
     * @param days
     * @return
     */
    public static Date addDays(Date date, long days) {
        try {
            if (date == null) {
                return null;
            }
            return addHours(date, days * 24);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 计算当前时间几小时之后的时间
     * @param date
     * @param hours
     * @return
     */
    public static Date addHours(Date date, long hours) {
        if (date == null) {
            return null;
        }
        return addMinutes(date, hours * 60);
    }

    /**
     * 计算当前时间几分钟之后的时间
     *
     * @param date
     * @param minutes
     *
     * @return
     */
    public static Date addMinutes(Date date, long minutes) {
        return addSeconds(date, minutes * 60);
    }

    /**
     * @param date1
     * @param secs
     *
     * @return
     */

    public static Date addSeconds(Date date1, long secs) {
        if (date1 == null) {
            return null;
        }
        return new Date(date1.getTime() + (secs * 1000));
    }

    /**
     * 
     * 
     * @param date1
     * @param secs
     * @return
     */
    public static Date delSeconds(Date date1, long secs) {
        return new Date(date1.getTime() - (secs * 1000));
    }

    public static Date delMinutes(Date date1, long secs) {
        return new Date(date1.getTime() - (secs * 1000 * 60));
    }

    public static Date delHours(Date date1, long hours) {
        return new Date(date1.getTime() - (hours * 1000 * 60 * 60));
    }

    public static Date delDays(Date date1, long days) {
        return new Date(date1.getTime() - (days * 24 * 1000 * 60 * 60));
    }

    public static String getWebDateString(Date date) {
        if (date == null) {
            return StringUtils.EMPTY;
        }
        DateFormat dateFormat = getNewDateFormat(webFormat);

        return getDateString(date, dateFormat);
    }

    public static String getWebDateString2(Date date) {
        if (date == null) {
            return StringUtils.EMPTY;
        }
        DateFormat dateFormat = getNewDateFormat(webFormat2);

        return getDateString(date, dateFormat);
    }

    /**
     * 根据 分钟返回 x小时x分钟
     * @param elapsedTime
     * @return
     */
    public static String minuteToHour(Integer elapsedTime) {

        String hourTime = "";
        int hour = elapsedTime / 60;
        int minute = elapsedTime - hour * 60;
        if (hour > 0) {
            hourTime = hour + "小时";
        }
        if (minute > 0) {
            hourTime = hourTime + minute + "分钟";
        }

        return hourTime;
    }

    /**
     * 根据 分钟返回 x小时x分钟
     * @param elapsedTime
     * @return
     */
    public static String minuteToHourEn(Integer elapsedTime) {

        String hourTime = "";
        int hour = elapsedTime / 60;
        int minute = elapsedTime - hour * 60;
        if (hour > 0) {
            hourTime = hour + "h";
        }
        if (minute > 0) {
            hourTime = hourTime + minute + "m";
        }

        return hourTime;
    }

    /**
     * 根据 秒返回 x小时x分钟
     * @param elapsedTime
     * @return
     */
    public static String secondToHour(Long elapsedTime) {

        String hourTime = "";
        int hour = (int) (elapsedTime / 3600);
        int minute = (int) ((elapsedTime % 3600) / 60);
        if (hour > 0) {
            hourTime = hour + "小时";
        }
        if (minute > 0) {
            hourTime = hourTime + minute + "分钟";
        }

        return hourTime;
    }

    /**
     * 获取当前日期前一天下午17:00~18:00的一个随机时间. yyyymmdd
     * @param
     * @return
     */
    public static Date getBeforeIn1718(String departDay) {
        Date depart = parseDateShortFormat(departDay);
        long seconds = (long) (21600 + 3600 * Math.random());
        return addSeconds(depart, -seconds);
    }

    /**
     *获取指定时间的开始时间
     * @param date
     * @return
     */
    @SuppressWarnings("deprecation")
    public static Date getStartDate(Date date) {
        int hour = date.getHours();
        int minute = date.getMinutes();
        int secode = date.getSeconds();
        date = CIDateUtil.addHours(date, -hour);
        date = CIDateUtil.addMinutes(date, -minute);
        date = CIDateUtil.addSeconds(date, -secode);
        return date;
    }

    /**
     *获取指定时间的结束时间
     * @param date
     * @return
     */
    public static Date getEndDate(Date date) {
        return CIDateUtil.addHours(getStartDate(date), 24);
    }

    /**
     *获取当前小时
     * @param date
     * @return
     */
    @SuppressWarnings("deprecation")
    public static Date getStartHour(Date date) {
        int minute = date.getMinutes();
        int secode = date.getSeconds();
        date = CIDateUtil.addMinutes(date, -minute);
        date = CIDateUtil.addSeconds(date, -secode);
        return date;
    }

    /**
     * 获取当前日期这一天的起始时间.示例：入参date='2014-12-22 10:00:00',返回'2014-12-22 00:00:00'
     * @param date
     * @return
     */
    public static Date getDayBegin(Date date) {
        if (date == null) {
            return null;
        }
        String day = CIDateUtil.getWebDateString(date);
        return CIDateUtil.parseDateNewFormat(day + " 00:00:00");
    }

    /**
     * 获取当前日期这一天的中午12点.示例：入参date='2014-12-22 10:00:00',返回'2014-12-22 12:00:00'
     * @param date
     * @return
     */
    public static Date getDayMid(Date date) {
        if (date == null) {
            return null;
        }
        String day = CIDateUtil.getWebDateString(date);
        return CIDateUtil.parseDateNewFormat(day + " 12:00:00");
    }

    /**
     * 获取当前日期这一天的起始时间.示例：入参date='2014-12-22 10:00:00',返回'2014-12-22 23:59:59'
     * @param date
     * @return
     */
    public static Date getDayEnd(Date date) {
        if (date == null) {
            return null;
        }
        String day = CIDateUtil.getWebDateString(date);
        return CIDateUtil.parseDateNewFormat(day + " 23:59:59");
    }

    /**
     * 判断是否是同样的时间
     * @param date1 时间1
     * @param date2 时间2
     * @return  是否相同
     */
    public static boolean isSameTime(Date date1, Date date2) {
        if (ObjectUtils.anyNull(date1, date2)) {
            return false;
        }
        return date1.equals(date2);
    }

    /**
     * 判断是否属于同一天
     * @param newDate 2015-01-01 10:23:34
     * @param oldDate 2015-01-01 10:23:34
     * @return
     */
    public static boolean isSameDay(String newDate, String oldDate) {
        String nDate = changeFormat(newDate, newFormat, webFormat);
        String oDate = changeFormat(oldDate, newFormat, webFormat);
        return StringUtils.equals(nDate, oDate);
    }

    public static boolean isSameDay(Date newDate, Date oldDate) {
        try {
            if (newDate == null || oldDate == null) {
                return false;
            }
            String nDate = getWebDateString(newDate);
            String oDate = getWebDateString(oldDate);
            return StringUtils.equals(nDate, oDate);
        } catch (Exception e) {
            logger.error("isSameDay(Date newDate, Date oldDate) exception!", e);
            return false;
        }
    }

    /**
     * 判断是否同一个月
     */
    public static boolean isSameMonth(Date newDate, Date oldDate) {
        String nDate = getMonthString(newDate);
        String oDate = getMonthString(oldDate);
        return StringUtils.equals(nDate, oDate);
    }

    /**
     * 判断是否同一年
     */
    public static boolean isSameYear(String newDate, String oldDate) {
        String nDate = changeFormat(newDate, newFormat, yearFormat);
        String oDate = changeFormat(oldDate, newFormat, yearFormat);
        return StringUtils.equals(nDate, oDate);
    }

    public static boolean isSameYear(Date newDate, Date oldDate) {
        String nDate = getYear(newDate);
        String oDate = getYear(oldDate);
        return StringUtils.equals(nDate, oDate);
    }

    public static boolean isValidDate(String date,String strFormat) {
        boolean convertSuccess=true;
        // 指定日期格式为四位年/两位月份/两位日期，注意yyyy/MM/dd区分大小写；
        SimpleDateFormat format = new SimpleDateFormat(strFormat);
        try {
            // 设置lenient为false. 否则SimpleDateFormat会比较宽松地验证日期，比如2007/02/29会被接受，并转换成2007/03/01
            format.setLenient(false);
            format.parse(date);
        } catch (ParseException e) {
            // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
            convertSuccess=false;
        }
        return convertSuccess;
    }

    /**
     * 算相隔天数 查询航班使用 arrTime - depTime
     * @param depTime
     * @param arrTime
     * @return
     */
    public static int spanDays(Date depTime, Date arrTime) {
        if (depTime == null || arrTime == null) {
            return 0 ;
        }
        return Days.daysBetween(new LocalDate(depTime), new LocalDate(arrTime)).getDays() ;
    }

    /**
     * 相隔天数 arrTime-depTime
     * @param depTime
     * @param arrTime
     * @return
     */
    public static int spanDaysByStr(String depTime, String arrTime) {
        Date depDate = parseDateNewFormat(depTime);
        Date arrDate = parseDateNewFormat(arrTime);
        if (depDate == null || arrDate == null) {
            return 0 ;
        }
        return Days.daysBetween(new LocalDate(depTime), new LocalDate(arrTime)).getDays() ;
    }

    /**
     * localDateTime 转 Date
     * @param localDateTime
     * @return
     */
    public static Date convertLocalDateTime2Date(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDateTime.atZone(zoneId);
        return Date.from(zdt.toInstant());
    }

    /**
     * 获取间隔天数.
     * a - b
     * @param one a
     * @param two b
     * @return
     */
    public static long getDiffentDays(Date one, Date two) {
        if (one == null || two == null) {
            return 0L;
        }
        Date oneDate = DateUtils.truncate(one, Calendar.DATE);
        Date twoDate = DateUtils.truncate(two, Calendar.DATE);

        Calendar sysDate = new GregorianCalendar();
        sysDate.setTime(oneDate);

        Calendar failDate = new GregorianCalendar();
        failDate.setTime(twoDate);

        return (sysDate.getTimeInMillis() - failDate.getTimeInMillis()) / (24 * 60 * 60 * 1000);
    }


    /**
     * 获取当前时间往后第N天日期 例如1月6号
     * @param dayId 当前时间往后第N天
     */
    public static String getNDayDate(Integer dayId) {
        Date date = DateUtil.addDays(new Date(), dayId);
        return getLongDate3(date);
    }

    /**
     * 获取当前时间往后第N天日期 例如yyyy-MM-dd
     * @param dayId 当前时间往后第N天
     */
    public static String getNDayDateV2(Integer dayId) {
        Date date = DateUtil.addDays(new Date(), dayId);
        return DateUtil.getWebDateString(date);
    }

    /**
     * 获取时间 ，例如2020-02-01
     * @param strDate
     * @return
     */
    public static String getDayTimeByStr(String strDate) {

        Date date = parseDateNewFormat(strDate);
        if (date == null) {
            return "";
        }
        DateFormat df = new SimpleDateFormat(webFormat);
        df.setLenient(false);

        return df.format(date);
    }

    public static void main(String[] args) {
        try {
            Date date1 = CIDateUtil.stringToDate("2022-06-14 00:00:00", CIDateUtil.newFormat);
            Date date2 = CIDateUtil.stringToDate("2022-06-14 23:25:00", CIDateUtil.newFormat);
            long diff = getDiffDays(date2, date1);
            int diff2 = spanDays(date2, date1);
            System.out.println(diff);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 早于等于标准时间
     */
    public static boolean beforeStandard(Date cmp, Date standard) {
        try {
            return org.apache.commons.lang3.time.DateUtils.truncatedCompareTo(cmp, standard, Calendar.MILLISECOND) <= 0;
        } catch (Exception ignored) {
            return false;
        }
    }

    /**
     * 晚于等于标准时间
     */
    public static boolean afterStandard(Date cmp, Date standard) {
        try {
            return org.apache.commons.lang3.time.DateUtils.truncatedCompareTo(cmp, standard, Calendar.MILLISECOND) >= 0;
        } catch (Exception ignored) {
            return false;
        }
    }

    /**
     * 晚于标准时间
     */
    public static boolean strictAfterStandard(Date cmp, Date standard) {
        try {
            return org.apache.commons.lang3.time.DateUtils.truncatedCompareTo(cmp, standard, Calendar.MILLISECOND) > 0;
        } catch (Exception ignored) {
            return false;
        }
    }

    /**
     * 在标准时间范围内（闭区间）
     */
    public static boolean betweenStandard(Date cmp, Date left, Date right) {
        try {
            return afterStandard(cmp, left) && beforeStandard(cmp, right);
        } catch (Exception ignored) {
            return false;
        }
    }

    public static boolean equalsStandard(Date cmp, Date standard) {
        try {
            return org.apache.commons.lang3.time.DateUtils.truncatedEquals(cmp, standard, Calendar.MILLISECOND);
        } catch (Exception ignored) {
            return false;
        }
    }

    public static int compareTo(Date date1, Date date2) {
        if (Objects.nonNull(date1)) {
            if (Objects.nonNull(date2)) {
                return date1.compareTo(date2);
            } else {
                return 1;
            }
        } else {
            if (Objects.nonNull(date2)) {
                return -1;
            } else {
                return 0;
            }
        }
    }

    /**
     * 在标准时间范围外（开区间）
     */
    public static boolean outsideStandard(Date cmp, Date left, Date right) {
        return !betweenStandard(cmp, left, right);
    }

    /**
     * 离later时间更近
     */
    public static boolean nearLaterDate(Date cmp, Date early, Date later) {
        return Math.abs(early.getTime() - cmp.getTime()) > Math.abs(later.getTime() - cmp.getTime());
    }

    /**
     * 比较one和two哪个距离cmp更近
     * one更近返回1
     * two更近返回-1
     * 同样返回0
     * @param cmp
     * @param one
     * @param two
     * @return
     */
    public static int compareNearDateWithStandard(Date cmp, Date one, Date two) {
        if (Objects.isNull(cmp)) {
            return 0;
        }
        if (Objects.nonNull(one)) {
            if (Objects.isNull(two)) {
                return 1;
            }
        } else {
            if (Objects.nonNull(two)) {
                return -1;
            } else {
                return 0;
            }
        }
        try {
            long diff1 = Math.abs(one.getTime() - cmp.getTime());
            long diff2 = Math.abs(two.getTime() - cmp.getTime());
            return Long.compare(diff2, diff1);
        } catch (Exception e) {
            return 0;
        }
    }

    public static Date getDateByMilliSecond(Long milliSecond) {
        if (milliSecond == null || milliSecond == 0L) {
            return null;
        }

        Date date = new Date();
        date.setTime(milliSecond);
        return date;
    }

    /**
     * 获取时间戳
     *
     * @param timeStr   yyyy-MM-dd HH:mm:ss
     * @return 时间戳，解析失败返回null
     */
    public static Long getTimeStampByTime(String timeStr) {
        Date date = parseDateNewTimeFormat(timeStr);
        if (Objects.nonNull(date)) {
            return date.getTime();
        }
        return null;
    }

    /**
     * 根据订单卡片时间和开始时间时间算出卡片在第几天，从0开始
     * @param currentDate   当前时间
     * @param cardStartDate 开始时间
     * @param cardEndDate   结束时间
     * @return
     */
    public static int getDayNumByStartTimeAndEndTime(Date currentDate, Date cardStartDate, Date cardEndDate) {
        if (ObjectUtils.anyNull(currentDate, cardStartDate, cardEndDate)) {
            return 0;
        }

        // 限制在开始和结束时间之间
        if (currentDate.before(cardStartDate)) {
            currentDate = cardStartDate;
        } else if (currentDate.after(cardEndDate)) {
            currentDate = cardEndDate;
        }

        return (int) CIDateUtil.getDiffNatureDays(currentDate, cardStartDate);
    }

    /**
     * 根据传入时间计算
     * 5分钟内的写刚刚，5分钟-1小时写 xx分钟前，1小时-当日 xx小时前，过了当日就昨天， 过了两个自然日以上建议就不展示
     * @return
     */
    public static String getDateStrByLong(Long curTime) {
        if (ObjectUtils.anyNull(curTime)) {
            return "";
        }
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(curTime), ZoneId.systemDefault());
        LocalDateTime now = LocalDateTime.now();
        Duration duration = Duration.between(localDateTime, now);
        long seconds = duration.getSeconds();
        long minutes = duration.toMinutes();
        long hours = duration.toHours();
        if (seconds < 0) {
            return "";
        } else if (minutes < 1) {
            return "刚刚";
        } else if (minutes < 60) {
            return minutes + "分钟前";
        } else if (hours < 24 && localDateTime.toLocalDate().equals(now.toLocalDate())) {
            return hours + "小时前";
        } else if (localDateTime.toLocalDate().plusDays(1).equals(now.toLocalDate())) {
            return "昨天";
        } else {
            return "";
        }
    }

    /**
     * 获取当天时间
     * xxxx-xx-xx
     */
    public static String getTodayWebString() {
        Date today = new Date();
        return getWebDateString(today);
    }

    /**
     * 获取第二天对应的时间字符串
     * xxxx-xx-xx
     */
    public static String getTomorrowWebString() {
        Date today = new Date();
        Date tomorrow = addDays(today, 1);
        return getWebDateString(tomorrow);
    }
}
