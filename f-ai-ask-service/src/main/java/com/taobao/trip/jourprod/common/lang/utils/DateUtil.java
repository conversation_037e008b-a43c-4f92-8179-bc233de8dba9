package com.taobao.trip.jourprod.common.lang.utils;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 日期处理工具类
 * <AUTHOR>
 */
public class DateUtil {

    /** 完整时间 yyyy-MM-dd HH:mm:ss */
    public static final String SIMPLE = "yyyy-MM-dd HH:mm:ss";

    /** 完整时间 yyyy-MM-dd HH:mm */
    public static final String SIMPLE_MINITE = "yyyy-MM-dd HH:mm";

    /** 年月日 yyyy-MM-dd */
    public static final String DTSIMPLE = "yyyy-MM-dd";

    /** 年月日(无下划线) yyyyMMdd */
    public static final String DTSHORT = "yyyyMMdd";

    /** MM月dd日 */
    public static final String DT_CH_SUB_SHORT = "MM月dd日";

    /** 完整时间 yyyy-MM-dd HH:mm:ss */
    public static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat(SIMPLE);

    /**
     * 
     * @param pattern
     * @return
     */
    public static DateFormat getNewDateFormat(String pattern) {
        DateFormat df = new SimpleDateFormat(pattern);

        df.setLenient(false);
        return df;
    }

    /**
     * 12312434 转Date"yyyy-MM-dd HH:mm:ss"
     * @param time
     * @return
     */
    public static Date getStandardDate(long time) {
        Date dateTime = new Date(time);
        String strTime = getDateString(dateTime, getNewDateFormat(SIMPLE));
        try {
            return dateParse(strTime, SIMPLE);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 按格式转成对应的字符串.
     * @param date
     * @param dateFormat
     * @return
     */
    public static String getDateString(Date date, DateFormat dateFormat) {
        if (date == null || dateFormat == null) {
            return null;
        }

        return dateFormat.format(date);
    }

    /**
     * 格式为yyyy-MM-dd;
     * 
     * @param date
     * @return
     */
    public static String getWebDateString(Date date) {
        if (date == null) {
            return "";
        }

        DateFormat dateFormat = getNewDateFormat(DTSIMPLE);

        return getDateString(date, dateFormat);
    }

    /**
     * 格式为yyyyMMdd;
     *
     * @param date
     * @return
     */
    public static String getDtShortString(Date date) {
        if (date == null) {
            return "";
        }

        DateFormat dateFormat = getNewDateFormat(DTSHORT);

        return getDateString(date, dateFormat);
    }

    /**
     * 格式为MM月dd日;
     *
     * @param date
     * @return
     */
    public static String getDtChSubShortString(Date date) {
        if (date == null) {
            return "";
        }

        DateFormat dateFormat = getNewDateFormat(DT_CH_SUB_SHORT);

        return getDateString(date, dateFormat);
    }


    public static Date dateParse(String dateString, String formatType) throws ParseException {
        if (StringUtils.isBlank(dateString)) {
            return null;
        }
        DateFormat dateFormat = new SimpleDateFormat(formatType);
        return dateFormat.parse(dateString);
    }

    public static String dateFormat(Date date, String formatType) throws ParseException {
        if (null == date) {
            return null;
        }
        DateFormat dateFormat = new SimpleDateFormat(formatType);
        return dateFormat.format(date);
    }

    /**
     * 如果时区不为空，则用时区初始化时间
     * 
     * @param dateFormat
     * @param timeZone
     * @param dateStr
     * @return
     * @throws ParseException
     */
    public static Date parseDate(SimpleDateFormat dateFormat, String timeZone, String dateStr) throws ParseException {
        if (StringUtils.isNotBlank(timeZone)) {
            TimeZone timezone = TimeZone.getTimeZone(timeZone);
            dateFormat.setTimeZone(timezone);
        }
        return dateFormat.parse(dateStr);
    }

    /**
     * 计算当前时间后几个天的时间.
     * @param date 当前时间.
     * @param days 天数.
     * @return 当前时间后几个天的时间.
     */
    public static Date addDays(final Date date, final int days) {
        if (date == null) {
            return null;
        }
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        //化整为零，化一天为24小时
        final int hour = 24;
        calendar.add(Calendar.HOUR, days * hour);
        return calendar.getTime();
    }

    public static Date addHours(Date date, int hours) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR, hours);
        return calendar.getTime();
    }

    /**
     * 
     * @param time
     * @param minute
     * @return
     */
    public static Date addMinute(Date time, int minute) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(time);
        calendar.add(Calendar.MINUTE, minute);
        return calendar.getTime();
    }

    /**
     * 
     * @param time
     * @return
     */
    public static Date addSecond(Date time, int second) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(time);
        calendar.add(Calendar.SECOND, second);
        return calendar.getTime();
    }

    /**
     * 根据飞友返回的时区offset返回时区
     * 
     * @param timezoneOffset
     * @return
     */
    public static String getTimezoneByTimezoneOffset(int timezoneOffset) {
        int hour = (int) (Math.abs(timezoneOffset) / 3600);

        StringBuilder timezoneId = new StringBuilder();
        timezoneId.append("GMT");
        if (timezoneOffset < 0) {
            timezoneId.append("-");
        } else {
            timezoneId.append("+");
        }
        timezoneId.append(String.valueOf(hour));
        return timezoneId.toString();
    }

    /**
     * 
     * @param flightDate yyyy-MM-dd
     * @return
     * @throws ParseException 
     */
    public static boolean isHistoryDate(String flightDate) throws ParseException {
        Date flightTime = dateParse(flightDate, DTSIMPLE);
        Date today = dateParse(dateFormat(new Date(), DTSIMPLE), DTSIMPLE);
        if (Objects.isNull(flightTime) || Objects.isNull(today)) {
            return false;
        }
        if (flightTime.getTime() < today.getTime()) {
            return true;
        }
        return false;
    }

    /**
     *获取指定时间的开始时间
     * @param date
     * @return
     */
    @SuppressWarnings("deprecation")
    public static Date getStartDate(Date date) {
        int hour = date.getHours();
        int minute = date.getMinutes();
        int secode = date.getSeconds();
        date = DateUtil.addHours(date, -hour);
        date = DateUtil.addMinute(date, -minute);
        date = DateUtil.addSecond(date, -secode);
        return date;
    }

    /**
     *获取指定时间的结束时间
     * @param date
     * @return
     */
    public static Date getEndDate(Date date) {
        return DateUtil.addDays(getStartDate(date), 1);
    }

    /**
     *获取当前小时
     * @param date
     * @return
     */
    @SuppressWarnings("deprecation")
    public static Date getStartHour(Date date) {
        int minute = date.getMinutes();
        int secode = date.getSeconds();
        date = DateUtil.addMinute(date, -minute);
        date = DateUtil.addSecond(date, -secode);
        return date;
    }

    //Change to local time 
    public static long changeToLocalTime(Date sourceDate, TimeZone sourceTimeZone) {
        TimeZone localTimeZone = Calendar.getInstance().getTimeZone();
        return timeZoneChange(sourceDate, sourceTimeZone, localTimeZone);

    }

    // 时区转换函数
    public static long timeZoneChange(Date sourceDate, TimeZone sourceTimeZone,
                                      TimeZone targetTimeZone) {
        return (sourceDate.getTime() - sourceTimeZone.getRawOffset()
                + targetTimeZone.getRawOffset());
    }

    //获取日期， 例如10月6号
    public static String getDateByStr(String time) {
        try {
            Date date = dateParse(time, SIMPLE);
            return CIDateUtil.getLongDate(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 字符串时间转换
     * @param timeStr "yyyy-MM-dd HH:mm:ss"
     * @return "yyyy-MM-dd"
     */
    public static String getWebDateString(String timeStr) {

        Date date = CIDateUtil.parseDateNewFormat(timeStr);

        return getWebDateString(date);
    }

    /* 获取行程包开始时间
     * @param startTime yyyy-MM-dd HH:mm:ss
     * @return yyyy-MM-dd 00:00:00
     */
    public static String getPlanStartTime(String startTime) {
        Date startDate = CIDateUtil.parseDateNewFormat(startTime);
        if (startDate == null) {
            return StringUtils.EMPTY;
        }
        Date truncate = DateUtils.truncate(startDate, Calendar.DATE);
        return CIDateUtil.getTimeToString(truncate);
    }

    /**
     * 获取行程包结束时间
     * @param endTime yyyy-MM-dd HH:mm:ss
     * @return yyyy-MM-dd 23:59:59
     */
    public static String getPlanEndTime(String endTime) {
        Date startDate = CIDateUtil.parseDateNewFormat(endTime);
        if (startDate == null) {
            return StringUtils.EMPTY;
        }
        Date truncate = DateUtils.truncate(startDate, Calendar.DATE);
        if (truncate == null) {
            return StringUtils.EMPTY;
        }
        Date date = DateUtils.addDays(truncate, 1);
        if (date == null) {
            return StringUtils.EMPTY;
        }
        Date endDate = DateUtils.addSeconds(date, -1);
        return CIDateUtil.getTimeToString(endDate);
    }

    /**
     * 获取行程包结束时间
     * @param endDate yyyy-MM-dd HH:mm:ss
     * @return yyyy-MM-dd 23:59:59
     */
    public static Date getPlanEndDate(Date endDate) {
        if (endDate == null) {
            return null;
        }
        Date truncate = DateUtils.truncate(endDate, Calendar.DATE);
        if (truncate == null) {
            return null;
        }
        Date date = DateUtils.addDays(truncate, 1);
        if (date == null) {
            return null;
        }
        return DateUtils.addSeconds(date, -1);
    }

    public static Date getPlanEndDate(Date startDate, Long dayNum) {
        if (startDate != null && dayNum != null && dayNum != 0L) {
            Date addDays = CIDateUtil.addDays(startDate, dayNum);
            return DateUtils.addSeconds(addDays, -1);
        }

        return null;
    }

    /**
     * 以当前时间为基准，判断是否在时间范围内, left < 当前时间 < right
     * @param leftAmount    left时间偏移量
     * @param leftField     left偏移单位，例如:java.util.Calendar#DATE
     * @param rightAmount   right时间偏移量
     * @param rightField    right时间偏移单位
     * @return
     */
    public static Boolean betweenInterval(Date targetDate, Integer leftAmount, Integer leftField,  Integer rightAmount, Integer rightField) {
        if(leftAmount == null || rightAmount == null || leftField == null || rightField == null) {
            return false;
        }
        Date curTime = new Date();
        Date leftTime = DateUtils.add(targetDate, leftField, leftAmount);
        Date rightTime = DateUtils.add(targetDate, rightField, rightAmount);
        return curTime.after(leftTime) && curTime.before(rightTime);
    }

    public static void main(String[] args) {
        String result1 = DateUtil.formatDateForMd2Ymd("2024", "4-1");
        System.out.println("Formatted Date with year: " + result1); // 输出: 2024-04-01

        String result2 = DateUtil.formatDateForMd2Ymd(null, "4-1");
        System.out.println("Formatted Date with current year: " + result2); // 输出: 当前年份-04-01
    }

    /**
     *  计算两个日期相差多少天
     * @param start
     * @param end
     * @return
     */
    public static int getDifferDays(long start, long end) {
        if (end < start) {
            return 0;
        }
        return (int) ((end - start) / (24 * 60 * 60 * 1000));
    }


    /**
     * 将日期字符串格式化为指定年份的日期字符串
     * 处理示例：“M-d” 转化为 “yyyy-MM-dd”
     * @param yearStr 目标年份 不传就用当年
     * @param dateStr 目标月日 必传
     * @return
     */
    public static String formatDateForMd2Ymd(String yearStr, String dateStr) {

        // 定义输入日期的格式
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("M-d");

        try {
            // 解析输入日期字符串，获取月和日
            LocalDate parsedDate = LocalDate.parse("2000-" + dateStr, DateTimeFormatter.ofPattern("yyyy-M-d"));

            // 获取当前年份
            int currentYear = Calendar.getInstance().get(Calendar.YEAR);

            // 设置年份为目标年份，如果没有传入则使用当前年份
            int targetYear = (yearStr != null && !yearStr.isEmpty()) ? Integer.parseInt(yearStr) : currentYear;

            // 构造目标日期
            LocalDate targetDate = LocalDate.of(targetYear, parsedDate.getMonth(), parsedDate.getDayOfMonth());

            // 定义输出日期的格式
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            // 返回格式化后的日期字符串
            return targetDate.format(outputFormatter);
        } catch (Exception e) {
            // 捕获异常并返回错误信息
            return "Error: Invalid date format - " + e.getMessage();
        }
    }

}
