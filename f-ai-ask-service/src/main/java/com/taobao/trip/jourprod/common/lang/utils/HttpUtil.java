/**
 * Taobao.com Inc.
 * Copyright (c) 2004-2014 All Rights Reserved.
 */
package com.taobao.trip.jourprod.common.lang.utils;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;

import com.taobao.trip.jourprod.common.lang.enums.BizErrorCodeEnum;
import com.taobao.trip.jourprod.utils.JSONUtil;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.httpclient.Cookie;
import org.apache.commons.httpclient.Header;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.httpclient.MultiThreadedHttpConnectionManager;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.commons.httpclient.params.HttpConnectionManagerParams;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * http请求工具类
 *
 * <AUTHOR>
 */
public class HttpUtil {

    private static final Logger logger = LoggerFactory.getLogger(HttpUtil.class);

    private final static Logger picLogger = LoggerFactory.getLogger("UMEPICSERVER");
    private final static FliggyNewLogger newLogger = LogUtil.getFliggyNewLogger(HttpUtil.class);

    /**
     * 默认超时时间
     */
    private static final Integer defaultTimeOut = 10000;

    private static final Integer TIMEOUT = 60000;

    public static final String CONTENT_TYPE_JSON = "application/json";

    public static final String UTF_8 = "utf-8";
    private static HttpClient httpClient = createDefaultHttpClient();


    public static void init() {
    }

    public static void destroy() {
    }

    /**
     * 创建HttpClient实例，考虑连接池大小和超时配置
     *
     * @param maxConnections 最大连接数
     * @param connectTimeout 连接超时时间(毫秒)
     * @param soTimeout Socket超时时间(毫秒)
     * @return 配置好的HttpClient实例
     */
    public static HttpClient createHttpClient(int maxConnections, int connectTimeout, int soTimeout) {
        // 创建多线程连接管理器
        MultiThreadedHttpConnectionManager connectionManager = new MultiThreadedHttpConnectionManager();

        // 设置连接管理器参数
        HttpConnectionManagerParams params = connectionManager.getParams();

        // 设置最大连接数
        params.setMaxTotalConnections(maxConnections);

        // 设置每个主机的最大连接数
        params.setDefaultMaxConnectionsPerHost(maxConnections / 2);

        // 设置连接超时
        params.setConnectionTimeout(connectTimeout);

        // 设置Socket超时
        params.setSoTimeout(soTimeout);

        // 创建HttpClient并使用连接管理器
        HttpClient httpClient = new HttpClient(connectionManager);

        // 设置重试次数
        httpClient.getParams().setParameter(HttpMethodParams.RETRY_HANDLER,
                new org.apache.commons.httpclient.DefaultHttpMethodRetryHandler(3, false));

        return httpClient;
    }

    /**
     * 使用默认参数创建HttpClient实例
     *
     * @return 默认配置的HttpClient实例
     */
    public static HttpClient createDefaultHttpClient() {
        // 默认设置：100个最大连接，5秒连接超时，30秒socket超时
        return createHttpClient(10000, 3000, 10000);
    }

    public static void main(String[] args) {
        //写一个测试
        System.out.println("resttt:"+doGet("https://www.baidu.com",null));
    }

    /**
     * 可定制header的get请求，如果header为null，则不添加任何header
     *
     * @param requestUrl 请求url
     * @param headers    请求头，格式（注意空格）：xxx::xxx | xxx::xxx
     * @return 失败则返回null
     */
    public static String doGet(String requestUrl, String headers) {
        return doGet(requestUrl, headers, null, -1, 0).getValue();
    }



    private static NameValuePair doGet(String requestUrl, String headers, String proxyHost,
                                       int proxyPort, int redirectCount) {

        GetMethod getMethod = new GetMethod(requestUrl);

        if (headers != null) {
            //添加请求头
            addHeaders(getMethod, headers);
        }

        if (StringUtil.isNotBlank(proxyHost) && proxyPort > 0) {
            httpClient.getHostConfiguration().setProxy(proxyHost, proxyPort);
        }

        try {
            int status = httpClient.executeMethod(getMethod);

            if (status >= 200 && status < 300) {
                //请求成功
                String response = getMethod.getResponseBodyAsString();
                Header[] resHeaders = getMethod.getResponseHeaders();
                LoggerUtil.debug(logger, "请求成功，url=", requestUrl, ", response=", response);
                return new NameValuePair(getHttpCookie(httpClient), response);
            } else if (status == 302) {
                //重定向
                String redirectedUrl = getMethod.getResponseHeader("Location").getValue();
                if (StringUtil.isBlank(redirectedUrl)) {
                    LoggerUtil.warn(logger, "BAD_REDIRECT", null, "重定向URL为空：url=", requestUrl);
                    return null;
                }
                if (StringUtil.equals(redirectedUrl, requestUrl)) {
                    LoggerUtil.warn(logger, "LOOP_REDIRECT", null, "循环重定向：url=", requestUrl);
                    return null;
                }
                if (redirectCount >= 10) {
                    LoggerUtil.warn(logger, "ENDLESS_REDIRECT", null, "重定向次数超过上限：url=", requestUrl);
                    return null;
                }
                return doGet(redirectedUrl, headers, proxyHost, proxyPort, redirectCount + 1);
            } else {
                //请求错误
                LoggerUtil.warn(logger, "BAD_RESPONSE:" + status, null, "HTTP GET请求错误： url=",
                        requestUrl, ", status=", status);
                LoggerUtil.debug(logger, "HTTP GET请求错误： url=", requestUrl, ", status=", status,
                        ", response=", getMethod.getResponseBodyAsString());
                return null;
            }
        } catch (IOException ex) {
            LoggerUtil.warn(logger, "IO_ERROR", ex, "HTTP GET请求异常： url=", requestUrl);
            return null;
        } finally {
            if (getMethod != null) {
                getMethod.releaseConnection();
            }
        }

    }

    private static String getHttpCookie(HttpClient httpClient) {
        StringBuilder strHeader = new StringBuilder();
        Cookie[] cookies = httpClient.getState().getCookies();
        for (Cookie cookie : cookies) {
            String domain = cookie.getDomain();
            String path = cookie.getPath();
            String name = cookie.getName();
            String value = cookie.getValue();
            Date expired = cookie.getExpiryDate();
            boolean isSecure = cookie.getSecure();
            //strHeader.append("domain = " + domain + ";");
            //strHeader.append("path = " + path + ";");
            strHeader.append(name + "=" + value + ";");
            //            if (expired != null) {
            //                strHeader.append("expired = " + expired.toGMTString() + ";");
            //            }
            //            strHeader.append("isSecure = " + isSecure + "\n");
        }
        return strHeader.toString();
    }



    public static NameValuePair doGetWithHeaders(String requestUrl, List<NameValuePair> headers,
                                                 String proxyHost, int proxyPort,
                                                 int redirectCount) {
        return doGetWithHeaders(requestUrl, headers, proxyHost, proxyPort, redirectCount, "utf-8");
    }

    public static NameValuePair doGetWithHeaders(String requestUrl, List<NameValuePair> headers,
                                                 String proxyHost, int proxyPort, int redirectCount,
                                                 String encode) {
        GetMethod getMethod = new GetMethod(requestUrl);

        if (headers != null && headers.size() > 0) {
            //添加请求头
            for (NameValuePair header : headers) {
                getMethod.addRequestHeader(header.getName(), header.getValue());
            }
        }

        HttpClient httpClient = new HttpClient();

        if (StringUtil.isNotBlank(proxyHost) && proxyPort > 0) {
            httpClient.getHostConfiguration().setProxy(proxyHost, proxyPort);
        }

        // 设置超时
        httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(defaultTimeOut);
        httpClient.getHttpConnectionManager().getParams().setSoTimeout(defaultTimeOut);

        try {
            int status = httpClient.executeMethod(getMethod);
            if (status >= 200 && status < 300) {
                //请求成功
                String response = getFromStream(getMethod.getResponseBodyAsStream(), encode);
                String cookieStr = getHttpCookie(httpClient);
                LoggerUtil.debug(logger, "请求成功，url=", requestUrl, ", response=", response);
                return new NameValuePair(cookieStr, response);
            } else if (status == 302) {
                //重定向
                String redirectedUrl = getMethod.getResponseHeader("Location").getValue();
                if (StringUtil.isBlank(redirectedUrl)) {
                    LoggerUtil.warn(logger, "BAD_REDIRECT", null, "重定向URL为空：url=", requestUrl);
                    return null;
                }
                if (StringUtil.equals(redirectedUrl, requestUrl)) {
                    LoggerUtil.warn(logger, "LOOP_REDIRECT", null, "循环重定向：url=", requestUrl);
                    return null;
                }
                if (redirectCount >= 10) {
                    LoggerUtil.warn(logger, "ENDLESS_REDIRECT", null, "重定向次数超过上限：url=", requestUrl);
                    return null;
                }
                return doGetWithHeaders(redirectedUrl, headers, proxyHost, proxyPort,
                        redirectCount + 1);
            } else {
                //请求错误
                LoggerUtil.warn(logger, "BAD_RESPONSE:" + status, null, "HTTP GET请求错误： url=",
                        requestUrl, ", status=", status);
                LoggerUtil.debug(logger, "HTTP GET请求错误： url=", requestUrl, ", status=", status,
                        ", response=", getMethod.getResponseBodyAsString());
                return null;
            }
        } catch (IOException ex) {
            LoggerUtil.warn(logger, "IO_ERROR", ex, "HTTP GET请求异常： url=", requestUrl);
            return null;
        } finally {
            if (getMethod != null) {
                getMethod.releaseConnection();
            }
        }

    }


    /**
     * HTTP post请求。
     *
     * @param postData 请求参数,如果为null表示不带请求参数
     * @return 失败则返回null
     * @throws UnsupportedEncodingException
     */
    public static <T> T doPost(String requestUrl, String postData, Class<T> clazz) {
        String result = doPost(requestUrl, postData, CONTENT_TYPE_JSON, null, -1, null, UTF_8);
        return JSONUtil.parseObject(result, clazz);
    }

    /**
     * HTTP post请求。
     *
     * @param 请求url
     * @param postData 请求参数,如果为null表示不带请求参数
     * @return 失败则返回null
     * @throws UnsupportedEncodingException
     */
    public static String doPost(String requestUrl, String postData, String contentType,
                                String proxyHost, int proxyPort, List<NameValuePair> header, String charset) {

        //2.构造PostMethod的实例
        PostMethod postMethod = new PostMethod(requestUrl);

        if (header != null) {
            for (NameValuePair pair : header) {
                postMethod.addRequestHeader(pair.getName(), pair.getValue());
            }
        }
        if (StringUtil.isNotBlank(charset)) {
            HttpMethodParams params = new HttpMethodParams();
            params.setContentCharset(charset);
            postMethod.setParams(params);
        }

        if (postData != null) {
            //3.把参数值放入到PostMethod对象中
            try {
                postMethod
                        .setRequestEntity(new StringRequestEntity(postData, contentType, "utf-8"));
            } catch (UnsupportedEncodingException ex) {
                LoggerUtil.warn(logger, BizErrorCodeEnum.ANALYSIS_ERROR, ex, "不支持的编码格式");
            }
        }

        try {
            // 4.执行postMethod,调用http接口

            httpClient.executeMethod(postMethod);

            // 5.判断返回状态
            int status = postMethod.getStatusCode();
            // 5.1如果是重定向，那么进行get请求
            if (status == HttpStatus.SC_MOVED_PERMANENTLY
                    || status == HttpStatus.SC_MOVED_TEMPORARILY) {
                // 从头中取出转向的地址
                Header locationHeader = postMethod.getResponseHeader("location");
                String location = null;
                if (locationHeader != null) {
                    location = locationHeader.getValue();
                    GetMethod getMethod = new GetMethod(location);
                    LoggerUtil.debug(logger, "地址重定向location=", location);
                    httpClient.executeMethod(getMethod);
                    int getStatus = getMethod.getStatusCode();
                    if (getStatus >= 200 && getStatus < 300) {
                        //请求成功
                        String response = getMethod.getResponseBodyAsString();
                        LoggerUtil.debug(logger, "请求成功，url=", requestUrl, ", response=", response);
                        return response;
                    } else {
                        //请求错误
                        LoggerUtil.warn(logger, "BAD_RESPONSE:" + status, null,
                                "HTTP POST请求错误： url=", requestUrl, ", status=", status, ", postData=",
                                postData, ", response=", postMethod.getResponseBodyAsString());
                    }

                } else {
                    LoggerUtil.error(logger, "重定向location为null");
                }
                //return;
            } else if (status >= 200 && status < 300) {
                //请求成功
                String response = postMethod.getResponseBodyAsString();
                LoggerUtil.debug(logger, "请求成功，url=", requestUrl, ", response=", response);
                return response;
            } else {
                //请求错误
                LoggerUtil.warn(logger, "BAD_RESPONSE:" + status, null, "HTTP POST请求错误： url=",
                        requestUrl, ", status=", status, ", postData=", postData, ", response=",
                        postMethod.getResponseBodyAsString());
            }

        } catch (IOException ex) {
            LoggerUtil.warn(logger, "IO_ERROR", ex, "HTTP POST请求异常");
        } finally {
            if (postMethod != null) {
                postMethod.releaseConnection();
            }
        }
        return null;
    }

    /**
     * 填充请求头
     *
     * @param getMethod 请求方法
     * @param headers   可配置请求头字符串，格式：xxx::xxx | xxx::xxx
     */
    private static void addHeaders(GetMethod getMethod, String headers) {
        String[] headerDefs = headers.split(" \\| ");
        for (String s : headerDefs) {
            getMethod.addRequestHeader(string2Header(s));
        }
    }

    /**
     * 字符串转换成
     *
     * @param s 格式 xxx:xxx
     * @return 如果s不满足指定格式，返回null
     */
    private static Header string2Header(String s) {
        if (s != null) {
            String[] str = s.split("::");
            if (str.length == 2) {
                return new Header(str[0], str[1]);
            }
        }
        return null;
    }

    public static String getFromStream(InputStream data, String encode) throws IOException {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        byte[] buffer = new byte[256];
        int n;
        while ((n = data.read(buffer)) >= 0) {
            out.write(buffer, 0, n);
        }
        // toString()使用平台默认编码，也可以显式的指定如toString(&quot;GBK&quot;)   
        return out.toString(encode);
    }

    /**
     * 替换url中的中文为编码形式
     *
     * @param httpUrl       http url
     * @param requiredParam 所需参数
     * @return { String}
     */

    public static String transferToEncodedChineseUrl(String httpUrl, String requiredParam) {
        try {
            String value = extractUrlParam(httpUrl, requiredParam);
            //那就是没这个字段
            if (StringUtils.isBlank(value)) {
                return null;
            }
            // 不是中文的不要转码 -已经转码的肯定包含%符号
            if (StringUtils.contains(value, "%")) {
                return null;
            }
            String encodedChinese = URLEncoder.encode(value, "UTF-8");
            if (StringUtils.isBlank(encodedChinese)) {
                return null;
            }
            return StringUtils.replace(httpUrl, value, encodedChinese);
        } catch (Exception e) {
            newLogger.recordDangerException(new LogModel("transferToEncodedChineseUrl").e(e).condition(httpUrl + "_" + requiredParam));
            return null;
        }
    }

    /**
     * 提取url中参数
     *
     * @param httpUrl       http url
     * @param requiredParam 所需参数
     * @return { String}
     */
    public static String extractUrlParam(String httpUrl, String requiredParam) {
        if (StringUtils.isBlank(httpUrl) || StringUtils.isBlank(requiredParam)) {
            return null;
        }
        try {
            URL url = new URL(httpUrl);
            String query = url.getQuery();
            //query: 以& 分隔，from=Zfcb4QSRqj&orderId=2757466874841647323&sellerId=2856437246&biz_fliggy_channel=questionComponent
            if (StringUtils.isBlank(query)) {
                return null;
            }
            String[] paramList = query.split("&");
            for (String param : paramList) {
                String[] kvPair = param.split(("="));
                if (kvPair.length != 2) {
                    continue;
                }
                String key = kvPair[0];
                String value = kvPair[1];
                if (StringUtils.equals(key, requiredParam)) {
                    return value;
                }
            }
            return null;
        } catch (Exception e) {
            newLogger.recordDangerException(new LogModel("extractUrlParam").e(e).condition(httpUrl + "_" + requiredParam));
            return null;
        }
    }


    /**
     * 生成uri
     *
     * @param requestUri 请求uri
     * @param urlParams  url参数
     * @return { URI}
     */
    public static URI generateURL(String requestUri, Map<String, String> urlParams) {
        try {
            URIBuilder uriBuilder = new URIBuilder(requestUri);
            if (urlParams != null && !urlParams.isEmpty()) {
                for (Map.Entry<String, String> entry : urlParams.entrySet()) {
                    uriBuilder.setParameter(entry.getKey(), entry.getValue());
                }
            }
            return uriBuilder.build();
        } catch (Exception e) {
            newLogger.recordNormalException(new LogModel("generateURI").e(e).request(requestUri).response(JSON.toJSONString(urlParams)));
            return null;
        }
    }

    /**
     * @param url 接口地址(无参数)
     * @param urlParams 拼接参数集合
     * @Description get请求URL拼接参数
     */
    public static String generateAppendUrl(String url, Map<String, String> urlParams) {
        if (StringUtils.isBlank(url)  ) {
            return null;
        }
        try {
            if (MapUtils.isNotEmpty(urlParams)) {
                StringBuffer buffer = new StringBuffer();
                Iterator<Map.Entry<String, String>> iterator = urlParams.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<String, String> entry = iterator.next();
                    if (StringUtils.isEmpty(buffer.toString())) {
                        buffer.append("?");
                    } else {
                        buffer.append("&");
                    }
                    buffer.append(entry.getKey()).append("=").append(entry.getValue());
                }
                url += buffer.toString();
            }
            return url;
        } catch (Exception e) {
            newLogger.recordNormalException(new LogModel("generateURI").e(e).request(url).response(JSON.toJSONString(urlParams)));
            return null;
        }
    }


    /**
     * 附加参数
     * 接口地址(可能有参数)
     *
     * @param url       url
     * @param urlParams url参数
     * @return { String}
     */
    public static String appendParams(String url, Map<String, String> urlParams) {
        try {
            if (StringUtils.isEmpty(url)) {
                return url;
            }
            if (MapUtils.isEmpty(urlParams)) {
                return url;
            }
            //那url就是原始的 无参数的url
            if (!url.contains("?")) {
                return generateAppendUrl(url, urlParams);
            }

            //此时url已经有参数了 那么只能通过&进行链接

            StringBuilder buffer = new StringBuilder();
            for (Map.Entry<String, String> entry : urlParams.entrySet()) {
                buffer.append("&");
                buffer.append(entry.getKey()).append("=").append(entry.getValue());
            }
            //最后一个字符如果为&
            if (url.endsWith("&")) {
                url = url.substring(0, url.length() - 1);
            }
            url += buffer.toString();
            return url;
        } catch (Exception e) {
            newLogger.recordNormalException(new LogModel("appendParams").e(e).request(url).response(JSON.toJSONString(urlParams)));
            return url;
        }
    }


}
