package com.taobao.trip.jourprod.common.lang.utils;

import com.google.common.collect.Lists;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import static com.taobao.trip.jourprod.common.lang.utils.CIDateUtil.newFormat;
import static com.taobao.trip.jourprod.common.lang.utils.CIDateUtil.webFormat;

/**
 * Created by mojin on 2018/9/5.
 */
@Slf4j
public class JourPlanUtil {
    /// 赤道半径
    public static final double EARTH_RADIUS = 6378137;
    public static final Long CHINA_COUNTRY_ID = 100000L;
    public static final String CHINA_COUNTRY_NAME = "中国";

    public static final String CHINA_COUNTRY_CODE = "CN";
    //港澳台
    public static List<Long> CHINA_CITY_IDS = Lists.newArrayList();

    static {
        //香港
        CHINA_CITY_IDS.add(203090L);
        CHINA_CITY_IDS.add(810100L);
        //澳门
        CHINA_CITY_IDS.add(200525L);
        CHINA_CITY_IDS.add(820100L);
        //台北
        CHINA_CITY_IDS.add(200496L);
        CHINA_CITY_IDS.add(710100L);
        //高雄
        CHINA_CITY_IDS.add(200497L);
        CHINA_CITY_IDS.add(710200L);
        //台中
        CHINA_CITY_IDS.add(200499L);
        CHINA_CITY_IDS.add(710400L);
        //台南市
        CHINA_CITY_IDS.add(710300L);
        //金门县
        CHINA_CITY_IDS.add(710500L);
        //基隆市
        CHINA_CITY_IDS.add(710700L);
        //新竹市
        CHINA_CITY_IDS.add(710800L);
        //嘉义市
        CHINA_CITY_IDS.add(710900L);
        //新北市
        CHINA_CITY_IDS.add(711100L);
        //花莲县
        CHINA_CITY_IDS.add(712600L);
    }
    /**
     * mapbox地图url
     */
    public static String MAP_BOX_MAP_URL = "https://h5.m.taobao.com/trip/dest/city-map/index.html?spm=a2o8d.corp_prod_issue_detail.0.0.20e020535hsgSs&_projVer=0.1.47";

    /**
     * 是否新的版本
     * @param version
     * @return
     */
    public static Boolean isNewVersion(String version, String appVersion2) {
        try {
            if (StringUtils.isEmpty(version)) {
                return false;
            }
            //如果是新版本 >= 0
            int result = versionCompare(version, appVersion2);
            if (result >= 0) {
                return true;
            }
        } catch (Exception e) {
            log.error("JourPlanUtil | isNewVersion is error! version={}, appVersion2={}", version, appVersion2, e);
        }
        return false;
    }

    /*
    比较Version
     */
    public static int versionCompare(String str1, String str2) {
        String[] vals1 = str1.split("\\.");
        String[] vals2 = str2.split("\\.");
        int i = 0;
        // set index to first non-equal ordinal or length of shortest version string
        while (i < vals1.length && i < vals2.length && vals1[i].equals(vals2[i])) {
            i++;
        }
        // compare first non-equal ordinal number
        if (i < vals1.length && i < vals2.length) {
            //如果str1不是数字，则返回-1
            if (!NumberUtils.isNumber(vals1[i]) || !NumberUtils.isNumber(vals2[i])) {
                return -1;
            }
            Integer number1 = NumberUtils.toInt(vals1[i]);
            Integer number2 = NumberUtils.toInt(vals2[i]);
            int diff = number1.compareTo(number2);
            return Integer.signum(diff);
        }
        // the strings are equal or one string is a substring of the other
        // e.g. "1.2.3" = "1.2.3" or "1.2.3" < "*******"
        return Integer.signum(vals1.length - vals2.length);
    }

    /**
     * 是否是苹果
     * @param ttid
     * @return
     */
    public static Boolean isIphone(String ttid) {
        try {
            if (StringUtils.isEmpty(ttid)) {
                return false;
            }
            return ttid.contains("iphone");
        } catch (Exception e) {
            //
        }
        return false;
    }

    /**
     * mapbox地图url
     * @param poiId
     * @param abroad
     * @return
     */
    public static String buildMapBoxUrl(Long poiId, Integer abroad) {
        if (poiId == null) {
            return StringUtils.EMPTY;
        }
        StringBuilder sb = new StringBuilder();
        sb.append(MAP_BOX_MAP_URL).append("&poiId=").append(poiId);
        sb.append("&isAbroad=").append(abroad);//国外1，国内0

        return sb.toString();
    }

    /**
     * 字符串yyyy-MM-dd 转date yyyy-MM-dd HH:mm:ss
     * @param startTime
     * @return
     */
    public static Date getStartDate(String startTime) {
        try {
            if (StringUtils.isEmpty(startTime)) {
                return null;
            }
            Date dayDate = org.apache.commons.lang3.time.DateUtils.parseDate(startTime, webFormat);
            if (dayDate == null) {
                return null;
            }
            String time = org.apache.commons.lang3.time.DateFormatUtils.format(dayDate, newFormat);
            if (StringUtils.isNotEmpty(time)) {
                Date startDate = org.apache.commons.lang3.time.DateUtils.parseDate(time, newFormat);
                return startDate;
            }
        } catch (ParseException e) {
            log.error("PathPlanUtil | getStartDate error! startTime={}", startTime, e);
        }
        return null;
    }

    /**
     * 根据2018-07-08 获取当天yyyy-MM-dd HH:mm:ss 2018-07-08 23:59:59
     * @param endTime
     * @return
     */
    public static Date getEndDate(String endTime) {
        if (StringUtils.isEmpty(endTime)) {
            return null;
        }
        Date date = getStartDate(endTime);
        if (date == null) {
            return null;
        }
        Date dateTime = DateUtils.addDays(date, 1);
        if (dateTime == null) {
            return null;
        }
        return DateUtils.addSeconds(dateTime, -1);
    }

    /**
     * 时间转换
     * @param endTime  yyyy-MM-dd HH:mm:ss
     * @return  2018-07-08 23:59:59
     */
    public static String getEndTime(Date endTime) {
        if (endTime == null) {
            return null;
        }
        String groupEndTime = StringUtils.EMPTY;

        try {
            Date endTruncate = null;
            if (endTime != null) {
                endTruncate = DateUtils.truncate(endTime, Calendar.DATE);
            }
            Date endDate = null;
            if (endTruncate != null) {
                endDate = DateUtils.addDays(endTruncate, 1);
            }
            if (endDate != null) {
                Date dateEnd = DateUtils.addSeconds(endDate, -1);
                groupEndTime = CIDateUtil.getTimeToString(dateEnd);
            }
        } catch (Exception e) {
            log.error("getEndTime is error! endTime={}", endTime, e);
        }
        return groupEndTime;
    }

    /**
     * 根据行程包开始时间和天数确定结束时间
     * @param startDate
     * @param dayNums
     * @return
     */
    public static String getEndDate(String startDate, Integer dayNums) {
        if (dayNums - 1 < 0 || StringUtils.isEmpty(startDate)) {
            return startDate;
        }
        Date start = null;
        try {
            start = DateUtils.parseDate(startDate, new String[]{newFormat});
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Date dayDate = DateUtils.addDays(start, dayNums);
        dayDate = DateUtils.addSeconds(dayDate, -1);
        String result = DateFormatUtils.format(dayDate, newFormat);
        return result;
    }

    /**
     * 获取加dayNums的时间 yyyy-MM-dd HH:mm:ss + dayNums
     * @param startDate
     * @param dayNums
     * @return
     */
    public static Date getAddDate(String startDate, int dayNums) {
        Date start = null;
        if (startDate == null) {
            return start;
        }
        try {
            start = DateUtils.parseDate(startDate, new String[]{newFormat});
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (dayNums - 1 <= 0 || StringUtils.isEmpty(startDate)) {
            return start;
        }
        Date dayDate = DateUtils.addDays(start, dayNums - 1);
        return dayDate;
    }

    /**
     * 获取日期， 例如10月6号
     * @param startDate
     * @param dayNums
     * @return
     */
    public static String getDateStr(String startDate, Integer dayNums) {
        Date start = null;
        if (StringUtils.isEmpty(startDate) || dayNums == null) {
            return null;
        }
        try {
            start = DateUtils.parseDate(startDate, new String[]{newFormat});
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (dayNums - 1 <= 0 || StringUtils.isEmpty(startDate)) {
            return CIDateUtil.getLongDate3(start);
        }
        Date dayDate = DateUtils.addDays(start, dayNums - 1);
        return CIDateUtil.getLongDate3(dayDate);
    }

    /**
     * 获取日期， 例如2018年10月6日
     * @param startDate
     * @param dayNums
     * @return
     */
    public static String getDateStr2(String startDate, Integer dayNums) {
        Date start = null;
        if (StringUtils.isEmpty(startDate) || dayNums == null) {
            return null;
        }
        try {
            start = DateUtils.parseDate(startDate, new String[]{newFormat});
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (dayNums - 1 <= 0 || StringUtils.isEmpty(startDate)) {
            return CIDateUtil.getLongDate2(start);
        }
        Date dayDate = DateUtils.addDays(start, dayNums - 1);
        return CIDateUtil.getLongDate2(dayDate);
    }

    /**
     * 获取日期， 例如2018-10-6
     * @param startDate
     * @param dayNums
     * @return
     */
    public static String getDateTimeStr(String startDate, Integer dayNums) {
        Date start = null;
        if (StringUtils.isEmpty(startDate) || dayNums == null) {
            return null;
        }
        try {
            start = DateUtils.parseDate(startDate, new String[]{newFormat});
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (dayNums - 1 <= 0 || StringUtils.isEmpty(startDate)) {
            return CIDateUtil.getWebDateString(start);
        }
        Date dayDate = DateUtils.addDays(start, dayNums - 1);
        return CIDateUtil.getWebDateString(dayDate);
    }

    public static boolean isAbroad(Long countryId) {
        if (CHINA_COUNTRY_ID.equals(countryId) || countryId == null) {
            return false;
        }
        return true;
    }

    /**
     * 是否国内
     * @param countryId
     * @return
     */
    public static boolean isChina(Long countryId) {
        if (Objects.isNull(countryId)) {
            return false;
        }
        return CHINA_COUNTRY_ID.equals(countryId);
    }

    /**
     * 是否国外 true 国外
     * @param countryName
     * @return
     */
    public static boolean isAbroadByCountryName(String countryName) {
        if (CHINA_COUNTRY_NAME.equals(countryName)) {
            return false;
        }
        return true;
    }

    /**
     * 是否国外 true 国外
     * @param countryCode
     * @return
     */
    public static boolean isAbroadByCountryCode(String countryCode) {
        if (CHINA_COUNTRY_CODE.equals(countryCode)) {
            return false;
        }
        return true;
    }

    /**
     * 是否港澳台
     * @param cityId
     * @return
     */
    public static boolean isChinaOtherCity(Long cityId) {
        if (CHINA_CITY_IDS.contains(cityId)) {
            return true;
        }
        return false;
    }

    /**
     * 根据订单卡片时间和行程包第一天时间算出卡片在第几天
     * @param startDate 行程包第一天时间
     * @param cardStartDate
     * @return
     */
    public static String getDayIdByStartTime(Date startDate, Date cardStartDate) {
        if (startDate == null || cardStartDate == null
                || cardStartDate.before(startDate)) {
            return null;
        }
        long diff = CIDateUtil.getDiffDays(cardStartDate, startDate);
        return String.valueOf(diff + 1);
    }

    /**
     * 获取两个目的地距离
     * @param longitude
     * @param latitude
     * @param destLongitude
     * @param destLatitude
     * @return
     */
    public static String getDistanceByLngLat(String longitude, String latitude, String destLongitude, String destLatitude) {
        String result = null;
        if (StringUtils.isEmpty(longitude) && StringUtils.isEmpty(latitude) && StringUtils.isEmpty(destLongitude)
                || StringUtils.isEmpty(destLatitude)) {
            return result;
        }
        Double disDouble = getDistance(longitude, latitude, destLongitude, destLatitude);
        if (disDouble == null) {
            return result;
        }
        int disInt = disDouble.intValue();
        if (disInt < 1000) {
            result = disInt + "m";
        } else {
            Double dis = disDouble / 1000;
            result = String.format("%.1f", dis) + "km";
        }

        return result;
    }

    public static Double getDistance(String lng, String lat, String lng2, String lat2) {
        Double distance = null;
        if (StringUtils.isEmpty(lng) || StringUtils.isEmpty(lat) || StringUtils.isEmpty(lng2)
                || StringUtils.isEmpty(lat2)) {
            return distance;
        }
        distance = getDistance(Double.parseDouble(lng), Double.parseDouble(lat),
                Double.parseDouble(lng2), Double.parseDouble(lat2));
        return distance;
    }

    public static double getDistance(double lng1, double lat1, double lng2, double lat2) {
        try {
            double radLat1 = rad(lat1);
            double radLat2 = rad(lat2);
            double a = radLat1 - radLat2;
            double b = rad(lng1) - rad(lng2);
            double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1)
                    * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
            s = s * EARTH_RADIUS;
            s = Math.round(s * 10000) / 10000;
            return s;
        } catch (Exception e) {
        }

        return Double.MAX_VALUE;
    }

    private static double rad(double d) {
        return d * Math.PI / 180.0;
    }

    public static Date getStartTime(Date date, Integer dayNum) {
        date = DateUtils.addDays(date, dayNum);
        return DateUtils.truncate(date, Calendar.DATE);
    }

    public static Date getEndTime(Date start, Integer dayNums) {
        Date dayDate = DateUtils.addDays(start, dayNums);
        dayDate = DateUtils.addSeconds(dayDate, -1);
        return dayDate;
    }

    /**
     * 处理往返机票cardId
     * @param cardId
     * @return
     */
    public static String handlerFlightCardId(String cardId) {
        String result = null;
        if(cardId.contains("-")) {
            StringBuffer stringBuffer = new StringBuffer(cardId);
            int index = stringBuffer.indexOf("-");
            StringBuffer sb = stringBuffer.delete(index, stringBuffer.length());
            if (sb != null) {
                result = sb.toString();
            }
        } else {
            result = cardId;
        }
        return result;
    }

    /**
     * 判断是否往返机票
     * @param cardId
     * @return
     */
    public static Boolean judgeGoBackFlighCard(String cardId) {
        if(cardId.contains("-")) {
            return true;
        }
        return false;
    }

    public static < T > List< T > depCopy(List < T > srcList) {
        ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
        try {
            ObjectOutputStream out = new ObjectOutputStream(byteOut);
            out.writeObject(srcList);
            ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
            ObjectInputStream inStream = new ObjectInputStream(byteIn);
            List < T > destList = (List < T > ) inStream.readObject();
            return destList;
        } catch (IOException e) {
            e.printStackTrace();
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 是否包含国外城市 1(国外)|0
     */
    public static int isContainAbraodByCountryIds( List<Long> countryIds) {
        if (CollectionUtils.isEmpty(countryIds)) {
            return 0;
        }
        for (Long countryId : countryIds) {
            if (!CHINA_COUNTRY_ID.equals(countryId)) {
                return 1;
            }
        }
        return 0;
    }

    public static Boolean judgeStrEquals(String typeStr, String bizType) {
        if (StringUtils.isBlank(typeStr) || StringUtils.isBlank(bizType)) {
            return false;
        }

        String[] split = typeStr.split(",");
        for (String type : split) {
            if (StringUtils.equals(type, bizType)) {
                return true;
            }
        }

        return false;
    }

    public static Boolean judgeContainType(List<String> typeList, String bizType) {
        if (CollectionUtils.isEmpty(typeList) || StringUtils.isBlank(bizType)) {
            return false;
        }
        for (String type : typeList) {
            if (StringUtils.equals(type, bizType)) {
                return true;
            }
        }

        return false;
    }

    public static void main(String[] args) {
        Boolean newVersion = isNewVersion("9.0.5", "9.0.5");
        System.out.println(newVersion);
//        String result = getDateStr("2018-09-10 00:00:00", 2);
//        System.out.println(getStartTime(new Date()));
//        System.out.println(getEndTime(getStartTime(new Date()), 2));
    }
}
