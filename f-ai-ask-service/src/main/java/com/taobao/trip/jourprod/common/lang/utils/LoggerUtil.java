/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2014 All Rights Reserved.
 */
package com.taobao.trip.jourprod.common.lang.utils;

import java.text.MessageFormat;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;
import org.slf4j.Logger;

/**
 * 日志工具类
 * <AUTHOR>
 * @version $Id: LoggerUtil.java, v 0.1 2014-8-19 下午4:49:40 hongbao.xhb Exp $
 */
public class LoggerUtil {
    /**摘要日志分割线*/
    public static final String LOG_CHAR_SPLITOR               = ",";
    /**摘要日志分割线*/
    public static final String LOG_CHAR_START                 = "[(";
    /**摘要日志分割线*/
    public static final String LOG_CHAR_END                   = ")]";
    /**类和方法的链接符*/
    public static final String LOG_METHOND_CLASS_CONNECT_CHAR = ".";
    /** 业务参数开始符    */
    public static final String BIZ_MESSAGE_START              = "(";
    /** 业务参数结束符     */
    public static final String BIZ_MESSAGE_END                = ")";
    /**  空字串的分割符号*/
    public static final String NULL_SPLITOR                   = "-";

    /**
     * 输出debug level的log信息.
     * @param LOGGER 日志记录器
     * @param message log信息,如:<code>xxx{0},xxx{1}...</code>
     * @param params log格式化参数,数组length与message参数化个数相同, 如:<code>Object[]  object=new Object[]{"xxx","xxx"}</code>
     */
    public static void newDebug(Logger LOGGER, String message, Object... params) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(format(message, params));
        }
    }

    /**
     * 输出warn level的log信息.
     * @param LOGGER 日志记录器
     * @param message log信息,如:<code>xxx{0},xxx{1}...</code>
     * @param params log格式化参数,数组length与message参数化个数相同, 如:<code>Object[]  object=new Object[]{"xxx","xxx"}</code>
     */
    public static void newWarn(Logger LOGGER, String message, Object... params) {
        if (LOGGER.isWarnEnabled()) {
            LOGGER.warn(format(message, params));
        }
    }

    /**
     * 输出info level的log信息.
     * @param LOGGER 日志记录器
     * @param message log信息,如:<code>xxx{0},xxx{1}...</code>
     * @param params log格式化参数,数组length与message参数化个数相同, 如:<code>Object[]  object=new Object[]{"xxx","xxx"}</code>
     */
    public static void newInfo(Logger LOGGER, String message, Object... params) {
        if (LOGGER.isInfoEnabled()) {
            LOGGER.info(format(message, params));
        }
    }

    /**
     * 输出error level的log信息.
     * @param throwable 异常对象
     * @param LOGGER  日志记录器
     * @param message log信息,如:<code>xxx{0},xxx{1}...</code>
     * @param params log格式化参数,数组length与message参数化个数相同, 如:<code>Object[]  object=new Object[]{"xxx","xxx"}</code>
     */
    public static void newError(Throwable throwable, Logger LOGGER, String message,
                                Object... params) {
        if (LOGGER.isErrorEnabled()) {
            LOGGER.error(format(message, params), throwable);
        }
    }

    /**
     * 日志信息参数化格式化
     *
     * @param message log信息,如:<code>xxx{0},xxx{1}...</code>
     * @param params log格式化参数,数组length与message参数化个数相同, 如:<code>Object[]  object=new Object[]{"xxx","xxx"}</code>
     */
    private static String format(String message, Object... params) {
        if (params != null && params.length != 0) {
            return MessageFormat.format(message, params);
        }
        return message;

    }

    /**
     * <pre>
     * 记录debug日志 
     * </pre>
     * @param logger     
     * @param message
     */
    public static void debug(final Logger logger, String... messages) {
        if (logger.isDebugEnabled()) {
            logger.debug(mergeStrings(messages));
        }
    }

    /**
     * <pre>
     * 记录debug日志
     * </pre>
     * @param logger     
     * @param message
     */
    public static void debug(final Logger logger, Object... messages) {
        if (logger.isDebugEnabled()) {
            logger.debug(mergeObjects(messages));
        }
    }

    /**
     * <pre>
     * 记录Info日志
     * </pre>
     * @param logger     
     * @param message
     */
    public static void info(final Logger logger, Object... messages) {
        if (logger.isInfoEnabled()) {
            logger.info(mergeObjects(messages));
        }
    }

    /**
     * <pre>
     * 记录warn日志
     * </pre>
     * @param logger     
     * @param message
     */
    public static void warn(final Logger logger, Object... messages) {
        if (logger.isWarnEnabled()) {
            logger.warn(mergeObjects(messages));
        }
    }

    /**
     * <pre>
     * 记录错误日志 
     * <li>该方法不打印异常堆栈,如非必要不要使用该方法打印日志</li>
     * </pre>
     * @param logger     
     * @param message
     */
    public static void error(final Logger logger, String... messages) {
        if (logger.isErrorEnabled()) {
            logger.error(mergeStrings(messages));
        }

    }

    /**
     * <pre>
     * 记录error日志
     * <li>该方法不打印异常堆栈,如非必要不要使用该方法打印日志</li>
     * </pre>
     * @param logger     
     * @param message
     */
    public static void error(final Logger logger, Object... messages) {
        if (logger.isErrorEnabled()) {
            logger.error(mergeObjects(messages));
        }
    }

    /**
     * 记录error日志
     * @param logger     
     * @param message
     */
    public static void error(final Logger logger, Throwable e, Object... messages) {
        if (logger.isErrorEnabled()) {
            logger.error(mergeObjects(messages), e);
        }
    }

    /**
     * <pre>
     * 记录错误日志 
     * </pre>
     * @param logger
     * @param e
     * @param messages
     */
    public static void error(final Logger logger, Throwable e, String... messages) {
        if (logger.isErrorEnabled()) {
            if (e == null) {
                logger.error(mergeStrings(messages));
            } else {
                logger.error(mergeStrings(messages), e);
            }
        }
    }

    /**
     * messages对象处理类
     * 
     * 将string数组合并为一个字符串返回
     * @param strings
     * @return
     */
    private static String mergeStrings(String[] strings) {
        if (strings == null) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (String string : strings) {
            stringBuilder.append(string);
        }
        return stringBuilder.toString();
    }

    /**
     * messages对象处理类
     * 
     * 将string数组合并为一个字符串返回
     * @param strings
     * @return
     */
    private static String mergeObjects(Object[] objects) {
        if (objects == null) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (Object obj : objects) {
            if (obj == null) {
                continue;
            }
            if (obj instanceof String) {
                stringBuilder.append(obj);
            } else {
                stringBuilder.append(obj.toString());
            }
        }
        return stringBuilder.toString();
    }

    /**
     * <pre>
     * 将对象参数转换成字符串
     * </pre>
     * 
     *      字符串格式为<br>
     *      <pre>
     *      Person[name=John Doe,age=33,smoker=false]
     *      </pre>
     *      
     * @param object
     * @return
     */
    public static String toString(Object object) {
        if (object == null) {
            return "null";
        }
        if (object instanceof String) {
            return (String) object;
        }
        return ToStringBuilder.reflectionToString(object, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
