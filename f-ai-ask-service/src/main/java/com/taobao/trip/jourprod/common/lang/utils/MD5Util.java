package com.taobao.trip.jourprod.common.lang.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by <PERSON><PERSON><PERSON> on 15/9/7.
 */
public class MD5Util {

    private static Logger logger = LoggerFactory.getLogger(MD5Util.class);

    /**
     * MD5加密
     * @param data
     * @return
     * @throws NoSuchAlgorithmException
     */

    public static String md5(String data) {
        MessageDigest md = null;
        try {
            md = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            LoggerUtil.error(logger, "【MD5加密出现异常。】data = " + data);
            return "error";
        }
        md.update(data.getBytes());
        byte[] buffer = md.digest();
        return toHex(buffer);
    }

    /**
     * 每一个字节有8位，每4位代表一个16进制，则16位的字节数组可以转换为32位的16进制字符串
     * 240 ：11110000
     * 15  ：00001111
     * @param buffer 字节数组
     * @return
     */
    private static String toHex(byte[] buffer) {
        StringBuffer sb = new StringBuffer(buffer.length * 2);
        for (int i = 0; i < buffer.length; i++) {
            sb.append(Character.forDigit((buffer[i] & 240) >> 4, 16));
            sb.append(Character.forDigit(buffer[i] & 15, 16));
        }
        return sb.toString();
    }

}
