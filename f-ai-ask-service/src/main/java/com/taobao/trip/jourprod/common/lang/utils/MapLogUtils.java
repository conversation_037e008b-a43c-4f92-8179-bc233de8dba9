package com.taobao.trip.jourprod.common.lang.utils;

import com.taobao.ateye.util.SpringContextUtils;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.alibaba.fastjson.JSON;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/24
 */
public class MapLogUtils {
    
    private static final Logger logger = LoggerFactory.getLogger("map-api-logger");

    private final static FliggyNewLogger fliggyNewLogger = LogUtil.getFliggyNewLogger(MapLogUtils.class);

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
    
    // 高德地图 API 名称常量
    public static final String AMAP_API_PLACE_DETAIL_V3 = "place_detail_v3";
    public static final String AMAP_API_PLACE_DETAIL_V5 = "place_detail_v5";
    public static final String AMAP_API_PLACE_TEXT_V3 = "place_text_v3";
    public static final String AMAP_API_PLACE_TEXT_V5 = "place_text_v5";
    public static final String AMAP_API_PLACE_AROUND_V3 = "place_around_v3";
    public static final String AMAP_API_PLACE_AROUND_V5 = "place_around_v5";
    public static final String AMAP_API_TRANSIT_PLAN = "transit_plan";
    public static final String AMAP_API_DRIVING_PLAN = "driving_plan";
    public static final String AMAP_API_WALKING_PLAN = "walking_plan";
    public static final String AMAP_API_BATCH_QUERY = "batch_query";
    public static final String AMAP_API_BATCH_QUERY_ALL = "batch_query_all";
    public static final String AMAP_API_STATIC_MAP = "static_map";
    public static final String AMAP_API_STATIC_MAP_DEFAULT = "static_map_default";
    
    // 谷歌地图 API 名称常量
    public static final String GOOGLE_API_SEARCH_TEXT = "searchText";
    public static final String GOOGLE_API_GET_POI_BY_ID = "getPoiById";
    
    /**
     * 记录高德地图API调用日志
     * 
     * @param apiName 调用的接口名
     * @param request 原始请求参数
     * @param response 原始响应结果
     */
    public static void logAmapApiCall(String apiName, Object request, Object response) {
        logApiCall("amap", apiName, request, response);
    }
    
    /**
     * 记录谷歌地图API调用日志
     * 
     * @param apiName 调用的接口名
     * @param request 原始请求参数
     * @param response 原始响应结果
     */
    public static void logGoogleApiCall(String apiName, Object request, Object response) {
        logApiCall("google", apiName, request, response);
    }
    
    /**
     * 记录地图API调用日志
     * 
     * @param source 数据来源（amap或google）
     * @param apiName 调用的接口名
     * @param request 原始请求参数
     * @param response 原始响应结果
     */
    private static void logApiCall(String source, String apiName, Object request, Object response) {
        try {
            String timestamp = DATE_FORMAT.format(new Date());
            
            // 构建JSON格式日志
            Map<String, Object> logData = new HashMap<>();
            logData.put("timestamp", timestamp);
            logData.put("source", source);
            logData.put("api", apiName);
            logData.put("request", request);
            logData.put("response", response);
            
            // 转换为JSON字符串并输出日志
            String logMessage = JSON.toJSONString(logData);
            logger.info(logMessage);
        } catch (Exception e) {
            fliggyNewLogger.recordDangerException(new LogModel("logApiCall").e(e));
        }
    }
}
