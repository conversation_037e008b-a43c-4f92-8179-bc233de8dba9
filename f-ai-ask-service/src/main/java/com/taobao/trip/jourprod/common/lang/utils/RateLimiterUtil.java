package com.taobao.trip.jourprod.common.lang.utils;

import com.ali.com.google.common.util.concurrent.RateLimiter;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import org.springframework.stereotype.Component;

import java.util.concurrent.Semaphore;

/**
 * <AUTHOR>
 * @date 2025/4/17
 */
@Component
public class RateLimiterUtil {
    @Switch(name = "permits", description = "限流许可")
    public static int LIMITER_PERMITS = 40;
    // 默认限流允许同时支持5个
    private volatile static Semaphore semaphore = new Semaphore(LIMITER_PERMITS);

    @Switch(name = "limitMessage", description = "限流提示")
    public static String limitMessage = "AI开小差了，请稍候再试！";

    @AteyeInvoker(description = "设置限流", paraDesc = "permits")
    public void newRateLimiter(int permits) {
        semaphore = new Semaphore(permits);
    }

    public static Semaphore getRateLimiter() {
        return semaphore;
    }
}
