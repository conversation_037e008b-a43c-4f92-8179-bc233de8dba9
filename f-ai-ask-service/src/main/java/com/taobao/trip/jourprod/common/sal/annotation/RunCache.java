package com.taobao.trip.jourprod.common.sal.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义缓存
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RunCache {

    String keyPre() default "";

    /**
     * key
     * @return
     */
    String key() default "";

    /**
     * 缓存时间
     * @return
     */
    int expire() default 0;

}
