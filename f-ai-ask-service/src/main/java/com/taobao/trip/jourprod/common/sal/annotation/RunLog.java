package com.taobao.trip.jourprod.common.sal.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 记录运行日志
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RunLog {

    /**
     * 方法名称
     */
    String methodName() default "";

    /**
     * userid对应spel表达式
     */
    String userId() default "";

    /**
     * 场景，决定报错的时候返回什么信息，默认为空
     */
    String scene() default "";

    /**
     * 结果对应的jsonPath表达式
     */
    String success() default "";

    boolean throwException() default false;

}
