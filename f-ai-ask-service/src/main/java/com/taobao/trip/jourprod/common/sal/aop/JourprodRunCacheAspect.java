package com.taobao.trip.jourprod.common.sal.aop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fliggypoi.utils.SpelUtil;
import com.taobao.trip.jourprod.common.sal.annotation.RunCache;
import com.taobao.trip.jourprod.common.sal.tair.impl.LdbTairManager;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@Aspect
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class JourprodRunCacheAspect {

    private static final FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger("runCache");


    @Resource
    private LdbTairManager ldbTairManager;

    @Pointcut("@annotation(com.taobao.trip.jourprod.common.sal.annotation.RunCache)")
    public void operateCache(){
    }

    @Around("operateCache()")
    public Object doAroundOperate(ProceedingJoinPoint joinPoint) throws Throwable {
        RunCache runCache = ((MethodSignature) joinPoint.getSignature()).getMethod().getAnnotation(RunCache.class);
        Object[] args = joinPoint.getArgs();
        // 解析key
        String keyPre = runCache.keyPre();
        String keySpel = runCache.key();
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature)signature;
        Class<?> returnType = methodSignature.getReturnType();
        String methodName = signature.getName();
        long start = System.currentTimeMillis();

        if (StringUtils.isBlank(keySpel)) {
            return joinPoint.proceed(args);
        }
        Object proceed = null;
        String key = "";
        try {
            keySpel = String.valueOf(SpelUtil.generateKeyBySpEL(keySpel, joinPoint));
            key = keyPre + keySpel;
            Object value = ldbTairManager.get(key);
            if (Objects.nonNull(value)) {
                proceed = JSON.parseObject(value.toString(), returnType);
                return proceed;
            }
            proceed = joinPoint.proceed(args);
            if (Objects.nonNull(proceed)) {
                ldbTairManager.put(key, JSON.toJSONString(proceed), runCache.expire());
            }
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("runCache").e(e).request(key));
        } finally {
            LOGGER.recordOutput(new LogModel(methodName)
                    .request(JSON.toJSONString(args))
                    .cost(System.currentTimeMillis() - start)
                    .response(JSON.toJSONString(proceed))
            );
        }

        return proceed;
    }

}
