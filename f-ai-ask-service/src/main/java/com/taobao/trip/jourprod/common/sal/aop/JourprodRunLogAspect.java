package com.taobao.trip.jourprod.common.sal.aop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fliggypoi.utils.SpelUtil;

import com.taobao.mtop.common.Result;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.jourprod.common.sal.annotation.RunLog;
import com.taobao.trip.jourprod.common.sal.exception.TripJourneyException;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @time 2023/7/13 10:47
 */
@Aspect
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class JourprodRunLogAspect {

    private static final FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger("runLog");

    /**
     * mtop场景
     */
    public static final String SCENE_MTOP = "mtop";

    @Pointcut("@annotation(com.taobao.trip.jourprod.common.sal.annotation.RunLog)")
    public void operateLog(){
    }

    @Around("operateLog()")
    public Object doAroundOperate(ProceedingJoinPoint joinPoint) throws Throwable {
        RunLog runLog = ((MethodSignature) joinPoint.getSignature()).getMethod().getAnnotation(RunLog.class);
        // 解析方法名
        String methodName = runLog.methodName();
        if (StringUtils.isBlank(methodName)) {
            Signature signature = joinPoint.getSignature();
            methodName = signature.getName();
        }
        // 解析userid
        String userIdSpel = runLog.userId();
        String userId = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(userIdSpel)) {
            userId = String.valueOf(SpelUtil.generateKeyBySpEL(userIdSpel, joinPoint));
        }
        // 根据场景决定返回内容
        String scene = runLog.scene();
        // 解析是否成功的表达式
        String successJsonPath = runLog.success();
        // 是否向上抛异常
        boolean throwException = runLog.throwException();
        long start = System.currentTimeMillis();
        Object[] args = joinPoint.getArgs();
        Object proceedResult = null;
        Throwable throwable = null;
        boolean success = true;
        try {
            proceedResult = joinPoint.proceed(args);
            if (StringUtils.isNotBlank(successJsonPath)) {
                String successObj = JSONUtil.extractContentFromJSONPath(proceedResult, successJsonPath);
                success = Boolean.parseBoolean(String.valueOf(successObj));
            }
            return proceedResult;
        } catch (Throwable e) {
            throwable = e;
            success = false;
            if (throwException) {
                throw e;
            }
        } finally {
            LOGGER.recordOutput(new LogModel(methodName)
                    .request(JSONUtil.toJSONStringForLog(args))
                    .cost(System.currentTimeMillis() - start)
                    .userId(userId)
                    .response(JSONUtil.toJSONStringForLog(proceedResult))
                    .success(success)
                    );
        }
        if (StringUtils.equals(SCENE_MTOP, scene)) {
            if (throwable instanceof TripJourneyException) {
                return mtopFailed((TripJourneyException) throwable);
            } else {
                return mtopFailed("系统异常", "SYSTEM_ERROR");
            }
        }
        return null;
    }

    private   <T> Result<T> mtopFailed(TripJourneyException tripJourneyException){
        Result<T> result = new Result<T>();
        result.setSuccess(false);
        result.setMsgCode("FAIL_BIZ_"+tripJourneyException.getErrorCode());
        result.setMsgInfo(tripJourneyException.getErrorMsg());
        return result;
    }

    private   <T> Result<T> mtopFailed(String msgInfo,String msgCode){
        Result<T> result = new Result<T>();
        result.setSuccess(false);
        result.setMsgCode("FAIL_BIZ_"+msgCode);
        result.setMsgInfo(msgInfo);
        return result;
    }

}
