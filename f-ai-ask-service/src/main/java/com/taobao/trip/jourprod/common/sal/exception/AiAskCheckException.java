package com.taobao.trip.jourprod.common.sal.exception;

import com.taobao.trip.jourprod.service.facade.model.response.JourPlanError;
import lombok.Data;

/**
 * @Description AI问一问校验异常
 **/
@Data
public class AiAskCheckException extends RuntimeException {

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 错误枚举
     */
    private JourPlanError planError;

    public AiAskCheckException() {
        super();
    }

    public AiAskCheckException(String errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public AiAskCheckException(String errorMsg) {
        this.errorCode = errorMsg;
        this.errorMsg = errorMsg;
    }

    public AiAskCheckException(JourPlanError planError) {
        this.errorCode = planError.getMsgCode();
        this.errorMsg = planError.getMsgInfo();
        this.planError = planError;
    }

    public AiAskCheckException(Throwable throwable) {
        super(throwable);
    }

    public AiAskCheckException(Throwable throwable, String errorCode, String errorMsg) {
        super(throwable);
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

}
