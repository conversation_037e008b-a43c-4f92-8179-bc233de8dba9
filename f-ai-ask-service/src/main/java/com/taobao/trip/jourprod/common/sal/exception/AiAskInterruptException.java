package com.taobao.trip.jourprod.common.sal.exception;

import com.taobao.trip.jourprod.service.facade.model.response.JourPlanError;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.dubbo.rpc.TriRpcStatus;

@EqualsAndHashCode(callSuper = true)
@Data
public class AiAskInterruptException extends RuntimeException {
    /**
     * 错误码
     */
    private TriRpcStatus errorCode;

    /**
     * 错误信息
     */
    private String errorMsg;

    public AiAskInterruptException() {
        super();
    }

    public AiAskInterruptException(TriRpcStatus errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public AiAskInterruptException(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public AiAskInterruptException(Throwable throwable) {
        super(throwable);
    }

    public AiAskInterruptException(Throwable throwable, TriRpcStatus errorCode, String errorMsg) {
        super(throwable);
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }
}
