package com.taobao.trip.jourprod.common.sal.exception;

import lombok.Data;

/**
 * @Description 行程统一异常
 * <AUTHOR>
 * @Date 2025/1/21
 **/
@Data
public class TripJourneyException extends RuntimeException {

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMsg;

    public TripJourneyException() {
        super();
    }

    public TripJourneyException(String errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public TripJourneyException(String errorMsg) {
        this.errorCode = errorMsg;
        this.errorMsg = errorMsg;
    }

    public TripJourneyException(Throwable throwable) {
        super(throwable);
    }

    public TripJourneyException(Throwable throwable, String errorCode, String errorMsg) {
        super(throwable);
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

}
