package com.taobao.trip.jourprod.common.sal.hsf.collection;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.trip.wireless.shopping.domain.request.ShopExternalReq;
import com.taobao.trip.wireless.shopping.domain.response.CommonResult;
import com.taobao.trip.wireless.shopping.domain.response.ShopExternalCollectResp;
import com.taobao.trip.wireless.shopping.service.TripShopExternalService;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class CollectionHelper {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(CollectionHelper.class);

    @Resource
    TripShopExternalService tripShopExternalService;

    public boolean isCollected(Long user, String bizId, String bizType) {
        try {
            if (user == null || StringUtils.isBlank(bizId) || StringUtils.isBlank(bizType)) {
                return false;
            }
            ShopExternalReq shopExternalReq = new ShopExternalReq();
            shopExternalReq.setBizId(bizId);
            shopExternalReq.setBizType(bizType);
            shopExternalReq.setUserId(user);
            CommonResult<ShopExternalCollectResp> shopExternalCollectRespCommonResult = tripShopExternalService.queryCollectStatus(shopExternalReq);
            if (shopExternalCollectRespCommonResult != null && BooleanUtils.isTrue(shopExternalCollectRespCommonResult.getSuccess())) {
                ShopExternalCollectResp result = shopExternalCollectRespCommonResult.getResult();
                return Objects.equals(result.getStatus(), 1);
            }
            LOGGER.recordNormalFailedRpc(new LogModel("isCollected")
                    .request(JSON.toJSONString(Lists.newArrayList(user, bizId, bizType)))
                    .response(JSON.toJSONString(shopExternalCollectRespCommonResult))
                    .message("查询收藏状态失败"));
        } catch (Throwable t) {
            LOGGER.recordNormalException(new LogModel("isCollected")
                    .request(JSON.toJSONString(Lists.newArrayList(user, bizId, bizType)))
                    .e(t)
                    .message("查询收藏状态异常"));
        }
        return false;
    }

    public Map<String, Boolean> isCollected(Long userId, List<String> bizIds) {
        Map<String, Boolean> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(bizIds)) {
            return result;
        }
        try {
            ShopExternalReq shopExternalReq = new ShopExternalReq();
            shopExternalReq.setBizIds(bizIds);
            shopExternalReq.setUserId(userId);
            CommonResult<List<ShopExternalCollectResp>> listCommonResult = tripShopExternalService.batchQueryCollectAllBizStatus(shopExternalReq);
            if (listCommonResult != null && BooleanUtils.isTrue(listCommonResult.getSuccess())) {
                List<ShopExternalCollectResp> resultList = listCommonResult.getResult();
                if (CollectionUtils.isNotEmpty(resultList)) {
                    resultList.forEach(shopExternalCollectResp -> {
                        result.put(shopExternalCollectResp.getBizId(), Objects.equals(shopExternalCollectResp.getStatus(), 1));
                    });
                }
            } else {
                LOGGER.recordNormalFailedRpc(new LogModel("isCollected")
                        .request(JSON.toJSONString(Lists.newArrayList(userId, bizIds)))
                        .response(JSON.toJSONString(listCommonResult))
                        .message("查询收藏状态失败"));
            }
        } catch (Throwable t) {
            LOGGER.recordNormalException(new LogModel("isCollected")
                    .request(JSON.toJSONString(Lists.newArrayList(userId, bizIds)))
                    .e(t)
                    .message("查询收藏状态异常"));
        } finally {
            LOGGER.recordOutput(new LogModel("isCollected")
                    .request(JSON.toJSONString(Lists.newArrayList(userId, bizIds)))
                    .response(JSON.toJSONString(result)));
        }
        return result;
    }
}
