package com.taobao.trip.jourprod.common.sal.hsf.common;

import com.taobao.ateye.annotation.Switch;
import org.springframework.stereotype.Component;

/**
 * Created by xiaoyi.lrj on 2016/9/19.
 */
@Component
public class AteyeFuncSwitch {

    @Switch(name = "cardDataFillTimeout", description = "卡片数据填充超时时间, 默认3s")
    public static volatile int CARD_DATA_FILL_TIMEOUT = 2;


    @Switch(name = "cardJsonSync", description = "卡片数据同步开关")
    public static volatile boolean ENABLE_CARD_DATA_SYNC = true;


    @Switch(name = "hsfInvokeLog", description = "是否打印HSF Invoke返回数据")
    public static volatile boolean ENABLE_HSF_INVOKE_LOG = true;

    @Switch(name = "bizHsfServiceLimit", description = "类目卡片填充服务最长等待时间，默认5s")
    public static volatile int bizHsfServiceLimit = 3;

    @Switch(name = "QUERY_POI_EXECUTOR_TIME_LIMIT", description = "并发查询poi最长等待时间，默认1s")
    public static volatile int QUERY_POI_EXECUTOR_TIME_LIMIT = 1;

    @Switch(name = "bizOrderPartitionSize", description = "类目订单并发切分数量，默认5条")
    public static volatile int bizOrderPartitionSize = 5;

    @Switch(name = "tryReplaceDateTime", description = "尝试根据类目返回的时间更新索引表中的时间，默认打开")
    public static volatile boolean tryReplaceDateTime = true;

    /**
     * added by shizhang.wl on 2016-12-11
     * 乘机/车人复制卡片允许的FuncButton按钮名称白名单列表
     */
    @Switch(name = "flightCopyCardFuncButtonWhileList", description = "国内机票类目乘机人复制卡片功能按钮白名单(默认:航班动态)")
    public static volatile String flightCopyCardFuncButtonWhileList = "航班动态";
    @Switch(name = "flightCopyCardLinkFilter", description = "国内机票类目乘机人复制卡片需要过滤的链接集合")
    public static volatile String flightCopyCardLinkFilter = "headJumpUrl,headJumpH5Url,headJumpAndroidUrl,headJumpIosUrl,passengerHref,passengerIosHref,passengerAndroidHref,assistHref";


    @Switch(name = "trainCopyCardFuncButtonWhileList", description = "火车票类目乘车人复制卡片功能按钮白名单(默认:列车时刻表)")
    public static volatile String trainCopyCardFuncButtonWhileList = "列车时刻表";
    @Switch(name = "trainCopyCardLinkFilter", description = "火车票类目乘车人复制卡片需要过滤的链接集合")
    public static volatile String trainCopyCardLinkFilter = "showDetailLink,subOrderLink,recommendLink";


    @Switch(name = "行程推荐覆盖的城市", description = "行程推荐覆盖的城市")
    public static volatile String RECOMMEND_CITY_ID = "903466";

    @Switch(name = "RECOMMEND_CITY_ID_1", description = "行程推荐覆盖的城市1")
    public static volatile String RECOMMEND_CITY_ID_1 = "";

    @Switch(name = "POI_DETAIL_URL", description = "poi详情页链接")
    public static volatile String POI_DETAIL_URL = "https://h5.m.taobao.com/trip/rx-poi/detail/index.html?_wx_tpl=https://h5.m.taobao.com/trip/rx-poi/detail/index.weex.js&_fli_newpage=1&poiId=%s";

    @Switch(name = "ITEM_DETAIL_URL", description = "商品详情页链接")
    public static volatile String ITEM_DETAIL_URL =  "https://h5.m.taobao.com/trip/traveldx-detail/index/index.html?id=%s";

    @Switch(name = "CARD_BACK_SHOW_OTHER_MODULE", description = "订单卡片背面展示非订单信息模块")
    public static volatile boolean CARD_BACK_SHOW_OTHER_MODULE = true;

    @Switch(name = "JOURNEY_PLAN_DETAIL_URL", description = "行程包详情页链接")
    public static volatile String JOURNEY_PLAN_DETAIL_URL = "/trip/rx-journey/detail/index.html?_wx_tpl=https://h5.m.taobao.com/trip/rx-journey/detail/index.weex.js&id=%s&day=1&wx_navbar_hidden=true&frm=app_home";

    @Switch(name = "JOURNEY_PLAN_DETAIL_LOCATION_URL", description = "行程包详情页定位卡片链接")
    public static volatile String JOURNEY_PLAN_DETAIL_LOCATION_URL = "/trip/rx-journey/detail/index.html?_wx_tpl=https://h5.m.taobao.com/trip/rx-journey/detail/index.weex.js&id=%s&cardId=%s&day=1&wx_navbar_hidden=true&frm=app_home";

    @Switch(name = "HOTEL_LIST_URL", description = "根据cityCode和时间跳转酒店小搜页")
    public static volatile String HOTEL_LIST_URL = "https://h5.m.taobao.com/trip/hotel/searchlist/index.html?=&cityCode=%s&cityName=%s&checkIn=%s&checkOut=%s&_projVer=1.2.6&source=fliggyHome";

    @Switch(name = "TEMPLATE_CARD_PARTITION_SIZE", description = "类目模版卡片并发切分数量，默认5条")
    public static volatile int TEMPLATE_CARD_PARTITION_SIZE = 5;

    @Switch(name = "publishUser", description = "后台发布用户")
    public static volatile String publishUser = "111356";

    @Switch(name = "version", description = "前端版本")
    public  static volatile String version = "1.0.1";

    @Switch(name = "strategyCenterVersion", description = "策略中心前端版本")
    public  static volatile String strategyCenterVersion = "1.0.1";

    @Switch(name = "EDITOR_UPDATE_ROUTE_JUDGE_TIPS", description = "编辑器判断线路元素删除提示")
    public static volatile boolean EDITOR_UPDATE_ROUTE_JUDGE_TIPS = false;

    @Switch(name = "DETAIL_UPDATE_ROUTE_JUDGE_TIPS", description = "详情页判断线路元素删除提示")
    public static volatile boolean DETAIL_UPDATE_ROUTE_JUDGE_TIPS = false;

    @Switch(name = "PLAN_ROUTE_TIPS", description = "出团书小黄条提示缓存时间")
    public static int PLAN_ROUTE_TIPS = 365 * 24 * 60 * 60;

    @Switch(name = "CLOSA_PLAN_DETAIL_ROUTE_TIPS", description = "是否关闭详情页线路提示小黄条")
    public static volatile boolean CLOSA_PLAN_DETAIL_ROUTE_TIPS = true;
}
