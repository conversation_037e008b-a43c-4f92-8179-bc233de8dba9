package com.taobao.trip.jourprod.common.sal.hsf.common;

import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import com.taobao.eagleeye.EagleEye;
import com.taobao.mtop.api.agent.MtopContext;
import lombok.Data;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * Created by k<PERSON><PERSON> on 16/2/3.
 */
@Setter
@Data
@Component
public class EnvSwitch {

    @Switch(name = "MOCK_ONLINE_ENV", description = "模拟线上环境")
    public static Boolean MOCK_ONLINE_ENV = true;

    private static final String DAILY  = "daily";
    private static final String PRE    = "pre";
    private static final String ONLINE = "online";
    private static final String PROD = "prod";

    /**
     * -- GETTER --
     *  注意：线上环境标识是online，aone中配置的
     *  使用Env这个枚举类判断时要注意，枚举类中的线上标识是prod
     *
     * @return
     */
    @Value("${project.env}")
    private String env;

    private static String environment;

    @PostConstruct
    public void init() {
        environment = env;
    }

    @AteyeInvoker(description = "获得环境变量env")
    public String printEnv() {
        return environment;
    }

    public static boolean isPre() {
        return PRE.equals(environment);
    }

    public static boolean isOnline() {
        return ONLINE.equals(environment);
    }

    public boolean inProjectEnv() {
        if (!PRE.equals(environment)) {
            return false;
        }
        String dpathEnv = EagleEye.getRpcContext().getUserData("dpath_env");
        if (StringUtils.isNotBlank(dpathEnv)) {
            return !"null".equals(dpathEnv) && !"DPathBaseEnv".equals(dpathEnv);
        }
        String bizInfo = MtopContext.getHeader("x-biz-info");
        return StringUtils.contains(bizInfo, "mc-sys-aenv=");
    }
}
