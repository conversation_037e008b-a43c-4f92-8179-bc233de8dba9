package com.taobao.trip.jourprod.common.sal.hsf.config;

import javax.annotation.PostConstruct;

import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch;
import com.taobao.csp.switchcenter.core.SwitchManager;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/9/7 20:48
 */
@Component
public class ChannelV3ForEmergencySwitch {

    @PostConstruct
    private void init() {
        SwitchManager.init(ChannelV3ForEmergencySwitch.class);
    }

    @AppSwitch(des = "是否打印耗时日志", level = Switch.Level.p1)
    public static Boolean PRINT_SLS_TIME_LOG = false;

}
