package com.taobao.trip.jourprod.common.sal.hsf.config;

import javax.annotation.PostConstruct;

import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch;
import com.taobao.csp.switchcenter.core.SwitchManager;
import org.springframework.stereotype.Service;

/**
 * 行程频道-地图开关
 */
@Service
public class JourneyChannelPlanNewSwitch {

    @PostConstruct
    public void init() {
        SwitchManager.init(JourneyChannelPlanNewSwitch.class);
    }

    @AppSwitch(des = "行程规划-频道页下方-我的行程规划-模块-详情跳转后缀2", level = Switch.Level.p4)
    public static String CHANNEL_PLAN_NEW_MODEL_PLAN_DETAIL_URL_AFTER_TWO = "%22";
    @AppSwitch(des = "行程规划-频道页下方-我的行程规划-模块-详情跳转后缀3", level = Switch.Level.p4)
    public static String CHANNEL_PLAN_NEW_MODEL_PLAN_DETAIL_URL_AFTER_THREE = "%7D";

    @AppSwitch(des = "新行程规划-poi详情页", level = Switch.Level.p3)
    public static String POI_DETAIL_URL = "https://outfliggys.m.taobao.com/app/trip/rx-trip-ticket/pages/detail?_fli_newpage=1&un_flutter=true&flutter_path=/poi_detail_page&_fli_unify=false&titleBarHidden=2&disableNav=YES&poiId=%s";

    @AppSwitch(des = "新行程规划-酒店详情页", level = Switch.Level.p3)
    public static String HOTEL_DETAIL_URL = "https://market.m.taobao.com/app/trip/h5-hotel-detail/pages/detail/index.html?_fli_newpage=1&hid=0&shid=%s";

    @AppSwitch(des = "ai问一问，小模型获取行政区划列表层级入参", level = Switch.Level.p4)
    public static Integer AI_ANALYSIS_DIVISION_LIST_PARAM = 5;

    @AppSwitch(des = "新行程规划，识别攻略城市权重阈值", level = Switch.Level.p4)
    public static Double PLAN_NEW_WARN_PROVINCE_LIST_CONFIDENCE = 0.1;

    @AppSwitch(des = "ai问一问-猜你想去城市算法推荐查询数量", level = Switch.Level.p3)
    public static Integer AI_CHANNEL_GUESS_CITY_RECOMMEND_PAGE_SIZE = 4;

    @AppSwitch(des = "查询qp的poi的超时时间", level = Switch.Level.p1)
    public static Integer QUERY_POI_QP_TIME = 500;

}
