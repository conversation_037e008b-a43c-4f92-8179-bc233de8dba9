package com.taobao.trip.jourprod.common.sal.hsf.config;

import javax.annotation.PostConstruct;

import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.csp.switchcenter.core.SwitchManager;
import org.springframework.stereotype.Component;

@Component
public class LLMSwitcher {

    @PostConstruct
    private void init() {
        SwitchManager.init(LLMSwitcher.class);
    }

    @AppSwitch(des = "抖音DeepSeek模型的API KEY", level = Level.p4)
    public static String TIKTOK_DEEPSEEK_API_KEY = "312e726a-9d84-48a3-a89a-2b406cf745f1";

    @AppSwitch(des = "抖音DeepSeek模型版本", level = Level.p4)
    public static String TIKTOK_DEEPSEEK_MODEL_VERSION = "bot-20250301201036-9dcjv";

    @AppSwitch(des = "高德静态地图authkey", level = Level.p2)
    public static String GAODE_STATIC_MAP_AUTH_KEY = "";

    @AppSwitch(des = "日志输出频率", level = Level.p4)
    public static int OUTPUT_TOKENS_PRINT_LOG = 50;

}
