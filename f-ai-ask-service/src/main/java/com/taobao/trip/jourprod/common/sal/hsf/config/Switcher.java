package com.taobao.trip.jourprod.common.sal.hsf.config;

import javax.annotation.PostConstruct;

import com.alibaba.fastjson.JSON;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.ateye.annotation.Switch;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.trip.jourprod.service.facade.model.response.EnumItemVO;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

import static com.taobao.trip.jourdprod.core.model.common.ConstantElement.PLATFORM_SCENE_ID.FLIGGY_HOME;
import static com.taobao.trip.jourdprod.core.model.common.ConstantElement.PLATFORM_SCENE_ID.JOURNEY_HOME_NEW;

/**
 * <AUTHOR>
 * @date 2018/12/18
 */
@Component
public class Switcher {

    @PostConstruct
    private void init() {
        SwitchManager.init(Switcher.class);
    }

    @AppSwitch(des = "关闭路线规划查询", level = Level.p1)
    public static Boolean closeTrafficPlanQuery = false;

    @AppSwitch(des = "关闭高德搜索", level = Level.p1)
    public static Boolean closeGaoDeSearchQuery = false;

    @AppSwitch(des = "关闭高德2.0周边搜索", level = Level.p1)
    public static Boolean closeGaoDeSearch2AroundQuery = false;

    @AppSwitch(des = "手绘地图类型兜底icon", level = Level.p1)
    public static Map<String,String> iconMap = new HashMap<>();
    static {
        iconMap.put("flight", "https://img.alicdn.com/imgextra/i2/O1CN01HdRvzA26NIYQyKjQW_!!6000000007649-2-tps-220-220.png");
        iconMap.put("train", "https://img.alicdn.com/imgextra/i1/O1CN01isndxT24Yd7Yo7IWQ_!!6000000007403-2-tps-220-220.png");
    }

    @AppSwitch(des = "去程-手绘地图类型交通icon", level = Level.p1)
    public static Map<String,String> GO_TRAFFIC_ICON_MAP = new HashMap<>();
    static {
        iconMap.put("flight", "https://img.alicdn.com/imgextra/i4/O1CN010mDxdU1NJ2A55ZTqT_!!6000000001548-2-tps-220-220.png");
        iconMap.put("train", "https://img.alicdn.com/imgextra/i4/O1CN01c4Tlbj1dO6OcnsodZ_!!6000000003725-2-tps-220-220.png");
    }

    @AppSwitch(des = "返程-手绘地图类型交通icon", level = Level.p1)
    public static Map<String,String> BACK_TRAFFIC_ICON_MAP = new HashMap<>();
    static {
        iconMap.put("flight", "https://img.alicdn.com/imgextra/i3/O1CN01SS9HOk1HQStQtmrEP_!!6000000000752-2-tps-220-220.png");
        iconMap.put("train", "https://img.alicdn.com/imgextra/i3/O1CN01qRKk8U1fYIRZm7VPo_!!6000000004018-2-tps-220-220.png");
    }

    @AppSwitch(des = "行程后台枚举列表", level = Level.p4)
    public static Map<String, List<EnumItemVO>> platformEnumMap = Maps.newHashMap();

    static {
        platformEnumMap.put("NODE_LIST", Lists.newArrayList(
                new EnumItemVO("下单", "ORDERED"),
                new EnumItemVO("出票", "TICKETING"),
                new EnumItemVO("交易成功", "TRADE_SUCCESS"),
                new EnumItemVO("航班动态", "FLIGHT_DYNAMIC"),
                new EnumItemVO("登机动态", "BOARD_DYNAMIC"),
                new EnumItemVO("抵达出发poi", "ARRIVED_DEP_POI"),
                new EnumItemVO("行程开始", "TRIP_START"),
                new EnumItemVO("抵达到达poi", "ARRIVED_ARR_POI"),
                new EnumItemVO("行程结束", "TRIP_FINISH"),
                new EnumItemVO("非常住地", "NOT_LIVE_CITY"),
                new EnumItemVO("用户位移", "USER_LBS"),
                new EnumItemVO("司机动态", "DRIVER_DYNAMIC")
        ));
        platformEnumMap.put("STRATEGY_NODE_LIST", Lists.newArrayList(
                new EnumItemVO("下单", "ORDERED"),
                new EnumItemVO("交易成功", "TRADE_SUCCESS"),
                new EnumItemVO("航班动态", "FLIGHT_DYNAMIC"),
                new EnumItemVO("登机动态", "BOARD_DYNAMIC"),
                new EnumItemVO("抵达出发poi", "ARRIVED_DEP_POI"),
                new EnumItemVO("行程开始", "TRIP_START"),
                new EnumItemVO("抵达到达poi", "ARRIVED_ARR_POI"),
                new EnumItemVO("行程结束", "TRIP_FINISH"),
                new EnumItemVO("用户位移", "USER_LBS")
        ));
        platformEnumMap.put("STRATEGY_TYPE", Lists.newArrayList(
                new EnumItemVO("诸葛人群", "CROWD_ID"),
                new EnumItemVO("CRM规则", "CRM_RULE"),
                new EnumItemVO("时序节点", "SEQUENCE_NODE")
        ));
        platformEnumMap.put("FLIGHT_DYNAMIC", Lists.newArrayList(
                new EnumItemVO("航班计划", "PLAN"),
                new EnumItemVO("航班起飞", "FLY"),
                new EnumItemVO("航班到达", "ARRIVE"),
                new EnumItemVO("航班延误", "DELAY"),
                new EnumItemVO("延误预警", "DELAY_WARN"),
                new EnumItemVO("航班取消", "CANCEL"),
                new EnumItemVO("航班保护", "PROTECT"),
                new EnumItemVO("提前取消", "PLAN_CANCEL"),
                new EnumItemVO("航班备降", "LAND"),
                new EnumItemVO("航班返航", "BACK"),
                new EnumItemVO("航班提前", "PRE"),
                new EnumItemVO("机场变更", "AIRPORT_CHANGE"),
                new EnumItemVO("航班恢复", "RECOVER")
        ));
        platformEnumMap.put("BOARD_DYNAMIC", Lists.newArrayList(
                new EnumItemVO("正在值机", "CHECKING"),
                new EnumItemVO("值机结束", "CHECK_END"),
                new EnumItemVO("正在登机", "BOARDING"),
                new EnumItemVO("登机结束", "BOARD_END"),
                new EnumItemVO("舱门关闭", "DOOR_CLOSE")
        ));
    }

    @AppSwitch(des = "行程时序节点id映射表", level = Level.p4)
    public static Map<String, String> journeyNodeIdMap = Maps.newHashMap();

    static {
        journeyNodeIdMap.put("ORDERED", "NODE163730641403864");
        journeyNodeIdMap.put("TICKETING", "NODE163706442200870");
        journeyNodeIdMap.put("TRADE_SUCCESS", "NODE163729331708770");
        journeyNodeIdMap.put("FLIGHT_DYNAMIC", "NODE163758006783167");
        journeyNodeIdMap.put("BOARD_DYNAMIC", "NODE163758175450232");
        journeyNodeIdMap.put("ARRIVED_DEP_POI", "NODE163730156135471");
        journeyNodeIdMap.put("TRIP_START", "NODE163730291875230");
        journeyNodeIdMap.put("ARRIVED_ARR_POI", "NODE163730515294763");
        journeyNodeIdMap.put("TRIP_FINISH", "NODE163730529071723");
        journeyNodeIdMap.put("NOT_LIVE_CITY", "NODE163730583107471");
        journeyNodeIdMap.put("USER_LBS", "NODE163755400681983");
        journeyNodeIdMap.put("DRIVER_DYNAMIC", "");

    }

    public static void main(String[] args) {
        String str = JSON.toJSONString(platformEnumMap);
        System.out.println(str);
    }

    @AppSwitch(des = "行程频道机票行业购买页", level = Level.p4)
    public static Map<String, Object> JOURNEY_HOME_FLIGHT_BUY_PAGE = Maps.newHashMap();

    static {
        JOURNEY_HOME_FLIGHT_BUY_PAGE.put("text", "机票");
        JOURNEY_HOME_FLIGHT_BUY_PAGE.put("icon", "https://img.alicdn.com/tfs/TB1I70u4Lb2gK0jSZK9XXaEgFXa-68-68.png");
        JOURNEY_HOME_FLIGHT_BUY_PAGE.put("url", "page://flight_home");
        JOURNEY_HOME_FLIGHT_BUY_PAGE.put("h5Url", "https://market.m.taobao.com/app/trip/h5-traffic-search/pages/search/index.html?biz=flight");
    }

    @AppSwitch(des = "行程频道火车票行业购买页", level = Level.p4)
    public static Map<String, Object> JOURNEY_HOME_TRAIN_BUY_PAGE = Maps.newHashMap();

    static {
        JOURNEY_HOME_TRAIN_BUY_PAGE.put("text", "火车票");
        JOURNEY_HOME_TRAIN_BUY_PAGE.put("icon", "https://img.alicdn.com/tfs/TB141VA4UY1gK0jSZFCXXcwqXXa-68-68.png");
        JOURNEY_HOME_TRAIN_BUY_PAGE.put("url", "page://train_home");
        JOURNEY_HOME_TRAIN_BUY_PAGE.put("h5Url", "https://h5.m.taobao.com/trip/train-main/search/index.html?_projVer=1.17.0");
    }

    @AppSwitch(des = "行程频道酒店行业购买页", level = Level.p4)
    public static Map<String, Object> JOURNEY_HOME_HOTEL_BUY_PAGE = Maps.newHashMap();

    static {
        JOURNEY_HOME_HOTEL_BUY_PAGE.put("text", "酒店");
        JOURNEY_HOME_HOTEL_BUY_PAGE.put("icon", "https://img.alicdn.com/tfs/TB1dgVr4HY1gK0jSZTEXXXDQVXa-68-68.png");
        JOURNEY_HOME_HOTEL_BUY_PAGE.put("url", "page://hotel_home");
        JOURNEY_HOME_HOTEL_BUY_PAGE.put("h5Url", "https://h5.m.taobao.com/trip/hotel/search/index.html?type=0");
    }

    @AppSwitch(des = "行程频道门票行业购买页", level = Level.p4)
    public static Map<String, Object> JOURNEY_HOME_ENTRANCE_BUY_PAGE = Maps.newHashMap();

    static {
        JOURNEY_HOME_ENTRANCE_BUY_PAGE.put("text", "门票");
        JOURNEY_HOME_ENTRANCE_BUY_PAGE.put("icon", "https://img.alicdn.com/imgextra/i1/O1CN01485kvl1MQWch0534b_!!6000000001429-2-tps-68-68.png");
        JOURNEY_HOME_ENTRANCE_BUY_PAGE.put("url", "");
        JOURNEY_HOME_ENTRANCE_BUY_PAGE.put("h5Url", "https://market.m.taobao.com/app/trip/rx-trip-ticket/pages/home?titleBarHidden=2&_fli_wk=true&_projVer=1.27.5");
    }

    //---------------------行程意图识别-------------------------------------------------
    @AppSwitch(des = "行程意图猜测非Mock名单", level = Level.p4)
    public static List<Long> INTENT_INSIGHT_NONE_MOCK_USER_LIST = new ArrayList<>();

    static {
        INTENT_INSIGHT_NONE_MOCK_USER_LIST.add(2212682894523L);
    }

    @AppSwitch(des = "行程意图猜测-参与此次推荐的poi", level = Level.p4)
    public static List<Long> INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST = new ArrayList<>();

    static {
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(54L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(1352L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(14955L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(1874L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(2409L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(141033491L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(1901L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(30780L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(36743186L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(14945788L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(187010L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(139L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(45636L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(1828L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(135L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(8791L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(8793L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(198459L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(10085L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(16665036L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(19L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(180398004L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(43564L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(99L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(14844L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(335513L);
        INTENT_INSIGHT_PARTICIPATE_POI_ID_LIST.add(8880L);
    }

    @AppSwitch(des = "首页行程规则编排灰度-  白名单用户-命中AB ", level = Level.p4)
    public static List<Long> INTENT_INSIGHT_INDEX_RULE_WHITE_USER = new ArrayList<>();

    static {
        INTENT_INSIGHT_INDEX_RULE_WHITE_USER.add(2212682894523L);
        INTENT_INSIGHT_INDEX_RULE_WHITE_USER.add(2211582244532L);
        INTENT_INSIGHT_INDEX_RULE_WHITE_USER.add(2465353781L);
        INTENT_INSIGHT_INDEX_RULE_WHITE_USER.add(440903939L);
        INTENT_INSIGHT_INDEX_RULE_WHITE_USER.add(478686304L);
    }

    @AppSwitch(des = "客户端-交叉组件-白名单", level = Level.p1)
    public static List<Long> ANDROID_IOS_NEW_VERSION_WHITE_LIST = new ArrayList<>();
    static {
        ANDROID_IOS_NEW_VERSION_WHITE_LIST.add(373028122L);
        ANDROID_IOS_NEW_VERSION_WHITE_LIST.add(49238504L);
        ANDROID_IOS_NEW_VERSION_WHITE_LIST.add(1025803556L);
        ANDROID_IOS_NEW_VERSION_WHITE_LIST.add(86203004L);
        ANDROID_IOS_NEW_VERSION_WHITE_LIST.add(44609947L);
        ANDROID_IOS_NEW_VERSION_WHITE_LIST.add(2293002232L);
        ANDROID_IOS_NEW_VERSION_WHITE_LIST.add(829908578L);
        ANDROID_IOS_NEW_VERSION_WHITE_LIST.add(1066941209L);
        ANDROID_IOS_NEW_VERSION_WHITE_LIST.add(55624448L);
        ANDROID_IOS_NEW_VERSION_WHITE_LIST.add(200747240L);
        ANDROID_IOS_NEW_VERSION_WHITE_LIST.add(121516056L);
        ANDROID_IOS_NEW_VERSION_WHITE_LIST.add(788204169L);

        ANDROID_IOS_NEW_VERSION_WHITE_LIST.add(373028122L);
        ANDROID_IOS_NEW_VERSION_WHITE_LIST.add(403226841L);
        ANDROID_IOS_NEW_VERSION_WHITE_LIST.add(2187259014L);
        ANDROID_IOS_NEW_VERSION_WHITE_LIST.add(830312413L);

        ANDROID_IOS_NEW_VERSION_WHITE_LIST.add(2890636618L);
        ANDROID_IOS_NEW_VERSION_WHITE_LIST.add(65190179L);
        ANDROID_IOS_NEW_VERSION_WHITE_LIST.add(1680074146L);

        ANDROID_IOS_NEW_VERSION_WHITE_LIST.add(3878519910L);
        ANDROID_IOS_NEW_VERSION_WHITE_LIST.add(1884962353L);
    }

    @AppSwitch(des = "行程的首页回归测试", level = Level.p4)
    public static List<Long> RegressionTestUserIdList = new ArrayList<>();


    //---------------------新版行程的兜底-------------------------------------------------
    static {
        RegressionTestUserIdList.add(2212682894523L);
        RegressionTestUserIdList.add(2211582244532L);
        RegressionTestUserIdList.add(2465353781L);
        RegressionTestUserIdList.add(440903939L);
        RegressionTestUserIdList.add(478686304L);
    }

    @AppSwitch(des = "酒店权益-sellerId", level = Level.p1)
    public static List<Long> HOTEL_RIGHT_SPECIFIC_SELLER_ID_LIST = new ArrayList<>();

    static {
        HOTEL_RIGHT_SPECIFIC_SELLER_ID_LIST.add(3504342744L);
        HOTEL_RIGHT_SPECIFIC_SELLER_ID_LIST.add(2986476987L);
        HOTEL_RIGHT_SPECIFIC_SELLER_ID_LIST.add(3325798970L);
        HOTEL_RIGHT_SPECIFIC_SELLER_ID_LIST.add(1610864497L);
        HOTEL_RIGHT_SPECIFIC_SELLER_ID_LIST.add(3430997342L);
    }

    @AppSwitch(des = "火车停运特殊符号", level = Level.p1)
    public static Map<Integer, String> MEMEBER_RIGHTI_CON = new HashMap();
    static {
        MEMEBER_RIGHTI_CON.put(1, "https://gw.alicdn.com/imgextra/i2/O1CN01NsmUr11r7yo4eEXKD_!!6000000005585-2-tps-184-48.png");
        MEMEBER_RIGHTI_CON.put(2, "https://gw.alicdn.com/imgextra/i4/O1CN01Y0QSTJ1pB4FFyXKhk_!!6000000005321-2-tps-184-48.png");
        MEMEBER_RIGHTI_CON.put(3, "https://gw.alicdn.com/imgextra/i1/O1CN01nVqT0y1gnh6fENfbK_!!6000000004187-2-tps-184-48.png");
        MEMEBER_RIGHTI_CON.put(4, "https://gw.alicdn.com/imgextra/i2/O1CN01Fe14ZF1WMzrNBSxd6_!!6000000002775-2-tps-184-48.png");
    }

    @AppSwitch(des = "服务卡type", level = Level.p1)
    public static List<String> ALL_CHECK_SERVICE_CARD_TYPE_V3 = new ArrayList<>();
    static {
        ALL_CHECK_SERVICE_CARD_TYPE_V3.add("ANTICOVID19CITYPOLICY");
        ALL_CHECK_SERVICE_CARD_TYPE_V3.add("HOTELINTERSECT");
        ALL_CHECK_SERVICE_CARD_TYPE_V3.add("HOTELRIGHT");
        ALL_CHECK_SERVICE_CARD_TYPE_V3.add("JOURNEYTAXI");
        ALL_CHECK_SERVICE_CARD_TYPE_V3.add("PHOTOGUIDE");
        ALL_CHECK_SERVICE_CARD_TYPE_V3.add("ENTRANCEINTERSECT");
        ALL_CHECK_SERVICE_CARD_TYPE_V3.add("HOTELTAOXIANDA");

    }

    /**
     * 统一放到 { com.taobao.trip.jourprod.common.sal.hsf.config.FliggyHomeCardSwitcher#journeyChannelToolsV2}
     */
    @Deprecated
    @AppSwitch(des = "航班动态图片", level = Level.p1)
    public static String CHANNEL_LITTLE_COMPONENT_HANG_BAN_DONG_TAI_ICON = "https://gw.alicdn.com/imgextra/i3/O1CN01z8fNR21FCbPObjrts_!!6000000000451-2-tps-80-80.png";

    /**
     * 统一放到 { com.taobao.trip.jourprod.common.sal.hsf.config.FliggyHomeCardSwitcher#journeyChannelToolsV2}
     */
    @Deprecated
    @AppSwitch(des = "航班动态-跳转链接", level = Level.p1)
    public static String CHANNEL_LITTLE_COMPONENT_HANG_BAN_DONG_TAI_URL = "page://flight_dynamic_home?params={\"flutter_path\":\"\\/flight_dynamic_home\",\"un_flutter\":true}";

    static {

    }

    @AppSwitch(des = "会员等级与会员权益icon映射", level = Level.p1)
    public static Map<String, String> Member_Level_To_Right_Icon = new HashMap<>();
    static {
        Member_Level_To_Right_Icon.put("1", "https://gw.alicdn.com/imgextra/i2/O1CN01NsmUr11r7yo4eEXKD_!!6000000005585-2-tps-184-48.png");
        Member_Level_To_Right_Icon.put("2", "https://gw.alicdn.com/imgextra/i4/O1CN01Y0QSTJ1pB4FFyXKhk_!!6000000005321-2-tps-184-48.png");
        Member_Level_To_Right_Icon.put("3", "https://gw.alicdn.com/imgextra/i1/O1CN01nVqT0y1gnh6fENfbK_!!6000000004187-2-tps-184-48.png");
        Member_Level_To_Right_Icon.put("4", "https://gw.alicdn.com/imgextra/i2/O1CN01Fe14ZF1WMzrNBSxd6_!!6000000002775-2-tps-184-48.png");
    }

    @AppSwitch(des = "是否转换境外经纬度", level = Level.p4)
    public static Boolean TRANSFER_LATITUDE_AND_LONGITUDE_FROM_POI = true;

    @AppSwitch(des = "酒店卡片状态映射", level = Level.p4)
    public static Map<String, String> HOTEL_CARD_STATUS_MAPPING = Maps.newHashMap();

    static {
        HOTEL_CARD_STATUS_MAPPING.put("PREPAY_PAID", "酒店确认中");
        HOTEL_CARD_STATUS_MAPPING.put("CASH_STATUS_BOOK_WAITSELLERDONE", "酒店确认中");
        HOTEL_CARD_STATUS_MAPPING.put("ALIPAY_CASH_STATUS_BOOK_WAITSELLERDONE", "酒店确认中");
        HOTEL_CARD_STATUS_MAPPING.put("RESERVATION_STATUS_BOOK_WAITSELLERDONE", "酒店确认中");
        HOTEL_CARD_STATUS_MAPPING.put("ORDER_ORDER_STATUS_BOOK_WAIT_CONFIRM", "酒店确认中");
        HOTEL_CARD_STATUS_MAPPING.put("PERIOD_STATUS_BOOK_WAIT_CONFIRM", "酒店确认中");
    }

    @AppSwitch(des = "火车卡片状态映射", level = Level.p4)
    public static Map<String, String>  TRAIN_CARD_STATUS_MAPPING = Maps.newHashMap();

    static {
        TRAIN_CARD_STATUS_MAPPING.put("1", "确认中");
        TRAIN_CARD_STATUS_MAPPING.put("2", "确认中");
    }

    @AppSwitch(des = "机票卡片状态映射", level = Level.p4)
    public static Map<String, String> FLIGHT_CARD_STATUS_MAPPING = Maps.newHashMap();

    static {
        FLIGHT_CARD_STATUS_MAPPING.put("TICKETING", "出票中");
        FLIGHT_CARD_STATUS_MAPPING.put("REFUNDING", "有退票记录");
        FLIGHT_CARD_STATUS_MAPPING.put("REFUND_FAIL", "有退票记录");
        FLIGHT_CARD_STATUS_MAPPING.put("REFUND_RECORD", "有退票记录");
        FLIGHT_CARD_STATUS_MAPPING.put("MODIFYING", "有改签记录");
        FLIGHT_CARD_STATUS_MAPPING.put("MODIFY_SUCC", "有改签记录");
        FLIGHT_CARD_STATUS_MAPPING.put("MODIFY_FAIL", "有改签记录");
        FLIGHT_CARD_STATUS_MAPPING.put("MODIFY_RECORD", "有改签记录");
        FLIGHT_CARD_STATUS_MAPPING.put("REFUNDMODIFY_RECORD", "有退改记录");
    }

    @AppSwitch(des = "门票卡片状态映射", level = Level.p4)
    public static Map<String, String> ENTRANCE_CARD_STATUS_MAPPING = Maps.newHashMap();

    static {
        ENTRANCE_CARD_STATUS_MAPPING.put("2001", "出票中");
    }

    @AppSwitch(des = "汽车卡片状态映射", level = Level.p4)
    public static Map<Integer, String> BUS_CARD_STATUS_MAPPING = Maps.newHashMap();

    static {
        BUS_CARD_STATUS_MAPPING.put(1, "出票中");
        BUS_CARD_STATUS_MAPPING.put(7, "待出票");
    }

    @AppSwitch(des = "按钮埋点映射", level = Level.p4)
    public static Map<String, String> INSTANCE_ID_2_KEY_MAP = Maps.newHashMap();

    static {
        INSTANCE_ID_2_KEY_MAP.put("429005", "tripdetail");
        INSTANCE_ID_2_KEY_MAP.put("429006", "orderdetail");
        INSTANCE_ID_2_KEY_MAP.put("429007", "Contact_sell");
    }

    @AppSwitch(des = "是否停止线程监控", level = Level.p4)
    public static Boolean IS_STOP_MONITOR = false;

    @AppSwitch(des = "线程监控间隔时间", level = Level.p4)
    public static Long MONITOR_SLEEP_TIME = 60000L;

    @AppSwitch(des = "场景positionSpms",level = Level.p1)
    public static Map<String, String> POSITION_SPMS = Maps.newConcurrentMap();

    static {
        POSITION_SPMS.put("AIRPLANE", "translate_assistant_airplane");

        POSITION_SPMS.put("HOTEL", "translate_assistant_hotel");
    }

    @AppSwitch(des="翻译助手支持语种",level = Level.p1)
    public static LinkedHashMap<String, Map<String, String>> LANGUAGE = new LinkedHashMap<>();

    static {
        Map<String, String> mapEn = Maps.newConcurrentMap();
        mapEn.put("code", "en");
        mapEn.put("language", "英语");
        mapEn.put("appkey", "xmQveCLq2QR8Ap1n");
        LANGUAGE.put("en", mapEn);
        Map<String, String> mapKo = Maps.newConcurrentMap();
        mapKo.put("code", "ko");
        mapKo.put("language", "韩语");
        mapKo.put("appkey", "RCayVWGUKTHu4sam");
        LANGUAGE.put("ko", mapKo);
        Map<String, String> mapJa = Maps.newConcurrentMap();
        mapJa.put("code", "ja");
        mapJa.put("language", "日语");
        mapJa.put("appkey", "rthbcMVcSglFBcs7");
        LANGUAGE.put("ja", mapJa);
        Map<String, String> mapTh = Maps.newConcurrentMap();
        mapTh.put("code", "th");
        mapTh.put("language", "泰语");
        mapTh.put("appkey", "sy3zwBy5ivRX94y0");
        LANGUAGE.put("th", mapTh);
        Map<String, String> mapMs = Maps.newConcurrentMap();
        mapMs.put("code", "ms");
        mapMs.put("language", "马来语");
        mapMs.put("appkey", "cGp0D1GaCbi8LrXf");
        LANGUAGE.put("ms", mapMs);
        Map<String, String> mapFr = Maps.newConcurrentMap();
        mapFr.put("code", "fr");
        mapFr.put("language", "法语");
        mapFr.put("appkey", "8z4XUgzRYCN2QxgA");
        LANGUAGE.put("fr", mapFr);
        Map<String, String> mapEs = Maps.newConcurrentMap();
        mapEs.put("code", "es");
        mapEs.put("language", "西班牙语");
        mapEs.put("appkey", "PKPNVx806wnyalPJ");
        LANGUAGE.put("es", mapEs);
        Map<String, String> mapPt = Maps.newConcurrentMap();
        mapPt.put("code", "pt");
        mapPt.put("language", "葡萄牙语");
        mapPt.put("appkey", "HCacSjTO3JmieVqD");
        LANGUAGE.put("pt", mapPt);
        Map<String, String> mapIt = Maps.newConcurrentMap();
        mapIt.put("code", "it");
        mapIt.put("language", "意大利语");
        mapIt.put("appkey", "3hzpelDSIhJ57Fg2");
        LANGUAGE.put("it", mapIt);
        Map<String, String> mapRu = Maps.newConcurrentMap();
        mapRu.put("code", "ru");
        mapRu.put("language", "俄语");
        mapRu.put("appkey", "vHqaLi6kR77JtQuX");
        LANGUAGE.put("ru", mapRu);
        Map<String, String> mapDe = Maps.newConcurrentMap();
        mapDe.put("code", "de");
        mapDe.put("language", "德语");
        mapDe.put("appkey", "v6NhMdnxiY1gbZoD");
        LANGUAGE.put("de", mapDe);
        Map<String, String> mapYue = Maps.newConcurrentMap();
        mapYue.put("code", "yue");
        mapYue.put("language", "粤语繁体");
        mapYue.put("appkey", "1yhJ4pYkcJ9RgXL0");
        LANGUAGE.put("yue", mapYue);
    }

    @AppSwitch(des = "国家语种映射关系", level = Level.p4)
    public static  Map<String, String> COUNTRY_NAME_TO_LANGUAGE = new HashMap<>();
    static {
        COUNTRY_NAME_TO_LANGUAGE.put("美国","en");
        COUNTRY_NAME_TO_LANGUAGE.put("日本","ja");
        COUNTRY_NAME_TO_LANGUAGE.put("泰国","th");
        COUNTRY_NAME_TO_LANGUAGE.put("韩国","ko");
        COUNTRY_NAME_TO_LANGUAGE.put("法国","fr");
        COUNTRY_NAME_TO_LANGUAGE.put("西班牙","es");
        COUNTRY_NAME_TO_LANGUAGE.put("墨西哥","es");
        COUNTRY_NAME_TO_LANGUAGE.put("阿根廷","es");
        COUNTRY_NAME_TO_LANGUAGE.put("哥伦比亚","es");
        COUNTRY_NAME_TO_LANGUAGE.put("秘鲁","es");
        COUNTRY_NAME_TO_LANGUAGE.put("委内瑞拉","es");
        COUNTRY_NAME_TO_LANGUAGE.put("智利","es");
        COUNTRY_NAME_TO_LANGUAGE.put("古巴","es");
        COUNTRY_NAME_TO_LANGUAGE.put("多米尼加共和国","es");
        COUNTRY_NAME_TO_LANGUAGE.put("玻利维亚","es");
        COUNTRY_NAME_TO_LANGUAGE.put("巴拉圭","es");
        COUNTRY_NAME_TO_LANGUAGE.put("乌拉圭","es");
        COUNTRY_NAME_TO_LANGUAGE.put("洪都拉斯","es");
        COUNTRY_NAME_TO_LANGUAGE.put("萨尔瓦多","es");
        COUNTRY_NAME_TO_LANGUAGE.put("尼加拉瓜","es");
        COUNTRY_NAME_TO_LANGUAGE.put("巴拿马","es");
        COUNTRY_NAME_TO_LANGUAGE.put("哥斯达黎加","es");
        COUNTRY_NAME_TO_LANGUAGE.put("巴西","pt");
        COUNTRY_NAME_TO_LANGUAGE.put("葡萄牙","pt");
        COUNTRY_NAME_TO_LANGUAGE.put("意大利","it");
        COUNTRY_NAME_TO_LANGUAGE.put("德国","de");

    }

    @AppSwitch(des = "手绘地图mock数据查询时间", level = Level.p3)
    public static String AI_ASK_MOCK_DRAW_TIME = "2025-04-25";

    @AppSwitch(des = "门票卡片展示字段", level = Level.p4)
    public static Map<String, List<String>> ENTRANCE_CARD_SHOW_FIELD_MAP = Maps.newHashMap();

    static {
        ENTRANCE_CARD_SHOW_FIELD_MAP.put(JOURNEY_HOME_NEW, Lists.newArrayList("jumpUrl", "showType", "picUrl", "codeStatus"));
        ENTRANCE_CARD_SHOW_FIELD_MAP.put(FLIGGY_HOME, Lists.newArrayList("jumpUrl", "showType", "picUrl", "codeStatus", "spm", "trackArgs2"));
    }

    @AppSwitch(des = "入境卡模版", level = Level.p4)
    public static Map<String,String> ENTRY_CARD_TEMPLATE=new HashMap<>();
    static {
        ENTRY_CARD_TEMPLATE.put("Japan","日本");
//        ENTRY_CARD_TEMPLATE.put("Republic of Korea", "韩国");
//        ENTRY_CARD_TEMPLATE.put("Jeju Island", "韩国济州岛");
//        ENTRY_CARD_TEMPLATE.put("Singapore", "新加坡");
        ENTRY_CARD_TEMPLATE.put("Malaysia", "马来西亚");
//        ENTRY_CARD_TEMPLATE.put("Thailand","泰国");
    }

    @AppSwitch(des = "城市地区", level = Level.p4)
    public static Map<String, String> CITY_RIGON = Maps.newHashMap();

    static {
        CITY_RIGON.put("吉隆坡", "WP KUALA");
        CITY_RIGON.put("亚庇", "SABAH");
        CITY_RIGON.put("斗湖", "SABAH");
    }

    @AppSwitch(des = "城市邮编", level = Level.p4)
    public static Map<String, String> CITY_CODE = Maps.newHashMap();

    static {
        CITY_CODE.put("吉隆坡", "50000");
        CITY_CODE.put("亚庇", "88000");
        CITY_CODE.put("斗湖", "88000");
    }

    @AppSwitch(des = "城市名称", level = Level.p4)
    public static Map<String, String> CITY_NAME = Maps.newHashMap();

    static {
        CITY_NAME.put("吉隆坡", "KUALA");
        CITY_NAME.put("仙本那斗湖", "TAWAU");
        CITY_NAME.put("亚庇", "KOTA KINABALU");
        CITY_NAME.put("济州岛","JEJU");
    }
    @AppSwitch(des = "调fai的终止状态错误识别", level = Level.p4)
    public static Map<String, String> CALL_FAI_STATUS = Maps.newHashMap();

    static {
        CALL_FAI_STATUS.put("BLOCK", "FORBIDDEN");
    }



    @AppSwitch(des = "不支持的场景默认回答问题", level = Level.p4)
    public static Map<String, String> NOT_SUPPORT_SCENE_DEFAULT_ANSWER_MAP = Maps.newHashMap();
    static {
        NOT_SUPPORT_SCENE_DEFAULT_ANSWER_MAP.put("default", "抱歉，暂时没有找到合适的答案。 \n  你可以告诉我你未来想去的城市，我可以为你快速规划日程安排。");
        NOT_SUPPORT_SCENE_DEFAULT_ANSWER_MAP.put("政治相关问题", "我是一个旅行ai助手，擅长为你规划行程，你可以告诉我未来想去的城市，我可以为你快速规划日程安排");
        NOT_SUPPORT_SCENE_DEFAULT_ANSWER_MAP.put("自然灾害", "我是一个旅行ai助手，擅长为你规划行程，你可以告诉我未来想去的城市，我可以为你快速规划日程安排");
        NOT_SUPPORT_SCENE_DEFAULT_ANSWER_MAP.put("实时的当地安全问题", "抱歉，暂时没有找到合适的答案。 \n  你可以告诉我你未来想去的城市，我可以为你快速规划日程安排");
        NOT_SUPPORT_SCENE_DEFAULT_ANSWER_MAP.put("黑名单用户", "抱歉，暂时没有找到合适的答案。 \n  你可以告诉我你未来想去的城市，我可以为你快速规划日程安排");
    }
    public static String getNotSuppotSceneDefaultAnswer(String type) {
        return NOT_SUPPORT_SCENE_DEFAULT_ANSWER_MAP.getOrDefault(type, NOT_SUPPORT_SCENE_DEFAULT_ANSWER_MAP.get("default"));
    }
    public static String NO_FLIGHT_DATA_DEFAULT_ANSWER = "由于当前未获取到实时航班数据，建议您打开飞猪App，在首页点击「机票」版块，查看最新航班动态与价格。系统会根据您的会员等级智能推荐最优方案，并显示剩余票量提醒。";

    @AppSwitch(des = "AI问一问对话黑名单列表", level = Level.p4)
    public static List<Long> AI_CHAT_BLACK_LIST = Lists.newArrayList();
    @AppSwitch(des = "AI问一问对话限流次数", level = Level.p4)
    public static Integer AI_MESSAGE_CHAT_LIMITER_COUNT = 100;

    @AppSwitch(des = "问问手绘地图大于多少天不展示", level = Level.p4)
    public static Integer AI_ASK_DAY_MORE = 12;

    @AppSwitch(des = "问问手绘地图POI大于多少天不展示", level = Level.p4)
    public static Integer AI_ASK_DAY_POI = 12;

    @AppSwitch(des = "问问手绘地图小于多少天不展示", level = Level.p4)
    public static Integer AI_ASK_DAY_LESS = 3;
    @AppSwitch(des = "手绘地图查询离线表逻辑", level = Level.p4)
    public static boolean DRAW_ODPS = false;

    @AppSwitch(des = "wheal api key", level = Level.p4)
    public static String WHALE_API_KEY = "P8Z1UCM51Y";
    @AppSwitch(des = "WHALE模型调用超时时间", level = Level.p3)
    public static Long WHALE_CALL_TIME_OUT = 10 * 60 * 1000L;
    @AppSwitch(des = "WHALE模型帧间超时时间", level = Level.p3)
    public static Long WHALE_PRE_EVENT_TIME_OUT = 30 * 1000L;
    @AppSwitch(des = "AI问一问是否开启收藏", level = Level.p3)
    public static Boolean AI_ASK_COLLECTION_ENABLED = true;

    @AppSwitch(des = "手绘地图展示旧地图开关", level = Level.p1)
    public static Boolean DRAWING_MAP_SHOW_COMMON_MAP = false;

    @AppSwitch(des = "手绘地图全走展示新地图开关", level = Level.p1)
    public static Boolean DRAWING_MAP_SHOW_NEW_MAP = false;

    @AppSwitch(des = "手绘地图城市转换", level = Level.p4)
    public static Map<String, String> DRAW_CITY_DEAL = Maps.newHashMap();

    @AppSwitch(des = "手绘地图展示多城市多poi逻辑开关", level = Level.p1)
    public static Boolean DRAWING_MAP_MUTIL_CITY_AND_MUTIL_POI = true;

    @AppSwitch(des = "手绘地图增加境外判断逻辑", level = Level.p1)
    public static Boolean DRAWING_MAP_ABORAD = false;

    @AppSwitch(des = "手绘地图展示多城市多poi逻辑开关", level = Level.p1)
    public static int DRAWING_MAP_CITYNAME_NUM_MIN = 2;

    @AppSwitch(des = "手绘地图展示多城市多poi逻辑开关", level = Level.p1)
    public static int DRAWING_MAP_CITYNAME_NUM_MAX = 8;

    @AppSwitch(des = "手绘地图展示多城市多poi逻辑开关", level = Level.p1)
    public static int DRAWING_MAP_DAY_NUM_MIN = 2;

    @AppSwitch(des = "手绘地图展示多城市多poi逻辑开关", level = Level.p1)
    public static int DRAWING_MAP_DAY_NUM_MAX = 8;

    @AppSwitch(des = "手绘地图展示多城市多poi逻辑开关", level = Level.p1)
    public static int DRAWING_MAP_POI_NUM_MIN = 1;

    @AppSwitch(des = "手绘地图展示多城市多poi逻辑开关", level = Level.p1)
    public static int DRAWING_MAP_POI_NUM_MAX = 12;

    @AppSwitch(des = "单程多天，天数范围下限", level = Level.p1)
    public static int SINGLE_CITY_MULTI_DAY_DAY_NUM_MIN = 2;


    @AppSwitch(des = "单程多天，天数范围上限", level = Level.p1)
    public static int SINGLE_CITY_MULTI_DAY_DAY_NUM_MAX = 7;



}
