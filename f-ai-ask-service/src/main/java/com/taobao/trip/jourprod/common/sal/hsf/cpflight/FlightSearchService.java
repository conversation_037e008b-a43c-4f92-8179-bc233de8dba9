package com.taobao.trip.jourprod.common.sal.hsf.cpflight;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;

import com.fliggy.forge.enums.ContainerTypeEnum;
import com.fliggy.forge.enums.TerminalTypeEnum;
import com.fliggy.forge.enums.TripTypeEnum;
import com.fliggy.forge.model.atom.FlightShoppingItem;
import com.fliggy.forge.model.route.RouteInfo;
import com.fliggy.forge.req.ODInfo;
import com.fliggy.forge.req.SpReq;
import com.fliggy.forge.req.TrafficReq;
import com.fliggy.forge.req.TripPreference;
import com.fliggy.forge.res.SpRes;
import com.fliggy.forge.res.TrafficRes;
import com.fliggy.forge.service.DomesticSearchService;
import com.fliggy.forge.service.InterTrafficSearchService;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import com.taobao.trip.jourprod.common.lang.utils.CIDateUtil;
import com.taobao.trip.jourprod.common.sal.annotation.RunLog;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.utils.LogUtil;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import static com.fliggy.forge.enums.CabinTypeEnum.ALL_CABIN;
import static com.fliggy.forge.enums.ChannelTypeEnum.APP;
import static com.fliggy.forge.enums.SearchModeEnum.OUTBOUND;

/**
 * @Description 特价机票查询服务
 * <AUTHOR>
 * @Date 2025/2/17
 **/
@Component
public class FlightSearchService {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(FlightSearchService.class);

    /**
     * 机票成人价格标签
     */
    public final static String FLIGHT_ADULT_PRICE_KEY = "ADT";

    @Resource
    private DomesticSearchService domesticSearchService;

    @Resource
    private InterTrafficSearchService interTrafficSearchService;

    @AteyeInvoker(description = "搜索实时国内机票", paraDesc = "depDate&depCity&arrCity&directOnly")
    public void testSearchRealTimeFlight(String depDate, String depCity, String arrCity, Boolean directOnly) {
        List<FlightInfo> groupItems = searchRealTimeDomesticFlight(CIDateUtil.parseWebDateFormat(depDate), depCity, arrCity, directOnly);
        Ateye.out.println(JSON.toJSONString(groupItems));
    }

    @AteyeInvoker(description = "搜索实时国际机票", paraDesc = "depDate&depCity&arrCity&directOnly")
    public void testSearchRealTimeInterFlight(String depDate, String depCity, String arrCity, Boolean directOnly) {
        List<FlightInfo> groupItems = searchRealTimeInterFlight(CIDateUtil.parseWebDateFormat(depDate), depCity, arrCity, directOnly);
        Ateye.out.println(JSON.toJSONString(groupItems));
    }

    /**
     * 搜索实时机票
     * 返回两部分
     * routeinfo-行程信息
     * shoppingItemMap-价格信息
     * @param depDate       出发时间
     * @param depCity       出发城市
     * @param arrCity       目的城市
     * @param directOnly    是否要查询中转航班
     */
    @RunLog
    public List<FlightInfo> searchRealTimeInterFlight(Date depDate, String depCity, String arrCity, Boolean directOnly) {
        SpReq spReq = new SpReq();
        spReq.setSearchMode(OUTBOUND);
        spReq.setChannelType(APP);
        ODInfo odInfo = new ODInfo();
        odInfo.setArrCityCode(arrCity);
        odInfo.setDepCityCode(depCity);
        odInfo.setDepDate(depDate);
        spReq.setOdInfoList(Lists.newArrayList(odInfo));
        spReq.setContainerType(ContainerTypeEnum.H5);
        spReq.setTerminalType(TerminalTypeEnum.WIRELESS);
        spReq.setTripType(TripTypeEnum.OW);
        TripPreference tripPreference = new TripPreference();
        tripPreference.setDirectOnly(directOnly);
        tripPreference.setCabinType(ALL_CABIN);
        tripPreference.setCabinClassList(Lists.newArrayList(ALL_CABIN));
        spReq.setTripPreference(tripPreference);
        TrafficReq req = new TrafficReq();
        req.setSpReq(spReq);
        TrafficRes search = interTrafficSearchService.search(req);
        if (search == null || !search.isSuccess() || search.getFlightRes() == null || CollectionUtils.isEmpty(search.getFlightRes().getItemList())) {
            return Lists.newArrayList();
        }
        return search.getFlightRes().getItemList().stream().map(item -> {
            FlightInfo flightInfo = new FlightInfo();
            flightInfo.setRouteInfo(item.getRouteInfo());
            Map<String, FlightShoppingItem> shoppingItemMap = item.getShoppingItemMap();
            if (MapUtils.isNotEmpty(shoppingItemMap) && shoppingItemMap.containsKey(FLIGHT_ADULT_PRICE_KEY)) {
                FlightShoppingItem flightShoppingItem = shoppingItemMap.get(FLIGHT_ADULT_PRICE_KEY);
                Integer sellPrice = flightShoppingItem.getSearchPrice().getSellPrice();
                flightInfo.setSellerPrice(sellPrice);
            }
            return flightInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 搜索实时机票
     * 返回两部分
     * routeinfo-行程信息
     * shoppingItemMap-价格信息
     * @param depDate       出发时间
     * @param depCity       出发城市
     * @param arrCity       目的城市
     * @param directOnly    是否要查询中转航班
     */
    @RunLog
    public List<FlightInfo> searchRealTimeDomesticFlight(Date depDate, String depCity, String arrCity, Boolean directOnly) {
        SpReq spReq = new SpReq();
        spReq.setSearchMode(OUTBOUND);
        spReq.setChannelType(APP);
        ODInfo odInfo = new ODInfo();
        odInfo.setArrCityCode(arrCity);
        odInfo.setDepCityCode(depCity);
        odInfo.setDepDate(depDate);
        spReq.setOdInfoList(Lists.newArrayList(odInfo));
        spReq.setContainerType(ContainerTypeEnum.H5);
        spReq.setTerminalType(TerminalTypeEnum.WIRELESS);
        spReq.setTripType(TripTypeEnum.OW);
        TripPreference tripPreference = new TripPreference();
        tripPreference.setDirectOnly(directOnly);
        tripPreference.setCabinType(ALL_CABIN);
        tripPreference.setCabinClassList(Lists.newArrayList(ALL_CABIN));
        spReq.setTripPreference(tripPreference);
        SpRes search = domesticSearchService.search(spReq);
        if (search == null || !search.isSuccess() || CollectionUtils.isEmpty(search.getItemList())) {
            return Lists.newArrayList();
        }
        return search.getItemList().stream().map(item -> {
            FlightInfo flightInfo = new FlightInfo();
            flightInfo.setRouteInfo(item.getRouteInfo());
            Map<String, FlightShoppingItem> shoppingItemMap = item.getShoppingItemMap();
            if (MapUtils.isNotEmpty(shoppingItemMap) && shoppingItemMap.containsKey(FLIGHT_ADULT_PRICE_KEY)) {
                FlightShoppingItem flightShoppingItem = shoppingItemMap.get(FLIGHT_ADULT_PRICE_KEY);
                Integer sellPrice = flightShoppingItem.getSearchPrice().getSellPrice();
                flightInfo.setSellerPrice(sellPrice);
            }
            return flightInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 航班信息
     */
    @Data
    public static class FlightInfo {

        /**
         * 行程信息
         */
        private RouteInfo routeInfo;

        /**
         * 价格信息
         */
        private Integer sellerPrice;

    }

}
