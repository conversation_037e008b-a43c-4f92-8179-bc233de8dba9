package com.taobao.trip.jourprod.common.sal.hsf.crowds;

import javax.annotation.Resource;

import com.fliggy.crowd.service.TripCrowdCommonService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.Uninterruptibles;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import com.taobao.trip.platform.protocol.TripCommonPlatformResult;
import fliggy.content.common.logger.LogRunnable;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/2/4
 */
@Service
public class TripCrowdService {

    private FliggyNewLogger logger = LogUtil.getFliggyNewLogger(TripCrowdService.class);

    @Resource
    private TripCrowdCommonService tripCrowdCommonService;

    private static final Integer DEFAULT_SIZE = 50;

    /**
     * 线程池
     */
    protected ListeningExecutorService crowdQueryExecutor = MoreExecutors
            .listeningDecorator(new ThreadPoolExecutor(10, 10, 0, TimeUnit.MILLISECONDS,
                    new LinkedBlockingQueue<Runnable>(20), new CustomizableThreadFactory("CrowdQueryExecutor")));

    /**
     * 用户是否在当前人群中
     * @param userId
     * @param crowdId
     * @return
     */
    public Boolean isUserInCrowd(Long userId, Long crowdId) {
        if (userId == null || crowdId == null) {
            return null;
        }
        try {
            TripCommonPlatformResult<Boolean> result = tripCrowdCommonService.isUidInCrowd(userId, crowdId);
            if (result != null && result.isSuccess()) {
                return result.getData();
            }
        } catch (Exception e) {
            logger.recordDangerException(new LogModel("isUserInCrowd").e(e).userId(userId).message("crowdId={0}", crowdId));
        }
        return null;
    }

    /**
     * 多人群查询
     * @param userId
     * @param crowdIds
     * @return
     */
    public Map<Long, Boolean> isUserInCrowds(Long userId, List<Long> crowdIds) {
        if (userId == null || CollectionUtils.isEmpty(crowdIds)) {
            return null;
        }
        final Map<Long, Boolean> crowdResult = Maps.newConcurrentMap();
        // 15个人群一组
        List<List<Long>> crowdIdPartList = Lists.partition(crowdIds, 15);
        final CountDownLatch countDownLatch = new CountDownLatch(crowdIdPartList.size());
        for (final List<Long> crowdIdList: crowdIdPartList) {
            crowdQueryExecutor.submit(new LogRunnable() {
                @Override
                public void logRun() {
                    long t1 = System.currentTimeMillis();
                    try {
                        TripCommonPlatformResult<Map<Long, Boolean>> result = tripCrowdCommonService.isUidInCrowd(userId, crowdIdList);
                        if (result != null && result.getData() != null) {
                            crowdResult.putAll(result.getData());
                        }
                    } catch (Exception e) {
                        logger.recordDangerException(new LogModel("isUserInCrowds").e(e));
                    } finally {
                        long t2 = System.currentTimeMillis();
                        logger.recordCost(new LogModel("isUserInCrowds").cost(t2 - t1));
                        countDownLatch.countDown();
                    }
                }
            });
        }
        if (!Uninterruptibles.awaitUninterruptibly(countDownLatch, 1, TimeUnit.SECONDS)) {
            logger.recordDangerException(new LogModel("isUserInCrowds").message("timeout"));
        }
        return crowdResult;
    }

    @AteyeInvoker(description = "测试用户是否在新人人群中",paraDesc = "userId&crowdId")
    public void testQueryIsUserInCrowd(Long userId, Long crowdId) throws ParseException {
        Boolean userInCrowd = isUserInCrowd(userId, crowdId);
        Ateye.out.println(userInCrowd);
    }
}
