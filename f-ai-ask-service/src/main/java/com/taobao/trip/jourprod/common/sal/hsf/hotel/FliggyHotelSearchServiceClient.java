package com.taobao.trip.jourprod.common.sal.hsf.hotel;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;

import com.google.common.collect.Lists;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import com.taobao.ateye.util.Ateye;
import com.taobao.eagleeye.EagleEye;
import com.taobao.mtop.common.Result;
import com.taobao.trip.jourdprod.core.model.common.ConstantElement;
import com.taobao.trip.jourprod.common.lang.utils.CIDateUtil;
import com.taobao.trip.jourprod.common.sal.annotation.RunLog;
import com.taobao.trip.wireless.hotel.FliggyHotelSearchService;
import com.taobao.trip.wireless.hotel.HotelWirelessQueryService;
import com.taobao.trip.wireless.hotel.domain.FliggyHotelSearchQuery;
import com.taobao.trip.wireless.hotel.domain.HotelCityPromotion;
import com.taobao.trip.wireless.hotel.domain.SearchHotelListParam;
import com.taobao.trip.wireless.hotel.domain.SearchHotelListVO;
import com.taobao.trip.wireless.hotel.domain.SearchHotelListVO.HotelListInfo;
import com.taobao.trip.wireless.hotel.domain.SearchResult;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @date 2019/8/19
 */
@Service
@Slf4j
public class FliggyHotelSearchServiceClient {

    private Logger logger = LoggerFactory.getLogger(FliggyHotelSearchServiceClient.class);
    private final FliggyNewLogger newLogger = LogUtil.getFliggyNewLogger(FliggyHotelSearchServiceClient.class);


    @Resource
    private FliggyHotelSearchService fliggyHotelSearchService;

    @Resource
    private HotelWirelessQueryService hotelWirelessQueryService;

    @Switch(name = "WHETHER_CLOSE_HOTEL_DISCOUNT_QUERY", description = "是否从酒店类目查询酒店利益点信息")
    public static volatile boolean WHETHER_CLOSE_HOTEL_DISCOUNT_QUERY = true;


    private volatile Integer QUEUE_SIZE = 100;

    private volatile Integer POOL_SIZE = 20;

    @Switch(name = "DEFAULT_TIME_OUT", description = "默认过期时间")
    private volatile Integer DEFAULT_TIME_OUT = 500;

    @Switch(name = "BATCH_SIZE", description = "批量查询个数")
    private volatile int BATCH_SIZE = 3;

    protected final ListeningExecutorService hotelExecutor = MoreExecutors.listeningDecorator(new ThreadPoolExecutor(POOL_SIZE, POOL_SIZE, 0, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<Runnable>(QUEUE_SIZE), new CustomizableThreadFactory("poiExecutor")));

    /**
     * 查询酒店接口 获取酒店利益点
     * @param userId
     * @param shidList
     * @param checkInDate
     * @param checkOutDate
     * @return
     */
    private List<HotelListInfo> batchGetHotelsFromHotel(Long userId, List<String> shidList, Date checkInDate, Date checkOutDate) {
        List<HotelListInfo> result = Lists.newArrayList();

        if (!WHETHER_CLOSE_HOTEL_DISCOUNT_QUERY) {
            return result;
        }

        try {
            FliggyHotelSearchQuery query = new FliggyHotelSearchQuery();
            query.setUserId(userId);

            Long[] shids = new Long[shidList.size()];
            for (int i=0; i< shidList.size(); i++) {
                shids[i] = NumberUtils.toLong(shidList.get(i));
            }
            query.setShids(shids);

            if (checkInDate != null && checkOutDate != null) {
                query.setCheckIn(checkInDate);
                query.setCheckOut(checkOutDate);
            }
            query.setSource(ConstantElement.APP_NAME);

            SearchResult<SearchHotelListVO> searchResult = fliggyHotelSearchService.batchGetHotels(query);
            if (searchResult != null && searchResult.isSuccess()
                && searchResult.getModule() != null) {
                return searchResult.getModule().getHotelList();
            }
        } catch (Exception e) {
            logger.error("FliggyHotelSearchServiceClient, batchGetHotelsFromHotel batchGetHotels is error!", e);
        }

        return result;
    }

    /**
     * 城市优惠筛选标签
     * @param userId
     * @param cityCode
     * @return
     */
    public HotelCityPromotion getHotelCityPromotion(Long userId, Integer cityCode, Date checkInDate, Date checkOutDate) {

        try {
            if (userId == null || cityCode == null) {
                return null;
            }

            FliggyHotelSearchQuery query = new FliggyHotelSearchQuery();
            query.setUserId(userId);
            query.setCityCode(cityCode);
            if (checkInDate != null && checkOutDate != null) {
                query.setCheckIn(checkInDate);
                query.setCheckOut(checkOutDate);
            }

            SearchResult<HotelCityPromotion> searchResult = fliggyHotelSearchService.getCityPromotion(query);
            if (searchResult == null || !searchResult.isSuccess()
                || searchResult.getModule() == null) {
                return null;
            }

            return searchResult.getModule();
        } catch (Exception e) {
            logger.error("FliggyHotelSearchServiceClient getHotelCityPromotion is error!", e);
        }

        return null;
    }

    /**
     * 手动添加卡片查询酒店列表
     *
     * @param pageNo
     * @param checkIn
     * @param checkOut
     * @param hotelKeywords
     * @return
     */
    @RunLog
    public SearchHotelListVO searchHotelList(Integer pageNo, String checkIn, String checkOut, Long cityId, String hotelKeywords) {
        try {
            if (StringUtils.isBlank(checkIn) || StringUtils.isBlank(checkOut)
                    || cityId == null) {
                return null;
            }
            if (pageNo == null || pageNo == 0) {
                pageNo = 1;
            }

            SearchHotelListParam listParam = new SearchHotelListParam();
            listParam.setCityCode(cityId.toString());
            listParam.setPageNo(pageNo);
            listParam.setPageSize(20);
            listParam.setCheckIn(checkIn);
            listParam.setCheckOut(checkOut);
            listParam.setSellerId(-1L);
            listParam.setKeyWords(hotelKeywords);

            Result<SearchHotelListVO> listVOResult = hotelWirelessQueryService.searchHotelList(listParam);
            if (listVOResult == null || !listVOResult.isSuccess()) {
                newLogger.recordOutput(new LogModel("searchHotelList_null")
                        .request(JSON.toJSONString(listParam))
                        .response(JSON.toJSONString(listVOResult)));
                return null;
            }
            newLogger.recordOutput(new LogModel("searchHotelList")
                    .request(JSON.toJSONString(listParam))
                    .response(JSON.toJSONString(listVOResult)));

            return listVOResult.getModel();
        } catch (Exception e) {
            newLogger.recordDangerException(new LogModel("searchHotelList_error")
                    .request(JSON.toJSONString(cityId))
                    .success(false)
                    .e(e));
        }
        return null;
    }

    public List<SearchHotelListVO>  concurrentSearchDainiwanHotelList(long userId, List<Long> shidList, String checkIn, String checkOut, boolean hasDiscountPrice){

        if(CollectionUtils.isEmpty(shidList)){
            return Lists.newArrayList();
        }
        List<List<Long>> shidLists = Lists.partition(shidList, BATCH_SIZE);
        List<ListenableFuture<SearchHotelListVO>> futures = Lists.newArrayList();
        for(List<Long> shids : shidLists){
            ListenableFuture<SearchHotelListVO> future = submitHotelRequest(userId, shids, checkIn, checkOut, hasDiscountPrice);
            futures.add(future);
        }

        ListenableFuture<List<SearchHotelListVO>> resultFuture = Futures.successfulAsList(futures);

        List<SearchHotelListVO> listVOS = null;

        try{
            listVOS = resultFuture.get(DEFAULT_TIME_OUT, TimeUnit.MILLISECONDS);
        }catch(Exception e){
            log.error("get hotels error", e);
        }

        return listVOS;
    }

    private SearchHotelListVO searchDainiwanHotelList(long userId, List<Long> shidList, String checkIn, String checkOut, boolean hasDiscountPrice){

        try {
            if (CollectionUtils.isEmpty(shidList)) {
                return null;
            }

            /**
             * 酒店接口比较慢,分批查询
             */
            if(shidList.size() > BATCH_SIZE){
                return null;
            }

            SearchHotelListParam param = new SearchHotelListParam();
            param.setShids(StringUtils.join(shidList, ","));
            param.setPageSize(shidList.size());
            param.setSellerId(-1L);
            //要获取折扣价格，必须带有userId
            if(hasDiscountPrice){
                param.setUserId(userId);
            }
            param.setSversion(25);
            param.setIsIncludePayLater(1);
            param.setIsShowExpedia(1);
            param.setSupportPCI(1);
            param.setCheckIn(checkIn);
            param.setCheckOut(checkOut);
            param.setOffset(0);

            Result<SearchHotelListVO> listVOResult = hotelWirelessQueryService.searchHotelList(param);
            if (listVOResult == null || !listVOResult.isSuccess()) {
                logger.error("FliggyHotelSearchServiceClient searchHotelList is error! param={}", JSON.toJSONString(param));
            }
            return listVOResult.getModel();

        }catch(Exception e){
            logger.error("search hotels error", e);
        }

        return null;
    }

    private ListenableFuture<SearchHotelListVO> submitHotelRequest(long userId, List<Long> shidList, String checkIn, String checkOut, boolean hasDiscountPrice) {

        final Object eagleEyeCtx = EagleEye.getRpcContext();
        return hotelExecutor.submit(new Callable<SearchHotelListVO>() {
            @Override
            public SearchHotelListVO call() throws Exception {
                try {
                    EagleEye.setRpcContext(eagleEyeCtx);
                    return searchDainiwanHotelList(userId, shidList, checkIn, checkOut, hasDiscountPrice);

                } catch (Exception e) {
                    log.error("query hotel error", e);
                    return null;
                }finally {
                    EagleEye.clearRpcContext();
                }
            }
        });
    }

    @AteyeInvoker(description = "测试查询酒店城市优惠筛选标签", paraDesc = "userId&cityCode&checkIn&checkOut")
    public void testGetHotelCityPromotion(Long userId, Integer cityCode, String checkIn, String checkOut) {

        Date checkInDate = CIDateUtil.parseWebDateFormat(checkIn);
        Date checkOutDate = CIDateUtil.parseWebDateFormat(checkOut);
        HotelCityPromotion hotelCityPromotion = getHotelCityPromotion(userId, cityCode, checkInDate, checkOutDate);

        Ateye.out.print(JSON.toJSONString(hotelCityPromotion));
    }

    @AteyeInvoker(description = "根据酒店ID列表查询酒店信息", paraDesc = "userId&shId&checkIn&checkOut&hasDiscountPrice")
    public String ateyeGetHotelsByShids(Long userId, Long shId, String checkIn, String checkOut, boolean hasDiscountPrice){

        List<Long> shIdList = Lists.newArrayList(shId);
        return JSON.toJSONString(searchDainiwanHotelList(userId, shIdList, checkIn, checkOut,hasDiscountPrice));
    }
}
