package com.taobao.trip.jourprod.common.sal.hsf.poi;

import com.alibaba.tripc.logicengine.domain.AppInfo;
import com.alibaba.tripc.logicengine.domain.LogicResult;
import com.alibaba.tripc.logicengine.domain.Result;
import com.alibaba.tripc.logicengine.domain.RuleRequest;
import com.alibaba.tripc.logicengine.domain.biz.PoiToPoilist;
import com.alibaba.tripc.logicengine.service.LogicService;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class PoiListServiceClient {
    private final FliggyNewLogger newLogger = LogUtil.getFliggyNewLogger(PoiReadServiceClient.class);

    @Resource
    LogicService logicService;

    /**
     * 获取poi
     * @param poiId 飞猪poiId
     */
    public PoiToPoilist getFirstPoiList(Long poiId) {
        if (null == poiId) {
            return null;
        }
        long ruleId = 15L;
        RuleRequest<PoiToPoilist> ruleRequest = new RuleRequest<>(PoiToPoilist.class);
        ruleRequest.setRuleId(ruleId);
        ruleRequest.addBizParam("poiId", String.valueOf(poiId));

        AppInfo appInfo = new AppInfo();
        appInfo.setAppName("f-ai-ask");
        appInfo.setDescription("AI问一问");
        Result<LogicResult<PoiToPoilist>> result = logicService.inferByRule(ruleRequest, appInfo);
        if (invalidResult(result)) {
            return null;
        }
        return result.getModule().getData().get(0);
    }

    private <T> boolean invalidResult(Result<LogicResult<T>> result) {
        return null == result || !result.isSuccess() || null == result.getModule() ||
                CollectionUtils.isEmpty(result.getModule().getData());
    }
}
