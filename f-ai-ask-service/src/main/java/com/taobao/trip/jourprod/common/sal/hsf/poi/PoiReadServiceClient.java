package com.taobao.trip.jourprod.common.sal.hsf.poi;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fliggypoi.client.domain.*;
import com.alibaba.fliggypoi.client.domain.airport.AirportPoiRequest;
import com.alibaba.fliggypoi.client.domain.airport.AirportPoiResponse;
import com.alibaba.fliggypoi.client.domain.airport.PoiType;
import com.alibaba.fliggypoi.client.domain.aroundpoi.AroundPoiSortEnum;
import com.alibaba.fliggypoi.client.domain.page.Order;
import com.alibaba.fliggypoi.client.domain.search.BaseFields;
import com.alibaba.fliggypoi.client.domain.search.FilterField;
import com.alibaba.fliggypoi.client.domain.search.PoiFields;
import com.alibaba.fliggypoi.client.domain.search.QueryCondition;
import com.alibaba.fliggypoi.client.domain.search.SearchField;
import com.alibaba.fliggypoi.client.domain.search.SearchGroup;
import com.alibaba.fliggypoi.client.domain.search.SearchRelation;
import com.alibaba.fliggypoi.client.domain.search.SortCondition;
import com.alibaba.fliggypoi.client.domain.search.SortEnum;
import com.alibaba.fliggypoi.client.domain.search.SortField;
import com.alibaba.fliggypoi.client.domain.tools.RequestFactory;
import com.alibaba.fliggypoi.client.domain.tools.RequestNewFactory;
import com.alibaba.fliggypoi.client.domain.tools.SearchGroupUtil;
import com.alibaba.fliggypoi.client.service.AirportPoiService;
import com.alibaba.fliggypoi.client.service.PoiImageService;
import com.alibaba.fliggypoi.client.service.TripPoiReadService;
import com.alibaba.fliggypoi.domain.constants.TripPoiIndustryEnum;
import com.alibaba.trip.tripdata.common.domain.page.Page;
import com.alibaba.trip.trippoi.biz.client.TrpoPoiBizReadService;
import com.alibaba.trip.trippoi.biz.client.TrpoPoiBizSearchService;
import com.alibaba.trip.trippoi.biz.client.domain.TrpoPoiBizMapAroundSearchParam;
import com.alibaba.trip.trippoi.biz.client.domain.TrpoPoiBizSearchByKeywordParam;
import com.alibaba.trip.trippoi.biz.client.domain.TrpoPoiBizSearchParam;
import com.alibaba.trip.trippoi.domain.TrpoPoiDO;
import com.alibaba.trip.trippoi.domain.enumerate.TrpoPoiSourceType;
import com.alibaba.trip.trippoi.domain.enumerate.TrpoPoiType;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.Uninterruptibles;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import com.taobao.ateye.util.Ateye;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.trip.jourprod.common.lang.utils.POIUtil;
import com.taobao.trip.jourprod.common.sal.annotation.RunLog;
import com.taobao.trip.jourprod.common.sal.hsf.common.AteyeFuncSwitch;
import com.taobao.trip.jourprod.common.sal.hsf.common.EnvSwitch;
import com.taobao.trip.jourprod.common.sal.hsf.config.Switcher;
import com.taobao.trip.jourprod.common.sal.hsf.tair.MdbTairHelper;
import com.taobao.trip.jourprod.common.sal.hsf.template.AbstractClient;
import fliggy.content.common.logger.LogRunnable;
import fliggy.content.model.FliggyLogger;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;

import static com.taobao.trip.hpc.client.utils.LatLngUtils.EARTH_RADIUS;

/**
 * 查詢poi信息 Created by mojin on 2018/1/19.
 */
@Service
@Slf4j
public class PoiReadServiceClient extends AbstractClient {

    private final FliggyLogger logger = LogUtil.getFliggyLogger(PoiReadServiceClient.class);
    private final FliggyNewLogger newLogger = LogUtil.getFliggyNewLogger(PoiReadServiceClient.class);

    @PostConstruct
    public void init() {
        SwitchManager.init(PoiReadServiceClient.class);
    }

    @AppSwitch(des = "批量根据源id查询poi的数量限制", level = com.taobao.csp.switchcenter.bean.Switch.Level.p3)
    public static int GET_POI_ID_BY_SOURCE_ID_COUNT = 20;


    @AppSwitch(des = "poi搜索是否用融合数据", level = com.taobao.csp.switchcenter.bean.Switch.Level.p3)
    public static Boolean POI_SEARCH_MERGE = true;

    @AppSwitch(des = "新版行程规划，手动搜索poi支持的一级类目列表", level = com.taobao.csp.switchcenter.bean.Switch.Level.p3)
    public static List<Integer> PLAN_NEW_FIRST_CATEGORY = Lists.newArrayList(1, 4, 5, 6, 7, 9);

    @AppSwitch(des = "新版行程规划，手动搜索poi支持的酒店一级类目列表", level = com.taobao.csp.switchcenter.bean.Switch.Level.p3)
    public static List<Integer> PLAN_NEW_HOTEL_FIRST_CATEGORY = Lists.newArrayList(4);

    @AppSwitch(des = "新版行程规划，某城市下热门poi支持的一级类目列表", level = com.taobao.csp.switchcenter.bean.Switch.Level.p3)
    public static List<Integer> PLAN_NEW_FIRST_CATEGORY_BY_HOT_POI = Lists.newArrayList(5, 9);

    @AppSwitch(des = "新版行程规划，某城市下热门poi支持排序", level = com.taobao.csp.switchcenter.bean.Switch.Level.p3)
    public static String PLAN_NEW_HOT_POI_SORT = PoiFields.IPV_MUV;

    @AppSwitch(des = "新版行程规划，某城市下热门poi数量", level = com.taobao.csp.switchcenter.bean.Switch.Level.p3)
    public static Integer PLAN_NEW_HOT_POI_COUNT = 10;

    @AppSwitch(des = "新版行程规划，推荐地点-某城市下热门poi数量", level = com.taobao.csp.switchcenter.bean.Switch.Level.p3)
    public static Integer PLAN_NEW_CITY_HOT_POI_COUNT = 20;

    @AppSwitch(des = "新版行程规划，手动搜索poi支持的二级类目列表", level = com.taobao.csp.switchcenter.bean.Switch.Level.p3)
    public static List<Integer> PLAN_NEW_SECOND_CATEGORY = Lists.newArrayList(4031, 4032, 4034, 1001, 1002);

    @AppSwitch(des = "新版行程规划，手动搜索poi支持的酒店二级类目列表", level = com.taobao.csp.switchcenter.bean.Switch.Level.p3)
    public static List<Integer> PLAN_NEW_HOTEL_SECOND_CATEGORY = Lists.newArrayList(4031);


    @Resource
    private TrpoPoiBizReadService trpoPoiBizReadService;

    @Resource
    private TrpoPoiBizSearchService trpoPoiBizSearchService;

    @Resource
    private TripPoiReadService tripPoiReadService;

    @Resource
    private AirportPoiService airportPoiService;

    @Resource
    private PoiImageService poiImageService;

    @Switch(name = "poiCacheTime", description = "poi缓存时间(s)")
    public static Integer poiCacheTime = 2 * 60;

    @Resource
    private MdbTairHelper mdbTairHelper;

    @Resource
    private EnvSwitch envSwitch;

    @Switch(name = "TAIR_MOVE_POI", description = "行程缓存切到新空间,同时修改TAIR_MOVE")
    public static volatile boolean TAIR_MOVE_POI = false;

    @Switch(name = "NEW_POI_SERVICE", description = "行程切换到新的poi服务")
    public static volatile boolean NEW_POI_SERVICE = true;

    @Switch(name = "USE_CACHE_POIS_QUERY", description = "批量查询poi是否使用缓存")
    public static volatile boolean USE_CACHE_POIS_QUERY = true;

    @Switch(name = "USE_CACHE_AIRPORT_POIS_QUERY", description = "机场poi是否使用缓存")
    public static volatile boolean USE_CACHE_AIRPORT_POIS_QUERY = true;

    @Switch(name = "MAX_QUERY_POI_NUM", description = "批量查询poi最大查询个数")
    private static Integer MAX_QUERY_POI_NUM = 120;

    @Switch(name = "batchPoiImageSize", description = "批量查询poi图片个数")
    private volatile Integer batchPoiImageSize = 3;

    @Switch(name = "queryPoiImageTimeOut", description = "查询poi图片超时时间")
    private volatile Integer queryPoiImageTimeOut = 200;

    @Switch(name = "POI_QUERY_HOT_POI_CITY_CACHE_TTL", description = "根据城市查询热门poi缓存超时时间")
    public static Integer POI_QUERY_HOT_POI_CITY_CACHE_TTL = 7 * 24 * 60 * 60;



    private static final Integer MAX_ONE_QUERY_POI_NUM = 20;

    //根据城市查询热门poi缓存key
    private static final String POI_QUERY_HOT_POI_CITY_CACHE_KEY = "tripOd:poi:hotByCity:";

    //查询poi缓存key
    private static final String POI_QUERY_CACHE_KEY = "tripOd:poi:";

    protected final ListeningExecutorService poiImageExecutor = MoreExecutors.listeningDecorator(
            new ThreadPoolExecutor(10, 10, 0, TimeUnit.MILLISECONDS,
                    new LinkedBlockingQueue<Runnable>(50), new CustomizableThreadFactory("poiImageExecutor")));

    /**
     * 并发查询poi 线程池
     */
    private static final ThreadPoolExecutor poiQueryExecutor = new ThreadPoolExecutor(20, 20, 0, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(50), new CustomizableThreadFactory("poiQueryExecutor"));

    /**
     * 新的poi批量查询接口 最多查询200个
     *
     * @param poiIds
     * @return
     */
    public List<TrpoPoiDO> batchQueryPoiListByIds(List<Long> poiIds) {
        List<TrpoPoiDO> result = Lists.newArrayList();
        try {
            if (CollectionUtils.isEmpty(poiIds) || poiIds.size() > MAX_QUERY_POI_NUM) {
                log.error("PoiReadServiceClient, batchQueryPoiListByIds error! ids is null or ids size > 200! size={}",
                        poiIds.size());
                logger.recordNormalException("batchQueryPoiListByIds", "ids is null or ids size > 200! size={}",
                        poiIds.size());
                return result;
            }

            //如果poiIds超过20个要拆分，一次请求最多20个
            List<Long> ids = Lists.newArrayList();
            for (Long poiId : poiIds) {
                if (ids.size() < MAX_ONE_QUERY_POI_NUM) {
                    ids.add(poiId);
                } else {
                    TripPoiResult<List<FliggyPoi>> poiResult = tripPoiReadService.getPois(ids);
                    if (poiResult == null || !poiResult.isSuccess()
                            || CollectionUtils.isEmpty(poiResult.getData())) {
                        continue;
                    }
                    List<FliggyPoi> fliggyPois = poiResult.getData();
                    List<TrpoPoiDO> poiDOS = transformList(fliggyPois);
                    if (CollectionUtils.isNotEmpty(poiDOS)) {
                        result.addAll(poiDOS);
                    }
                    ids = Lists.newArrayList();
                    ids.add(poiId);
                }
            }
            if (ids.size() <= MAX_ONE_QUERY_POI_NUM) {
                TripPoiResult<List<FliggyPoi>> poiResult = tripPoiReadService.getPois(ids);
                if (poiResult == null || !poiResult.isSuccess()
                        || CollectionUtils.isEmpty(poiResult.getData())) {
                    return result;
                }
                List<FliggyPoi> fliggyPois = poiResult.getData();
                List<TrpoPoiDO> poiDOS = transformList(fliggyPois);
                if (CollectionUtils.isNotEmpty(poiDOS)) {
                    result.addAll(poiDOS);
                }
            }
            newLogger.recordOutput(new LogModel("PoiReadServiceClient_batchQueryPoiListByIds")
                    .request(JSON.toJSONString(poiIds))
                    .response(JSON.toJSONString(result)));
            return result;
        } catch (Exception e) {
            log.error("PoiReadServiceClient, batchQueryPoiListByIds error! ids={}", JSON.toJSONString(poiIds), e);
            logger.recordDangerException("batchQueryPoiListByIds", e, "ids={}", JSON.toJSONString(poiIds));
        }
        return result;
    }

    /**
     * 新的poi批量查询接口 最多查询200个
     *
     * @param poiIds
     * @return
     */
    @RunLog(userId = "#userId")
    public List<FliggyPoi> batchQueryPoiListNewByIds(Long userId, List<Long> poiIds) {
        List<FliggyPoi> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(poiIds)) {
            return result;
        }
        poiIds = poiIds.stream().distinct().collect(Collectors.toList());
        //每20拆分一组
        List<List<Long>> poiPartList = Lists.partition(poiIds, MAX_ONE_QUERY_POI_NUM);

        CountDownLatch countDownLatch = new CountDownLatch(poiPartList.size());
        Map<Long, FliggyPoi> poiMap = Maps.newHashMap();
        for (List<Long> singleList : poiPartList) {
            poiQueryExecutor.submit(new LogRunnable() {
                @Override
                public void logRun() {
                    try {
                        if (CollectionUtils.isEmpty(singleList)) {
                            return;
                        }
                        List<Long> curPoiIdList = Lists.newArrayList();
                        for (Long poiId : singleList) {
                            String key = POI_QUERY_CACHE_KEY + poiId;
                            Object value = mdbTairHelper.getValue(key);
                            if (Objects.nonNull(value)) {
                                poiMap.put(poiId, JSON.parseObject(value.toString(), FliggyPoi.class));
                            } else {
                                curPoiIdList.add(poiId);
                            }
                        }
                        if (CollectionUtils.isEmpty(curPoiIdList)) {
                            return;
                        }
                        TripPoiResult<List<FliggyPoi>> poiResult = tripPoiReadService.getPois(curPoiIdList);
                        if (Objects.isNull(poiResult) || !poiResult.isSuccess() || CollectionUtils.isEmpty(poiResult.getData())) {
                            return;
                        }
                        List<FliggyPoi> fliggyPois = poiResult.getData();
                        if (CollectionUtils.isNotEmpty(fliggyPois)) {
                            Map<Long, FliggyPoi> poiIdObjectMap = fliggyPois.stream()
                                    .filter(poi -> Objects.nonNull(poi.getPoiBase()))
                                    .collect(Collectors.toMap(poi -> poi.getPoiBase().getPoiId(), Function.identity(), (key1, key2) -> key1));
                            if (MapUtils.isNotEmpty(poiIdObjectMap)) {
                                poiMap.putAll(poiIdObjectMap);
                                poiIdObjectMap.forEach((key, value) -> {
                                    String cacheKey = POI_QUERY_CACHE_KEY + key;
                                    mdbTairHelper.putValue(cacheKey, JSON.toJSONString(value, SerializerFeature.WriteClassName), POI_QUERY_HOT_POI_CITY_CACHE_TTL);
                                });
                            }
                        }
                    } catch (Exception e) {
                        newLogger.recordDangerException(new LogModel("batchQueryPoiListNewByIds").e(e).request(JSON.toJSONString(singleList)));
                    } finally {
                        countDownLatch.countDown();
                    }
                }
            });
        }
        if (!Uninterruptibles.awaitUninterruptibly(countDownLatch, AteyeFuncSwitch.QUERY_POI_EXECUTOR_TIME_LIMIT, TimeUnit.SECONDS)) {
            newLogger.recordDangerException(new LogModel("batchQueryPoiListNewByIds_poiQueryExecutor")
                    .request(JSON.toJSONString(poiIds)));
        }
        for (Long poiId : poiIds) {
            FliggyPoi fliggyPoi = poiMap.get(poiId);
            if (Objects.isNull(fliggyPoi)) {
                continue;
            }
            result.add(fliggyPoi);
        }
        return result;
    }

    /**
     * 根据外部POI ID查询平台POI id
     *
     * @param sourceType
     * @param sourceId
     * @return
     */
    public Long getPoiIdBySourceId(TrpoPoiSourceType sourceType, String sourceId) {
        Long poiId = null;
        try {
            if (sourceType == null || StringUtils.isEmpty(sourceId)) {
                return poiId;
            }
            if (NEW_POI_SERVICE) {
                TripPoiResult<Long> poiIdResult = tripPoiReadService.getPoiIdByRawPoiId(sourceId,
                        sourceType.getCode());
                if (poiIdResult != null && poiIdResult.isSuccess()) {
                    poiId = poiIdResult.getData();
                }
            } else {
                poiId = trpoPoiBizReadService.getPoiIdBySourceId(sourceType, sourceId);
            }
        } catch (Exception e) {
            log.error("PoiReadServiceClient, getPoiIdBySourceId error! sourceType={}, sourceId={}",
                    sourceType.getDescription(), sourceId, e);
            logger.recordDangerException("getPoiIdBySourceId", e, "sourceType={}, sourceId={}",
                    sourceType.getDescription(), sourceId);
        }

        return poiId;
    }

    /**
     * 根据外部POI ID查询平台POI
     *
     * @param sourceType
     * @param sourceId
     * @return
     */
    public TrpoPoiDO getPoiBySourceId(TrpoPoiSourceType sourceType, String sourceId) {
        TrpoPoiDO poiDO = null;
        try {
            if (sourceType == null || StringUtils.isEmpty(sourceId)) {
                return poiDO;
            }
            if (NEW_POI_SERVICE) {
                TripPoiResult<Long> poiIdByRawPoiId = tripPoiReadService.getPoiIdByRawPoiId(sourceId,
                        sourceType.getCode());
                if (poiIdByRawPoiId == null || !poiIdByRawPoiId.isSuccess()
                        || poiIdByRawPoiId.getData() == null) {
                    return poiDO;
                }
                Long poiId = poiIdByRawPoiId.getData();
                TripPoiResult<FliggyPoi> poiResult = tripPoiReadService.getPoi(poiId);
                if (poiResult != null && poiResult.isSuccess()) {
                    poiDO = transform(poiResult.getData());
                }
            } else {
                poiDO = trpoPoiBizReadService.getPoiBySourceId(sourceType, sourceId);
            }
        } catch (Exception e) {
            log.error("PoiReadServiceClient, getPoiBySourceId error! sourceType={}, sourceId={}",
                    sourceType.getDescription(), sourceId, e);
            logger.recordDangerException("getPoiBySourceId", "sourceType={}, sourceId={}", sourceType.getDescription(),
                    sourceId);
        }

        return poiDO;
    }

    /**
     * 根据poiId和类型查询对应源id
     *
     * @param poiIds
     * @param sourceType
     * @return
     */
    public Map<Long, List<String>> getRawPoiIdByPoiIds(List<Long> poiIds, TrpoPoiSourceType sourceType) {
        Map<Long, List<String>> poiOriginMapping = Maps.newHashMap();
        try {
            if (sourceType == null || CollectionUtils.isEmpty(poiIds)) {
                return poiOriginMapping;
            }
            TripPoiResult<Map<Long, List<String>>> rawPoiResult = tripPoiReadService.getRawPoiIdByPoiIds(poiIds,
                    sourceType.getCode());
            if (rawPoiResult == null || !rawPoiResult.isSuccess() || MapUtils.isEmpty(rawPoiResult.getData())) {
                return poiOriginMapping;
            }
            return rawPoiResult.getData();
        } catch (Exception e) {
            log.error("PoiReadServiceClient, getRawPoiIdByPoiIds error! sourceType={}, poiIds={}",
                    sourceType.getDescription(), JSON.toJSONString(poiIds), e);
            logger.recordDangerException("getRawPoiIdByPoiIds", e, "sourceType={}, poiIds={}",
                    sourceType.getDescription(), JSON.toJSONString(poiIds));
        }
        return poiOriginMapping;
    }

    /**
     * 根据POI id 查询平台POI
     *
     * @param poiId
     * @return
     */
    public TrpoPoiDO getPoiByPoiId(Long poiId) {
        TrpoPoiDO poiDO = null;
        try {
            if (poiId == null) {
                return poiDO;
            }
            if (NEW_POI_SERVICE) {
                TripPoiResult<FliggyPoi> fligguPoiResult = tripPoiReadService.getPoi(poiId);
                if (fligguPoiResult != null && fligguPoiResult.isSuccess()) {
                    poiDO = transform(fligguPoiResult.getData());
                }
            } else {
                poiDO = trpoPoiBizReadService.get(poiId);
            }
        } catch (Exception e) {
            log.error("PoiReadServiceClient, getPoiByPoiId error! sourceType={}, sourceId={}", poiId, e);
            logger.recordDangerException("getPoiByPoiId", e, "poiId={}", poiId);
        }

        return poiDO;
    }

    public FliggyPoi getFliggyPoiByPoiId(Long poiId) {
        FliggyPoi poiDO = null;
        try {
            if (poiId == null) {
                return poiDO;
            }
            TripPoiResult<FliggyPoi> poiResult = tripPoiReadService.getPoi(poiId);
            if (poiResult == null || !poiResult.isSuccess()) {
                return poiDO;
            }
            poiDO = poiResult.getData();
            transformLatitudeAndLongitude(poiDO);
        } catch (Exception e) {
            log.error("PoiReadServiceClient, getFliggyPoiByPoiId error! sourceType={}, sourceId={}", poiId, e);
            logger.recordDangerException("getFliggyPoiByPoiId", e, "poiId={}", poiId);
        }
        return poiDO;
    }

    public FliggyPoi getFliggyPoiByPoiIdLanguage(Long poiId, String language) {
        FliggyPoi poiDO = null;
        try {
            if (poiId == null) {
                return null;
            }
            PoiQueryParam poiQueryParam = new PoiQueryParam();
            poiQueryParam.setPoiId(poiId);
            poiQueryParam.setQueryExtend(true);
            poiQueryParam.setQueryFluctuantProperties(true);
            poiQueryParam.setIndustry(TripPoiIndustryEnum.PLATFORM.getCode());
            poiQueryParam.setLanguageList(Lists.newArrayList(language));
            TripPoiResult<FliggyPoi> poiResult = tripPoiReadService.getPoi(poiQueryParam);
            if (poiResult == null || !poiResult.isSuccess()) {
                return null;
            }
            poiDO = poiResult.getData();
            transformLatitudeAndLongitude(poiDO);
        } catch (Exception e) {
            log.error("PoiReadServiceClient, getFliggyPoiByPoiId error! sourceType={}, sourceId={}", poiId, e);
            logger.recordDangerException("getFliggyPoiByPoiId", e, "poiId={}", poiId);
        }
        return poiDO;
    }

    public Map<Long, FliggyPoi> batchGetFliggyPoiByPoiIds(List<Long> poiIds) {
        try {
            if (CollectionUtils.isEmpty(poiIds)) {
                return null;
            }
            TripPoiResult<List<FliggyPoi>> poiResult = tripPoiReadService.getPois(poiIds);
            if (poiResult == null || !poiResult.isSuccess()
                    || CollectionUtils.isEmpty(poiResult.getData())) {
                return null;
            }
            Map<Long, FliggyPoi> fliggyPoiMap = Maps.newHashMap();
            List<FliggyPoi> fliggyPois = poiResult.getData();
            fliggyPois.forEach(item -> {
                if (Objects.nonNull(item.getPoiBase())) {
                    Long id = item.getPoiBase().getPoiId();
                    fliggyPoiMap.put(id, item);
                }
            });
            return fliggyPoiMap;
        } catch (Exception e) {
            logger.recordDangerException("batchGetFliggyPoiByPoiIds", e, "poiIds={}", JSON.toJSONString(poiIds));
        }
        return null;
    }

    /**
     * 根据距离和热度排序
     *
     * @param searchParam
     * @return
     */
    public List<TrpoPoiDO> searchPoiMapAround(TrpoPoiBizMapAroundSearchParam searchParam) {
        List<TrpoPoiDO> trpoPoiDOList = null;
        try {
            if (searchParam == null) {
                return trpoPoiDOList;
            }
            if (NEW_POI_SERVICE) {
                SearchPoiParam param = new SearchPoiParam();
                List<Integer> poiTypes = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(searchParam.getPoiTypes())) {
                    for (TrpoPoiType poiType : searchParam.getPoiTypes()) {
                        poiTypes.add(poiType.getCode());
                    }
                }
                param.setPoiTypes(poiTypes);
                param.setLongitude(searchParam.getLongitude());
                param.setLatitude(searchParam.getLatitude());
                if (searchParam.getOffset() != 0) {
                    param.setOffset(searchParam.getOffset());
                }
                param.setRows(searchParam.getRows());
                param.setFilterDistance(true);
                param.setRadius(searchParam.getRadius());
                param.setSearchPoiAroundByProductAndLBS(true);
                RequestFactory requestFactory = new RequestFactory();
                SearchPoiRequest seachRequest = (SearchPoiRequest) requestFactory.buildSearchPoiRequest(param);
                TripPoiResult<List<FliggyPoi>> poiResult = tripPoiReadService.searchPoi(seachRequest);
                if (poiResult != null && poiResult.isSuccess()) {
                    trpoPoiDOList = transformList(poiResult.getData());
                }
            } else {
                trpoPoiDOList = trpoPoiBizSearchService.searchPoiMapAround(searchParam);
            }
        } catch (Exception e) {
            log.error("PoiReadServiceClient, searchPoiMapAround error!", e);
            logger.recordDangerException("searchPoiMapAround", e, JSON.toJSONString(searchParam));
        }
        return trpoPoiDOList;
    }

    @AteyeInvoker(description = "测试搜索服务", paraDesc = "lat&lng&radius&raws&types")
    public String ateyeSearchPoiMapAroundNew(Double lat, Double lng, Double radius, Integer rows, String types) {

        SearchPoiParam searchParam = new SearchPoiParam();
        String[] typeArray = StringUtils.split(types, ",");
        List<Integer> poiTypeInts = Lists.newArrayList();
        for (String typeStr : typeArray) {
            poiTypeInts.add(NumberUtils.toInt(typeStr));
        }
        searchParam.setPoiTypes(poiTypeInts);
        searchParam.setLongitude(lng);
        searchParam.setLatitude(lat);
        searchParam.setRows(rows);
        searchParam.setRadius(radius);
        searchParam.setFilterDistance(true);
        searchParam.setSortDistance(true);
        searchParam.setOnLine(true);

        return JSON.toJSONString(searchPoiMapAround(searchParam));
    }

    /*
      新的搜索服务
     */
    public List<TrpoPoiDO> searchPoiMapAround(SearchPoiParam param) {
        List<TrpoPoiDO> trpoPoiDOList = null;
        try {
            RequestFactory requestFactory = new RequestFactory();
            SearchPoiRequest seachRequest = (SearchPoiRequest) requestFactory.buildSearchPoiRequest(param);
            TripPoiResult<List<FliggyPoi>> poiResult = tripPoiReadService.searchPoi(seachRequest);
            if (poiResult != null && poiResult.isSuccess()) {
                trpoPoiDOList = transformList(poiResult.getData());
            }
        } catch (Exception e) {
            log.error("PoiReadServiceClient, searchPoiMapAround error!", e);
            logger.recordDangerException("searchPoiMapAround", e, JSON.toJSONString(param));
        }
        return trpoPoiDOList;
    }

    /**
     * 根据目的地查询周边热门poi
     *
     * @param param
     * @return
     */
    public List<TrpoPoiDO> searchPoiByCityId(TrpoPoiBizSearchParam param) {
        List<TrpoPoiDO> trpoPoiDOList = null;
        try {
            if (param == null || param.getSdivisionId() == null
                    || param.getPoiType() == null) {
                return trpoPoiDOList;
            }
            if (NEW_POI_SERVICE) {
                SearchPoiParam poiParam = new SearchPoiParam();
                poiParam.setDivisionId(param.getSdivisionId());
                List<Integer> poiTypes = Lists.newArrayList();
                poiTypes.add(param.getPoiType().getCode());
                poiParam.setPoiTypes(poiTypes);
                poiParam.setOffset(param.getOffset());
                poiParam.setRows(param.getRows());
                RequestFactory requestFactory = new RequestFactory();
                SearchPoiRequest seachRequest = (SearchPoiRequest) requestFactory.buildSearchPoiRequest(poiParam);
                //按热度降序排序
                List<SortCondition> sorts = new ArrayList<>();
                seachRequest.setSorts(sorts);
                SortCondition sortCondition = new SortCondition();
                sorts.add(sortCondition);
                SortField sortField = new SortField();
                BaseFields baseFields = new BaseFields();
                sortField.setField(baseFields.PRIORITY.getQueryFiled());
                sortCondition.setField(sortField);
                sortCondition.setSort(SortEnum.DESC);
                TripPoiResult<List<FliggyPoi>> poiResult = tripPoiReadService.searchPoi(seachRequest);
                if (poiResult == null || !poiResult.isSuccess()) {
                    return trpoPoiDOList;
                }
                return transformList(poiResult.getData());
            } else {
                Page<TrpoPoiDO> doPage = trpoPoiBizSearchService.searchPoi(param);
                if (doPage != null && CollectionUtils.isNotEmpty(doPage.getData())) {
                    trpoPoiDOList = doPage.getData();
                }
            }
        } catch (Exception e) {
            log.error("PoiReadServiceClient, searchPoiByCityId error! param={}", JSON.toJSONString(param), e);
            logger.recordDangerException("searchPoiByCityId", e, JSON.toJSONString(param));
        }
        return trpoPoiDOList;
    }

    /**
     * 根据关键字查询poi
     *
     * @param searchParam
     * @return
     */
    public TrpoPoiDO searchPoiByKeyword(TrpoPoiBizSearchByKeywordParam searchParam) {
        TrpoPoiDO trpoPoiDo = null;
        try {
            List<TrpoPoiDO> result = Lists.newArrayList();
            if (NEW_POI_SERVICE) {
                SearchPoiParam param = new SearchPoiParam();
                List<Integer> poiTypes = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(searchParam.getPoiTypes())) {
                    for (TrpoPoiType poiType : searchParam.getPoiTypes()) {
                        poiTypes.add(poiType.getCode());
                    }
                }
                param.setPoiTypes(poiTypes);
                param.setKeyWord(searchParam.getKeyword());
                param.setDivisionId(searchParam.getDivisionId());
                param.setOnLine(searchParam.getOnline());
                RequestFactory requestFactory = new RequestFactory();
                SearchPoiRequest seachRequest = (SearchPoiRequest) requestFactory.buildSearchPoiRequest(param);
                TripPoiResult<List<FliggyPoi>> poiResult = tripPoiReadService.searchPoi(seachRequest);
                if (poiResult != null && poiResult.isSuccess()) {
                    result = transformList(poiResult.getData());
                }
            } else {
                Page<TrpoPoiDO> poiDOPage = trpoPoiBizSearchService.searchPoiByKeyword(searchParam);
                if (poiDOPage != null) {
                    result = poiDOPage.getData();
                }
            }
            if (CollectionUtils.isNotEmpty(result)) {
                for (TrpoPoiDO poiDO : result) {
                    if (poiDO.getName().equals(searchParam.getKeyword())) {
                        trpoPoiDo = poiDO;
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("PoiReadServiceClient, searchPoiByKeyword error! searchParam={}", JSON.toJSONString(searchParam),
                    e);
            logger.recordDangerException("searchPoiByKeyword", e, JSON.toJSONString(searchParam));
        }
        return trpoPoiDo;
    }

    /**
     * 根据关键字查询poi集合
     *
     * @param searchParam
     * @return
     */
    public List<TrpoPoiDO> searchPoiListByKeyword(TrpoPoiBizSearchByKeywordParam searchParam) {
        List<TrpoPoiDO> result = Lists.newArrayList();
        try {
            if (NEW_POI_SERVICE) {
                SearchPoiParam param = new SearchPoiParam();
                List<Integer> poiTypes = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(searchParam.getPoiTypes())) {
                    for (TrpoPoiType poiType : searchParam.getPoiTypes()) {
                        poiTypes.add(poiType.getCode());
                    }
                }
                param.setPoiTypes(poiTypes);
                param.setKeyWord(searchParam.getKeyword());
                param.setDivisionId(searchParam.getDivisionId());
                param.setRows(searchParam.getRows());
                param.setOnLine(searchParam.getOnline());
                RequestFactory requestFactory = new RequestFactory();
                SearchPoiRequest seachRequest = (SearchPoiRequest) requestFactory.buildSearchPoiRequest(param);
                TripPoiResult<List<FliggyPoi>> poiResult = tripPoiReadService.searchPoi(seachRequest);
                if (poiResult != null && poiResult.isSuccess()) {
                    return transformList(poiResult.getData());
                }
            } else {
                Page<TrpoPoiDO> poiDOPage = trpoPoiBizSearchService.searchPoiByKeyword(searchParam);
                if (poiDOPage == null || CollectionUtils.isEmpty(poiDOPage.getData())) {
                    return result;
                }
                return poiDOPage.getData();
            }
        } catch (Exception e) {
            log.error("PoiReadServiceClient, searchPoiListByKeyword error! searchParam={}",
                    JSON.toJSONString(searchParam), e);
            logger.recordDangerException("searchPoiListByKeyword", e, JSON.toJSONString(searchParam));
        }
        return result;
    }

    public TripPoiResult<List<FliggyPoiImage>> getPoiImages(Long poiId, int pageNo, int pageSize) {
        SearchPoiImageRequest searchPoiImageRequest = new SearchPoiImageRequest();
        searchPoiImageRequest.setOnline(true);
        searchPoiImageRequest.setPageSize(pageSize);
        searchPoiImageRequest.setPageNo(pageNo);
        searchPoiImageRequest.setPoiId(poiId);
        try {
            TripPoiResult<List<FliggyPoiImage>> result = poiImageService.searchPoiImage(searchPoiImageRequest);
            logger.recordOutput("getPoiImages", String.valueOf(poiId), JSON.toJSONString(result));
            if (result == null || (!result.isSuccess())) {
                return TripPoiResult.success(Lists.newArrayList());
            }
            return result;
        } catch (Throwable t) {
            log.error("get poi image error", t);
            logger.recordDangerException("getPoiImage", t);
        }
        return TripPoiResult.success(Lists.newArrayList());
    }

    private String getPhotographKey(Long poiId) {
        return "POI_PHOTO_GUIDE_" + poiId + "_" + (EnvSwitch.isOnline() ? "2" : "1");
    }

    /**
     * 计算与poi距离
     *
     * @param lat 经度
     * @param lng 纬度
     * @return
     */
    public Double getDistance(String lng, String lat, String lng2, String lat2) {
        Double distance = null;
        if (StringUtils.isEmpty(lng) || StringUtils.isEmpty(lat) || StringUtils.isEmpty(lng2)
                || StringUtils.isEmpty(lat2)) {
            return distance;
        }
        distance = getDistance(Double.parseDouble(lng), Double.parseDouble(lat),
                Double.parseDouble(lng2), Double.parseDouble(lat2));
        return distance;
    }

    public static double getDistance(double lng1, double lat1, double lng2, double lat2) {
        try {
            double radLat1 = rad(lat1);
            double radLat2 = rad(lat2);
            double a = radLat1 - radLat2;
            double b = rad(lng1) - rad(lng2);
            double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1)
                    * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
            s = s * EARTH_RADIUS;
            s = Math.round(s * 10000) / 10000;
            return s;
        } catch (Exception e) {
        }

        return Double.MAX_VALUE;
    }

    private static double rad(double d) {
        return d * Math.PI / 180.0;
    }

    @AteyeInvoker(description = "查询poiId")
    public void queryPoiIdBySourceId(Integer type, String sourceId) {
        Long poiId = getPoiIdBySourceId(TrpoPoiSourceType.codeOf(type), sourceId);
        Ateye.out.print(poiId);
    }

    @AteyeInvoker(description = "根据源id查询poiId，新方法")
    public void queryNewPoiIdBySourceId(Integer type, String sourceId) {
        TripPoiResult<Long> poiIdResult = tripPoiReadService.getPoiIdByRawPoiId(sourceId, type);
        Long poiId = poiIdResult.getData();
        Ateye.out.print(poiId);
    }

    @AteyeInvoker(description = "获取源poi和飞猪poi的对应关系")
    public void getRawPoiIdByPoiId(Integer type, Long poiId) {
        TripPoiResult<List<String>> rawPoiIdByPoiId = tripPoiReadService.getRawPoiIdByPoiId(poiId, type);
        List<String> data = rawPoiIdByPoiId.getData();
        Ateye.out.print(JSON.toJSONString(data));
    }

    @AteyeInvoker(description = "新的搜索poi")
    public void searchPoiNew(Integer type, Long poiId) {
        SearchPoiRequest param = new SearchPoiRequest();
        //param.setSdivisionId(cityId);
        //param.setPoiType(TrpoPoiType.codeOf(request.getPoiType()));
        //if (request.getStartPageNum() > 0) {
        //    param.setOffset((request.getStartPageNum() - 1) * 10);
        //    param.setRows(10);
        //}
        TripPoiResult<List<FliggyPoi>> poiResult = tripPoiReadService.searchPoi(param);
        Ateye.out.print(JSON.toJSONString(poiResult.getData()));
    }

    @AteyeInvoker(description = "根据id查询poiDO")
    public void queryPoiByPoiId(Long id) {
        TrpoPoiDO result = getPoiByPoiId(id);
        Ateye.out.print(JSON.toJSONString(result));
    }

    @AteyeInvoker(description = "根据id查询poiDO老接口")
    public void queryPoiByPoiIdNew(Long id) {
        TripPoiResult<FliggyPoi> result = tripPoiReadService.getPoi(id);
        Ateye.out.print(JSON.toJSONString(result));
    }

    @AteyeInvoker(description = "根据id查询新的poiDO")
    public void queryFliggyPoiByPoiId(Long id) {
        TripPoiResult<FliggyPoi> fliggyPoi = tripPoiReadService.getPoi(id);
        TrpoPoiDO result = transform(fliggyPoi.getData());
        Ateye.out.print(JSON.toJSONString(result));
    }

    @AteyeInvoker(description = "根据poi名称查询poiDOList")
    public void testSearchPoiListByKeyword(String keyword, Long divisionId, Integer rows) {
        TrpoPoiBizSearchByKeywordParam param = new TrpoPoiBizSearchByKeywordParam();
        param.setKeyword(keyword);
        List<TrpoPoiType> poiTypes = Lists.newArrayList();
        poiTypes.add(TrpoPoiType.SCENIC);
        poiTypes.add(TrpoPoiType.SHOPPING_MALL);
        poiTypes.add(TrpoPoiType.RESTAURANT);
        poiTypes.add(TrpoPoiType.PLAY);
        param.setPoiTypes(poiTypes);
        if (divisionId != null) {
            param.setDivisionId(divisionId);
        }
        param.setRows(rows);
        param.setOnline(true);
        List<TrpoPoiDO> poiDOS = searchPoiListByKeyword(param);
        Ateye.out.print(JSON.toJSONString(poiDOS));
    }

    @AteyeInvoker(description = "根据poi名称查询poiDO")
    public void testSearchPoiByKeyword(String keyword, Long divisionId, Integer rows) {
        TrpoPoiBizSearchByKeywordParam param = new TrpoPoiBizSearchByKeywordParam();
        param.setKeyword(keyword);
        List<TrpoPoiType> poiTypes = Lists.newArrayList();
        poiTypes.add(TrpoPoiType.SCENIC);
        poiTypes.add(TrpoPoiType.SHOPPING_MALL);
        poiTypes.add(TrpoPoiType.RESTAURANT);
        poiTypes.add(TrpoPoiType.PLAY);
        param.setPoiTypes(poiTypes);
        if (divisionId != null) {
            param.setDivisionId(divisionId);
        }
        param.setOnline(true);
        param.setRows(rows);
        TrpoPoiDO poiDO = searchPoiByKeyword(param);
        Ateye.out.print(JSON.toJSONString(poiDO));
    }

    @AteyeInvoker(description = "根据热度和距离查询poi周边推荐")
    public void searchPoiMapAround(String type, Double longitude, Double latitude, double radius, Integer rows) {
        TrpoPoiBizMapAroundSearchParam searchParam = new TrpoPoiBizMapAroundSearchParam();
        searchParam.setLongitude(longitude);
        searchParam.setLatitude(latitude);
        searchParam.setRadius(radius);

        List<TrpoPoiType> list = Lists.newArrayList();
        String[] strings = type.split(",");
        for (String ss : strings) {
            TrpoPoiType trpoPoiType = TrpoPoiType.codeOf(Integer.valueOf(ss));
            list.add(trpoPoiType);
        }
        searchParam.setPoiTypes(list);
        searchParam.setRows(rows);

        List<TrpoPoiDO> result = searchPoiMapAround(searchParam);

        Ateye.out.print(JSON.toJSONString(result));
    }

    @AteyeInvoker(description = "测试根据经纬度算距离")
    public void testGetDistance(String lng, String lat, String lng2, String lat2) {
        Double dis = getDistance(lng, lat, lng2, lat2);
        Ateye.out.print(dis);
    }

    @AteyeInvoker(description = "测试查询目的地热门poi")
    public void testSearchPoiByCityId(Long sdivisionId, Integer poiType, Integer offset, Integer rows) {
        TrpoPoiBizSearchParam param = new TrpoPoiBizSearchParam();
        param.setSdivisionId(sdivisionId);
        param.setPoiType(TrpoPoiType.codeOf(poiType));
        param.setOffset(offset);
        param.setRows(rows);
        List<TrpoPoiDO> result = searchPoiByCityId(param);
        Ateye.out.print(JSON.toJSONString(result));
    }

    @AteyeInvoker(description = "测试批量根据poiIds查询源ids")
    public void testGetRawPoiIdByPoiIds(Integer type, String sourceIds) {
        List<Long> poiIds = Lists.newArrayList();
        String[] split = sourceIds.split(",");
        for (String id : split) {
            poiIds.add(NumberUtils.toLong(id));
        }
        Map<Long, List<String>> result = getRawPoiIdByPoiIds(poiIds, TrpoPoiSourceType.codeOf(type));
        Ateye.out.print(JSON.toJSONString(result));
    }

    private String buildTairKey(Long poiId) {

        return "TRIPWJOURPROD_TRIP_POI_" + poiId;
    }

    /**
     * 列表FliggyPoi 批量转换成TrpoPoiDO列表
     *
     * @param pois
     * @return
     */
    private static List<TrpoPoiDO> transformList(List<FliggyPoi> pois) {

        if (CollectionUtils.isEmpty(pois)) {
            return null;
        }
        List<TrpoPoiDO> trpoPoiDOS = Lists.newArrayList();

        for (FliggyPoi fliggyPoi : pois) {
            TrpoPoiDO trpoPoiDO = transform(fliggyPoi);
            if (trpoPoiDO != null) {
                trpoPoiDOS.add(trpoPoiDO);
            }
        }
        return trpoPoiDOS;
    }

    /**
     * 新老poi模型转换
     *
     * @param poi
     * @return
     */
    private static TrpoPoiDO transform(FliggyPoi poi) {
        TrpoPoiDO trpoPoiDO = new TrpoPoiDO();
        try {
            if (poi == null) {
                return null;
            }
            PoiBase poiBase = poi.getPoiBase();
            if (poiBase != null) {
                trpoPoiDO.setId(poiBase.getPoiId());
                trpoPoiDO.setGmtCreate(poiBase.getGmtCreate());
                trpoPoiDO.setGmtModified(poiBase.getGmtModified());
                trpoPoiDO.setName(poiBase.getName());
                trpoPoiDO.setLocation(poiBase.getAddress());
                trpoPoiDO.setLongitude(poiBase.getLongitude());
                trpoPoiDO.setLatitude(poiBase.getLatitude());
                if (poiBase.getFirstCategory() != null) {
                    trpoPoiDO.setTypeEnum(TrpoPoiType.codeOf(poiBase.getFirstCategory().intValue()));
                }
                trpoPoiDO.setMainPic(poiBase.getMainPic());
                //国外
                Integer isAbroad = 1;
                trpoPoiDO.setIsAbroad(isAbroad.equals(poiBase.getIsAbroad()));
                trpoPoiDO.setOnline(poiBase.getOnline());
                trpoPoiDO.setNameEn(poiBase.getNameEn());
                // trpoPoiDO.setNameAlias(poiBase.getNameLocal());
                // poiBase.getAddressEn());
                // poiBase.getAddressLocal());
                trpoPoiDO.setDivisionId(poiBase.getDivisionId());
                trpoPoiDO.setDescription(poiBase.getDescription());
                trpoPoiDO.setTelephone(poiBase.getTelephone());
                transformLatitudeAndLongitude(trpoPoiDO);
            }
            PoiDivision poiDivision = poi.getPoiDivision();
            if (poiDivision != null) {
                trpoPoiDO.setDivisionId(poiDivision.getId());
                // poiDivision.getName();
                // poiDivision.getNameEn();
                // poiDivision.getLevel();
                // poiDivision.getStatus();
                // poiDivision.getLongitude();
                // poiDivision.getLatitude();
                // poiDivision.getTravelDivisionId();
                trpoPoiDO.setDivisionIdTree(poiDivision.getTreeId());
                trpoPoiDO.setDivisionNameTree(poiDivision.getTreeName());
                trpoPoiDO.setContinentId(poiDivision.getContinentId());
                trpoPoiDO.setContinent(poiDivision.getContinentName());
                trpoPoiDO.setCountryId(poiDivision.getCountryId());
                trpoPoiDO.setCountry(poiDivision.getCountryName());
                trpoPoiDO.setProvinceId(poiDivision.getProvId());
                trpoPoiDO.setProvince(poiDivision.getProvName());
                trpoPoiDO.setScityId(poiDivision.getCityId());
                trpoPoiDO.setCityId(poiDivision.getCityId());
                trpoPoiDO.setCity(poiDivision.getCityName());
                trpoPoiDO.setDistrictId(poiDivision.getDistrictId());
                trpoPoiDO.setDistrict(poiDivision.getDistrictName());
            }

            PoiExtend poiExtend = poi.getPoiExtend();
            if (poiExtend != null) {
                poiExtend.getMedia();
                trpoPoiDO.setTitle(poiExtend.getTitle());
                trpoPoiDO.setTags(poiExtend.getTags());
                trpoPoiDO.setCharacteristicTags(poiExtend.getCharacteristicTags());
                if (poiExtend.getPriority() != null) {
                    trpoPoiDO.setPriority(poiExtend.getPriority().intValue());
                }
                trpoPoiDO.setRank(poiExtend.getRank());
                trpoPoiDO.setQualityLevel(poiExtend.getQualityLevel());
                // poiExtend.getCatProperties();
                trpoPoiDO.setTransport(poiExtend.getTransport());
                trpoPoiDO.setWebUrl(poiExtend.getWebUrl());
                trpoPoiDO.setNameAlias(poiExtend.getNameAlias());
                // poiExtend.getAnnouncement();
            }

            List<PoiCatProperty> catProperties = poi.getCatProperties();
            if (CollectionUtils.isEmpty(catProperties)) {
                return trpoPoiDO;
            }

            for (PoiCatProperty property : catProperties) {
                if (property.getPropertyId() == 2 && StringUtils.isNotBlank(property.getPropertyValue())) {
                    trpoPoiDO.setOpenTime(property.getPropertyValue());
                }
                if (property.getPropertyId() == 8 && StringUtils.isNotBlank(property.getPropertyValue())) {
                    trpoPoiDO.setSuggestTravelTime(property.getPropertyValue());
                }
                if (property.getPropertyId() == 5 && StringUtils.isNotBlank(property.getPropertyValue())) {
                    trpoPoiDO.setAvgCost(property.getPropertyValue());
                }

                //酒店属性
                String hotelAttr = StringUtils.EMPTY;
                //停车场信息
                if (property.getPropertyId().equals(270001L)) {
                    String parking = MapUtils.getString(property.getPropertyValueMap(), 3248L);
                    String parking2 = MapUtils.getString(property.getPropertyValueMap(), 3249L);
                    if (StringUtils.isNotBlank(parking)) {
                        hotelAttr += parking + ",";
                    } else if (StringUtils.isNotBlank(parking2)) {
                        hotelAttr += parking2 + ",";
                    }
                }
                //洗衣服务
                if (property.getPropertyId().equals(290003L)) {
                    String laundry = MapUtils.getString(property.getPropertyValueMap(), 3365L);
                    if (StringUtils.isNotBlank(laundry)) {
                        hotelAttr += laundry + ",";
                    }
                }
                if (StringUtils.isNotBlank(hotelAttr)) {
                    String substring = hotelAttr.substring(0, hotelAttr.length() - 1);
                    trpoPoiDO.setCharacteristicTags(substring);
                }
            }
        } catch (Exception e) {
            log.error("PoiReadServiceClient | transform is error! poi={}", JSON.toJSONString(poi), e);
        }
        return trpoPoiDO;
    }

    /**
     * 转换经纬度坐标
     * 对于境外目标，目前给的是谷歌的经纬度，需要转一下才能用
     *
     * @param fliggyPoi poi信息
     */
    private static void transformLatitudeAndLongitude(FliggyPoi fliggyPoi) {
        Integer isAbroad = 1;
        if (Objects.isNull(fliggyPoi) || Objects.isNull(fliggyPoi.getPoiBase())) {
            return;
        }
        if (BooleanUtils.isFalse(Switcher.TRANSFER_LATITUDE_AND_LONGITUDE_FROM_POI) || BooleanUtils.isFalse(isAbroad.equals(fliggyPoi.getPoiBase().getIsAbroad()))) {
            return;
        }
        Double longitude = fliggyPoi.getPoiBase().getLongitude();
        Double latitude = fliggyPoi.getPoiBase().getLatitude();
        if (Objects.isNull(latitude) || Objects.isNull(longitude)) {
            return;
        }
        Map<String, Double> stringDoubleMap = POIUtil.wgs84ToGcj02(longitude, latitude);
        fliggyPoi.getPoiBase().setLongitude(stringDoubleMap.get(POIUtil.LON));
        fliggyPoi.getPoiBase().setLatitude(stringDoubleMap.get(POIUtil.LAT));
    }

    /**
     * 转换经纬度坐标
     * 对于境外目标，目前给的是谷歌的经纬度，需要转一下才能用
     * { JourPlanUtil#CHINA_CITY_IDS} 存了港澳台的常见城市列表
     *
     * @param trpoPoiDO poi信息
     */
    private static void transformLatitudeAndLongitude(TrpoPoiDO trpoPoiDO) {
        if (BooleanUtils.isFalse(Switcher.TRANSFER_LATITUDE_AND_LONGITUDE_FROM_POI) || BooleanUtils.isFalse(trpoPoiDO.getIsAbroad())) {
            return;
        }
        Double longitude = trpoPoiDO.getLongitude();
        Double latitude = trpoPoiDO.getLatitude();
        if (Objects.isNull(latitude) || Objects.isNull(longitude)) {
            return;
        }
        Map<String, Double> stringDoubleMap = POIUtil.wgs84ToGcj02(longitude, latitude);
        trpoPoiDO.setLongitude(stringDoubleMap.get(POIUtil.LON));
        trpoPoiDO.setLatitude(stringDoubleMap.get(POIUtil.LAT));
    }

    /**
     * 判断poi是否包含 机场大屏、进出港信息
     *
     * @param poiId
     * @param dataType
     * @return
     */
    public String queryRealTimeData(Long poiId, Integer dataType) {
        try {
            TripPoiResult<String> queryResult = airportPoiService.realTimeData(poiId, dataType);
            if (queryResult == null || !queryResult.isSuccess()) {
                logger.recordNormalFailedRpc("queryRealTimeData", "airportPoiService", "realTimeData",
                        JSON.toJSONString(poiId), JSON.toJSONString(queryResult));
                return null;
            }
            return queryResult.getData();
        } catch (Exception e) {
            logger.recordDangerException("queryRealTimeData", e, "poiId={}, dataType={}",
                    poiId, dataType);
        }
        return null;
    }

    /**
     * 根据经纬度查询附近的poi
     *
     * @param lon
     * @param lat
     */
    public List<FliggyPoi> queryAroundHotPoi(Double lon, Double lat, List<Integer> types) {
        if (Objects.isNull(lon) || Objects.isNull(lat)) {
            return Lists.newArrayList();
        }
        long t1 = System.currentTimeMillis();
        try {
            SearchPoiParam param = new SearchPoiParam();
            // 按照热度逆序
            Order order = new Order();
            order.setAsc(false);
            order.setProperty(AroundPoiSortEnum.HOT.getValue());
            param.addOrder(order);
            param.setPoiTypes(types);
            param.setOffset(0);
            param.setRows(10);
            param.setLatitude(lat);
            param.setLongitude(lon);

            RequestNewFactory requestFactory = new RequestNewFactory();
            SeachRequest seachRequest = requestFactory.buildSearchPoiRequest(param);

            TripPoiResult<List<FliggyPoi>> tripPoiResult =
                    tripPoiReadService.searchNewPoi((SearchNewPoiRequest) seachRequest);
            if (tripPoiResult == null || !tripPoiResult.isSuccess()) {
                return Lists.newArrayList();
            }
            return tripPoiResult.getData();
        } catch (Exception e) {
            newLogger.recordDangerException(new LogModel("queryAroundHotPoi").e(e)
                    .message("lat={0},lon={1},types={2}", lat, lon, JSON.toJSONString(types)));
        } finally {
            newLogger.recordCost(new LogModel("queryAroundHotPoi").cost(System.currentTimeMillis() - t1));
        }
        return Lists.newArrayList();
    }

    /**
     * 构建请求-某城市下的热门top poi
     *
     * @param cityId
     * @return
     */
    public SearchTripPoiRequest buildSearchRequestByHotPoiList(Long cityId, Integer size) {
        SearchTripPoiRequest request = new SearchTripPoiRequest();
        // 搜索条件
        // 搜索条件组，可以有多个组，类似括号的作用
        List<SearchGroup> queryGroupList = Lists.newArrayList();

        SearchGroup<QueryCondition> queryGroupCondition = new SearchGroup<>();
        // 在线状态为上线的
        QueryCondition onlineStatusCondition = new QueryCondition();
        onlineStatusCondition.setField(new SearchField(PoiFields.ONLINE, Lists.newArrayList(1, 2)));
        onlineStatusCondition.setSearchRelation(SearchRelation.AND);
        // 查平台的数据
        QueryCondition industryCondition = new QueryCondition();
        industryCondition.setField(new SearchField(PoiFields.INDUSTRY, "platform"));
        industryCondition.setSearchRelation(SearchRelation.AND);
        // 类目关键词搜索
        QueryCondition catCondition = new QueryCondition();
        catCondition.setField(new SearchField(PoiFields.FIRST_CATEGORY, PLAN_NEW_FIRST_CATEGORY_BY_HOT_POI));
        catCondition.setSearchRelation(SearchRelation.AND);
        queryGroupCondition.setSearchConditions(Lists.newArrayList(onlineStatusCondition, catCondition, industryCondition));
        queryGroupCondition.setSearchRelation(SearchRelation.AND);
        queryGroupList.add(queryGroupCondition);

        // 可分发或类目为住宿
        SearchGroup<QueryCondition> distributeGroupCondition = new SearchGroup<>();
        QueryCondition distributeStatusCondition = new QueryCondition();
        distributeStatusCondition.setField(new SearchField("distributable", 1));
        distributeStatusCondition.setSearchRelation(SearchRelation.AND);
        QueryCondition cityCondition = new QueryCondition();
        cityCondition.setField(new SearchField(PoiFields.D_TREE_ID, cityId));
        cityCondition.setSearchRelation(SearchRelation.AND);
        distributeGroupCondition.setSearchConditions(Lists.newArrayList(distributeStatusCondition, cityCondition));
        distributeGroupCondition.setSearchRelation(SearchRelation.AND);
        queryGroupList.add(distributeGroupCondition);


        // 排序条件
        List<SortCondition> sortConditionList = Lists.newArrayList();
        // 热度排序
        SortCondition prioritySortCondition = new SortCondition();
        SortField prioritySortField = new SortField();
        prioritySortField.setField(PLAN_NEW_HOT_POI_SORT);

        prioritySortCondition.setField(prioritySortField);
        prioritySortCondition.setSort(SortEnum.DESC);
        // 先按热度排序
        sortConditionList.add(prioritySortCondition);
        // 拼装请求
        request.setQueryGroupList(queryGroupList);
        request.setSorts(sortConditionList);
        if (POI_SEARCH_MERGE) {
            request.setQueryMergeIndex(true);
        }
        request.setQueryMergeData("0");
        request.setPageNo(1);
        request.setPageSize(size);
        return request;
    }

    /**
     * 根据经纬度查询附近的poi
     *
     * @param lon
     * @param lat
     */
    public List<FliggyPoi> queryAroundPoi(String lon, String lat) {
        if (lon == null || lat == null) {
            return null;
        }

        try {
            SearchNewPoiRequest searchPoiRequest = new SearchNewPoiRequest();

            //分页
            searchPoiRequest.setPageSize(10);
            searchPoiRequest.setPageNo(0);

            //设置queryGroupList
            List<SearchGroup> searchGroupList = new ArrayList<>();

            List<SearchField> mainFieldList = new ArrayList<>();
            //上线状态，1-人工上线，2-自动上线
            mainFieldList.add(new SearchField(PoiFields.ONLINE, Lists.newArrayList("1", "2")));
            //搜索附近，经纬度和搜索半径（单位米）
            mainFieldList.add(new SearchField(PoiFields.GEO_POINT, String.format("circle(%s %s,%s)", lon, lat, 1000)));
            //类目 5-景点
            mainFieldList.add(new SearchField(PoiFields.FIRST_CATEGORY, Lists.newArrayList(5)));

            searchGroupList.add(SearchGroupUtil.buildAndQueryGroup(mainFieldList));
            searchPoiRequest.setQueryGroupList(searchGroupList);

            //List<SearchGroup> filterGroupList = new ArrayList<>();
            //List<FilterField> fieldList = new ArrayList<>();
            //
            //CollectionUtils.addIgnoreNull(filterGroupList, SearchGroupUtil.buildAndFilterGroup(fieldList));
            //searchPoiRequest.setFilterGroupList(filterGroupList);

            //设置排序条件
            List<SortCondition> sortList = new ArrayList<>();
            //按照距离升序
            FilterField.Distance distance = new FilterField.Distance(Double.parseDouble(lon), Double.parseDouble(lat));
            SortCondition distanceSortCondition = new SortCondition(distance.getDistanceKey(), SortEnum.ASC);
            sortList.add(distanceSortCondition);
            searchPoiRequest.setSorts(sortList);

            TripPoiResult<List<FliggyPoi>> tripPoiResult = tripPoiReadService.searchNewPoi(searchPoiRequest);
            if (tripPoiResult == null || !tripPoiResult.isSuccess() || tripPoiResult.getData() == null) {
                newLogger.recordDangerException(new LogModel("queryAroundPoi")
                        .message("lat={0},lon={1} resp:{2}", lat, lon, JSON.toJSONString(tripPoiResult)));
                return null;
            }
            return tripPoiResult.getData();
        } catch (Exception e) {
            newLogger.recordDangerException(
                    new LogModel("queryAroundPoi")
                            .e(e)
                            .message("lat={0},lon={1},resp:{2}", lat, lon));
            return null;
        }
    }

    public boolean setDataToCache(String key, Object object) {
        if (object == null) {
            return true;
        }

        if (StringUtils.isBlank(key)) {
            return false;
        }

        boolean succ = false;

        try {
            if (object instanceof String) {
                succ = mdbTairHelper.putValue(key, (Serializable) object, poiCacheTime);
            } else {
                succ = mdbTairHelper.putValue(key, (Serializable) JSON.toJSONString(object), poiCacheTime);
            }
        } catch (Exception e) {
            logger.recordDangerException("setDataToCache", e, key);
        }

        return succ;
    }

    public <T> T queryDataFromCache(String key, Class<T> clazz) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        try {
            String cardJson = (String) mdbTairHelper.getValue(key);
            return JSON.parseObject(cardJson, clazz);
        } catch (Exception e) {
            logger.recordDangerException("queryDataFromCache", e, key);
        }
        return null;
    }

    private String buildRiskPoiTairKey(Long poiId) {
        return "TRIPWJOURPROD_RISK_" + poiId;
    }

    private String buildCityPolicyTairKey(Long cityCode) {
        return "TRIPWJOURPROD_CITY_POLICY_" + cityCode;
    }

    private String getPoiPolicyTairKey(Long poiId) {
        return "TRIPWJOURPROD_POI_POLICY_" + poiId;
    }

    /**
     * poi详情
     *
     * @param poiIdList poi id列表
     */
    @AteyeInvoker(description = "获取poi详情")
    public List<FliggyPoi> poiDetail(List<Long> poiIdList, Integer pageNo, Integer pageSize) {
        try {
            SearchNewPoiRequest searchPoiRequest = new SearchNewPoiRequest();

            //分页
            searchPoiRequest.setPageSize(pageSize);
            searchPoiRequest.setPageNo(pageNo);

            //设置queryGroupList
            List<SearchGroup> searchGroupList = new ArrayList<>();

            List<SearchField> mainFieldList = new ArrayList<>();

            mainFieldList.add(new SearchField(PoiFields.POI_ID, poiIdList));

            searchGroupList.add(SearchGroupUtil.buildAndQueryGroup(mainFieldList));
            searchPoiRequest.setQueryGroupList(searchGroupList);

            TripPoiResult<List<FliggyPoi>> tripPoiResult = tripPoiReadService.searchNewPoi(searchPoiRequest);
            if (tripPoiResult == null || !tripPoiResult.isSuccess() || tripPoiResult.getData() == null) {
                log.error("poiDetail HSF error req:{}", JSON.toJSONString(searchPoiRequest));
                return null;
            }
            return tripPoiResult.getData();
        } catch (Exception e) {
            log.error("poiDetail HSF error poi id list:{}", JSON.toJSONString(poiIdList), e);
            return null;
        }

    }

    private String buildAirportTairKey(Long poiId, List<PoiType> types) {
        List<String> typeName = types.stream()
                .sorted(Comparator.comparing(Enum::name))
                .map(Enum::name)
                .collect(Collectors.toList());
        return "TRIPWJOURPROD_AIRPORT_POI_" + poiId + "_" + Strings.join(typeName, "_");
    }

    @AteyeInvoker(description = "查询机场子poi", paraDesc = "airpiroId&types")
    public void testSearchAirportChildPoi(Long airpiroId, String types) {
        try {
            List<PoiType> poiTypes = Lists.newArrayList();
            if (StringUtils.isBlank(types)) {
                poiTypes.add(PoiType.STAR_BUCK);
                poiTypes.add(PoiType.VIP_ROOM);
            } else {
                for (String type : types.split(",")) {
                    PoiType poiType = PoiType.valueOf(type);
                    CollectionUtils.addIgnoreNull(poiTypes, poiType);
                }
            }
            AirportPoiRequest request = new AirportPoiRequest();
            request.setPoiId(airpiroId);
            request.setPoiTypes(poiTypes);
            TripPoiResult<AirportPoiResponse> queryResult = airportPoiService.searchChildPoi(request);
            Ateye.out.print(queryResult);
        } catch (Exception e) {
            Ateye.out.print(e);
        }
    }

    public String getOpenTime(FliggyPoi poiDO) {
        if (poiDO == null || CollectionUtils.isEmpty(poiDO.getCatProperties())) {
            return "";
        }
        List<PoiCatProperty> catProperties = poiDO.getCatProperties();
        for (PoiCatProperty property : catProperties) {
            if (property.getPropertyId() == 2 && StringUtils.isNotBlank(property.getPropertyValue())) {
                return property.getPropertyValue();
            }
        }
        return "";
    }

}
