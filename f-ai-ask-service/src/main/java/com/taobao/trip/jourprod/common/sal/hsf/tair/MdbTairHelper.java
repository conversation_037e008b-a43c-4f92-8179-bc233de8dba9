package com.taobao.trip.jourprod.common.sal.hsf.tair;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import com.taobao.ateye.util.Ateye;
import com.taobao.tair.DataEntry;
import com.taobao.tair.Result;
import com.taobao.tair.ResultCode;
import com.taobao.tair.TairManager;
import com.taobao.trip.jourprod.utils.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * Created by mojin on 2018/10/16.
 */
@Slf4j
@Service
public class MdbTairHelper {
    @Resource
    private TairManager mdbTairManager;

    @Switch
    private static volatile int NAME_SPACE = 407;


    /**
     * 存放数据到tair
     * @param key
     * @param value
     * @param expireTime
     * @return
     */
    @AteyeInvoker(description = "存放数据到tair", paraDesc = "key&value&expireTime")
    public boolean putValue(String key, Serializable value, int expireTime) {
        try {
            ResultCode resultCode = mdbTairManager.put(NAME_SPACE, key, value, 0, expireTime);
            if (resultCode == null || !resultCode.isSuccess()) {
                log.error("MdbTairHelper put data to tair failed,params：key->{},value->{},expireTime->{},resultCode->{}",
                    key, value, expireTime, JSON.toJSONString(resultCode));
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("MdbTairHelper putValue is error! key->{},value->{},expireTime->{}", key, value, expireTime, e);
        }
        return false;
    }

    /**
     * 根据key读取缓存数据
     * @param key
     * @return
     */
    @AteyeInvoker(description = "查询tair存储数据", paraDesc = "key值")
    public Object getValue(String key) {
        try {
            Result<DataEntry> result = mdbTairManager.get(NAME_SPACE, key);
            if (result == null || !result.isSuccess()) {
                log.error("MdbTairHelper get data from tair failed,params：key->{}, result->{}",
                    key, JSON.toJSONString(result));
                return null;
            }
            if(result.getValue() == null){
                return null;
            }
            return result.getValue().getValue();
        } catch (Exception e) {
            log.error("MdbTairHelper getValue is error! key->{}", key, e);
        }
        return null;
    }

    /**
     * 根据keys读取缓存数据
     * @param tairKeys
     * @return
     */
    @AteyeInvoker(description = "查询tair存储数据", paraDesc = "key值")
    public Result<List<DataEntry>> mgetValue(List<String> tairKeys) {
        Result<List<DataEntry>> result = mdbTairManager.mget(NAME_SPACE, tairKeys);
        if (result == null || !result.isSuccess() || result.getValue() == null) {
            log.error("MdbTairHelper mget data from tair failed,params：tairKeys->{}, result->{}",
                    JSON.toJSONString(tairKeys), JSON.toJSONString(result));
            return null;
        }
        return result;
    }

    /**
     * 根据key失效缓存
     * @param key
     * @return
     */
    @AteyeInvoker(description = "根据key失效缓存", paraDesc = "key值")
    public boolean invalidValue(String key) {
        try {
            ResultCode resultCode = mdbTairManager.invalid(NAME_SPACE, key);
            if (resultCode == null || !resultCode.isSuccess()) {
                log.error("TairService put data to tair failed,params：key->{},resultCode->{}",
                    key, JSON.toJSONString(resultCode));
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("MdbTairHelper invalidValue failed,params：key->{}", key, e);
        }
        return false;
    }

    @AteyeInvoker(description = "测试添加缓存", paraDesc = "key值&value&expireTime")
    public void testPutValue(String key, String value, int expireTime) {
        boolean result = putValue(key, (Serializable) value, expireTime);
        Ateye.out.print(result);
    }

    /**
     * 带范型的缓存结果获取方法
     */
    public <T> T getValue(String key, Class<T> clazz) {
        Object value = getValue(key);
        if (Objects.isNull(value)) {
            return null;
        }
        return JSONUtil.parseObject(value, clazz);
    }

    /**
     * 带范型的缓存结果获取方法
     */
    public <T> T getValue(String key, TypeReference<T> typeReference) {
        Object value = getValue(key);
        if (Objects.isNull(value)) {
            return null;
        }
        return JSONUtil.parseObject(String.valueOf(value), typeReference);
    }

}

