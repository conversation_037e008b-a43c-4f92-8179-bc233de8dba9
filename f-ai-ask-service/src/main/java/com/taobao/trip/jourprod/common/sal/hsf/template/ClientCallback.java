package com.taobao.trip.jourprod.common.sal.hsf.template;

import com.taobao.trip.jourprod.common.lang.model.RuntimeInfo;

/**
 * T : 外部接口返回对象
 * V : 处理过后返回对象
 * Created by <PERSON><PERSON><PERSON> on 15/12/23.
 */
public abstract class ClientCallback<T, V> {

    /**
     * 触发外部接口调用
     * @return
     */
    public abstract T invoke();

    /**
     * 检查外部接口返回结果是否成功
     * @param t
     * @return
     */
    public abstract boolean check(T t);

    /**
     * 获取有用的返回结果
     * @param t
     * @return
     */
    public abstract V returnValue(T t);

    /**
     * 获取方法信息
     * @return
     */
    public abstract RuntimeInfo getRuntimeInfo();

    /**
     * 是否打印debug日志
     * @return
     */
    public boolean debug() {
        return true;
    }
}
