package com.taobao.trip.jourprod.common.sal.hsf.template;

/**
 * 外部调用统一模板
 * 主要做的事情：
 * try...catch
 * log(info, error, time)
 * 判断返回是否成功
 * Created by <PERSON><PERSON><PERSON> on 15/12/23.
 */
public interface ClientTemplate {

    /**
     * @param callback
     * @param <T> 外部接口返回的对象
     * @param <V> 实际需要的对象
     * @return 异常情况返回null
     */
    <T, V> V execute( ClientCallback<T, V> callback );
}
