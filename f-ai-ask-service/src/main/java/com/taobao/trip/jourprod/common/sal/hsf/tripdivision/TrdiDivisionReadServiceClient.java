package com.taobao.trip.jourprod.common.sal.hsf.tripdivision;

import com.alibaba.fastjson.JSON;
import com.alibaba.trip.tripdata.common.domain.page.Page;
import com.alibaba.trip.tripdivision.client.TrdiDivisionMappingService;
import com.alibaba.trip.tripdivision.client.TrdiDivisionQueryService;
import com.alibaba.trip.tripdivision.client.TrdiDivisionReadService;
import com.alibaba.trip.tripdivision.client.domain.TrdiDivisionMtopRequest;
import com.alibaba.trip.tripdivision.client.domain.TrdiDivisionSearchParam;
import com.alibaba.trip.tripdivision.client.mtop.TrdiDivisionMtopService;
import com.alibaba.trip.tripdivision.client.vo.TrdiDivisionExtendVO;
import com.alibaba.trip.tripdivision.client.vo.TrdiDivisionVO;
import com.alibaba.trip.tripdivision.domain.TrdiDivisionDO;
import com.alibaba.trip.tripdivision.domain.TrdiThirdPartyDivisionMappingDO;
import com.alibaba.trip.tripdivision.domain.enumerate.TrdiDivisionLevel;
import com.alibaba.trip.tripdivision.domain.enumerate.TrdiThirdPartyType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.mtop.common.Result;
import com.taobao.trip.jourdprod.core.model.common.ConstantElement;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.CompletableUtils;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.CompletableUtils.CompletableInstance;
import com.taobao.trip.jourprod.common.sal.annotation.RunLog;
import com.taobao.trip.jourprod.common.sal.tair.impl.LdbTairManager;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 新版行政区划服务
 * Created by mojin on 2018/6/5.
 */
@Service
@Slf4j
public class TrdiDivisionReadServiceClient {

    private final static FliggyNewLogger slsLogger = LogUtil.getFliggyNewLogger(TrdiDivisionReadServiceClient.class);

    /**
     * 线程池
     */
    public static final ThreadPoolExecutor batchQueryDivisionExecutor = new ThreadPoolExecutor(20, 20, 0, TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<Runnable>(100),
        new CustomizableThreadFactory("batchQueryDivisionExecutor"));

    @PostConstruct
    public void init() {
        SwitchManager.init(TrdiDivisionReadServiceClient.class);
    }

    @AppSwitch(des = "新行程规划-查询行政区划层级列表", level = Switch.Level.p3)
    public static List<TrdiDivisionLevel> PLAN_NEW_DIDIVISION_LEVEL_LIST = Lists.newArrayList(
            TrdiDivisionLevel.CITY, TrdiDivisionLevel.DISTRICT, TrdiDivisionLevel.TOWN, TrdiDivisionLevel.VILLAGE);

    @AppSwitch(des = "高德搜索中的中国名称", level = Switch.Level.p3)
    public static String GAO_DE_SEARCH_DOMESTIC_NAME = "中国";

    @AppSwitch(des = "并发请求行政区划的超时时间", level = Switch.Level.p4)
    public static int BATCH_QUERY_DIVISION_TIMEOUT_MS = 200;

    @AppSwitch(des = "行政区划的缓存时间", level = Switch.Level.p4)
    public static Integer QUERY_DIVISION_TTL_S = 30 * 60 * 60;

    @Resource
    private TrdiDivisionReadService trdiDivisionReadService;

    @Resource
    private TrdiDivisionMappingService trdiDivisionMappingService;

    @Resource
    private TrdiDivisionQueryService trdiDivisionQueryService;

    @Resource
    private TrdiDivisionMtopService trdiDivisionMtopService;

    @Resource
    private LdbTairManager ldbTairManager;

    private static final String CHINA_COUNTRY_NAME = "中国";
    //查询行政区划缓存key
    private static final String QUERY_DIVISION_CACHE_KEY = "tripOd:queryDivision:";



    /**
     * 判断是否国外，传入对象为空，默认为false
     */
    public static boolean isAbroad(TrdiDivisionExtendVO trdiDivisionDO) {
        if (Objects.isNull(trdiDivisionDO)) {
            return false;
        }

        return !CHINA_COUNTRY_NAME.equals(trdiDivisionDO.getCountryName());
    }

    /**
     * 根据id查询新的行政区划数据
     *
     * @param id
     * @return
     */
    public TrdiDivisionExtendVO getExtendDivisionById(Long id) {
        TrdiDivisionExtendVO divisionDO = null;
        try {
            if (id == null) {
                return null;
            }
            Result<TrdiDivisionExtendVO> divisionExtendById = trdiDivisionReadService.getDivisionExtendById(id);
            if (Objects.nonNull(divisionExtendById) && divisionExtendById.isSuccess() &&
                    Objects.nonNull(divisionExtendById.getModel())) {
                divisionDO =  divisionExtendById.getModel();
            }
        } catch (Exception e) {
            slsLogger.recordDangerException(new LogModel("getDivisionById").e(e).request(String.valueOf(id)));
        }
        return divisionDO;
    }

    /**
     * 根据id查询新的行政区划数据
     *
     * @param id
     * @return
     */
    @AteyeInvoker
    public TrdiDivisionDO getDivisionById(Long id) {
        TrdiDivisionDO divisionDO = null;
        try {
            if (id == null) {
                return null;
            }
            divisionDO = trdiDivisionReadService.getDivision(id);
        } catch (Exception e) {
            slsLogger.recordDangerException(new LogModel("getDivisionById").e(e).request(String.valueOf(id)));
        }
        return divisionDO;
    }


    @AteyeInvoker(description = "根据名称查城市", paraDesc = "cityName")
    public TrdiDivisionDO queryByCityName(String cityName) {

        TrdiDivisionDO divisionDO = null;
        try {
            Result<TrdiDivisionDO> result = trdiDivisionQueryService.queryDomesticDivisionByCityName(cityName);

            if (result == null || result.getModel() == null) {
                return null;
            }
            divisionDO = result.getModel();
        } catch (Exception e) {
            slsLogger.recordDangerException(new LogModel("queryByCityName").e(e).request(cityName));
        }
        Ateye.out.print(JSON.toJSONString(divisionDO));

        return divisionDO;
    }

    /**
     * 根据id获取行政区数据result
     *
     * @param id
     * @return 行政区数据
     */
    public TrdiDivisionExtendVO getDivisionExtendById(Long id) {
        TrdiDivisionExtendVO vo = null;
        try {
            if (id == null) {
                return null;
            }
            Result<TrdiDivisionExtendVO> result = trdiDivisionReadService.getDivisionExtendById(id);
            if (result == null) {
                return null;
            }
            vo = result.getModel();
        } catch (Exception e) {
            slsLogger.recordDangerException(new LogModel("getDivisionExtendById").e(e).request(String.valueOf(id)));
        }
        return vo;
    }


    public List<TrdiDivisionExtendVO> getDivisionExtendByIds(List<Long> ids) {
        List<TrdiDivisionExtendVO> list = Lists.newArrayList();
        try {
            if (CollectionUtils.isEmpty(ids)) {
                return list;
            }
            Result<List<TrdiDivisionExtendVO>> result = trdiDivisionReadService.getDivisionExtendByIds(ids);
            if (result == null) {
                return Lists.newArrayList();
            }
            list = result.getModel();
        } catch (Exception e) {
            slsLogger.recordDangerException(new LogModel("getDivisionExtendByIds").e(e).request(JSON.toJSONString(ids)));
        }

        return list;
    }

    public List<TrdiDivisionDO> queryDivisions(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        try {
            List<TrdiDivisionDO> result = trdiDivisionReadService.getDivisionByIds(ids);
            if (CollectionUtils.isEmpty(result)) {
                return Lists.newArrayList();
            }
            return result;
        } catch (Exception e) {
            slsLogger.recordDangerException(new LogModel("queryDivisions").e(e).request(JSON.toJSONString(ids)));
        }

        return Lists.newArrayList();
    }

    /**
     * 根据老版本行政区id查询行政区数据
     */
    public TrdiDivisionDO queryDivisionByOldDivisionId(Long travelDivisionId) {
        TrdiDivisionDO divisionDO = null;
        try {
            if (travelDivisionId == null) {
                return null;
            }
            Result<TrdiDivisionDO> result = trdiDivisionMappingService.queryByTravelDivisionId(travelDivisionId);
            divisionDO = result.getModel();
        } catch (Exception e) {
            slsLogger.recordDangerException(new LogModel("queryDivisionByOldDivisionId").e(e).request(String.valueOf(travelDivisionId)));
        }
        return divisionDO;
    }

    /**
     * 根据平台 行政区划id 查询对应 数据源的id
     */
    @RunLog(userId = "#userId")
    @AteyeInvoker(description = "根据平台的行政区划id和类型，查询三方的行政区划id", paraDesc = "userId&travelDivisionId&thirdPartyType")
    public TrdiThirdPartyDivisionMappingDO queryDivisionByThirdPartyType(Long userId, Long travelDivisionId, TrdiThirdPartyType thirdPartyType) {
        if (travelDivisionId == null) {
            return null;
        }
        Result<TrdiThirdPartyDivisionMappingDO> result = trdiDivisionMappingService.queryByDivisionId(travelDivisionId, thirdPartyType);
        if (Objects.isNull(result) || Objects.isNull(result.getModel())) {
            return null;
        }
        return result.getModel();
    }

    /**
     * 根据平台 行政区划id 查询对应 数据源的id
     */
    @RunLog(userId = "#userId")
    @AteyeInvoker(description = "根据三方的行政区划id 查询平台的行政区划id", paraDesc = "userId&thirdDivisionId&thirdPartyType")
    public TrdiThirdPartyDivisionMappingDO queryDivisionByThirdDivisionId(Long userId, String thirdDivisionId, TrdiThirdPartyType thirdPartyType) {
        if (StringUtils.isBlank(thirdDivisionId)) {
            return null;
        }
        Result<TrdiThirdPartyDivisionMappingDO> result = trdiDivisionMappingService.queryByThirdPartyId(thirdDivisionId, thirdPartyType);
        if (Objects.isNull(result) || Objects.isNull(result.getModel())) {
            return null;
        }
        return result.getModel();
    }

    /**
     * 根据平台 行政区划id列表 查询对应 数据源的id列表
     */
    @RunLog(userId = "#userId")
    public List<TrdiThirdPartyDivisionMappingDO> queryDivisionListByThirdPartyType(Long userId, List<Long> travelDivisionIdList, TrdiThirdPartyType thirdPartyType) {
        if (CollectionUtils.isEmpty(travelDivisionIdList)) {
            return Lists.newArrayList();
        }
        Result<List<TrdiThirdPartyDivisionMappingDO>> listResult = trdiDivisionMappingService.queryByDivisionIds(travelDivisionIdList, thirdPartyType);
        if (Objects.isNull(listResult) || CollectionUtils.isEmpty(listResult.getModel())) {
            return Lists.newArrayList();
        }
        return listResult.getModel();
    }

    /**
     * 根据老版本行政区id批量查询行政区数据
     */
    public List<TrdiDivisionDO> queryDivisionByOldDivisionIds(List<Long> travelDivisionIds) {
        List<TrdiDivisionDO> list = Lists.newArrayList();
        try {
            if (CollectionUtils.isEmpty(travelDivisionIds)) {
                return list;
            }
            Result<List<TrdiDivisionDO>> result = trdiDivisionMappingService.queryByTravelDivisionIds(travelDivisionIds);
            if (result == null || !result.isSuccess() || result.getModel() == null || CollectionUtils.isEmpty(result.getModel())) {
                return list;
            }
            list = result.getModel();
        } catch (Exception e) {
            slsLogger.recordDangerException(new LogModel("queryDivisionByOldDivisionIds").e(e).request(JSON.toJSONString(travelDivisionIds)));
        }
        return list;
    }

    /**
     * 根据老的或者新的行政区划id查询新的行政区划id
     *
     * @param id
     * @return
     */
    @AteyeInvoker(description = "根据老的或者新的行政区划id查询新的行政区划id", paraDesc = "id")
    public Long getDivisionIdByOldId(Long id) {
        Long divisionId = null;
        try {
            if (id == null) {
                return null;
            }
            TrdiDivisionDO divisionDO = getDivisionById(id);
            if (divisionDO != null) {
                return id;
            }
            TrdiDivisionDO divisionDOOld = queryDivisionByOldDivisionId(id);
            if (divisionDOOld != null) {
                divisionId = divisionDOOld.getId();
            }
        } catch (Exception e) {
            slsLogger.recordDangerException(new LogModel("getDivisionIdByOldId").e(e).request(String.valueOf(id)));
        }
        return divisionId;
    }

    /**
     * 根据cityId获取城市短名
     *
     * @param id
     * @return
     */
    @AteyeInvoker(description = "根据cityId获取城市短名", paraDesc = "id")
    public String getCityNameAbbr(Long id) {
        TrdiDivisionDO division = getDivisionById(id);
        if (division != null) {
            return division.getNameAbbr();
        }
        return null;
    }

    /**
     * 根据老的或者新的行政区划id查询老的行政区划id
     *
     * @param id
     * @return
     */
    @AteyeInvoker(description = "根据老的或者新的行政区划id查询老的行政区划id", paraDesc = "id")
    public Long getOldDivisionIdByNewOrOldId(Long id) {
        Long divisionId = null;
        try {
            if (id == null) {
                return null;
            }
            TrdiDivisionDO divisionDOOld = queryDivisionByOldDivisionId(id);
            if (divisionDOOld != null) {
                return id;
            }
            TrdiDivisionDO divisionDO = getDivisionById(id);
            if (divisionDO != null && NumberUtils.isNumber(divisionDO.getTravelDivisionId())) {
                divisionId = NumberUtils.toLong(divisionDO.getTravelDivisionId());
            }
        } catch (Exception e) {
            slsLogger.recordDangerException(new LogModel("getOldDivisionIdByNewOrOldId").e(e).request(String.valueOf(id)));
        }
        return divisionId;
    }

    /**
     * 根据老的或者新的行政区划ids查询新的行政区划List,key是新的id
     *
     * @param ids
     * @return
     */
    @AteyeInvoker(description = "根据老的或者新的行政区划id查询新的行政区划，key是新的id", paraDesc = "ids")
    public Map<Long, TrdiDivisionDO> getDivisionMapByOldId(List<Long> ids) {
        Map<Long, TrdiDivisionDO> map = Maps.newHashMap();
        try {
            if (CollectionUtils.isEmpty(ids)) {
                return map;
            }
            List<TrdiDivisionDO> divisionDOs = trdiDivisionReadService.getDivisionByIds(ids);
            if (CollectionUtils.isNotEmpty(divisionDOs)) {
                for (TrdiDivisionDO divisionDO : divisionDOs) {
                    if (divisionDO == null) {
                        continue;
                    }
                    map.put(divisionDO.getId(), divisionDO);
                }
            }
            //获取老idList
            List<Long> oldIds = Lists.newArrayList();
            for (Long id : ids) {
                if (!map.containsKey(id)) {
                    oldIds.add(id);
                }
            }
            List<TrdiDivisionDO> divisionDOS = queryDivisionByOldDivisionIds(oldIds);
            if (CollectionUtils.isEmpty(divisionDOS)) {
                return map;
            }
            for (TrdiDivisionDO divisionDO : divisionDOS) {
                map.put(divisionDO.getId(), divisionDO);
            }
        } catch (Exception e) {
            slsLogger.recordDangerException(new LogModel("getDivisionMapByOldId").e(e).request(JSON.toJSONString(ids)));
        }
        return map;
    }

    @AteyeInvoker(description = "根据老的或者新的行政区划id查询新的行政区划，key是原来id", paraDesc = "ids")
    public Map<Long, TrdiDivisionDO> getDivisionMapInfoByOldId(List<Long> ids) {
        Map<Long, TrdiDivisionDO> map = Maps.newHashMap();
        try {
            if (CollectionUtils.isEmpty(ids)) {
                return map;
            }
            List<TrdiDivisionDO> divisionDOs = trdiDivisionReadService.getDivisionByIds(ids);
            if (CollectionUtils.isNotEmpty(divisionDOs)) {
                for (TrdiDivisionDO divisionDO : divisionDOs) {
                    if (divisionDO == null) {
                        continue;
                    }
                    map.put(divisionDO.getId(), divisionDO);
                }
            }
            //获取老idList
            List<Long> oldIds = Lists.newArrayList();
            for (Long id : ids) {
                if (!map.containsKey(id)) {
                    oldIds.add(id);
                }
            }
            for (Long oldId : oldIds) {
                TrdiDivisionDO divisionDO = queryDivisionByOldDivisionId(oldId);
                if (divisionDO != null) {
                    map.put(oldId, divisionDO);
                }
            }
        } catch (Exception e) {
            slsLogger.recordDangerException(new LogModel("getDivisionMapInfoByOldId").e(e).request(JSON.toJSONString(ids)));
        }
        return map;
    }

    /**
     * 根据行政区划DO获取省份id
     *
     * @param divisionDO
     * @return
     */
    public Long getProvinceIdByDivisionDO(TrdiDivisionDO divisionDO) {
        Long result = null;
        if (divisionDO == null) {
            return result;
        }
        String treeIds = divisionDO.getTreeId();
        String[] treeIdArr = null;
        if (StringUtils.isNotEmpty(treeIds)) {
            treeIdArr = treeIds.split(",");
        }
        if (divisionDO != null && ConstantElement.CHINA_COUNTRY_NAME.equals(divisionDO.getCountryName())
                && treeIdArr != null && treeIdArr.length >= 3) {
            String id = treeIdArr[2];
            if (StringUtils.isNotEmpty(id)) {
                result = NumberUtils.toLong(id);
            }
        }
        return result;
    }

    /**
     * 根据行政区划DO获取国家id
     *
     * @param divisionDO
     * @return
     */
    public Long getCountryIdByDivisionDO(TrdiDivisionDO divisionDO) {
        Long result = null;
        if (divisionDO == null) {
            return result;
        }
        String treeIds = divisionDO.getTreeId();
        String[] treeIdArr = null;
        if (StringUtils.isNotEmpty(treeIds)) {
            treeIdArr = treeIds.split(",");
        }
        if (treeIdArr != null && treeIdArr.length > 1) {
            result = NumberUtils.toLong(treeIdArr[1]);
        }
        return result;
    }

    /**
     * 根据经纬度查询对应行政区划信息
     *
     * @param lat
     * @param lng
     * @return
     */
    public TrdiDivisionExtendVO getExtendDivisionInfoByLatLng(Double lat, Double lng) {
        TrdiDivisionDO divisionInfoByLatLng = getDivisionInfoByLatLng(lat, lng);
        if (Objects.isNull(divisionInfoByLatLng)) {
            return null;
        }
        return getExtendDivisionInfoById(divisionInfoByLatLng.getId());
    }

    /**
     * 根据行政区划id度查询对应行政区划信息
     */
    public TrdiDivisionExtendVO getExtendDivisionInfoById(Long divisionId) {
        if (Objects.isNull(divisionId)) {
            return null;
        }
        try {
            Result<TrdiDivisionExtendVO> divisionExtendById = trdiDivisionReadService.getDivisionExtendById(divisionId);
            if (Objects.nonNull(divisionExtendById) && divisionExtendById.isSuccess() &&
                    Objects.nonNull(divisionExtendById.getModel())) {
                return divisionExtendById.getModel();
            }

        } catch (Exception e) {
            slsLogger.recordDangerException(new LogModel("getExtendDivisionInfoById")
                    .e(e)
                    .request(String.valueOf(divisionId)));
        }
        return null;
    }

    /**
     * 根据经纬度查询对应行政区划信息
     *
     * @param lat
     * @param lng
     * @return
     */
    public TrdiDivisionDO getDivisionInfoByLatLng(Double lat, Double lng) {
        try {
            if (lat == null || lng == null) {
                return null;
            }
            List<TrdiDivisionDO> divisionDOS = trdiDivisionReadService.geoInverse(lat, lng);
            if (CollectionUtils.isEmpty(divisionDOS)) {
                slsLogger.recordOutput(new LogModel("getDivisionInfoByLatLng")
                        .message("divisionDOS is empty")
                        .request(lat + "," + lng));
                return null;
            }
            for (int i = divisionDOS.size() - 1; i >= 0; i--) {
                TrdiDivisionDO divisionDO = divisionDOS.get(i);
                if (divisionDO != null && divisionDO.getLevelEnum() != null
                        && divisionDO.getLevelEnum().getCode() == TrdiDivisionLevel.CITY.getCode()) {
                    return divisionDO;
                }
            }
            slsLogger.recordOutput(new LogModel("getDivisionInfoByLatLng")
                    .message("can not find divisionDO frin divisionDOS")
                    .request(lat + "," + lng));
            return null;
        } catch (Exception e) {
            slsLogger.recordDangerException(new LogModel("getDivisionInfoByLatLng")
                    .message("getDivisionInfoByLatLng error")
                    .e(e).request(lat + "," + lng));
        }
        return null;
    }

    public List<TrdiDivisionDO> searchDivisions(String query) {
        try {
            TrdiDivisionSearchParam param = new TrdiDivisionSearchParam();
            param.setKeywords(query);
            param.setOffset(0);
            param.setRows(20);
            param.setLevels(Lists.newArrayList(TrdiDivisionLevel.CONTINENT, TrdiDivisionLevel.COUNTRY, TrdiDivisionLevel.PROVINCE, TrdiDivisionLevel.CITY));
            Page<TrdiDivisionDO> page = trdiDivisionReadService.searchDivision(param);
            if (page == null || page.getData() == null) {
                return Lists.newArrayList();
            }
            return page.getData();
        } catch (Exception e) {
            slsLogger.recordDangerException(new LogModel("searchDivisions").e(e).request(query));

        }
        return Lists.newArrayList();
    }

    /**
     * 获取 国家的首都
     * @param countryId
     * @return
     */
    public List<TrdiDivisionDO> searchDivisionsByCapital(Long countryId) {
        try {
            TrdiDivisionSearchParam param = new TrdiDivisionSearchParam();
            param.setParentId(countryId);
            param.setOffset(0);
            param.setRows(20);
            param.setOnlyCapital(true);
            param.setLevels(Lists.newArrayList(TrdiDivisionLevel.CITY));
            Page<TrdiDivisionDO> page = trdiDivisionReadService.searchDivision(param);
            if (page == null || page.getData() == null) {
                return Lists.newArrayList();
            }
            return page.getData();
        } catch (Exception e) {
            slsLogger.recordDangerException(new LogModel("searchDivisionsByCapital").e(e).request(String.valueOf(countryId)));

        }
        return Lists.newArrayList();
    }

    /**
     * 获取 某父级下的几个
     * @param parentId
     * @return
     */
    public List<TrdiDivisionDO> searchDivisionsByParentId(Long parentId) {
        try {
            TrdiDivisionSearchParam param = new TrdiDivisionSearchParam();
            param.setParentId(parentId);
            param.setOffset(0);
            param.setRows(20);
            param.setLevels(Lists.newArrayList(TrdiDivisionLevel.CITY));
            Page<TrdiDivisionDO> page = trdiDivisionReadService.searchDivision(param);
            if (page == null || page.getData() == null) {
                return Lists.newArrayList();
            }
            return page.getData();
        } catch (Exception e) {
            slsLogger.recordDangerException(new LogModel("searchDivisionsByParentId").e(e).request(String.valueOf(parentId)));

        }
        return Lists.newArrayList();
    }

    public List<TrdiDivisionVO> queryByParentId(Long parentId) {
        try {
            TrdiDivisionMtopRequest request = new TrdiDivisionMtopRequest();
            request.setId(parentId);
            Result<List<TrdiDivisionVO>> result = trdiDivisionMtopService.queryChildDivisionsById(request);
            if (result == null || result.getModel() == null) {
                return Lists.newArrayList();
            }
            return result.getModel();
        } catch (Exception e) {
            slsLogger.recordDangerException(new LogModel("queryByParentId").e(e).request(String.valueOf(parentId)));
        }

        return Lists.newArrayList();
    }

    /**
     * 查询入参行政区划信息
     *
     * @param cityIdList
     * @return
     */
    public Map<Long, TrdiDivisionDO> queryDivisionInfos(List<String> cityIdList) {
        Map<Long, TrdiDivisionDO> divisionMap = Maps.newHashMap();

        if (CollectionUtils.isEmpty(cityIdList)) {
            return divisionMap;
        }
        List<Long> ids = Lists.newArrayList();
        for (String cityId : cityIdList) {
            ids.add(NumberUtils.toLong(cityId));
        }
        return getDivisionMapInfoByOldId(ids);
    }

    /**
     * 根据关键字返回行政区划
     *
     * @param keywords
     * @param isAbroad
     * @return
     */
    public List<TrdiDivisionDO> suggestDivision(String keywords, Boolean isAbroad, List<TrdiDivisionLevel> levels) {
        try {
            TrdiDivisionSearchParam searchParam = new TrdiDivisionSearchParam();
            searchParam.setRows(20);
            searchParam.setOffset(0);
            searchParam.setAbroad(isAbroad);
            searchParam.setKeywords(keywords);
            searchParam.setLevels(levels);

            Page<TrdiDivisionDO> page = trdiDivisionReadService.searchDivision(searchParam);
            if (CollectionUtils.isEmpty(page.getData())) {
                // 无数据返回空
                return Lists.newArrayList();
            }

            return page.getData();
        } catch (Exception e) {
            slsLogger.recordDangerException(new LogModel("suggestDivision").e(e).request(keywords));
        }
        return Lists.newArrayList();
    }

    /**
     * 根据关键字返回行政区划
     *
     * @param userId
     * @param keywords
     * @return
     */
    @RunLog(userId = "#userId")
    public List<TrdiDivisionDO> queryDivisionList(Long userId, String keywords, List<TrdiDivisionLevel> levels) {
        try {
            String key = QUERY_DIVISION_CACHE_KEY + keywords + JSON.toJSONString(levels);
            Object value = ldbTairManager.get(key);
            if (Objects.nonNull(value)) {
                return JSON.parseArray(value.toString(), TrdiDivisionDO.class);
            }

            TrdiDivisionSearchParam searchParam = new TrdiDivisionSearchParam();
            searchParam.setRows(20);
            searchParam.setOffset(0);
            searchParam.setKeywords(keywords);
            searchParam.setLevels(levels);

            Page<TrdiDivisionDO> page = trdiDivisionReadService.searchDivision(searchParam);
            if (CollectionUtils.isEmpty(page.getData())) {
                // 无数据返回空
                return Lists.newArrayList();
            }
            List<TrdiDivisionDO> data = page.getData();
            ldbTairManager.put(key, JSON.toJSONString(data), QUERY_DIVISION_TTL_S);

            return data;
        } catch (Exception e) {
            slsLogger.recordDangerException(new LogModel("suggestDivision").e(e).request(keywords));
        }
        return Lists.newArrayList();
    }

    /**
     * 查询城市三字码
     */
    @RunLog(userId = "#userId")
    public TrdiDivisionDO queryCity(Long userId, String keywords) {
        List<TrdiDivisionDO> trdiDivisionDOS = queryDivisionList(userId, keywords, Lists.newArrayList(TrdiDivisionLevel.CITY));
        return trdiDivisionDOS.stream().findFirst().orElse(null);
    }

    /**
     * 根据关键字返回行政区划
     *
     * @param userId
     * @param keywords
     * @return
     */
    @RunLog(userId = "#userId")
    public List<TrdiDivisionDO> queryDivisionList(Long userId, String keywords) {
        return queryDivisionList(userId, keywords, PLAN_NEW_DIDIVISION_LEVEL_LIST);
    }

    /**
     * 批量查询行政区划
     */
    public List<TrdiDivisionDO> batchQueryDivision(Long userId, List<String> destinationList, List<TrdiDivisionLevel> levels) {
        if (CollectionUtils.isEmpty(destinationList)) {
            return Lists.newArrayList();
        }
        try {
            List<TrdiDivisionDO> divisionDOS = Lists.newCopyOnWriteArrayList();
            CompletableInstance completableInstance = CompletableUtils.getInstance(batchQueryDivisionExecutor, "batchQueryDivisionExecutor");
            for (String destination : destinationList) {
                completableInstance.executeCompletableFuture((Supplier<Void>)() -> {
                    List<TrdiDivisionDO> trdiDivisionDOS = queryDivisionList(userId, destination, levels);
                    if (CollectionUtils.isEmpty(trdiDivisionDOS)) {
                        return null;
                    }
                    divisionDOS.add(trdiDivisionDOS.get(0));
                    return null;
                });
            }
            completableInstance.end(throwable -> {}, BATCH_QUERY_DIVISION_TIMEOUT_MS, TimeUnit.MILLISECONDS);
            return divisionDOS;
        } catch (Exception e) {
            slsLogger.recordDangerException(new LogModel("batchQueryDivision")
                .e(e)
                .request(JSON.toJSONString(destinationList)));
        }
        return Lists.newArrayList();
    }

    @AteyeInvoker(description = "测试根据经纬度查询对应行政区划信息", paraDesc = "lng&lat")
    public void testGetDivisionInfoByLatLng(String lng, String lat) {
        TrdiDivisionDO divisionInfo = getDivisionInfoByLatLng(NumberUtils.toDouble(lat),
                NumberUtils.toDouble(lng));
        Ateye.out.print(JSON.toJSONString(divisionInfo));
    }

    @AteyeInvoker(description = "测试根据老的或者新的行政区划id查询新的行政区划，key是新的id", paraDesc = "cityIds")
    public void testGetDivisionMapByOldId(String cityIds) {
        String[] split = cityIds.split(",");
        List<Long> cityIdList = Lists.newArrayList();
        for (String id : split) {
            cityIdList.add(NumberUtils.toLong(id));
        }
        Map<Long, TrdiDivisionDO> divisionMapByOldId = getDivisionMapByOldId(cityIdList);
        Ateye.out.print(JSON.toJSONString(divisionMapByOldId));
    }

    @AteyeInvoker(description = "根据id查询extendVO", paraDesc = "cityId")
    public String ateyeGetDivisionExtend(Long cityId) {

        return JSON.toJSONString(getDivisionExtendById(cityId));
    }

    @AteyeInvoker(description = "根据关键字查询行政区划", paraDesc = "keywords&isAbroad&levels")
    public String ateyeSuggestDivision(String keywords, Integer isAbroad, String levels) {
        Boolean abroad = (null == isAbroad ? null : (1 == isAbroad));
        List<TrdiDivisionLevel> divisionLevels = Lists.newArrayList();
        if (StringUtils.isBlank(levels)) {
            divisionLevels = Arrays.asList(TrdiDivisionLevel.COUNTRY, TrdiDivisionLevel.PROVINCE, TrdiDivisionLevel.CITY);
        } else {
            String[] arr = levels.split(",");
            for (String level : arr) {
                divisionLevels.add(TrdiDivisionLevel.valueOf(level));
            }
        }
        return JSON.toJSONString(suggestDivision(keywords, abroad, divisionLevels));
    }
}
