package com.taobao.trip.jourprod.common.sal.hsf.userRelated;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.fliggy.crowd.service.domain.profile.LabelQueryDTO;
import com.fliggy.crowd.service.domain.profile.ProfileQueryRequest;
import com.fliggy.crowd.service.domain.profile.ProfileValue;
import com.fliggy.crowd.service.open.api.PicassoCommonService;
import com.fliggy.crowd.service.result.TripCrowdCommonResult;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import com.taobao.trip.jourprod.metaq.MetaQService;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.utils.LogUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 诸葛 用户画像 manager
 *
 * <AUTHOR>
 * @date 2022/4/27 14:54
 */

@Component
@Slf4j
public class UserProfileManager {
    private final static String PICASSO_TAOBAO_USER = "PICASSO_TAOBAO_USER";
    private final static FliggyNewLogger slsLogger = LogUtil.getFliggyNewLogger(UserProfileManager.class);

    @Resource
    private PicassoCommonService picassoCommonService;
    @Resource
    private MetaQService metaQService;

    /**
     * 常住城市属性
     *
     * @param userId 用户id
     * @return { Pair}
     */

    public Map<String, ProfileValue> userProfile(Long userId, List<String> neededUserTags) {
        if (userId == null) {
            return null;
        }

        try {
            ProfileQueryRequest request = new ProfileQueryRequest();
            request.setEntityId(String.valueOf(userId));
            request.setLabelQueryDTOS(new ArrayList<>());

            for (String neededUserTag : neededUserTags) {
                LabelQueryDTO label = new LabelQueryDTO();
                label.setLabelName(neededUserTag);
                request.getLabelQueryDTOS().add(label);
            }

            TripCrowdCommonResult<Map<String, ProfileValue>> profileMapResult = picassoCommonService.queryProfiles(request);
            if (profileMapResult == null || !profileMapResult.isSuccess() || profileMapResult.getData() == null) {
                log.error("");
                return null;
            }
            return profileMapResult.getData();
        } catch (Exception e) {
            return null;
        }
    }

    public void addUserTag(String userId, String tag, String label, String crmId) {
        if (StringUtils.isAnyBlank(userId, tag) || label == null) {
            return;
        }
        try {
            byte[] bytes = buildMsg(userId, tag, label);
            SendResult result = metaQService.send(PICASSO_TAOBAO_USER, "DEFAULT", crmId, bytes);
            log.info("sendMessage:{},status:{},params:{}", crmId, result.getSendStatus(), label);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private byte[] buildMsg(String userId, String tag, String label) {
        JSONObject object = new JSONObject();
        object.put("id", userId);
        object.put("key", tag);
        object.put("val", label);
        object.put("ts", System.currentTimeMillis());
        return JSON.toJSONBytes(object);
    }

    @AteyeInvoker(description = "标签查询", paraDesc = "userId&neededUserTags")
    public Map<String, ProfileValue> userProfile(Long userId, String neededUserTags) {
        Map<String, ProfileValue> stringProfileValueMap = userProfile(userId, JSON.parseArray(neededUserTags, String.class));
        Ateye.out.println(JSON.toJSONString(stringProfileValueMap));
        return stringProfileValueMap;
    }
}
