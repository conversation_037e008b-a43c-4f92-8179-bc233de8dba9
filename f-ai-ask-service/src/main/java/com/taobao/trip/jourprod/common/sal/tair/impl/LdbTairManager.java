package com.taobao.trip.jourprod.common.sal.tair.impl;

import com.alibaba.fastjson.JSON;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.tair.DataEntry;
import com.taobao.tair.Result;
import com.taobao.tair.ResultCode;
import com.taobao.tair.TairManager;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Optional;
import java.util.UUID;

/**
 * 封装一个申请的LDB关于Tair的服务 该Tair 目前只有1GB存储，500QPS 的读写，请慎用
 *
 * <AUTHOR>
 */
@Component
public class LdbTairManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(LdbTairManager.class);

    /**
     * 当前LDB 对应的NameSpace
     */
    private static final int LDB_NAME_SPACE = 2230;

    private static final String SUCCESS = "success";

    @Qualifier("ldbTair")
    @Autowired
    private TairManager tairManager;

    /**
     * 往LDB中写入数据
     *
     * @param key        Tair Key
     * @param value      Tair值
     * @param expireTime 超时时间
     * @return 是否存放成功
     */
    public boolean put(String key, Serializable value, int expireTime) {
        try {
            // tair的put操作，返回的ResultCode包括操作是否成功的信息
            ResultCode result = tairManager.put(LDB_NAME_SPACE, key, value, 0, expireTime);
            if (result == null || !result.isSuccess()) {
                LOGGER.error("LdbTairManager.get failed.key:{}.result:{}", key, JSON.toJSONString(result));
            }
            return result != null && result.isSuccess();
        } catch (Exception e) {
            LOGGER.error("LdbTairManager.put exception.key:{}", key);
            throw e;
        }
    }

    /**
     * 从LDB中读取数据
     *
     * @param key Tair Key
     * @return 在Tair中保存的数据
     */
    @AteyeInvoker(description = "ldb获取数据", paraDesc = "key")
    public Object get(String key) {
        try {
            Result<DataEntry> result = tairManager.get(LDB_NAME_SPACE, key);
            if (result == null || !result.isSuccess()) {
                LOGGER.error("LdbTairManager.get failed.key:{}.result:{}",
                    key, JSON.toJSONString(result));
            }
            return Optional.ofNullable(result)
                .filter(Result::isSuccess)
                .map(Result::getValue)
                .map(DataEntry::getValue)
                .orElse(null);
        } catch (Exception e) {
            LOGGER.error("LdbTairManager.get failed.key:{}", key);
            throw e;
        }
    }

    /**
     * @param key          指定的Tair Key
     * @param value        TairKey对应的Value需要累加的数字
     * @param defaultValue 当TairKey不存在时 默认为该值
     * @param expireTime   该Tair Key的超时时间
     * @return Tair Key累加后的结果
     */
    public int incr(String key, int value, int defaultValue, int expireTime) {
        try {
            Result<Integer> result = tairManager.incr(LDB_NAME_SPACE, key, value, defaultValue, expireTime);
            if (result == null || !result.isSuccess()) {
                LOGGER.error("LdbTairManager.incr failed.key:{} . value:{} . defaultValue:{},expireTime:{},result:{}",
                    key, value, defaultValue, expireTime,
                    JSON.toJSONString(result));
            }
            return Optional.ofNullable(result)
                .filter(Result::isSuccess)
                .map(Result::getValue)
                .orElse(defaultValue);
        } catch (Exception e) {
            LOGGER.error(
                String.format("LdbTairManager.incr exception.key:%s . value:%d . defaultValue:%d,expireTime:%d",
                    key, value, defaultValue, expireTime), e);
            throw e;
        }
    }


    /**
     * @param key          指定的Tair Key
     * @param value        TairKey对应的Value需要累加的数字
     * @param defaultValue 当TairKey不存在时 默认为该值
     * @param expireTime   该Tair Key的超时时间
     * @return Tair Key累加后的结果
     */
    public int decr(String key, int value, int defaultValue, int expireTime) {
        try {
            Result<Integer> result = tairManager.decr(LDB_NAME_SPACE, key, value, defaultValue, expireTime);
            if (result == null || !result.isSuccess()) {
                LOGGER.error("LdbTairManager.decr failed.key:{} . value:{} . defaultValue:{},expireTime:{},result:{}",
                        key, value, defaultValue, expireTime,
                        JSON.toJSONString(result));
            }
            return Optional.ofNullable(result)
                    .filter(Result::isSuccess)
                    .map(Result::getValue)
                    .orElse(defaultValue);
        } catch (Exception e) {
            LOGGER.error(
                    String.format("LdbTairManager.decr exception.key:%s . value:%d . defaultValue:%d,expireTime:%d",
                            key, value, defaultValue, expireTime), e);
            throw e;
        }
    }

    /**
     * 删除指定的TairKey
     *
     * @param key Tair Key
     * @return 是否删除成功
     */
    @AteyeInvoker(description = "ldb删除key", paraDesc = "key")
    public boolean delete(String key) {
        if (StringUtils.isEmpty(key)) {
            LOGGER.error("LdbTairManager.delete key is null.");
            return false;
        }
        try {
            ResultCode result = tairManager.delete(LDB_NAME_SPACE, key);
            if (result == null || !result.isSuccess()) {
                LOGGER.error("LdbTairManager.delete failed.key:{} . result:{}", key, JSON.toJSONString(result));
                return false;
            }
            return result.isSuccess();
        } catch (Exception e) {
            LOGGER.error("LdbTairManager.delete exception.key:{}", key);
            throw e;
        }
    }

    /**
     * 带超时的锁。
     *
     * @param tairKey
     * @param maxLockTime       最大锁定时间，单位：秒
     * @return 是否锁定
     */
    @AteyeInvoker(description = "tair加锁", paraDesc = "tairKey&maxLockTime")
    public boolean lock(String tairKey, int maxLockTime) {
        try {
            // 随便写的，这个数无所谓，只要不是1就行。
            final int initVersion= 5;
            if (tairManager.put(LDB_NAME_SPACE, tairKey, System.currentTimeMillis(), initVersion, maxLockTime).isSuccess()) {
                LOGGER.debug("{} lock success", tairKey);
                return true;
            }
            LOGGER.debug("{} lock fail", tairKey);
            return false;
        } catch (Exception e) {
            LOGGER.error("LdbTairManager lock is error! tairKey->{},maxLockTime->{}", tairKey, maxLockTime, e);
        }
        return true;
    }

    @AteyeInvoker(description = "tair解锁", paraDesc = "key值")
    public boolean unlock(String tairKey) {
        try {
            return tairManager.invalid(LDB_NAME_SPACE, tairKey).isSuccess();
        } catch (Exception e) {
            LOGGER.error("LdbTairManager unlock is error! tairKey->{}", tairKey, e);
        }
        return false;
    }

    /**
     * 尝试获取锁
     * @param tairKey       锁key
     * @param maxWaitTime   尝试次数
     * @param maxLockTime   最大加锁时间
     * @return  是否获取了锁
     */
    public boolean tryLock(String tairKey, int maxWaitTime, int maxLockTime) {
        long start = System.currentTimeMillis();
        while (System.currentTimeMillis() - start < maxWaitTime) {
            if (lock(tairKey, maxLockTime)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 写入数据，并返回数据对应的key
     */
    public String putData(Serializable object, int time) {
        try {
            // 生成key
            UUID uuid = UUID.randomUUID();
            String key = uuid.toString();
            boolean putResult = put(key, object, time);
            if (putResult) {
                return key;
            }
            return StringUtils.EMPTY;
        } catch (Throwable e) {
            return StringUtils.EMPTY;
        }
    }

}
