package com.taobao.trip.jourprod.common.sal.tair.impl;

import com.alibaba.fastjson.JSON;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import com.taobao.ateye.util.Ateye;
import com.taobao.tair.*;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantContentHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;

/**
 * Created by mojin on 2018/8/31.
 */
@Slf4j
@Service
public class MdbTairCommonHelper {
    @Resource
    private TairManager mdbTairManager;

    @Resource
    private TairManager odMdbTairManager;

    @Switch
    private static volatile int TRIPW_MDB_TAIR = 185;

    @Switch
    private static volatile int NAME_SPACE = 407;

    /**
     * 存放数据到tair
     * @param key
     * @param value
     * @param expireTime
     * @return
     */
    @AteyeInvoker(description = "存放数据到tair", paraDesc = "key&value&expireTime")
    public boolean putValue(String key, Serializable value, int expireTime) {
        try {
            ResultCode resultCode = mdbTairManager.put(NAME_SPACE, key, value, 0, expireTime);
            if (resultCode == null || !resultCode.isSuccess()) {
                return false;
            }
            return true;
        }catch (Exception e) {
            log.error("MdbTairCommonHelper putValue is error! key->{},value->{},expireTime->{}", key, value, expireTime, e);
        }
        return false;
    }

    /**
     * 根据key读取缓存数据
     * 从od的旧tair中查询数据
     * @param key
     * @return
     */
    @AteyeInvoker(description = "查询tair存储数据", paraDesc = "key值")
    public Object getValueFromOD(String key) {
        try {
            Result<DataEntry> result = odMdbTairManager.get(TRIPW_MDB_TAIR, key);
            if (result == null || !result.isSuccess()) {
                return null;
            }
            if(result.getValue() == null){
                return null;
            }
            return result.getValue().getValue();
        } catch (Exception e) {
            log.error("MdbTairCommonHelper getValue is error! key->{}", key, e);
        }
        return null;
    }

    /**
     * 根据key读取缓存数据
     * @param key
     * @return
     */
    public Object getValue(String key) {
        try {
            Result<DataEntry> result = mdbTairManager.get(NAME_SPACE, key);
            if (result == null || !result.isSuccess()) {
                return null;
            }
            if(result.getValue() == null){
                return null;
            }
            return result.getValue().getValue();
        } catch (Exception e) {
            log.error("MdbTairCommonHelper getValue is error! key->{}", key, e);
        }
        return null;
    }

    /**
     * 根据keys读取缓存数据
     * @param tairKeys
     * @return
     */
    @AteyeInvoker(description = "查询多个key的tair存储数据", paraDesc = "key值")
    public Result<List<DataEntry>> mgetValue(List<String> tairKeys) {
        try {
            Result<List<DataEntry>> result = mdbTairManager.mget(NAME_SPACE, tairKeys);
            if (result == null || result.getValue() == null) {
                return null;
            }
            return result;
        } catch (Exception e) {
            log.error("MdbTairCommonHelper mgetValue is error! tairKeys->{}", JSON.toJSONString(tairKeys), e);
        }
        return null;
    }

    /**
     * 根据key失效缓存
     * @param key
     * @return
     */
    @AteyeInvoker(description = "根据key失效缓存", paraDesc = "key值")
    public boolean invalidValue(String key) {
        try {
            ResultCode resultCode = mdbTairManager.invalid(NAME_SPACE, key);
            if (resultCode == null || !resultCode.isSuccess()) {
                log.error("TairService put data to tair failed,params：key->{},resultCode->{}",
                    key, JSON.toJSONString(resultCode));
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("MdbTairCommonHelper invalidValue is error! key->{}", key, e);
        }
        return false;
    }

    /**
     * 根据key失效缓存
     * @param keys
     * @return
     */
    @AteyeInvoker(description = "根据key失效缓存", paraDesc = "keys值")
    public boolean minvalidValue(List<String> keys) {
        try {
            ResultCode resultCode = mdbTairManager.minvalid(NAME_SPACE, keys);
            if (resultCode == null || !resultCode.isSuccess()) {
                log.error("TairService minvalidValue data to tair failed,params：keys->{},resultCode->{}",
                        JSON.toJSONString(keys), JSON.toJSONString(resultCode));
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("MdbTairCommonHelper minvalidValue is error! keys->{}", JSON.toJSONString(keys), e);
        }
        return false;
    }

    /**
     * 存放数据到tair
     * @param pkey
     * @param skey
     * @param value
     * @param expireTime
     * @return
     */
    @AteyeInvoker(description = "存放数据到tair", paraDesc = "pkey&skey&value&expireTime")
    public boolean prefixPutValue(String pkey, String skey, Serializable value, int expireTime) {
        try {
            ResultCode resultCode = mdbTairManager.prefixPut(NAME_SPACE, pkey, skey, value, 0, expireTime);
            if (resultCode == null || !resultCode.isSuccess()) {
                return false;
            }
            return true;
        }catch (Exception e) {
            log.error("MdbTairCommonHelper prefixPutValue is error! pkey->{},skey->{},value->{},expireTime->{}", pkey, skey, value, expireTime, e);
        }
        return false;
    }

    /**
     * 根据前缀读取缓存数据
     * @param pkey
     * @param skey
     * @return
     */
    @AteyeInvoker(description = "根据前缀读取缓存数据", paraDesc = "pkey&skey")
    public Result<DataEntry> prefixGetValue(String pkey, String skey) {
        try {
            Result<DataEntry> result = mdbTairManager.prefixGet(NAME_SPACE, pkey, skey);
            if (result == null || !result.isSuccess() || result.getValue() == null) {
                return null;
            }
            return result;
        } catch (Exception e) {
            log.error("MdbTairCommonHelper prefixGetValue is error! pkey->{},skey->{}", pkey, skey, e);
        }
        return null;
    }

    /**
     * 根据前缀读取缓存数据
     * @param pkey
     * @param skeys
     * @return
     */
    @AteyeInvoker(description = "根据前缀读取缓存数据", paraDesc = "pkey&skeys")
    public Result<Map<Object, Result<DataEntry>>> prefixGetsValue(String pkey, List<String> skeys) {
        try {
            Result<Map<Object, Result<DataEntry>>> result = mdbTairManager.prefixGets(NAME_SPACE, pkey, skeys);
            if (result == null || !result.isSuccess() || result.getValue() == null) {
                return null;
            }
            return result;
        } catch (Exception e) {
            log.error("MdbTairCommonHelper prefixGetsValue is error! pkey->{},skeys->{}", pkey, JSON.toJSONString(skeys), e);
        }
        return null;
    }

    /**
     * 根据前缀失效缓存
     * @param pkey
     * @param skey
     * @return
     */
    @AteyeInvoker(description = "根据前缀失效缓存", paraDesc = "pkey&skey")
    public boolean prefixInvalidValue(String pkey, String skey) {
        try {
            ResultCode resultCode = mdbTairManager.prefixInvalid(NAME_SPACE, pkey, skey, CallMode.SYNC);
            if (resultCode == null || !resultCode.isSuccess()) {
                log.error("TairService prefixInvalid data to tair failed,params：pkey->{},skey->{},resultCode->{}",
                        pkey, skey, JSON.toJSONString(resultCode));
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("MdbTairCommonHelper prefixInvalid is error! pkey->{},skey->{}", pkey, skey, e);
        }
        return false;
    }

    /**
     * 尝试获取锁
     * @param tairKey       锁key
     * @param maxWaitTime   尝试次数
     * @param maxLockTime   最大加锁时间
     * @return  是否获取了锁
     */
    public boolean tryLock(String tairKey, int maxWaitTime, int maxLockTime) {
        long start = System.currentTimeMillis();
        while (System.currentTimeMillis() - start < maxWaitTime) {
            if (lock(tairKey, maxLockTime)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 带超时的锁。
     *
     * @param tairKey
     * @param maxLockTime       最大锁定时间，单位：秒
     * @return 是否锁定
     */
    @AteyeInvoker(description = "tair加锁", paraDesc = "tairKey&maxLockTime")
    public boolean lock(String tairKey, int maxLockTime) {
        try {
            // 随便写的，这个数无所谓，只要不是1就行。
            final int initVersion= 5;
            if (mdbTairManager.put(NAME_SPACE, tairKey, System.currentTimeMillis(), initVersion, maxLockTime).isSuccess()) {
                log.debug("{} lock success", tairKey);
                return true;
            }
            log.debug("{} lock fail", tairKey);
            return false;
        } catch (Exception e) {
            log.error("MdbTairCommonHelper lock is error! tairKey->{},maxLockTime->{}", tairKey, maxLockTime, e);
        }
        return true;
    }

    @AteyeInvoker(description = "tair解锁", paraDesc = "key值")
    public boolean unlock(String tairKey) {
        try {
            return mdbTairManager.invalid(NAME_SPACE, tairKey).isSuccess();
        } catch (Exception e) {
            log.error("MdbTairCommonHelper unlock is error! tairKey->{}", tairKey, e);
        }
        return false;
    }

    @AteyeInvoker(description = "测试添加缓存", paraDesc = "key值&value&expireTime")
    public void testPutValue(String key, String value, int expireTime) {
        boolean result = putValue(key, (Serializable) value, expireTime);
        Ateye.out.print(result);
    }

    @AteyeInvoker(description = "查询mdb缓存", paraDesc = "key值")
    public void getTairValue(String key) {
        Object result = getValue(key);
        Ateye.out.print(Objects.isNull(result) ? "null" : result.toString());
    }

    @AteyeInvoker(description = "测试查询多个key的tair存储数据", paraDesc = "key值&value&expireTime")
    public void testMgetValue(String keys) {
        String[] split = keys.split(",");
        ArrayList<String> keyList = new ArrayList<String>(Arrays.asList(split));
        Ateye.out.print(mgetValue(keyList));
    }

    @AteyeInvoker(description = "测试前缀添加缓存", paraDesc = "pkey值&skey&value&expireTime")
    public void testPrefixPutValue(String pkey, String skey, String value, int expireTime) {
        boolean result = prefixPutValue(pkey, skey, (Serializable) value, expireTime);
        Ateye.out.print(result);
    }

    @AteyeInvoker(description = "测试前缀查询多个key的tair存储数据", paraDesc = "pkey&skeys")
    public void testPrefixGetsValue(String pkey, String skeys) {
        String[] split = skeys.split(",");
        ArrayList<String> skeyList = new ArrayList<String>(Arrays.asList(split));
        Ateye.out.print(prefixGetsValue(pkey, skeyList));
    }

    @AteyeInvoker(description = "测试前缀失效缓存", paraDesc = "pkey值&skey")
    public void testPrefixInvalidValue(String pkey, String skey) {
        boolean result = prefixInvalidValue(pkey, skey);
        Ateye.out.print(result);
    }


    /**
     * @param key          指定的Tair Key
     * @param value        TairKey对应的Value需要累加的数字
     * @param defaultValue 当TairKey不存在时 默认为该值
     * @param expireTime   该Tair Key的超时时间
     * @return Tair Key累加后的结果
     */
    public int increment(String key, int value, int defaultValue, int expireTime) {
        try {
            Result<Integer> result = mdbTairManager.incr(NAME_SPACE, key, value, defaultValue, expireTime);
            if (result == null || !result.isSuccess()) {
                log.error("mdbTairManager.incr failed.key:{} . value:{} . defaultValue:{},expireTime:{},result:{}",
                        key, value, defaultValue, expireTime,
                        JSON.toJSONString(result));
            }
            return Optional.ofNullable(result)
                    .filter(Result::isSuccess)
                    .map(Result::getValue)
                    .orElse(defaultValue);
        } catch (Exception e) {
            log.error(
                    String.format("mdbTairManager.incr exception.key:%s . value:%d . defaultValue:%d,expireTime:%d",
                            key, value, defaultValue, expireTime), e);
            throw e;
        }
    }

    @AteyeInvoker(description = "根据sysMessageId批量查询缓存数据", paraDesc = "sysMessageId")
    public String testBatchGetMessage(String sysMessageId) {
        StringBuilder log = new StringBuilder(1024);
        for (int i = 0; i < 5000; i++) {
            String key = JourneyPlanAiAssistantContentHelper.CONTENT_MAP_KEY + sysMessageId + i;
            Object obj = this.getValue(key);
            if (null == obj) {
                break;
            }
            log.append("第" + i + "次：" + obj + "\n");
        }
        return log.toString();
    }

}
