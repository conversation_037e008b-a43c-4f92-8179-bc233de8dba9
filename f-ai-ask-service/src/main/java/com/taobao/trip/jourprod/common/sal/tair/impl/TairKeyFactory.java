package com.taobao.trip.jourprod.common.sal.tair.impl;

/**
 * 缓存key工厂
 */
public class TairKeyFactory {

    /**
     * 获取用户同时进行对话的key
     * @param userId
     * @return
     */
    public static String getUserChatInProgressKey(Long userId) {
        return "chat_in_progress:" + userId;
    }

    /**
     * 获取消息被暂停的key
     * @param systemMessageId 系统消息ID
     * @return
     */
    public static String getSysMessageIdKey(String systemMessageId) {
        return "ai:journey:stop:messageId:" + systemMessageId;
    }
}
