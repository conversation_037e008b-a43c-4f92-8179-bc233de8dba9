package com.taobao.trip.jourprod.core.service.converter;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.taobao.tair.DataEntry;
import com.taobao.tair.Result;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.jourprod.common.sal.hsf.tair.MdbTairHelper;
import com.taobao.trip.jourprod.service.converter.AiJourneyHistoryConvert;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantContentHelper.CONTENT_MAP_KEY;

/**
 * AI对话的转换器
 */
@Component
public class AiJourneyChatConvert {

    @Resource
    private MdbTairHelper mdbTairHelper;

    public StreamMessageResult getAlreadyOutputStreamMessageResult(String systemMessageId, int maxSeq) {
        // 拼接结果
        StreamMessageResult mergedResult = null;
        // 批量查询，每100个一批
        int batchSize = 100;
        for (int i = 0; i < maxSeq; i += batchSize) {
            int endIndex = Math.min(i + batchSize, maxSeq);
            // 构建批量查询的key列表
            List<String> keys = new ArrayList<>(endIndex - i);
            for (int j = i; j < endIndex; j++) {
                keys.add(CONTENT_MAP_KEY + systemMessageId +  j);
            }

            // 使用mgetValue批量查询
            Result<List<DataEntry>> result = mdbTairHelper.mgetValue(keys);
            if (result == null || !result.isSuccess() || CollectionUtils.isEmpty(result.getValue())) {
                return null;
            }
            Map<Object, Object> cacheMap = result.getValue().stream().filter(Objects::nonNull).collect(Collectors.toMap(DataEntry::getKey, DataEntry::getValue));
            for (String key : keys) {
                Object value = cacheMap.get(key);
                if (value == null) {
                    continue;
                }
                StreamMessageResult streamResult = JSONUtil.parseObject(String.valueOf(value), StreamMessageResult.class);
                if (streamResult != null) {
                    // 合并结果
                    mergedResult = AiJourneyChatConvert.mergeStreamMessageResult(mergedResult, streamResult);
                }
            }
        }
        return mergedResult;
    }

    /**
     * 合并StreamMessageResult结果
     *
     * @param mergedResult 已合并的结果，如果为null则会创建一个新的
     * @param newResult 新的结果
     * @return 合并后的结果
     */
    public static StreamMessageResult mergeStreamMessageResult(StreamMessageResult mergedResult, StreamMessageResult newResult) {
        if (Objects.isNull(mergedResult) && Objects.nonNull(newResult)) {
            // 第一次初始化结果
            mergedResult = JSON.parseObject(JSON.toJSONString(newResult), StreamMessageResult.class);
            List<Map<String, Object>> thinkingThoughtList = Lists.newArrayList();
            if (Objects.nonNull(newResult.getThinkingThought())) {
                thinkingThoughtList.add(newResult.getThinkingThought());
            }
            mergedResult.setThinkingThought(null);
            mergedResult.setThinkingThoughtList(thinkingThoughtList);
        } else {
            // 合并结果
            // 字符串直接append
            mergedResult.setMessage(mergedResult.getMessage() + newResult.getMessage());
            // 卡片列表直接add
            if (CollectionUtils.isNotEmpty(newResult.getComponents())) {
                if (mergedResult.getComponents() == null) {
                    mergedResult.setComponents(Lists.newArrayList());
                }
                mergedResult.getComponents().addAll(newResult.getComponents());
            }
            mergedResult.setSystemMessageId(newResult.getSystemMessageId());
            mergedResult.setUserMessageId(newResult.getUserMessageId());
            // 状态以最后一个为准
            mergedResult.setStatus(newResult.getStatus());
            // 思维链需要合并内部List结构
            if (MapUtils.isNotEmpty(newResult.getThinkingThought())) {
                mergedResult.getThinkingThoughtList().add(newResult.getThinkingThought());
            }
        }
        // 按照历史消息返回的逻辑，处理思维链数据
        if (CollectionUtils.isNotEmpty(mergedResult.getThinkingThoughtList())) {
            List<Map<String, Object>> afterMergeThinkingThought = AiJourneyHistoryConvert.mergeCardsToLastElement(mergedResult.getThinkingThoughtList());
            mergedResult.setThinkingThoughtList(afterMergeThinkingThought);
        }
        return mergedResult;
    }

}
