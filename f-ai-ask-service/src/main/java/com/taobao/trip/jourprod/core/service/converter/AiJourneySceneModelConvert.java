package com.taobao.trip.jourprod.core.service.converter;

import com.alibaba.fastjson.JSON;
import com.alibaba.mtop3.invocation.MtopStream;
import com.google.common.collect.Lists;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.diamond.CheapFlightKeyBoardTagValueConfigManager;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.diamond.FastReturnMappingConfig;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.diamond.model.DestKeyBoardTagValueConfigManager;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantInitHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.*;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageCardActionEnum;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageCardStatusEnum;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.CapsuleVO;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatRequest;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanAiInitResult;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanMessageStatusEnum.NORMAL;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum.PR_TERRIER;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantHelper.AI_JOURNEY_SCENE_NAME_2_CODE_MAP;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch.*;

@Component
public class AiJourneySceneModelConvert {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(AiJourneySceneModelConvert.class);

    @Resource
    private JourneyPlanAiAssistantInitHelper journeyPlanAiAssistantInitHelper;
    @Resource
    private FastReturnMappingConfig fastReturnMappingConfig;
    @Resource
    private CheapFlightKeyBoardTagValueConfigManager cheapFlightKeyBoardTagValueConfigManager;
    @Resource
    private DestKeyBoardTagValueConfigManager destKeyBoardTagValueConfigManager;

    /**
     * 把智能体返回的场景转化成具体的枚举
     */
    public static AiJourneyPlanSceneEnum extractAiJourneyScene(AiJourneySceneModel journeySceneModel) {
        if (Objects.nonNull(journeySceneModel.getSceneEnum())) {
            return journeySceneModel.getSceneEnum();
        }
        if (StringUtils.isAllBlank(journeySceneModel.getType())) {
            return null;
        }
        AiJourneyPlanSceneEnum sceneEnum = AI_JOURNEY_SCENE_NAME_2_CODE_MAP.entrySet().stream()
                .filter(entry -> journeySceneModel.getType().contains(entry.getKey()))
                .map(t -> AiJourneyPlanSceneEnum.getByCode(t.getValue()))
                .findFirst().orElse(null);
        journeySceneModel.setSceneEnum(sceneEnum);
        return sceneEnum;
    }
    /**
     * 判断是否需要直接返回提示文案，不走推荐
     */
    public String fastReturnWithoutRecommend(ChatContext chatContext, MtopStream mtopStream) {
        AiJourneySceneModel sceneModel = chatContext.getAiJourneySceneModel();
        JourneyPlanAiChatRequest request = chatContext.getRequest();
        String fastReturnContent = null;

        AiJourneyPlanSceneEnum sceneEnum = Optional.ofNullable(sceneModel).map(AiJourneySceneModel::getSceneEnum).orElse(AiJourneyPlanSceneEnum.NOT_SUPPORT_SCENE);
        //如果是快速返回，收到填充键盘标签
        List<KeyboardTagDTO> keyboardTags = Lists.newArrayList();
        List<KeyboardTagDTOV2> keyboardTagsV2 = Lists.newArrayList();
        List<KeyboardTagValueDTOV2> inspirationsV2 = Lists.newArrayList();
        switch (sceneEnum) {
            case READY_TO_PLAN:
                if (BooleanUtils.isTrue(sceneModel.getLackDestination()) && StringUtils.isBlank(sceneModel.getDestination())) {
                    List<String> capsuleVOS = this.queryRecommendDestinations(chatContext);
                    if (CollectionUtils.isNotEmpty(capsuleVOS)) {
                        keyboardTags.add(KeyboardTagDTO.builder().title(AI_KEYBOARD_TAGS_TITLE_DEST).keys(capsuleVOS).build());
                    }
                    if (BooleanUtils.isTrue(sceneModel.getLackDay())) {
                        keyboardTags.add(KeyboardTagDTO.builder().title(AI_KEYBOARD_TAGS_TITLE_DAYS).keys(AI_KEYBOARD_TAGS_TITLE_DAYS_LIST).build());
                    }
                    fastReturnContent = writeNewCapsuleData(chatContext, mtopStream, sceneModel);
                }
                break;
            case HOTEL_BOOKING:
                if (BooleanUtils.isTrue(sceneModel.getLackDestination())) {
                    List<String> capsuleVOS = this.queryRecommendDestinations(chatContext);
                    if (CollectionUtils.isNotEmpty(capsuleVOS) && BooleanUtils.isTrue(sceneModel.getLackDestination())) {
                        keyboardTags.add(KeyboardTagDTO.builder().title(AI_KEYBOARD_TAGS_TITLE_DEST).keys(capsuleVOS).build());
                    }
                    if (BooleanUtils.isTrue(sceneModel.getLackOutType())) {
                        keyboardTags.add(KeyboardTagDTO.builder().title(AI_KEYBOARD_TAGS_TITLE_OUT_TYPE).keys(AI_KEYBOARD_TAGS_TITLE_OUT_TYPE_LIST).build());
                    }
                    fastReturnContent = writeNewCapsuleData(chatContext, mtopStream, sceneModel);
                }
                break;
            case TRAFFIC:
                //如果 是特价机票场景，且chat=AI_CHEAP_FLIGHT_INSPIRATIONS_V2_TEXT，不走 快出模块
                if (BooleanUtils.isTrue(sceneModel.getCheapFlightScene()) && !StringUtils.equals(chatContext.getRequest().getChat(), AI_CHEAP_FLIGHT_INSPIRATIONS_V2_TEXT_CLICK)) {
                    List<String> capsuleVOS = this.queryRecommendDestinations(chatContext);
                    if (CollectionUtils.isNotEmpty(capsuleVOS)) {
                        List<KeyboardTagValueDTOV2> tagList = capsuleVOS.stream()
                                .map(name -> KeyboardTagValueDTOV2.builder().name(name).clickText(name).build())
                                .collect(Collectors.toList());
                        keyboardTagsV2.add(KeyboardTagDTOV2.builder().title(AI_KEYBOARD_TAGS_TITLE_DEST).keys(tagList).build());
                    }
                    //获取出行时间配置
                    List<KeyboardTagValueDTOV2> outTimeList = cheapFlightKeyBoardTagValueConfigManager.getCheapFlightOutTimeKeyboardTagValueDTOV2List();
                    if (CollectionUtils.isNotEmpty(outTimeList)) {
                        keyboardTagsV2.add(KeyboardTagDTOV2.builder()
                                .title(AI_KEYBOARD_TAGS_TITLE_OUT_TIME)
                                .keys(outTimeList)
                                .build());
                    }
                    //灵感
                    inspirationsV2 = JSON.parseArray(AI_CHEAP_FLIGHT_INSPIRATIONS_V2_LIST, KeyboardTagValueDTOV2.class);
                    fastReturnContent = writeNewCapsuleData(chatContext, mtopStream, sceneModel);
                }
                break;
            case DESTINATION_RECOMMEND:
                if (BooleanUtils.isTrue(sceneModel.getNeedClarify())) {
                    List<KeyboardTagValueDTOV2> outPeopleList = destKeyBoardTagValueConfigManager.getOutPeopleKeyboardTagValueDTOV2List();
                    if (CollectionUtils.isNotEmpty(outPeopleList)) {
                        keyboardTagsV2.add(KeyboardTagDTOV2.builder().title(AI_KEYBOARD_TAGS_TITLE_OUT_PEOPLE).keys(outPeopleList).build());
                    }
                    List<KeyboardTagValueDTOV2> destRangeList = destKeyBoardTagValueConfigManager.getDestRangeKeyboardTagValueDTOV2List();
                    if (CollectionUtils.isNotEmpty(destRangeList)) {
                        keyboardTagsV2.add(KeyboardTagDTOV2.builder().title(AI_KEYBOARD_TAGS_TITLE_DEST_RANGE).keys(destRangeList).build());
                    }
                    fastReturnContent = writeNewCapsuleData(chatContext, mtopStream, sceneModel);
                }
                break;
            case PR_TERRIER:
                FastReturnMappingConfig.Data data = fastReturnMappingConfig.getData(PR_TERRIER.getSceneCode(), request.getChat());
                fastReturnContent = writeCapsuleData(chatContext, data, mtopStream, null);

            default:
                break;
        }
        if (CollectionUtils.isNotEmpty(inspirationsV2)) {
            chatContext.setInspirationsV2(inspirationsV2);
        }
        if (CollectionUtils.isNotEmpty(keyboardTags)) {
            chatContext.setKeyboardTags(keyboardTags);
        }
        if (CollectionUtils.isNotEmpty(keyboardTagsV2)) {
            chatContext.setKeyboardTagsV2(keyboardTagsV2);
        }
        //判断是否是澄清
        if (CollectionUtils.isNotEmpty(keyboardTags) || CollectionUtils.isNotEmpty(keyboardTagsV2)) {
            chatContext.setIsClearQuery(true);
        }
        return fastReturnContent;
    }

    private String writeCapsuleData(ChatContext chatContext, FastReturnMappingConfig.Data data, MtopStream mtopStream, List<CapsuleVO> capsuleVOS) {
        if (Objects.isNull(mtopStream) || Objects.isNull(data)) {
            return null;
        }

        String systemMessageId = chatContext.getSystemMessageId();
        String userMessageId = chatContext.getUserMessageId();

        // 组装胶囊数据，优先使用传入的capsuleVOS，否则从配置里取，如都没有则不返回
        List<CapsuleVO> capsuleList = null;
        if (CollectionUtils.isNotEmpty(capsuleVOS)) {
            capsuleList = capsuleVOS;
        } else {
            capsuleList = data.getCapsuleList();
        }
        if (CollectionUtils.isNotEmpty(capsuleList)) {
            StreamMessageResult.StreamMessageCardModel cardModel = new StreamMessageResult.StreamMessageCardModel();
            cardModel.setId("0");
            cardModel.setItemType("reverse_question_component");
            cardModel.setAction(AiJourneyMessageCardActionEnum.REPLACE.getCode());
            cardModel.setStatus(AiJourneyMessageCardStatusEnum.FINISH.getCode());
            cardModel.setData(capsuleList);
            mtopStream.write(JSON.toJSONString(new StreamMessageResult(systemMessageId, userMessageId, Lists.newArrayList(cardModel), NORMAL.getStatusCode())));

            // 存入上下文，保证后续历史消息能存入
            List<StreamMessageResult.StreamMessageCardModel> streamMessageCardModels = Lists.newArrayList(cardModel);
            chatContext.setComponents(streamMessageCardModels);
        }

        String fastReturnContent = data.getText();
        if (StringUtils.isNotBlank(fastReturnContent)) {
            mtopStream.write(JSON.toJSONString(new StreamMessageResult(systemMessageId, userMessageId, null, fastReturnContent, NORMAL.getStatusCode())));
        }

        return fastReturnContent;
    }

    private String writeNewCapsuleData(ChatContext chatContext, MtopStream mtopStream, AiJourneySceneModel sceneModel) {
        if (Objects.isNull(mtopStream)) {
            return null;
        }

        String systemMessageId = chatContext.getSystemMessageId();
        String userMessageId = chatContext.getUserMessageId();

        String fastReturnContent = sceneModel.getGuide();
        if (StringUtils.isNotBlank(fastReturnContent)) {
            mtopStream.write(JSON.toJSONString(new StreamMessageResult(systemMessageId, userMessageId, null, fastReturnContent, NORMAL.getStatusCode())));
        }

        return fastReturnContent;
    }


    /**
     * 填充推荐目的地胶囊
     */
    protected List<String> queryRecommendDestinations(ChatContext chatContext) {
        try {
            List<JourneyPlanAiInitResult.RecommendCard> destinations = journeyPlanAiAssistantInitHelper.getRecommendCard(chatContext.getRequest().getUserId(), null, null);
            if (CollectionUtils.isNotEmpty(destinations)) {
                return destinations.stream()
                        .map(JourneyPlanAiInitResult.RecommendCard::getDestinationName)
                        .collect(Collectors.toList());
            }
        } catch (Exception ex) {
            LOGGER.recordNormalException(new LogModel("queryRecommendDestinations").message("推荐目的地城市报错").e(ex));
        }
        return null;
    }

}
