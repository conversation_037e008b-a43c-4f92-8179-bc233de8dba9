package com.taobao.trip.jourprod.core.service.converter;

import com.taobao.trip.jourprod.service.facade.model.request.train.TrainListQueryRequest;
import com.taobao.trip.tripjourneyop.common.util.AppNameUtils;
import com.taobao.trip.wireless.train.domain.search.param.ListSearchMtopParam;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 火车票列表查询参数转换
 *
 * <AUTHOR>
 * @time 2023/12/1 14:07
 */
@Component("trainListQueryConverter")
public class TrainListQueryConverter implements Converter<TrainListQueryRequest, ListSearchMtopParam> {

    // 出发/到达类型-城市
    // 0:火车，1:汽车，2:城市，3:区县
    public final static Integer DEP_ARR_TYPE_CITY = 2;

    // 出发/到达类型-火车
    // 0:火车，1:汽车，2:城市，3:区县
    public final static Integer DEP_ARR_TYPE_STATION = 0;

    // 搜索类型: 1:只查直达 2:只查中转 4:只查跨站 7:全查询
    public final static Integer SEARCH_TYPE_NO_STOP = 1;

    /**
     * @param trainListQueryRequest 原类型
     * @param map                   额外参数
     * @return T 目标结果
     */
    @Override
    public ListSearchMtopParam convert(TrainListQueryRequest trainListQueryRequest, Map map) {
        if (Objects.isNull(trainListQueryRequest) || StringUtils.isBlank(trainListQueryRequest.getDepDate())) {
            return null;
        }

        ListSearchMtopParam listSearchMtopParam = new ListSearchMtopParam();
        listSearchMtopParam.setUserId(trainListQueryRequest.getUserId());
        listSearchMtopParam.setTtid(trainListQueryRequest.getTtid());
        listSearchMtopParam.setDepDate(trainListQueryRequest.getDepDate());
        listSearchMtopParam.setSearchSource(AppNameUtils.getAppName());
        // 默认搜索直达
        listSearchMtopParam.setSearchType(SEARCH_TYPE_NO_STOP);
        listSearchMtopParam.setDepType(Objects.isNull(trainListQueryRequest.getDepType()) ? DEP_ARR_TYPE_CITY :
                trainListQueryRequest.getDepType());
        listSearchMtopParam.setArrType(Objects.isNull(trainListQueryRequest.getArrType()) ? DEP_ARR_TYPE_CITY :
                trainListQueryRequest.getArrType());
        // 规范化参数，如果搜索类型是城市，就只传城市，反之，只传站点
        switch (listSearchMtopParam.getDepType()) {
            case 0:
                // 火车
                if (StringUtils.isBlank(trainListQueryRequest.getDepLocation())) {
                    return null;
                }
                listSearchMtopParam.setDepLocation(trainListQueryRequest.getDepLocation());
                break;
            case 2:
                // 城市
                if (StringUtils.isBlank(trainListQueryRequest.getDepAreaName())) {
                    return null;
                }
                listSearchMtopParam.setDepAreaName(trainListQueryRequest.getDepAreaName());
                break;
            default:
                return null;
        }
        // 规范化参数，如果搜索类型是城市，就把站点去了
        switch (listSearchMtopParam.getArrType()) {
            case 0:
                // 火车
                if (StringUtils.isBlank(trainListQueryRequest.getArrLocation())) {
                    return null;
                }
                listSearchMtopParam.setArrLocation(trainListQueryRequest.getArrLocation());
                break;
            case 2:
                // 城市
                if (StringUtils.isBlank(trainListQueryRequest.getArrAreaName())) {
                    return null;
                }
                listSearchMtopParam.setArrAreaName(trainListQueryRequest.getArrAreaName());
                break;
            default:
                return null;
        }
        return listSearchMtopParam;
    }
}
