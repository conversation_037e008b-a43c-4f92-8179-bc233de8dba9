package com.taobao.trip.jourprod.core.service.converter;

import javax.annotation.Resource;

import com.alibaba.trip.tripdivision.domain.TrdiDivisionDO;

import com.taobao.trip.jourprod.common.lang.utils.CIDateUtil;
import com.taobao.trip.jourprod.common.sal.hsf.tripdivision.TrdiDivisionReadServiceClient;
import com.taobao.trip.jourprod.service.facade.model.request.train.TrainNumberQueryRequest;
import com.taobao.trip.tripjourneyop.common.util.AppNameUtils;
import com.taobao.trip.wireless.train.domain.search.param.ListSearchMtopParam;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import static com.taobao.trip.jourprod.core.service.converter.TrainListQueryConverter.DEP_ARR_TYPE_CITY;
import static com.taobao.trip.jourprod.core.service.converter.TrainListQueryConverter.SEARCH_TYPE_NO_STOP;

/**
 * <AUTHOR>
 * @time 2024/1/10 14:26
 */
@Component("trainNumberQueryConverter")
public class TrainNumberQueryConverter implements Converter<TrainNumberQueryRequest, ListSearchMtopParam>{

    @Resource
    private TrdiDivisionReadServiceClient trdiDivisionReadServiceClient;

    /**
     * @param trainNumberQueryRequest 原类型
     * @param map                     额外参数
     * @return T 目标结果
     */
    @Override
    public ListSearchMtopParam convert(TrainNumberQueryRequest trainNumberQueryRequest, Map map) {
        // 如果请求中的城市名称为空，即基于citycode转换
        String destCityName = trainNumberQueryRequest.getDestCityName();
        if (StringUtils.isBlank(trainNumberQueryRequest.getDestCityName())) {
            TrdiDivisionDO divisionById = trdiDivisionReadServiceClient.getDivisionById(NumberUtils.toLong(trainNumberQueryRequest.getDestCityId()));
            destCityName = Objects.nonNull(divisionById) ? divisionById.getName() : StringUtils.EMPTY;
        }
        String fromCityName = trainNumberQueryRequest.getFromCityName();
        if (StringUtils.isBlank(trainNumberQueryRequest.getFromCityName())) {
            TrdiDivisionDO divisionById = trdiDivisionReadServiceClient.getDivisionById(NumberUtils.toLong(trainNumberQueryRequest.getFromCityId()));
            fromCityName = Objects.nonNull(divisionById) ? divisionById.getName() : StringUtils.EMPTY;
        }
        String depDate = trainNumberQueryRequest.getDepDate();
        if (StringUtils.isBlank(depDate)) {
            depDate = CIDateUtil.getWebDateString(new Date());
        }
        if (StringUtils.isAnyBlank(destCityName, fromCityName)) {
            return null;
        }

        ListSearchMtopParam listSearchMtopParam = new ListSearchMtopParam();
        listSearchMtopParam.setSearchSource(AppNameUtils.getAppName());
        listSearchMtopParam.setDepDate(depDate);
        listSearchMtopParam.setSearchType(SEARCH_TYPE_NO_STOP);
        listSearchMtopParam.setDepType(DEP_ARR_TYPE_CITY);
        listSearchMtopParam.setArrType(DEP_ARR_TYPE_CITY);
        listSearchMtopParam.setDepAreaName(fromCityName);
        listSearchMtopParam.setArrAreaName(destCityName);
        listSearchMtopParam.setDepAreaCode(trainNumberQueryRequest.getFromCityId());
        listSearchMtopParam.setArrAreaCode(trainNumberQueryRequest.getDestCityId());
        return listSearchMtopParam;
    }
}
