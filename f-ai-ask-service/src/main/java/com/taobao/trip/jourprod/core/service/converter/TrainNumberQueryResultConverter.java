package com.taobao.trip.jourprod.core.service.converter;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.taobao.trip.jourprod.service.facade.model.response.JourPlanError;
import com.taobao.trip.jourprod.service.facade.model.response.train.TrainNumberQueryResult;
import com.taobao.trip.wireless.train.domain.search.n.result.StationSearch3VO;
import com.taobao.trip.wireless.train.domain.search.result.listsearch.NonstopVO;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @time 2024/1/10 14:50
 */
@Component("trainNumberQueryResultConverter")
public class TrainNumberQueryResultConverter implements Converter<NonstopVO, TrainNumberQueryResult>{

    /**
     * 火车票给的原始信息
     */
    private final static String RAW_TRAIN_INFO_KEY = "RAW_TRAIN_INFO_KEY";

    /**
     * @param nonstopVO 原类型
     * @param map       额外参数
     * @return T 目标结果
     */
    @Override
    public TrainNumberQueryResult convert(NonstopVO nonstopVO, Map map) {
        // 从车次信息中取所有的车票数量
        if (Objects.isNull(nonstopVO)) {
            return TrainNumberQueryResult.failed(JourPlanError.QUERY_TRAIN_LIST_ERROR);
        }
        List<StationSearch3VO.TrainStation3VO> trains = nonstopVO.getTrains();
        Map<String, Object> extInfo = Maps.newHashMap();
        extInfo.put(RAW_TRAIN_INFO_KEY, nonstopVO);
        if (CollectionUtils.isEmpty(trains)) {
            return TrainNumberQueryResult.success(0, Sets.newHashSet(), extInfo);
        }
        // 车票余量
        Set<String> trainNoSet = Sets.newHashSet();
        Integer trainNumber = trains.stream().map(trainStation3VO -> {
            trainNoSet.add(trainStation3VO.getTrainNo());
            List<StationSearch3VO.SeatVO> seatTypes = trainStation3VO.getSeatTypes();
            if (CollectionUtils.isEmpty(seatTypes)) {
                return 0;
            }
            return seatTypes.stream().map(seatVO -> {
                return Objects.nonNull(seatVO.getStock()) ? seatVO.getStock() : Integer.valueOf(0);
            }).reduce(Integer::sum).orElse(0);
        }).reduce(Integer::sum).orElse(0);
        // 车次列表
        return TrainNumberQueryResult.success(trainNumber, trainNoSet, extInfo);
    }
}
