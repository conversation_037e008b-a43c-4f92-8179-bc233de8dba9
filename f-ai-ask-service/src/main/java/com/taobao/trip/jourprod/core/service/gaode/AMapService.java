package com.taobao.trip.jourprod.core.service.gaode;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.trippoi.service.sync.GaoDePoiSyncService;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.trip.jourdprod.core.model.common.ConstantElement;
import com.taobao.trip.jourprod.common.gaode.GaodeCommon;
import com.taobao.trip.jourprod.common.lang.utils.HttpUtil;
import com.taobao.trip.jourprod.common.lang.utils.MapLogUtils;
import com.taobao.trip.jourprod.common.sal.hsf.config.ChannelV3ForEmergencySwitch;
import com.taobao.trip.jourprod.common.sal.hsf.config.Switcher;
import com.taobao.trip.jourprod.common.sal.tair.impl.LdbTairManager;
import com.taobao.trip.jourprod.common.sal.tair.impl.MdbTairCommonHelper;
import com.taobao.trip.jourprod.service.facade.model.request.gaode.GaoDeSearchQueryRequest;
import com.taobao.util.CollectionUtil;
import fliggy.content.model.FliggyLogger;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

/**
 * 高德服务
 * <AUTHOR>
 * @date 2021/6/24
 */
@Service
public class AMapService {

    @Resource
    private MdbTairCommonHelper mdbTairCommonHelper;
    @Resource
    private LdbTairManager ldbTairManager;
    @Resource
    private GaoDePoiSyncService gaoDePoiSyncService;

    @PostConstruct
    private void init() {
        SwitchManager.init(AMapService.class);
    }

    private final FliggyLogger logger = LogUtil.getFliggyLogger(AMapService.class);
    private final FliggyNewLogger newLogger = LogUtil.getFliggyNewLogger(AMapService.class);

    // 在类中定义静态HTTP客户端（使用连接池）
    private static final CloseableHttpClient HTTP_CLIENT = HttpClients.custom()
            .setMaxConnTotal(200) // 最大连接数
            .setMaxConnPerRoute(50) // 每个路由最大连接数
            .evictExpiredConnections() // 自动清理过期连接
            .build();

    @AppSwitch(des = "高德搜索缓存超时时间", level = Switch.Level.p2)
    public static Integer GAO_DE_SEARCH_CACHE_TTL = 6 * 30 * 24 * 60 * 60;

    @AppSwitch(des = "高德地图authkey", level = Switch.Level.p2)
    public static String REQUEST_AUTH_KEY = "f339f58fff46e7ab3427df86ad2de9a2";

    @AppSwitch(des = "高德地图authkey列表", level = Switch.Level.p2)
    public static List<String> REQUEST_AUTH_KEY_LIST = Lists.newArrayList(
            "f339f58fff46e7ab3427df86ad2de9a2",
            "8e39f8a541f7752176434fd5bef56545",
            "202493694bfe754c0fa8054181139126",
            "61ad98d2a01e1c0923ebefbc50b98dc4",
            "1f500b8729053525ced1cfe9efee8f29",
            "66e51f1a55f928fdb70eebcd4cde6b5f",
            "c77f251fd75c6498392130c70149d2f3",
            "839144b716f2923d2b1d2da925c3e549"
            );

    @AppSwitch(des = "高德地图authkey分场景失效,key是授权key + _ + 场景，value是是否mock", level = Switch.Level.p2)
    public static Map<String, Boolean> REQUEST_AUTH_KEY_INVALID_MOCK = new HashMap<>();

    @AppSwitch(des = "默认搜索字段", level = Switch.Level.p4)
    public static String DefaultShowFields = "children,business,indoor,navi,photos";

    private static String KEY = "key";
    private static String ORIGIN = "origin";
    private static String DESTINATION = "destination";
    private static String CITY = "city";
    private static String CITYLIMIT = "citylimit";
    private static String CITYD = "cityd";
    private static String NIGHTFLAG = "nightflag";
    private static String EXTENSIONS = "extensions";
    private static String ID = "id";
    private static String NAME = "name";
    private static String ORIGINID = "originid";
    private static String DESTINATIONID = "destinationid";
    private static String SHOW_FIELDS = "show_fields";
    private static String REGION = "region";
    private static String PAGE_NUM = "page_num";
    private static String PAGE_SIZE = "page_size";
    private static String LOCATION = "location";
    private static String TYPES = "types";
    private static String RADIUS = "radius";

    private static String KEYWORDS = "keywords";
    private static String PAGE = "page";
    private static String OFFSET = "offset";
    private static final String POIS = "pois";

    private static String BASE_URL = "https://restapi.amap.com";
    private static String BATCH_URL = "/v3/batch?key=" + REQUEST_AUTH_KEY;
    private static String TRANSIT_URL = "/v3/direction/transit/integrated?";
    private static String DRIVING_URL = "/v3/direction/driving?";
    private static String WALKING_URL = "/v3/direction/walking?";
    private static String SEARCH_URL = "/v3/place/text?";
    private static String SEARCH_BY_ID_URL = "/v3/place/detail?";
    private static String SEARCH_AROUND_URL = "/v3/place/around?";

    // 高德搜索2.0
    private static String SEARCH_URL_V2 = "/v5/place/text?";
    private static String SEARCH_BY_ID_URL_V2 = "/v5/place/detail?";
    private static String SEARCH_AROUND_URL_V2 = "/v5/place/around?";

    //高德搜索，根据城市名查询cityCode
    private static String SEARCH_CITY_URL = "/v3/config/district?";

    //高德搜索缓存key
    private static final String GAO_DE_SEARCH_LIST_CACHE_KEY = "tripOd:search:gaoDeList:";
    //高德搜索2.0缓存key
    private static final String GAO_DE_SEARCH_LIST_CACHE_KEY_V2 = "tripOd:search:gaoDeListV2:";
    //高德交通规划缓存key
    private static final String GAO_DE_DIRECTION_LIST_CACHE_KEY = "tripOd:gaoDe:direction:";
    //高德搜索周边2.0缓存key
    private static final String GAO_DE_SEARCH_AROUND_LIST_CACHE_KEY = "tripOd:search:gaoDeAroundList:";
    private static final String GAO_DE_SEARCH_AROUND_LIST_CACHE_KEY_V2 = "tripOd:search:gaoDeAroundListV2:";

    /**
     * 查询公交、地铁交通规划
     * @param request
     * @return
     */
    public Map<String,Object> queryTransitPlan(TrafficPlanQueryRequest request) {
        if (Switcher.closeTrafficPlanQuery || !checkParam(request)) {
            return null;
        }
        try {
            String reqUrl = BASE_URL + TRANSIT_URL + buildTransitReqParams(request);
            String response = HttpUtil.doGet(reqUrl, null);
            newLogger.recordOutput(new LogModel("queryTransitPlan").request(reqUrl).response(response));
            
            // 记录高德地图API调用日志
            MapLogUtils.logAmapApiCall(
                MapLogUtils.AMAP_API_TRANSIT_PLAN, 
                request, 
                response
            );

            return JSON.parseObject(response, new TypeReference<Map<String, Object>>(){});
        } catch (Exception e) {
            newLogger.recordDangerException(new LogModel("queryTransitPlan").e(e).request(JSON.toJSONString(request)));
        }
        return null;
    }

    /**
     * 查询驾车交通规划
     * @param request
     * @return
     */
    public Map<String,Object> queryDrivingPlan(TrafficPlanQueryRequest request) {
        if (Switcher.closeTrafficPlanQuery || !checkParam(request)) {
            return null;
        }
        try {
            String reqUrl = BASE_URL + DRIVING_URL + buildDrivingReqParams(request);
            String response = HttpUtil.doGet(reqUrl, null);
            newLogger.recordOutput(new LogModel("queryDrivingPlan").request(reqUrl).response(response));
            
            // 记录高德地图API调用日志
            MapLogUtils.logAmapApiCall(
                MapLogUtils.AMAP_API_DRIVING_PLAN, 
                request, 
                response
            );

            return JSON.parseObject(response, new TypeReference<Map<String, Object>>(){});
        } catch (Exception e) {
            newLogger.recordDangerException(new LogModel("queryDrivingPlan").e(e).request(JSON.toJSONString(request)));
        }
        return null;
    }

    /**
     * 查询步行规划
     * @param request
     * @return
     */
    public Map<String,Object> queryWalkingPlan(TrafficPlanQueryRequest request) {
        if (Switcher.closeTrafficPlanQuery || !checkParam(request)) {
            return null;
        }
        try {
            String reqUrl = BASE_URL + WALKING_URL + buildWalkingReqParams(request);
            String response = HttpUtil.doGet(reqUrl, null);
            newLogger.recordOutput(new LogModel("queryWalkingPlan").request(reqUrl).response(response));
            
            // 记录高德地图API调用日志
            MapLogUtils.logAmapApiCall(
                MapLogUtils.AMAP_API_WALKING_PLAN, 
                request, 
                response
            );

            return JSON.parseObject(response, new TypeReference<Map<String, Object>>(){});
        } catch (Exception e) {
            newLogger.recordDangerException(new LogModel("queryWalkingPlan").e(e).request(JSON.toJSONString(request)));
        }
        return null;
    }

    /**
     * 查询 高德 搜索
     * @param request
     * @return
     */
    public List<Map<String, Object>> queryGaoDeSearch(GaoDeSearchQueryRequest request) {
        return queryGaoDeSearchWithVersion(request, false);
    }

    /**
     * 根据城市名称查询城市code  cityName必须为标准行政区名称
     * @param cityName request
     * @return cityCode
     */
    public String queryCityCodeByCityName(String cityName) {
        if (StringUtils.isBlank(cityName)) {
            return null;
        }
        String cityCode = null;
        cityCode = GaodeCommon.chineseCityNameToCodeMap1.get(cityName);
        if(StringUtils.isNotBlank(cityCode)) {
            return cityCode;
        }
        cityCode = GaodeCommon.chineseCityNameToCodeMap2.get(cityName);
        if(StringUtils.isNotBlank(cityCode)) {
            return cityCode;
        }
        cityCode = GaodeCommon.chineseCityNameToCodeMap3.get(cityName);
        if(StringUtils.isNotBlank(cityCode)) {
            return cityCode;
        }
        cityCode = GaodeCommon.abroadCityNameToCodeMap1.get(cityName);
        if(StringUtils.isNotBlank(cityCode)) {
            return cityCode;
        }

        cityCode = GaodeCommon.abroadCityNameToCodeMap2.get(cityName);
        if(StringUtils.isNotBlank(cityCode)) {
            return cityCode;
        }

        cityCode = GaodeCommon.abroadCityNameToCodeMap3.get(cityName);
        if(StringUtils.isNotBlank(cityCode)) {
            return cityCode;
        }

        return cityCode;
    }

    public List<Map<String, Object>> queryGaoDeSearchWithVersion(GaoDeSearchQueryRequest request, Boolean newversion) {
        if (Switcher.closeGaoDeSearchQuery) {
            return null;
        }
        List<Map<String, Object>> poiSearchList = Lists.newArrayList();
        //加缓存
        String baseKey = newversion ? GAO_DE_SEARCH_LIST_CACHE_KEY_V2 : GAO_DE_SEARCH_LIST_CACHE_KEY;
        String key = baseKey + JSON.toJSONString(request);
        Object value = ldbTairManager.get(key);
        if (Objects.nonNull(value)) {
            poiSearchList = JSON.parseObject(value.toString(), new TypeReference<List<Map<String, Object>>>() {
            });
            newLogger.recordOutput(new LogModel("queryGaoDeSearch_cache")
                .request(JSON.toJSONString(request))
                .response(JSON.toJSONString(poiSearchList)));
            return poiSearchList;
        }

        long start = System.currentTimeMillis();
        try {
            //处理分页参数
            dealPageInfoParam(request);

            String baseReqUrl = newversion ? SEARCH_URL_V2 : SEARCH_URL;
            String reqUrl = BASE_URL + baseReqUrl + buildSearchReqParams(request);
            HttpResponse response = null;
            try {
                HttpGet httpGet = new HttpGet(reqUrl);
                response = HTTP_CLIENT.execute(httpGet);
                int responseCode = response.getStatusLine().getStatusCode();
                if (responseCode != 200) {
                    return null;
                }

                String res =  EntityUtils.toString(response.getEntity());
                newLogger.recordOutput(new LogModel("queryGaoDeSearch")
                    .request(JSON.toJSONString(request))
                    .path(reqUrl)
                    .cost(System.currentTimeMillis() - start)
                    .response(res));
                
                // 记录高德地图API调用日志
                MapLogUtils.logAmapApiCall(
                    newversion ? MapLogUtils.AMAP_API_PLACE_TEXT_V5 : MapLogUtils.AMAP_API_PLACE_TEXT_V3, 
                    request, 
                    res
                );

                Map<String, Object> result = JSON.parseObject(res, new TypeReference<Map<String, Object>>() {
                });
                poiSearchList = (List<Map<String, Object>>) result.get(POIS);
                if (CollectionUtils.isNotEmpty(poiSearchList)) {
                    ldbTairManager.put(key, JSON.toJSONString(poiSearchList), GAO_DE_SEARCH_CACHE_TTL);
                }
            } catch (Exception e) {
                newLogger.recordDangerException(new LogModel("queryGaoDeSearch_httpGet").e(e).request(JSON.toJSONString(request)));
            } finally {
                if (Objects.nonNull(response)) {
                    // 确保释放资源
                    EntityUtils.consumeQuietly(response.getEntity());
                }
            }
        } catch (Exception e) {
            newLogger.recordDangerException(new LogModel("queryGaoDeSearch").e(e).request(JSON.toJSONString(request)));
        }
        return poiSearchList;
    }

    public List<Map<String, Object>> queryGaoDeSearchAround(GaoDeSearchQueryRequest request, Boolean newversion) {
        if (Switcher.closeGaoDeSearch2AroundQuery) {
            return null;
        }
        List<Map<String, Object>> poiSearchList = Lists.newArrayList();
        //加缓存
        String baseKey = newversion ? GAO_DE_SEARCH_AROUND_LIST_CACHE_KEY_V2 : GAO_DE_SEARCH_AROUND_LIST_CACHE_KEY;
        String key = baseKey + JSON.toJSONString(request);
        Object value = ldbTairManager.get(key);
        if (Objects.nonNull(value)) {
            poiSearchList = JSON.parseObject(value.toString(), new TypeReference<List<Map<String, Object>>>() {
            });
            newLogger.recordOutput(new LogModel("queryGaoDeSearchAround_cache")
                    .request(JSON.toJSONString(request))
                    .response(JSON.toJSONString(poiSearchList)));
            return poiSearchList;
        }

        long start = System.currentTimeMillis();
        try {
            //处理分页参数
            dealPageInfoParam(request);

            String baseUrl = newversion ? SEARCH_AROUND_URL_V2 : SEARCH_AROUND_URL;
            String reqUrl = BASE_URL + baseUrl + buildSearchReqParams(request);
            HttpResponse response = null;
            try {
                HttpGet httpGet = new HttpGet(reqUrl);
                response = HTTP_CLIENT.execute(httpGet);
                int responseCode = response.getStatusLine().getStatusCode();
                if (responseCode != 200) {
                    return null;
                }

                String res =  EntityUtils.toString(response.getEntity());
                newLogger.recordOutput(new LogModel("queryGaoDeSearchAround")
                        .request(JSON.toJSONString(request))
                        .path(reqUrl)
                        .cost(System.currentTimeMillis() - start)
                        .response(res));
                
                // 记录高德地图API调用日志
                MapLogUtils.logAmapApiCall(
                    newversion ? MapLogUtils.AMAP_API_PLACE_AROUND_V5 : MapLogUtils.AMAP_API_PLACE_AROUND_V3, 
                    request, 
                    res
                );

                Map<String, Object> result = JSON.parseObject(res, new TypeReference<Map<String, Object>>() {
                });
                poiSearchList = (List<Map<String, Object>>) result.get(POIS);
                if (CollectionUtils.isNotEmpty(poiSearchList)) {
                    for (Map<String, Object> poi : poiSearchList) {
                        String id = MapUtils.getString(poi, ID);
                        //加缓存
                        String baseIdKey = newversion ? GAO_DE_SEARCH_LIST_CACHE_KEY_V2 : GAO_DE_SEARCH_LIST_CACHE_KEY;
                        String idKey = baseIdKey + id;
                        ldbTairManager.put(idKey, JSON.toJSONString(Lists.newArrayList(poi)), GAO_DE_SEARCH_CACHE_TTL);
                    }
                    ldbTairManager.put(key, JSON.toJSONString(poiSearchList), GAO_DE_SEARCH_CACHE_TTL);
                }
            } catch (Exception e) {
                newLogger.recordDangerException(new LogModel("queryGaoDeSearchAround_httpGet").e(e).request(JSON.toJSONString(request)));
            } finally {
                if (Objects.nonNull(response)) {
                    // 确保释放资源
                    EntityUtils.consumeQuietly(response.getEntity());
                }
            }
        } catch (Exception e) {
            newLogger.recordDangerException(new LogModel("queryGaoDeSearchAround").e(e).request(JSON.toJSONString(request)));
        }
        return poiSearchList;
    }

    /**
     * 处理高德搜索分页参数
     * @param request
     */
    private void dealPageInfoParam(GaoDeSearchQueryRequest request) {
        Integer page = request.getPage();
        Integer offset = request.getOffset();
        if (Objects.isNull(page) || page < 1) {
            page = 1;
        }
        request.setPage(page);
        if (Objects.isNull(offset) || offset < 20) {
            offset = 20;
        }
        request.setOffset(offset);

        Integer pageNum = request.getPageNum();
        Integer pageSize = request.getPageSize();
        if (Objects.isNull(pageNum) || pageNum < 1) {
            pageNum = 1;
        }
        request.setPageNum(pageNum);
        if (Objects.isNull(pageSize)) {
            pageSize = 10;
        }
        request.setPageSize(pageSize);
    }


    /**
     * 根据id查询 高德 搜索
     * @param id
     * @return
     */
    public List<Map<String, Object>> queryGaoDeSearchById(String id) {
        return queryGaoDeSearchByIdWithVersion(id, false, null);
    }

    public static void main(String[] args) {
        AMapService aMapService = new AMapService();
        List<Map<String, Object>> maps = aMapService.queryGaoDeSearchByIdWithVersion("B0FFKEPXS2", false, null);
        System.out.println(JSON.toJSONString(maps));
    }

    public List<Map<String, Object>> queryGaoDeSearchByIdWithVersion(String id, Boolean newversion, String showFields) {
        if (Switcher.closeGaoDeSearchQuery) {
            return null;
        }
        List<Map<String, Object>> poiSearchList = Lists.newArrayList();
        if (StringUtils.isBlank(id)) {
            return poiSearchList;
        }
        //加缓存
         String baseKey = newversion ? GAO_DE_SEARCH_LIST_CACHE_KEY_V2 : GAO_DE_SEARCH_LIST_CACHE_KEY;
         String key = baseKey + id;
         Object value = ldbTairManager.get(key);
        if (Objects.nonNull(value)) {
            poiSearchList = JSON.parseObject(value.toString(), new TypeReference<List<Map<String, Object>>>() {
            });
            newLogger.recordOutput(new LogModel("queryGaoDeSearchById_cache")
                .request(id)
                .response(JSON.toJSONString(poiSearchList)));
            return poiSearchList;
        }

        long start = System.currentTimeMillis();
        HttpResponse response = null;
        try {
            String baseReqUrl = newversion ? SEARCH_BY_ID_URL_V2 : SEARCH_BY_ID_URL;
            String reqUrl = BASE_URL + baseReqUrl + buildSearchReqParamsById(id, showFields);
            try {
                HttpGet httpGet = new HttpGet(reqUrl);
                response = HTTP_CLIENT.execute(httpGet);
                int responseCode = response.getStatusLine().getStatusCode();
                if (responseCode != 200) {
                    return null;
                }

                String res =  EntityUtils.toString(response.getEntity());
                newLogger.recordOutput(new LogModel("queryGaoDeSearchById")
                    .request(id)
                    .cost(System.currentTimeMillis() - start)
                    .response(res));
                
                // 记录高德地图API调用日志
                MapLogUtils.logAmapApiCall(
                    newversion ? MapLogUtils.AMAP_API_PLACE_DETAIL_V5 : MapLogUtils.AMAP_API_PLACE_DETAIL_V3, 
                    id, 
                    res
                );
                // 落库
                try {
                    gaoDePoiSyncService.syncPoiData(id, res);
                } catch (Exception e) {
                    newLogger.recordDangerException(new LogModel("queryGaoDeSearchById_syncPoiData").e(e).request(id));
                }

                Map<String, Object> result = JSON.parseObject(res, new TypeReference<Map<String, Object>>() {
                });
                poiSearchList = (List<Map<String, Object>>) result.get(POIS);
                if (CollectionUtils.isNotEmpty(poiSearchList)) {
                     ldbTairManager.put(key, JSON.toJSONString(poiSearchList), GAO_DE_SEARCH_CACHE_TTL);
                }
            } catch (Exception e) {
                newLogger.recordDangerException(new LogModel("queryGaoDeSearchById_httpGet").e(e).request(id));
            } finally {
                if (Objects.nonNull(response)) {
                    // 确保释放资源
                    EntityUtils.consumeQuietly(response.getEntity());
                }
            }
        } catch (Exception e) {
            newLogger.recordDangerException(new LogModel("queryGaoDeSearchById").e(e).request(id));
        }
        return poiSearchList;
    }

    /**
     * 根据传入的类型 批量查询交通规划
     * ITEM162503579406921 -> ASSISTANT_DRIVING_PLAN
     * ITEM162503474472532 -> ASSISTANT_TRANSIT_PLAN
     * @param request           导航查询参数
     * @param serviceIdList     要查询的类别，这里只有驾车和公交
     * @return 按照传入的类别顺序返回导航数据
     */
    public List<Map<String, Object>> batchQueryTrafficPlan(TrafficPlanQueryRequest request, List<String> serviceIdList) {
        if (Switcher.closeTrafficPlanQuery || !checkParam(request)) {
            return null;
        }
        try {
            //加缓存
            String key = GAO_DE_DIRECTION_LIST_CACHE_KEY + JSON.toJSONString(request);
            Object value = ldbTairManager.get(key);
            if (Objects.nonNull(value)) {
                if (ChannelV3ForEmergencySwitch.PRINT_SLS_TIME_LOG) {
                    newLogger.recordOutput(new LogModel("batchQueryTrafficPlan_cache")
                            .request(JSON.toJSONString(request))
                            .response(value.toString()));
                }
                return JSON.parseObject(value.toString(), new TypeReference<List<Map<String, Object>>>() {});
            }

            String batchUrl = BASE_URL + BATCH_URL;
            String transitUrl = TRANSIT_URL + buildTransitReqParams(request);
            String drivingUrl = DRIVING_URL + buildDrivingReqParams(request);
            // 构造body
            List<Map<String, Object>> urlList = Lists.newArrayList();
            Map<String, Object> transitMap = Maps.newHashMap();
            transitMap.put("url", transitUrl);

            Map<String, Object>  drivingMap = Maps.newHashMap();
            drivingMap.put("url", drivingUrl);

            // 根据传入的类型添加查询链接
            // 这里直接使用了魔法值，因为直接引用serviceIdEnum会导致循环依赖，todo 之后把这些通用的数据独立出去
            serviceIdList.forEach(serviceId -> {
                switch (serviceId) {
                    case "ITEM162503579406921":
                        urlList.add(drivingMap);
                        break;
                    case "ITEM162503474472532":
                        urlList.add(transitMap);
                        break;
                    default:
                        break;
                }
            });
            Map<String, Object> postData = new HashMap<>();
            postData.put("ops", urlList);
            String response = HttpUtil.doPost(batchUrl, JSON.toJSONString(postData), "application/json",
                    null, -1, null, "UTF-8");

            if (ChannelV3ForEmergencySwitch.PRINT_SLS_TIME_LOG) {
                newLogger.recordOutput(new LogModel("batchQueryTrafficPlan")
                        .request(JSON.toJSONString(request))
                        .response(response));
            }
            
            // 记录高德地图API调用日志
            MapLogUtils.logAmapApiCall(
                MapLogUtils.AMAP_API_BATCH_QUERY, 
                request, 
                response
            );
            
            List<Map<String, Object>> result = JSON.parseObject(response, new TypeReference<List<Map<String, Object>>>() {});
            if (CollectionUtils.isNotEmpty(result)) {
                ldbTairManager.put(key, JSON.toJSONString(result), GAO_DE_SEARCH_CACHE_TTL);
            }
            return result;
        } catch (Exception e) {
            logger.recordDangerException("batchQueryTrafficPlan", e, JSON.toJSONString(request));
        }
        return null;
    }

    /**
     * 批量查询交通规划
     * 返回列表顺序：0-公交地铁  1-步行  2-驾车
     * @param request
     * @return
     */
    public List<Map<String, Object>> batchQueryTrafficPlan(TrafficPlanQueryRequest request) {
        if (Switcher.closeTrafficPlanQuery || !checkParam(request)) {
            return null;
        }
        long start = System.currentTimeMillis();
        try {
            String batchUrl = BASE_URL + BATCH_URL;

            String transitUrl = TRANSIT_URL + buildTransitReqParams(request);
            String walkingUrl = WALKING_URL + buildWalkingReqParams(request);
            String drivingUrl = DRIVING_URL + buildDrivingReqParams(request);
            // 构造body
            List<Map<String, Object>> urlList = Lists.newArrayList();
            Map<String, Object> transitMap = Maps.newHashMap();
            transitMap.put("url", transitUrl);
            urlList.add(transitMap);

            Map<String, Object>  walkingMap = Maps.newHashMap();
            walkingMap.put("url", walkingUrl);
            urlList.add(walkingMap);

            Map<String, Object>  drivingMap = Maps.newHashMap();
            drivingMap.put("url", drivingUrl);
            urlList.add(drivingMap);

            Map<String, Object> postData = new HashMap<>();
            postData.put("ops", urlList);

            String response = HttpUtil.doPost(batchUrl, JSON.toJSONString(postData), "application/json",
                    null, -1, null, "UTF-8");

            newLogger.recordOutput(new LogModel("batchQueryTrafficPlan")
                    .request(JSON.toJSONString(request))
                    .cost(System.currentTimeMillis() - start)
                    .response(response));
            
            // 记录高德地图API调用日志
            MapLogUtils.logAmapApiCall(
                MapLogUtils.AMAP_API_BATCH_QUERY_ALL, 
                request, 
                response
            );

            return JSON.parseObject(response, new TypeReference<List<Map<String, Object>>>(){});
        } catch (Exception e) {
            newLogger.recordDangerException(new LogModel("batchQueryTrafficPlan").e(e).request(JSON.toJSONString(request)));
        }
        return null;
    }

    private String buildTransitReqParams(TrafficPlanQueryRequest request) {
        StringBuilder sb = new StringBuilder();
        sb.append(ORIGIN).append("=").append(request.getOrigin()).append("&");
        sb.append(DESTINATION).append("=").append(request.getDestination()).append("&");
        sb.append(CITY).append("=").append(request.getCity()).append("&");
        if (StringUtils.isNotBlank(request.getCityd())) {
            sb.append(CITYD).append("=").append(request.getCityd()).append("&");
        }
        sb.append(NIGHTFLAG).append("=").append(1).append("&");

        String authKey = getAmapRequestAuthKey(ConstantElement.AMAP_TRANSIT);
        sb.append(KEY).append("=").append(authKey);

        request.getAMapAuthKeyMap().put(ConstantElement.AMAP_TRANSIT, authKey);
        return sb.toString();
    }

    private String buildSearchReqParams(GaoDeSearchQueryRequest request) {
        StringBuilder sb = new StringBuilder();
        String keywords = request.getKeywords();
        try {
            if (StringUtils.isNotBlank(keywords)) {
                keywords = URLEncoder.encode(keywords, "UTF-8");
                sb.append(KEYWORDS).append("=").append(keywords).append("&");
            }
        } catch (Exception e) {
            newLogger.recordDangerException(new LogModel("buildSearchReqParams").e(e).request(JSON.toJSONString(request)));
        }

        try {
            if (StringUtils.isNotBlank(request.getCity())) {
                String cityEncode = URLEncoder.encode(request.getCity(), "UTF-8");
                sb.append(CITY).append("=").append(cityEncode).append("&");
            }
        } catch (Exception e) {
            newLogger.recordDangerException(new LogModel("buildSearchReqParams").e(e).request(JSON.toJSONString(request)));
        }

        if(request.getCitylimit() != null){
            sb.append(CITYLIMIT).append("=").append(request.getCitylimit()).append("&");
        }
        if (StringUtils.isNotBlank(request.getRegion())) {
            sb.append(REGION).append("=").append(request.getRegion()).append("&");
        }
        if (StringUtils.isNotBlank(request.getShowFields())) {
            sb.append(SHOW_FIELDS).append("=").append(request.getShowFields()).append("&");
        }
        if (Objects.nonNull(request.getPageNum())) {
            sb.append(PAGE_NUM).append("=").append(request.getPageNum()).append("&");
        }
        if (Objects.nonNull(request.getPageSize())) {
            sb.append(PAGE_SIZE).append("=").append(request.getPageSize()).append("&");
        }
        if (StringUtils.isNotBlank(request.getLocation())) {
            sb.append(LOCATION).append("=").append(request.getLocation()).append("&");
        }
        if (StringUtils.isNotBlank(request.getTypes())) {
            sb.append(TYPES).append("=").append(request.getTypes()).append("&");
        }
        if (Objects.nonNull(request.getRadius())) {
            sb.append(RADIUS).append("=").append(request.getRadius()).append("&");
        }
        sb.append(PAGE).append("=").append(request.getPage()).append("&");
        sb.append(OFFSET).append("=").append(request.getOffset()).append("&");
        String authKey = getAmapRequestAuthKey(ConstantElement.AMAP_SEARCH);
        if (StringUtils.isNotBlank(request.getKey())) {
            authKey = request.getKey();
        }
        sb.append(KEY).append("=").append(authKey).append("&");
        sb.append(EXTENSIONS).append("=").append("all");

        return sb.toString();
    }

    private String buildSearchReqParamsById(String id, String showFields) {
        StringBuilder sb = new StringBuilder();
        String authKey = getAmapRequestAuthKey(ConstantElement.AMAP_SEARCH);
        if (StringUtils.isNotBlank(showFields)) {
            sb.append(SHOW_FIELDS).append("=").append(showFields).append("&");
        }
        sb.append(KEY).append("=").append(authKey).append("&");
        sb.append(ID).append("=").append(id);
        return sb.toString();
    }

    private String buildWalkingReqParams(TrafficPlanQueryRequest request) {
        StringBuilder sb = new StringBuilder();
        sb.append(ORIGIN).append("=").append(request.getOrigin()).append("&");
        sb.append(DESTINATION).append("=").append(request.getDestination()).append("&");

        String authKey = getAmapRequestAuthKey(ConstantElement.AMAP_WALKING);
        sb.append(KEY).append("=").append(authKey);

        request.getAMapAuthKeyMap().put(ConstantElement.AMAP_WALKING, authKey);
        return sb.toString();
    }

    private String buildDrivingReqParams(TrafficPlanQueryRequest request) {
        StringBuilder sb = new StringBuilder();
        sb.append(ORIGIN).append("=").append(request.getOrigin()).append("&");
        sb.append(DESTINATION).append("=").append(request.getDestination()).append("&");
        if (Objects.nonNull(request.getOriginPoiId())) {
            sb.append(ORIGINID).append("=").append(request.getOriginPoiId()).append("&");
        }
        if (Objects.nonNull(request.getDestPoiId())) {
            sb.append(DESTINATIONID).append("=").append(request.getDestPoiId()).append("&");
        }
        sb.append(EXTENSIONS).append("=").append("base").append("&");

        String authKey = getAmapRequestAuthKey(ConstantElement.AMAP_DRIVING);
        sb.append(KEY).append("=").append(authKey);

        request.getAMapAuthKeyMap().put(ConstantElement.AMAP_DRIVING, authKey);
        return sb.toString();
    }

    private String getAmapRequestAuthKey(String scene) {
        String requestAuthKey = REQUEST_AUTH_KEY;
        if(StringUtils.isBlank(scene)){
            return requestAuthKey;
        }
        String dayTime = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
        String tairKeyPrefix = ConstantElement.AMAP_REQUEST_AUTH_TAIR_KEY + scene + "_" + dayTime + "_";
        if(CollectionUtil.isNotEmpty(REQUEST_AUTH_KEY_LIST)){
            for(String key: REQUEST_AUTH_KEY_LIST){
                if(StringUtils.isBlank(key)){
                    continue;
                }
                Boolean isAuthKeyTodayInvalid = null;
                // todo 临时注释，调试用
                // Boolean isAuthKeyTodayInvalid = (Boolean) mdbTairCommonHelper.getValue(tairKeyPrefix + key);
                if(isAuthKeyTodayInvalid == null || !isAuthKeyTodayInvalid){
                    requestAuthKey = key;
                    break;
                }
            }
        }
        return requestAuthKey;
    }

    public void invalidAmapRequestAuthKey(String scene, String authKey) {
        if (StringUtils.isBlank(scene) || StringUtils.isBlank(authKey) ) {
            return;
        }
        String dayTime = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
        String tairKey = ConstantElement.AMAP_REQUEST_AUTH_TAIR_KEY + scene + "_" + dayTime + "_" + authKey;
        // 请求的key失效，放入缓存，当日不再用了
        mdbTairCommonHelper.putValue(tairKey, true, 86400);
    }

    private boolean checkParam(TrafficPlanQueryRequest request) {
        return !(Objects.isNull(request)
                || StringUtils.isBlank(request.getOrigin())
                || StringUtils.isBlank(request.getDestination()));
    }
}
