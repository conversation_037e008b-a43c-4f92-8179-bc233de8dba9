package com.taobao.trip.jourprod.core.service.gaode;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/6/24
 */
@Data
public class TrafficPlanQueryRequest implements Serializable {
    private static final long serialVersionUID = -8768381398481241630L;

    /**
     * 出发点
     * lon，lat（经度，纬度）， “,”分割
     * 经纬度小数点不超过6位
     */
    private String origin;

    /**
     * 目的地
     * lon，lat（经度，纬度）， “,”分割
     * 经纬度小数点不超过6位
     */
    private String destination;

    /**
     * 目的地名称
     */
    private String destName;

    /**
     * 出发地poiid
     */
    private Long originPoiId;

    /**
     * 目的地poiid
     */
    private Long destPoiId;

    /**
     * 城市/跨城规划时的起点城市
     * citycode
     */
    private String city;

    /**
     * 跨城公交规划时的终点城市,跨城必填
     * citycode
     */
    private String cityd;

    /**
     * 各个场景请求高德的授权key
     */
    private Map<String, String> aMapAuthKeyMap = new HashMap<>();

    /**
     * 做日志
     */
    private Long userId;
}
