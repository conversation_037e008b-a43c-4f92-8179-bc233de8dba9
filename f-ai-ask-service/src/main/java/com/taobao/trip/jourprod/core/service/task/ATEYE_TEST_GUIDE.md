# Ateye测试方法使用指南

## 概述

`ItemProduceTask#ateyeProcessOdpsDataWithLlm` 是一个专门为 Ateye 平台设计的测试方法，用于测试从ODPS下载数据并批量调用大模型处理的完整流程。

## 方法签名

```java
@AteyeInvoker(description = "测试ODPS数据批量LLM处理", 
              paraDesc = "odpsProject&odpsTable&odpsPartition&columnsToGet&workflowAppId&workflowFlowId&streamOutputTask&llmPrompt&taskType&llmBatchSize&batchSize&llmTimeoutSeconds&singleRequestTimeoutSeconds")
public void ateyeProcessOdpsDataWithLlm(String odpsProject, 
                                       String odpsTable, 
                                       String odpsPartition,
                                       String columnsToGet,
                                       Long workflowAppId,
                                       Long workflowFlowId,
                                       String streamOutputTask,
                                       String llmPrompt,
                                       String taskType,
                                       Integer llmBatchSize,
                                       Integer batchSize,
                                       Long llmTimeoutSeconds,
                                       Long singleRequestTimeoutSeconds)
```

## 参数说明

### 必填参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `odpsProject` | String | ODPS项目名 | `trip_content` |
| `odpsTable` | String | ODPS表名 | `product_info` |
| `workflowAppId` | Long | 工作流应用ID | `21` |
| `workflowFlowId` | Long | 工作流流程ID | `1` |

### 可选参数

| 参数名 | 类型 | 说明 | 默认值 | 示例 |
|--------|------|------|--------|------|
| `odpsPartition` | String | ODPS分区信息 | 无 | `ds='20240127'` |
| `columnsToGet` | String | 需要获取的列名，逗号分隔 | 获取所有列 | `product_id,product_name,category` |
| `streamOutputTask` | String | 流式输出任务节点名称 | 无 | `llm_vDq0SH` |
| `llmPrompt` | String | LLM处理提示词 | 无 | `根据商品信息生成描述` |
| `taskType` | String | 任务类型标识 | 无 | `product_description` |
| `llmBatchSize` | Integer | LLM批次大小 | 5 | `10` |
| `batchSize` | Integer | ODPS下载批次大小 | 1000 | `500` |
| `llmTimeoutSeconds` | Long | LLM批量请求超时时间（秒） | 120 | `180` |
| `singleRequestTimeoutSeconds` | Long | 单个LLM请求超时时间（秒） | 30 | `60` |

## 使用方法

### 1. 访问 Ateye 平台

访问应用的 Ateye 测试页面：
```
http://your-app-domain/agent.ateye
```

### 2. 查找测试方法

在 Ateye 页面中搜索：
- 类名：`ItemProduceTask`
- 方法名：`ateyeProcessOdpsDataWithLlm`
- 描述：`测试ODPS数据批量LLM处理`

### 3. 填写参数

#### 基础测试示例
```
odpsProject: trip_content
odpsTable: product_info
odpsPartition: ds='20240127'
columnsToGet: product_id,product_name,category,features
workflowAppId: 21
workflowFlowId: 1
streamOutputTask: llm_vDq0SH
llmPrompt: 根据商品信息生成吸引人的商品描述
taskType: product_description_generation
llmBatchSize: 5
batchSize: 1000
llmTimeoutSeconds: 120
singleRequestTimeoutSeconds: 30
```

#### 情感分析测试示例
```
odpsProject: trip_content
odpsTable: user_comments
odpsPartition: ds='20240127'
columnsToGet: comment_id,comment_text,rating
workflowAppId: 21
workflowFlowId: 2
streamOutputTask: sentiment_analysis_task
llmPrompt: 分析用户评论的情感倾向，返回正面、负面或中性
taskType: sentiment_analysis
llmBatchSize: 10
batchSize: 2000
llmTimeoutSeconds: 90
singleRequestTimeoutSeconds: 20
```

#### 旅游推荐测试示例
```
odpsProject: trip_vacation
odpsTable: poi_info
odpsPartition: ds='20240127',city='beijing'
columnsToGet: poi_id,poi_name,category,description,rating
workflowAppId: 21
workflowFlowId: 3
streamOutputTask: content_generation_task
llmPrompt: 根据景点信息生成吸引游客的推荐理由
taskType: travel_recommendation
llmBatchSize: 8
batchSize: 500
llmTimeoutSeconds: 150
singleRequestTimeoutSeconds: 25
```

## 输出结果说明

### 1. 执行状态
- 显示任务是否成功执行
- 如果失败，会显示具体错误信息

### 2. 数据统计
- **ODPS数据统计**：总记录数、已处理记录数
- **LLM批次统计**：总批次数、成功/失败批次数、批次成功率
- **LLM请求统计**：总请求数、成功/失败请求数、请求成功率
- **时间统计**：总耗时、实际耗时

### 3. 结果示例
- **成功响应示例**：显示前3个成功的LLM响应，包括内容预览和思考过程
- **失败响应示例**：显示前3个失败的LLM响应，包括错误信息

### 4. 完整JSON
- 输出完整的响应JSON，便于详细分析和调试

## 注意事项

### 1. 参数验证
- 必填参数不能为空
- ODPS项目名和表名必须存在且有访问权限
- 工作流AppId和FlowId必须有效

### 2. 性能考虑
- 建议先用小批次测试（如 `llmBatchSize=2`, `batchSize=100`）
- 根据数据量调整超时时间
- 注意ODPS表的数据量，避免处理过大数据集

### 3. 权限要求
- 需要ODPS表的读取权限
- 需要工作流的执行权限
- 需要大模型网关的调用权限

### 4. 调试模式
- 测试方法默认开启调试模式（`debugMode=true`）
- 会输出更详细的日志信息
- 便于问题排查和性能分析

## 常见问题

### Q1: 如何处理大数据量测试？
A: 建议分步骤测试：
1. 先测试小批次（如10条数据）
2. 逐步增加数据量
3. 监控性能和成功率

### Q2: 如何优化性能？
A: 可以调整以下参数：
- 增加 `llmBatchSize` 提高并发度
- 调整 `batchSize` 优化ODPS下载性能
- 根据网络情况调整超时时间

### Q3: 如何处理失败情况？
A: 查看输出的错误信息：
- ODPS连接失败：检查项目名、表名、分区信息
- 工作流调用失败：检查AppId、FlowId、权限
- LLM调用失败：检查提示词、超时设置

### Q4: 如何验证结果正确性？
A: 可以通过以下方式验证：
1. 查看成功响应示例的内容预览
2. 检查统计数据的合理性
3. 分析完整JSON响应
4. 对比预期结果

## 最佳实践

1. **渐进式测试**：从小数据量开始，逐步增加
2. **参数调优**：根据实际情况调整批次大小和超时时间
3. **结果验证**：仔细检查输出结果的合理性
4. **性能监控**：关注成功率和响应时间
5. **错误处理**：及时分析和处理失败情况
