package com.taobao.trip.jourprod.core.service.task;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 批量调用大模型网关使用示例
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Component
@Slf4j
public class BatchLlmGatewayExample {

    @Resource
    private BatchLlmGatewayService batchLlmGatewayService;

    /**
     * 示例：批量调用大模型进行旅游规划
     */
    public void exampleBatchTravelPlanning() {
        // 构建批量请求
        List<BatchLlmGatewayRequest.SingleRequest> requests = new ArrayList<>();

        // 请求1：北京旅游规划
        Map<String, Object> beijingParams = Maps.newHashMap();
        beijingParams.put("destination", "北京");
        beijingParams.put("days", 3);
        beijingParams.put("budget", 5000);
        beijingParams.put("interests", "历史文化,美食");

        BatchLlmGatewayRequest.SingleRequest beijingRequest = BatchLlmGatewayRequest.SingleRequest.builder()
                .requestId("beijing_travel_001")
                .appId(21L)
                .flowId(1L)
                .streamOutputTask("llm_vDq0SH")
                .inputParams(beijingParams)
                .timeoutSeconds(30L)
                .build();

        // 请求2：上海旅游规划
        Map<String, Object> shanghaiParams = Maps.newHashMap();
        shanghaiParams.put("destination", "上海");
        shanghaiParams.put("days", 2);
        shanghaiParams.put("budget", 3000);
        shanghaiParams.put("interests", "现代建筑,购物");

        BatchLlmGatewayRequest.SingleRequest shanghaiRequest = BatchLlmGatewayRequest.SingleRequest.builder()
                .requestId("shanghai_travel_002")
                .appId(21L)
                .flowId(1L)
                .streamOutputTask("llm_vDq0SH")
                .inputParams(shanghaiParams)
                .timeoutSeconds(30L)
                .build();

        // 请求3：杭州旅游规划
        Map<String, Object> hangzhouParams = Maps.newHashMap();
        hangzhouParams.put("destination", "杭州");
        hangzhouParams.put("days", 2);
        hangzhouParams.put("budget", 2500);
        hangzhouParams.put("interests", "自然风光,茶文化");

        BatchLlmGatewayRequest.SingleRequest hangzhouRequest = BatchLlmGatewayRequest.SingleRequest.builder()
                .requestId("hangzhou_travel_003")
                .appId(21L)
                .flowId(1L)
                .streamOutputTask("llm_vDq0SH")
                .inputParams(hangzhouParams)
                .timeoutSeconds(30L)
                .build();

        requests.add(beijingRequest);
        requests.add(shanghaiRequest);
        requests.add(hangzhouRequest);

        // 构建批量请求
        BatchLlmGatewayRequest batchRequest = BatchLlmGatewayRequest.builder()
                .requests(requests)
                .timeoutSeconds(60L)
                .build();

        // 执行批量调用
        BatchLlmGatewayResponse batchResponse = batchLlmGatewayService.batchCall(batchRequest);

        // 处理响应结果
        if (batchResponse.getSuccess()) {
            log.info("批量调用成功，共处理 {} 个请求", batchResponse.getResponses().size());

            for (BatchLlmGatewayResponse.SingleResponse response : batchResponse.getResponses()) {
                if (response.getSuccess()) {
                    log.info("请求 {} 成功，内容长度: {}, 耗时: {}ms",
                            response.getRequestId(),
                            response.getContent() != null ? response.getContent().length() : 0,
                            response.getCostMs());

                    // 这里可以进一步处理每个响应的内容
                    processResponse(response);
                } else {
                    log.error("请求 {} 失败: {}", response.getRequestId(), response.getErrorMessage());
                }
            }
        } else {
            log.error("批量调用失败: {}", batchResponse.getErrorMessage());
        }
    }

    /**
     * 示例：批量调用大模型进行文本分析
     */
    public void exampleBatchTextAnalysis() {
        List<BatchLlmGatewayRequest.SingleRequest> requests = new ArrayList<>();

        String[] texts = {
            "这次旅行真的太棒了，风景美丽，服务周到！",
            "酒店设施陈旧，服务态度一般，不太满意。",
            "导游很专业，行程安排合理，值得推荐！"
        };

        for (int i = 0; i < texts.length; i++) {
            Map<String, Object> params = Maps.newHashMap();
            params.put("text", texts[i]);
            params.put("task", "sentiment_analysis");

            BatchLlmGatewayRequest.SingleRequest request = BatchLlmGatewayRequest.SingleRequest.builder()
                    .requestId("text_analysis_" + (i + 1))
                    .appId(21L)
                    .flowId(2L) // 假设流程ID为2是文本分析流程
                    .streamOutputTask("llm_analysis")
                    .inputParams(params)
                    .timeoutSeconds(20L)
                    .build();

            requests.add(request);
        }

        BatchLlmGatewayRequest batchRequest = BatchLlmGatewayRequest.builder()
                .requests(requests)
                .timeoutSeconds(45L)
                .build();

        BatchLlmGatewayResponse batchResponse = batchLlmGatewayService.batchCall(batchRequest);

        // 处理分析结果
        if (batchResponse.getSuccess()) {
            log.info("文本分析批量调用成功");
            for (BatchLlmGatewayResponse.SingleResponse response : batchResponse.getResponses()) {
                if (response.getSuccess()) {
                    log.info("文本分析结果 {}: {}", response.getRequestId(), response.getContent());
                }
            }
        }
    }

    /**
     * 处理单个响应结果
     */
    private void processResponse(BatchLlmGatewayResponse.SingleResponse response) {
        // 根据requestId判断请求类型并进行相应处理
        String requestId = response.getRequestId();
        String content = response.getContent();
        String thinking = response.getThinking();

        if (requestId.startsWith("beijing_travel")) {
            log.info("北京旅游规划结果: {}", content);
            // 可以进一步解析内容，提取景点、路线等信息
        } else if (requestId.startsWith("shanghai_travel")) {
            log.info("上海旅游规划结果: {}", content);
        } else if (requestId.startsWith("hangzhou_travel")) {
            log.info("杭州旅游规划结果: {}", content);
        }

        if (thinking != null && !thinking.isEmpty()) {
            log.info("AI思考过程: {}", thinking);
        }
    }
}
