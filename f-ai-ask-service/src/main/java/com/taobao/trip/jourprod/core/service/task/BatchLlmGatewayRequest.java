package com.taobao.trip.jourprod.core.service.task;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 批量调用大模型网关请求
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchLlmGatewayRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 批量请求列表
     */
    private List<SingleRequest> requests;

    /**
     * 批量请求超时时间（秒），默认60秒
     */
    @Builder.Default
    private Long timeoutSeconds = 60L;

    /**
     * 单个请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SingleRequest implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 请求ID，用于标识和关联响应
         */
        private String requestId;

        /**
         * 应用ID
         */
        private Long appId;

        /**
         * 流程ID
         */
        private Long flowId;

        /**
         * 指定流式输出的节点
         */
        private String streamOutputTask;

        /**
         * 输入参数
         */
        private Map<String, Object> inputParams;

        /**
         * 单个请求超时时间（秒），默认30秒
         */
        @Builder.Default
        private Long timeoutSeconds = 30L;
    }
}
