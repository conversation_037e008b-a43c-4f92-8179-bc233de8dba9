package com.taobao.trip.jourprod.core.service.task;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 批量调用大模型网关响应
 * 
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchLlmGatewayResponse implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 批量请求是否成功
     */
    private Boolean success;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 单个响应列表
     */
    private List<SingleResponse> responses;
    
    /**
     * 批量请求总耗时（毫秒）
     */
    private Long totalCostMs;
    
    /**
     * 成功请求数量
     */
    private Integer successCount;
    
    /**
     * 失败请求数量
     */
    private Integer failureCount;
    
    /**
     * 扩展信息
     */
    private Map<String, Object> extInfo;
    
    /**
     * 单个响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SingleResponse implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 对应的请求ID
         */
        private String requestId;
        
        /**
         * 单个请求是否成功
         */
        private Boolean success;
        
        /**
         * 错误信息
         */
        private String errorMessage;
        
        /**
         * 大模型返回的内容
         */
        private String content;
        
        /**
         * 大模型的思考过程
         */
        private String thinking;
        
        /**
         * 请求开始时间戳
         */
        private Long startTime;
        
        /**
         * 请求结束时间戳
         */
        private Long endTime;
        
        /**
         * 请求耗时（毫秒）
         */
        private Long costMs;
        
        /**
         * 流式数据块数量
         */
        private Integer chunkCount;
        
        /**
         * 扩展响应数据
         */
        private Map<String, Object> extData;
        
        /**
         * 获取耗时
         */
        public Long getCostMs() {
            if (costMs != null) {
                return costMs;
            }
            if (startTime != null && endTime != null) {
                return endTime - startTime;
            }
            return null;
        }
    }
}
