package com.taobao.trip.jourprod.core.service.task;

import com.alibaba.fastjson.JSON;

import com.fliggy.fai.client.FaiResponse;
import com.fliggy.fai.client.workflow.cmd.WorkflowClientStartCmd;
import com.fliggy.fai.client.workflow.model.WorkflowRunLiteDTO;
import com.fliggy.fai.client.workflow.service.FaiWorkflowClientService;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;

import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 批量调用大模型网关服务
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Service
@Slf4j
public class BatchLlmGatewayService {

    private static final FliggyNewLogger logger = LogUtil.getFliggyNewLogger("BatchLlmGatewayService");

    @Resource
    private FaiWorkflowClientService faiWorkflowClientService;

    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    @AteyeInvoker(description = "批量调用大模型网关（同步版本）", paraDesc = "batchRequest")
    public void testBatchCall(String batchRequest) {
        // 构建测试请求
        BatchLlmGatewayRequest request = JSON.parseObject(batchRequest, BatchLlmGatewayRequest.class);
        BatchLlmGatewayResponse batchLlmGatewayResponse = batchCall(request);
        Ateye.out.println("========== 同步调用结果 ==========");
        Ateye.out.println(JSON.toJSONString(batchLlmGatewayResponse));
    }

    /**
     * 批量调用大模型网关
     *
     * @param batchRequest 批量请求
     * @return 批量响应结果
     */
    public BatchLlmGatewayResponse batchCall(BatchLlmGatewayRequest batchRequest) {
        long startTime = System.currentTimeMillis();
        logger.recordOutput(new LogModel("batchCall").request(JSON.toJSONString(batchRequest)));

        List<BatchLlmGatewayRequest.SingleRequest> requests = batchRequest.getRequests();
        if (requests == null || requests.isEmpty()) {
            return BatchLlmGatewayResponse.builder()
                    .success(false)
                    .errorMessage("请求列表不能为空")
                    .totalCostMs(System.currentTimeMillis() - startTime)
                    .successCount(0)
                    .failureCount(0)
                    .build();
        }

        List<CompletableFuture<BatchLlmGatewayResponse.SingleResponse>> futures = new ArrayList<>();

        // 并发执行多个请求
        for (int i = 0; i < requests.size(); i++) {
            final int index = i;
            final BatchLlmGatewayRequest.SingleRequest singleRequest = requests.get(i);

            CompletableFuture<BatchLlmGatewayResponse.SingleResponse> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return processSingleRequest(singleRequest, index);
                } catch (Exception e) {
                    logger.recordDangerException(new LogModel("batchCall_single_error")
                            .message("单个请求处理异常")
                            .request(JSON.toJSONString(singleRequest))
                            .e(e));
                    return BatchLlmGatewayResponse.SingleResponse.builder()
                            .requestId(singleRequest.getRequestId())
                            .success(false)
                            .errorMessage("处理异常: " + e.getMessage())
                            .startTime(System.currentTimeMillis())
                            .endTime(System.currentTimeMillis())
                            .build();
                }
            }, executorService);

            futures.add(future);
        }

        // 等待所有请求完成
        List<BatchLlmGatewayResponse.SingleResponse> responses = new ArrayList<>();
        try {
            for (CompletableFuture<BatchLlmGatewayResponse.SingleResponse> future : futures) {
                responses.add(future.get(batchRequest.getTimeoutSeconds(), TimeUnit.SECONDS));
            }
        } catch (Exception e) {
            logger.recordDangerException(new LogModel("batchCall_wait_error").e(e));
            return BatchLlmGatewayResponse.builder()
                    .success(false)
                    .errorMessage("批量请求等待超时或异常: " + e.getMessage())
                    .responses(responses)
                    .totalCostMs(System.currentTimeMillis() - startTime)
                    .build();
        }

        // 统计成功和失败数量
        int successCount = 0;
        int failureCount = 0;
        for (BatchLlmGatewayResponse.SingleResponse response : responses) {
            if (response.getSuccess()) {
                successCount++;
            } else {
                failureCount++;
            }
        }

        long totalCostMs = System.currentTimeMillis() - startTime;

        logger.recordOutput(new LogModel("batchCall_success")
                .message("批量调用完成")
                .response("总请求数: " + requests.size() + ", 完成数: " + responses.size() +
                         ", 成功数: " + successCount + ", 失败数: " + failureCount +
                         ", 总耗时: " + totalCostMs + "ms"));

        return BatchLlmGatewayResponse.builder()
                .success(true)
                .responses(responses)
                .totalCostMs(totalCostMs)
                .successCount(successCount)
                .failureCount(failureCount)
                .build();
    }

    /**
     * 处理单个请求
     */
    private BatchLlmGatewayResponse.SingleResponse processSingleRequest(
            BatchLlmGatewayRequest.SingleRequest singleRequest, int index) {

        long requestStartTime = System.currentTimeMillis();
        logger.recordOutput(new LogModel("processSingleRequest")
                .message("开始处理单个请求")
                .request(JSON.toJSONString(singleRequest)));

        try {
            // 创建流程启动命令
            WorkflowClientStartCmd cmd = new WorkflowClientStartCmd();
            cmd.setAppId(singleRequest.getAppId());
            cmd.setFlowId(singleRequest.getFlowId());

            // 设置输入参数
            if (singleRequest.getInputParams() != null) {
                cmd.setVariables(singleRequest.getInputParams());
            }

            logger.recordOutput(new LogModel("sync_call_start")
                    .message("开始同步调用工作流")
                    .request(JSON.toJSONString(singleRequest)));

            // 同步调用工作流
            FaiResponse<WorkflowRunLiteDTO> result = faiWorkflowClientService.start(cmd);

            long endTime = System.currentTimeMillis();

            // 处理同步调用结果
            if (result != null && result.getSuccess() && result.getData() != null && result.getData().getOutputs() != null) {
                logger.recordOutput(new LogModel("sync_call_success")
                        .message("同步调用工作流成功，耗时: " + (endTime - requestStartTime) + "ms")
                        .request(JSON.toJSONString(singleRequest))
                        .response(JSON.toJSONString(result)));

                // 从结果中提取内容
                String content = "";
                String thinking = "";
                // 尝试从输出中获取内容
                Object outputContent = result.getData().getOutputs().get("output");
                if (outputContent != null) {
                    content = outputContent.toString();
                }

                if (StringUtils.isNotBlank(content)) {
                    return BatchLlmGatewayResponse.SingleResponse.builder()
                        .requestId(singleRequest.getRequestId())
                        .success(true)
                        .content(content)
                        .thinking(thinking)
                        .startTime(requestStartTime)
                        .endTime(endTime)
                        .chunkCount(1) // 同步调用只有一次响应
                        .build();
                } else {
                    return BatchLlmGatewayResponse.SingleResponse.builder()
                        .requestId(singleRequest.getRequestId())
                        .success(false)
                        .errorMessage("工作流调用失败: content返回内容为空")
                        .startTime(requestStartTime)
                        .endTime(endTime)
                        .chunkCount(0)
                        .build();
                }
            } else {
                String errorMessage = result != null ? result.getErrorMessage() : "工作流调用失败";
                logger.recordOutput(new LogModel("sync_call_fail")
                        .message("同步调用工作流失败")
                        .request(JSON.toJSONString(singleRequest))
                        .response("错误信息: " + errorMessage));

                return BatchLlmGatewayResponse.SingleResponse.builder()
                        .requestId(singleRequest.getRequestId())
                        .success(false)
                        .errorMessage("工作流调用失败: " + errorMessage)
                        .startTime(requestStartTime)
                        .endTime(endTime)
                        .chunkCount(0)
                        .build();
            }

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            logger.recordDangerException(new LogModel("processSingleRequest_error")
                    .message("处理单个请求异常")
                    .request(JSON.toJSONString(singleRequest))
                    .e(e));

            return BatchLlmGatewayResponse.SingleResponse.builder()
                    .requestId(singleRequest.getRequestId())
                    .success(false)
                    .errorMessage("处理异常: " + e.getMessage())
                    .startTime(requestStartTime)
                    .endTime(endTime)
                    .chunkCount(0)
                    .build();
        }
    }
}
