package com.taobao.trip.jourprod.core.service.task;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 批量调用大模型网关服务测试类
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Component
@Slf4j
public class BatchLlmGatewayServiceTest {

    @Resource
    private BatchLlmGatewayService batchLlmGatewayService;

    /**
     * 测试批量调用
     */
    public void testBatchCall() {
        log.info("开始测试批量调用大模型网关");

        // 构建测试请求
        List<BatchLlmGatewayRequest.SingleRequest> requests = createTestRequests();

        BatchLlmGatewayRequest batchRequest = BatchLlmGatewayRequest.builder()
                .requests(requests)
                .timeoutSeconds(60L)
                .build();

        // 执行批量调用
        long startTime = System.currentTimeMillis();
        BatchLlmGatewayResponse response = batchLlmGatewayService.batchCall(batchRequest);
        long endTime = System.currentTimeMillis();

        // 输出结果
        log.info("批量调用完成，总耗时: {}ms", endTime - startTime);
        log.info("批量调用结果: success={}, totalCostMs={}, successCount={}, failureCount={}",
                response.getSuccess(), response.getTotalCostMs(),
                response.getSuccessCount(), response.getFailureCount());

        if (response.getResponses() != null) {
            for (BatchLlmGatewayResponse.SingleResponse singleResponse : response.getResponses()) {
                log.info("单个响应: requestId={}, success={}, costMs={}, chunkCount={}, contentLength={}",
                        singleResponse.getRequestId(), singleResponse.getSuccess(),
                        singleResponse.getCostMs(), singleResponse.getChunkCount(),
                        singleResponse.getContent() != null ? singleResponse.getContent().length() : 0);

                if (!singleResponse.getSuccess()) {
                    log.error("请求失败: {}", singleResponse.getErrorMessage());
                } else {
                    log.info("请求成功，内容预览: {}",
                            truncateString(singleResponse.getContent(), 100));
                    if (singleResponse.getThinking() != null && !singleResponse.getThinking().isEmpty()) {
                        log.info("思考过程预览: {}",
                                truncateString(singleResponse.getThinking(), 100));
                    }
                }
            }
        }
    }

    /**
     * 创建测试请求
     */
    private List<BatchLlmGatewayRequest.SingleRequest> createTestRequests() {
        List<BatchLlmGatewayRequest.SingleRequest> requests = new ArrayList<>();

        // 请求1：简单问答
        Map<String, Object> params1 = Maps.newHashMap();
        params1.put("question", "请介绍一下北京的著名景点");
        params1.put("max_tokens", 500);

        BatchLlmGatewayRequest.SingleRequest request1 = BatchLlmGatewayRequest.SingleRequest.builder()
                .requestId("test_001")
                .appId(21L)
                .flowId(1L)
                .streamOutputTask("llm_vDq0SH")
                .inputParams(params1)
                .timeoutSeconds(30L)
                .build();

        // 请求2：旅游规划
        Map<String, Object> params2 = Maps.newHashMap();
        params2.put("destination", "上海");
        params2.put("days", 2);
        params2.put("budget", 3000);
        params2.put("interests", "现代建筑,购物,美食");

        BatchLlmGatewayRequest.SingleRequest request2 = BatchLlmGatewayRequest.SingleRequest.builder()
                .requestId("test_002")
                .appId(21L)
                .flowId(1L)
                .streamOutputTask("llm_vDq0SH")
                .inputParams(params2)
                .timeoutSeconds(30L)
                .build();

        // 请求3：文本分析
        Map<String, Object> params3 = Maps.newHashMap();
        params3.put("text", "这次旅行体验非常好，导游专业，景点安排合理，酒店舒适，强烈推荐！");
        params3.put("task", "sentiment_analysis");

        BatchLlmGatewayRequest.SingleRequest request3 = BatchLlmGatewayRequest.SingleRequest.builder()
                .requestId("test_003")
                .appId(21L)
                .flowId(1L)
                .streamOutputTask("llm_vDq0SH")
                .inputParams(params3)
                .timeoutSeconds(20L)
                .build();

        requests.add(request1);
        requests.add(request2);
        requests.add(request3);

        return requests;
    }

    /**
     * 截断字符串用于日志输出
     */
    private String truncateString(String str, int maxLength) {
        if (str == null) {
            return null;
        }
        if (str.length() <= maxLength) {
            return str;
        }
        return str.substring(0, maxLength) + "...";
    }

    /**
     * 测试单个请求
     */
    public void testSingleRequest() {
        log.info("开始测试单个请求");

        Map<String, Object> params = Maps.newHashMap();
        params.put("question", "请简单介绍一下杭州西湖");

        BatchLlmGatewayRequest.SingleRequest singleRequest = BatchLlmGatewayRequest.SingleRequest.builder()
                .requestId("single_test_001")
                .appId(21L)
                .flowId(1L)
                .streamOutputTask("llm_vDq0SH")
                .inputParams(params)
                .timeoutSeconds(30L)
                .build();

        List<BatchLlmGatewayRequest.SingleRequest> requests = new ArrayList<>();
        requests.add(singleRequest);

        BatchLlmGatewayRequest batchRequest = BatchLlmGatewayRequest.builder()
                .requests(requests)
                .timeoutSeconds(45L)
                .build();

        BatchLlmGatewayResponse response = batchLlmGatewayService.batchCall(batchRequest);

        log.info("单个请求测试完成: success={}, totalCostMs={}",
                response.getSuccess(), response.getTotalCostMs());

        if (response.getResponses() != null && !response.getResponses().isEmpty()) {
            BatchLlmGatewayResponse.SingleResponse singleResponse = response.getResponses().get(0);
            log.info("响应结果: success={}, costMs={}, contentLength={}",
                    singleResponse.getSuccess(), singleResponse.getCostMs(),
                    singleResponse.getContent() != null ? singleResponse.getContent().length() : 0);

            if (singleResponse.getSuccess()) {
                log.info("响应内容: {}", singleResponse.getContent());
            } else {
                log.error("请求失败: {}", singleResponse.getErrorMessage());
            }
        }
    }
}
