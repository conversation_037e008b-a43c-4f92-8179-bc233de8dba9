# 依赖修复总结

## 修复的问题

### 1. ExcelExportUtil 依赖问题
**问题**：原代码使用了 Apache POI 库来生成 Excel 文件，但项目中可能没有相关依赖。

**解决方案**：
- 将 Excel 导出改为 CSV 格式导出
- CSV 文件可以被 Excel 直接打开，功能基本等同
- 使用标准 Java IO 库，无需额外依赖

**修改内容**：
- 移除 Apache POI 相关导入
- 重写文件生成逻辑，使用 `FileWriter` 生成 CSV
- 添加 CSV 格式处理（转义逗号、引号、换行符）
- 添加 UTF-8 BOM 确保 Excel 正确识别中文

### 2. LlmContentParser JSONPath 依赖问题
**问题**：原代码使用了 `com.jayway.jsonpath.JsonPath` 库，但项目中可能没有相关依赖。

**解决方案**：
- 使用项目中已有的 FastJSON 库替代 JSONPath
- 实现自定义的字段提取方法

**修改内容**：
- 移除 JSONPath 相关导入
- 使用 `JSONObject` 直接解析和提取字段
- 实现 `extractFieldFromJson` 和 `extractNestedField` 方法
- 支持嵌套字段提取（如 `basic.title`）

## 修复后的功能

### 1. CSV 文件导出
- **文件格式**：CSV（Excel 兼容）
- **编码**：UTF-8 with BOM
- **字段分隔符**：逗号
- **特殊字符处理**：自动转义逗号、引号、换行符
- **文件位置**：`/tmp/llm_results_yyyyMMdd_HHmmss.csv`

### 2. 字段提取
支持从输入参数中提取以下字段：
- `id`：直接从根级别提取
- `originalTitle`：从 `basic.title` 路径提取
- `originalShortTitle`：从 `basic.shortTitle` 路径提取

### 3. 大模型内容解析
支持多种格式的大模型返回内容解析：
- 标准 markdown 格式：````json ... ```
- 大写 markdown 格式：````JSON ... ```
- 简单 markdown 格式：```` ... ```
- JSON 代码块：查找 `{...}` 内容
- 直接 JSON：整个内容作为 JSON 解析

## 使用方法

### 1. Ateye 测试
```
方法名：ateyeProcessOdpsDataWithLlm
参数示例：
- odpsProject: trip_content
- odpsTable: product_info
- odpsPartition: ds='20240127'
- columnsToGet: id,basic
- workflowAppId: 21
- workflowFlowId: 1
- streamOutputTask: llm_vDq0SH
- llmPrompt: 根据商品信息生成标题和卖点
- taskType: product_generation
```

### 2. 输入数据格式
ODPS 数据应包含以下结构：
```json
{
  "id": "12345",
  "basic": {
    "title": "原始商品标题",
    "shortTitle": "原始短标题"
  },
  "other_fields": "..."
}
```

### 3. 大模型返回格式
期望的大模型返回格式：
```
```json
{
  "title": "生成的新标题",
  "benefit": "生成的卖点描述"
}
```
```

### 4. 输出文件
生成的 CSV 文件包含以下列：
- ID：商品ID
- Original Title：原始标题
- Original Short Title：原始短标题
- Generated Title：生成的标题
- Generated Benefit：生成的卖点
- Request ID：请求标识
- Status：处理状态
- Error Message：错误信息（如果有）
- Cost (ms)：处理耗时
- Raw Content：原始大模型返回内容

## 注意事项

1. **文件权限**：确保应用有 `/tmp` 目录的写权限
2. **文件大小**：大量数据可能生成较大的 CSV 文件
3. **编码问题**：CSV 文件使用 UTF-8 with BOM，确保 Excel 正确显示中文
4. **数据格式**：确保 ODPS 数据包含期望的字段结构
5. **大模型格式**：确保大模型返回符合期望的 JSON 格式

## 兼容性

- ✅ 无需额外依赖
- ✅ 使用项目现有的 FastJSON 库
- ✅ 使用标准 Java IO 库
- ✅ CSV 格式兼容 Excel
- ✅ 支持中文字符
- ✅ 支持特殊字符转义
