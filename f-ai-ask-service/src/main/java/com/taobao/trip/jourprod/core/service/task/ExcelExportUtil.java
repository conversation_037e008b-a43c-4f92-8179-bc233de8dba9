package com.taobao.trip.jourprod.core.service.task;

import com.alibaba.fastjson.JSON;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * Excel导出工具类（使用CSV格式）
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Component
@Slf4j
public class ExcelExportUtil {

    private static final FliggyNewLogger logger = LogUtil.getFliggyNewLogger("ExcelExportUtil");

    /**
     * 导出LLM结果到CSV文件（Excel兼容格式）
     *
     * @param resultRows 结果数据
     * @param fileName 文件名（可选，为空时自动生成）
     * @return 生成的文件路径
     */
    public String exportToExcel(List<LlmResultRow> resultRows, String fileName) {
        if (resultRows == null || resultRows.isEmpty()) {
            logger.recordOutput(new LogModel("exportToExcel_empty")
                    .message("没有数据需要导出"));
            return null;
        }

        // 生成文件名
        if (StringUtils.isBlank(fileName)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss");
            fileName = "llm_results_" + sdf.format(new Date()) + ".csv";
        }

        // 确保文件名以.csv结尾
        if (!fileName.endsWith(".csv")) {
            fileName += ".csv";
        }

        String filePath = "/tmp/" + fileName;

        try {
            createCsvFile(resultRows, filePath);

            logger.recordOutput(new LogModel("exportToExcel_success")
                    .message("CSV文件导出成功")
                    .response("文件路径: " + filePath + ", 数据行数: " + resultRows.size()));

            return filePath;

        } catch (Exception e) {
            logger.recordDangerException(new LogModel("exportToExcel_error")
                    .message("CSV文件导出失败")
                    .request("文件路径: " + filePath)
                    .e(e));
            return null;
        }
    }

    /**
     * 创建CSV文件
     */
    private void createCsvFile(List<LlmResultRow> resultRows, String filePath) throws IOException {
        try (FileWriter writer = new FileWriter(filePath, false)) {
            // 写入BOM，确保Excel能正确识别UTF-8编码
            writer.write('\ufeff');

            // 写入表头
            writeHeader(writer);

            // 写入数据行
            writeDataRows(writer, resultRows);
        }
    }

    /**
     * 写入表头
     */
    private void writeHeader(FileWriter writer) throws IOException {
        String[] headers = {
            "ID",
            "Original Title",
            "Original Short Title",
            "cateId",
            "cateName",
            "catePath",
            "Generated Title",
            "Generated Benefit",
            "Request ID",
            "Status",
            "Error Message",
            "Cost (ms)",
            "Raw Content"
        };

        writer.write(String.join(",", headers));
        writer.write("\n");
    }

    /**
     * 写入数据行
     */
    private void writeDataRows(FileWriter writer, List<LlmResultRow> resultRows) throws IOException {
        for (LlmResultRow row : resultRows) {
            String[] values = {
                escapeCsvValue(row.getId()),
                escapeCsvValue(row.getOriginalTitle()),
                escapeCsvValue(row.getOriginalShortTitle()),
                escapeCsvValue(row.getCateId()),
                escapeCsvValue(row.getCateName()),
                escapeCsvValue(row.getCatePath()),
                escapeCsvValue(row.getTitle()),
                escapeCsvValue(row.getBenefit()),
                escapeCsvValue(row.getRequestId()),
                escapeCsvValue(row.getStatus()),
                escapeCsvValue(row.getErrorMessage()),
                row.getCostMs() != null ? row.getCostMs().toString() : "",
                escapeCsvValue(truncateContent(row.getRawContent(), 1000))
            };

            writer.write(String.join(",", values));
            writer.write("\n");
        }
    }

    /**
     * 转义CSV值，处理包含逗号、引号、换行符的情况
     */
    private String escapeCsvValue(String value) {
        if (value == null) {
            return "";
        }

        // 如果包含逗号、引号或换行符，需要用引号包围并转义内部引号
        if (value.contains(",") || value.contains("\"") || value.contains("\n") || value.contains("\r")) {
            // 转义引号：将 " 替换为 ""
            String escaped = value.replace("\"", "\"\"");
            return "\"" + escaped + "\"";
        }

        return value;
    }

    /**
     * 截断内容
     */
    private String truncateContent(String content, int maxLength) {
        if (StringUtils.isBlank(content)) {
            return "";
        }

        if (content.length() <= maxLength) {
            return content;
        }

        return content.substring(0, maxLength) + "...";
    }
}
