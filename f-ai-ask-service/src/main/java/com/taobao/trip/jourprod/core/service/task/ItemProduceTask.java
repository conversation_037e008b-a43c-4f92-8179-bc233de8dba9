package com.taobao.trip.jourprod.core.service.task;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import com.taobao.trip.jourprod.odps.DataFlowCallback;
import com.taobao.trip.jourprod.odps.OdpsTableBO;
import com.taobao.trip.jourprod.odps.OdpsUpDownloadManager;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @Description 商品信息生产任务 - 从ODPS下载数据并批量调用大模型
 * <AUTHOR>
 * @Date 2025/5/25
 **/
@Service
@Slf4j
public class ItemProduceTask {

    private static final FliggyNewLogger logger = LogUtil.getFliggyNewLogger("ItemProduceTask");

    @Resource
    private OdpsUpDownloadManager odpsUpDownloadManager;

    @Resource
    private BatchLlmGatewayService batchLlmGatewayService;

    @Resource
    private LlmContentParser llmContentParser;

    @Resource
    private ExcelExportUtil excelExportUtil;

    @Resource
    private OSSUtil ossUtil;

    /**
     * 内部处理结果类，包含响应和输入参数映射
     */
    private static class ProcessResult {
        final ItemProduceTaskResponse response;
        final Map<String, Map<String, Object>> requestInputParamsMap;

        ProcessResult(ItemProduceTaskResponse response, Map<String, Map<String, Object>> requestInputParamsMap) {
            this.response = response;
            this.requestInputParamsMap = requestInputParamsMap;
        }
    }

    /**
     * 从ODPS下载数据并批量调用大模型处理
     *
     * @param request 处理请求
     * @return 处理结果
     */
    public ItemProduceTaskResponse processOdpsDataWithLlm(ItemProduceTaskRequest request) {
        logger.recordOutput(new LogModel("processOdpsDataWithLlm")
                .message("开始处理ODPS数据")
                .request(JSON.toJSONString(request)));

        long startTime = System.currentTimeMillis();

        try {
            // 参数校验
            if (!validateRequest(request)) {
                return ItemProduceTaskResponse.builder()
                        .success(false)
                        .errorMessage("请求参数校验失败")
                        .build();
            }

            // 构建ODPS表配置
            OdpsTableBO tableBO = OdpsTableBO.buildTable(
                    request.getOdpsProject(),
                    request.getOdpsTable(),
                    request.getOdpsPartition()
            );

            // 创建数据处理回调
            OdpsDataProcessor processor = new OdpsDataProcessor(request);

            // 下载ODPS数据
            logger.recordOutput(new LogModel("downloadOdpsData")
                    .message("开始下载ODPS数据")
                    .request(JSON.toJSONString(tableBO)));

            long totalRecords = odpsUpDownloadManager.download(
                    tableBO,
                    request.getColumnsToGet(),
                    processor,
                    request.getBatchSize()
            );

            // 处理剩余数据
            processor.processRemainingData();

            long endTime = System.currentTimeMillis();

            ItemProduceTaskResponse response = ItemProduceTaskResponse.builder()
                    .success(true)
                    .totalRecords(totalRecords)
                    .processedRecords(processor.getProcessedCount())
                    .successfulBatches(processor.getSuccessfulBatches())
                    .failedBatches(processor.getFailedBatches())
                    .totalCostMs(endTime - startTime)
                    .llmResponses(processor.getAllResponses())
                    .build();

            logger.recordOutput(new LogModel("processOdpsDataWithLlm_success")
                    .message("ODPS数据处理完成")
                    .response(JSON.toJSONString(response)));

            return response;

        } catch (Exception e) {
            logger.recordDangerException(new LogModel("processOdpsDataWithLlm_error")
                    .message("处理ODPS数据异常")
                    .request(JSON.toJSONString(request))
                    .e(e));

            return ItemProduceTaskResponse.builder()
                    .success(false)
                    .errorMessage("处理异常: " + e.getMessage())
                    .totalCostMs(System.currentTimeMillis() - startTime)
                    .build();
        }
    }

    /**
     * 校验请求参数
     */
    private boolean validateRequest(ItemProduceTaskRequest request) {
        if (request == null) {
            logger.recordOutput(new LogModel("validateRequest").message("请求对象为空"));
            return false;
        }

        if (request.getOdpsProject() == null || request.getOdpsProject().trim().isEmpty()) {
            logger.recordOutput(new LogModel("validateRequest").message("ODPS项目名为空"));
            return false;
        }

        if (request.getOdpsTable() == null || request.getOdpsTable().trim().isEmpty()) {
            logger.recordOutput(new LogModel("validateRequest").message("ODPS表名为空"));
            return false;
        }

        if (request.getWorkflowAppId() == null || request.getWorkflowFlowId() == null) {
            logger.recordOutput(new LogModel("validateRequest").message("工作流AppId或FlowId为空"));
            return false;
        }

        return true;
    }

    /**
     * 内部方法：从ODPS下载数据并批量调用大模型处理（返回输入参数映射）
     */
    private ProcessResult processOdpsDataWithLlmInternal(ItemProduceTaskRequest request) {
        logger.recordOutput(new LogModel("processOdpsDataWithLlmInternal")
                .message("开始处理ODPS数据（内部方法）")
                .request(JSON.toJSONString(request)));

        long startTime = System.currentTimeMillis();

        try {
            // 参数校验
            if (!validateRequest(request)) {
                ItemProduceTaskResponse response = ItemProduceTaskResponse.builder()
                        .success(false)
                        .errorMessage("请求参数校验失败")
                        .build();
                return new ProcessResult(response, new HashMap<>());
            }

            // 构建ODPS表配置
            OdpsTableBO tableBO = OdpsTableBO.buildTable(
                    request.getOdpsProject(),
                    request.getOdpsTable(),
                    request.getOdpsPartition()
            );

            // 创建数据处理回调
            OdpsDataProcessor processor = new OdpsDataProcessor(request);

            // 下载ODPS数据
            logger.recordOutput(new LogModel("downloadOdpsDataInternal")
                    .message("开始下载ODPS数据（内部方法）")
                    .request(JSON.toJSONString(tableBO)));

            long totalRecords = odpsUpDownloadManager.download(
                    tableBO,
                    request.getColumnsToGet(),
                    processor,
                    request.getBatchSize()
            );

            // 处理剩余数据
            processor.processRemainingData();

            long endTime = System.currentTimeMillis();

            ItemProduceTaskResponse response = ItemProduceTaskResponse.builder()
                    .success(true)
                    .totalRecords(totalRecords)
                    .processedRecords(processor.getProcessedCount())
                    .successfulBatches(processor.getSuccessfulBatches())
                    .failedBatches(processor.getFailedBatches())
                    .totalCostMs(endTime - startTime)
                    .llmResponses(processor.getAllResponses())
                    .build();

            logger.recordOutput(new LogModel("processOdpsDataWithLlmInternal_success")
                    .message("ODPS数据处理完成（内部方法）")
                    .response(JSON.toJSONString(response)));

            return new ProcessResult(response, processor.getRequestInputParamsMap());

        } catch (Exception e) {
            logger.recordDangerException(new LogModel("processOdpsDataWithLlmInternal_error")
                    .message("处理ODPS数据异常（内部方法）")
                    .request(JSON.toJSONString(request))
                    .e(e));

            ItemProduceTaskResponse response = ItemProduceTaskResponse.builder()
                    .success(false)
                    .errorMessage("处理异常: " + e.getMessage())
                    .totalCostMs(System.currentTimeMillis() - startTime)
                    .build();

            return new ProcessResult(response, new HashMap<>());
        }
    }

    /**
     * ODPS数据处理器 - 实现DataFlowCallback接口
     */
    private class OdpsDataProcessor implements DataFlowCallback {

        private final ItemProduceTaskRequest request;
        private final List<Map<String, Object>> allData; // 存储所有接收到的数据
        private final AtomicLong processedCount;
        private final AtomicLong successfulBatches;
        private final AtomicLong failedBatches;
        private final List<BatchLlmGatewayResponse> allResponses;
        private final Map<String, Map<String, Object>> requestInputParamsMap; // 保存请求ID到输入参数的映射
        private long totalRecords;

        public OdpsDataProcessor(ItemProduceTaskRequest request) {
            this.request = request;
            this.allData = new ArrayList<>(); // 改为存储所有数据
            this.processedCount = new AtomicLong(0);
            this.successfulBatches = new AtomicLong(0);
            this.failedBatches = new AtomicLong(0);
            this.allResponses = new ArrayList<>();
            this.requestInputParamsMap = new HashMap<>();
        }

        @Override
        public void stream(List<Map<String, Object>> dataList) {
            if (CollectionUtils.isEmpty(dataList)) {
                return;
            }

            logger.recordOutput(new LogModel("odpsDataStream")
                    .message("接收到ODPS数据流")
                    .request("数据条数: " + dataList.size()));

            // 只收集数据，不立即处理
            allData.addAll(dataList);
            processedCount.addAndGet(dataList.size());

            logger.recordOutput(new LogModel("odpsDataCollected")
                    .message("数据收集中")
                    .request("当前已收集数据条数: " + allData.size()));

        }

        @Override
        public void total(long total) {
            this.totalRecords = total;
            logger.recordOutput(new LogModel("odpsDataTotal")
                    .message("ODPS数据总量")
                    .request("总记录数: " + total));
        }

        @Override
        public void success(int code, String message, Object data) {
            logger.recordOutput(new LogModel("odpsDownloadSuccess")
                    .message("ODPS数据下载成功")
                    .response("code: " + code + ", message: " + message + ", data: " + data));
        }

        @Override
        public void fail(int code, String message, Object data) {
            logger.recordDangerException(new LogModel("odpsDownloadFail")
                    .message("ODPS数据下载失败")
                    .response("code: " + code + ", message: " + message + ", data: " + data));
        }

        /**
         * 处理所有收集到的数据 - 按llmBatchSize分批处理
         */
        public void processRemainingData() {
            if (allData.isEmpty()) {
                logger.recordOutput(new LogModel("processRemainingData")
                        .message("没有数据需要处理"));
                return;
            }

            logger.recordOutput(new LogModel("processRemainingData")
                    .message("开始分批处理所有数据")
                    .request("总数据条数: " + allData.size() + ", LLM批次大小: " + request.getLlmBatchSize()));

            // 按llmBatchSize分批处理
            int batchSize = request.getLlmBatchSize();
            int totalBatches = (int) Math.ceil((double) allData.size() / batchSize);

            logger.recordOutput(new LogModel("batchProcessingPlan")
                    .message("分批处理计划")
                    .request("总批次数: " + totalBatches + ", 每批大小: " + batchSize));

            for (int i = 0; i < totalBatches; i++) {
                int startIndex = i * batchSize;
                int endIndex = Math.min(startIndex + batchSize, allData.size());

                List<Map<String, Object>> currentBatch = allData.subList(startIndex, endIndex);

                logger.recordOutput(new LogModel("processingBatch")
                        .message("处理第 " + (i + 1) + "/" + totalBatches + " 批")
                        .request("批次数据条数: " + currentBatch.size()));

                processBatchWithLlm(currentBatch, i + 1);
            }

            logger.recordOutput(new LogModel("allBatchesCompleted")
                    .message("所有批次处理完成")
                    .request("总批次数: " + totalBatches));
        }

        /**
         * 使用LLM处理指定批次数据
         */
        private void processBatchWithLlm(List<Map<String, Object>> batchData, int batchNumber) {
            if (batchData.isEmpty()) {
                return;
            }

            try {
                logger.recordOutput(new LogModel("llmBatchStart")
                        .message("开始LLM批次处理")
                        .request("批次号: " + batchNumber + ", 数据条数: " + batchData.size()));

                // 构建LLM批量请求
                List<BatchLlmGatewayRequest.SingleRequest> llmRequests = buildLlmRequestsWithMapping(batchData, request, requestInputParamsMap);

                BatchLlmGatewayRequest batchRequest = BatchLlmGatewayRequest.builder()
                        .requests(llmRequests)
                        .timeoutSeconds(request.getLlmTimeoutSeconds())
                        .build();

                // 调用LLM批量处理
                BatchLlmGatewayResponse response = batchLlmGatewayService.batchCall(batchRequest);

                // 记录响应
                allResponses.add(response);

                if (response.getSuccess()) {
                    successfulBatches.incrementAndGet();
                    logger.recordOutput(new LogModel("llmBatchSuccess")
                            .message("LLM批次处理成功")
                            .response("批次号: " + batchNumber +
                                     ", 成功数: " + response.getSuccessCount() +
                                     ", 失败数: " + response.getFailureCount()));
                } else {
                    failedBatches.incrementAndGet();
                    logger.recordOutput(new LogModel("llmBatchFail")
                            .message("LLM批次处理失败")
                            .response("批次号: " + batchNumber +
                                     ", 错误信息: " + response.getErrorMessage()));
                }

            } catch (Exception e) {
                failedBatches.incrementAndGet();
                logger.recordDangerException(new LogModel("processBatchWithLlm_error")
                        .message("LLM批次处理异常")
                        .request("批次号: " + batchNumber)
                        .e(e));
            }
        }

        // Getter方法
        public long getProcessedCount() {
            return processedCount.get();
        }

        public long getSuccessfulBatches() {
            return successfulBatches.get();
        }

        public long getFailedBatches() {
            return failedBatches.get();
        }

        public List<BatchLlmGatewayResponse> getAllResponses() {
            return new ArrayList<>(allResponses);
        }

        public Map<String, Map<String, Object>> getRequestInputParamsMap() {
            return new HashMap<>(requestInputParamsMap);
        }
    }

    /**
     * 根据ODPS数据构建LLM请求（带输入参数映射）
     */
    private List<BatchLlmGatewayRequest.SingleRequest> buildLlmRequestsWithMapping(
            List<Map<String, Object>> dataList,
            ItemProduceTaskRequest request,
            Map<String, Map<String, Object>> requestInputParamsMap) {
        List<BatchLlmGatewayRequest.SingleRequest> requests = new ArrayList<>();

        for (int i = 0; i < dataList.size(); i++) {
            Map<String, Object> dataRow = dataList.get(i);

            // 构建LLM输入参数
            Map<String, Object> inputParams = Maps.newHashMap();

            // 将ODPS数据作为输入参数传递给LLM
            inputParams.putAll(dataRow);

            // 添加额外的处理指令
            if (request.getLlmPrompt() != null) {
                inputParams.put("prompt", request.getLlmPrompt());
            }

            // 添加任务类型
            if (request.getTaskType() != null) {
                inputParams.put("task_type", request.getTaskType());
            }

            // 添加其他自定义参数
            if (request.getExtraParams() != null) {
                inputParams.putAll(request.getExtraParams());
            }

            // 生成请求ID（确保全局唯一性）
            String requestId = "odps_item_" + System.currentTimeMillis() + "_" + System.nanoTime() + "_" + i;

            // 保存输入参数映射关系
            requestInputParamsMap.put(requestId, new HashMap<>(inputParams));

            // 构建单个LLM请求
            BatchLlmGatewayRequest.SingleRequest llmRequest = BatchLlmGatewayRequest.SingleRequest.builder()
                    .requestId(requestId)
                    .appId(request.getWorkflowAppId())
                    .flowId(request.getWorkflowFlowId())
                    .streamOutputTask(request.getStreamOutputTask())
                    .inputParams(inputParams)
                    .timeoutSeconds(request.getSingleRequestTimeoutSeconds())
                    .build();

            requests.add(llmRequest);
        }

        return requests;
    }

    /**
     * Ateye测试方法：从ODPS下载数据并批量调用大模型处理
     *
     * @param odpsProject ODPS项目名
     * @param odpsTable ODPS表名
     * @param odpsPartition ODPS分区信息，如: ds='20240127'
     * @param columnsToGet 需要获取的列名，多个列用逗号分隔，为空时获取所有列
     * @param workflowAppId 工作流应用ID
     * @param workflowFlowId 工作流流程ID
     * @param streamOutputTask 流式输出任务节点名称
     * @param llmPrompt LLM处理提示词
     * @param taskType 任务类型
     * @param llmBatchSize LLM批次大小，默认5
     * @param batchSize ODPS下载批次大小，默认1000
     * @param llmTimeoutSeconds LLM批量请求超时时间（秒），默认120
     * @param singleRequestTimeoutSeconds 单个LLM请求超时时间（秒），默认30
     */
    @AteyeInvoker(description = "测试ODPS数据批量LLM处理",
                  paraDesc = "odpsProject&odpsTable&odpsPartition&columnsToGet&workflowAppId&workflowFlowId&streamOutputTask&llmPrompt&taskType&llmBatchSize&batchSize&llmTimeoutSeconds&singleRequestTimeoutSeconds")
    public void ateyeProcessOdpsDataWithLlm(String odpsProject,
                                           String odpsTable,
                                           String odpsPartition,
                                           String columnsToGet,
                                           Long workflowAppId,
                                           Long workflowFlowId,
                                           String streamOutputTask,
                                           String llmPrompt,
                                           String taskType,
                                           Integer llmBatchSize,
                                           Integer batchSize,
                                           Long llmTimeoutSeconds,
                                           Long singleRequestTimeoutSeconds) {

        Ateye.out.println("========== 开始执行ODPS数据批量LLM处理测试 ==========");

        try {
            // 构建请求参数
            ItemProduceTaskRequest.ItemProduceTaskRequestBuilder requestBuilder = ItemProduceTaskRequest.builder()
                    // ODPS配置
                    .odpsProject(odpsProject)
                    .odpsTable(odpsTable)
                    .odpsPartition(odpsPartition)
                    .batchSize(batchSize != null ? batchSize : 10)

                    // 工作流配置
                    .workflowAppId(workflowAppId)
                    .workflowFlowId(workflowFlowId)
                    .streamOutputTask(streamOutputTask)

                    // LLM配置
                    .llmBatchSize(llmBatchSize != null ? llmBatchSize : 10)
                    .llmTimeoutSeconds(llmTimeoutSeconds != null ? llmTimeoutSeconds : 600L)
                    .singleRequestTimeoutSeconds(singleRequestTimeoutSeconds != null ? singleRequestTimeoutSeconds : 500L)

                    // 任务配置
                    .taskType(taskType)
                    .llmPrompt(llmPrompt)
                    .description("Ateye测试任务")
                    .debugMode(true);

            // 处理列名参数
            if (StringUtils.isNotBlank(columnsToGet)) {
                String[] columns = columnsToGet.split(",");
                List<String> columnList = Lists.newArrayList();
                for (String column : columns) {
                    if (StringUtils.isNotBlank(column.trim())) {
                        columnList.add(column.trim());
                    }
                }
                requestBuilder.columnsToGet(columnList);
            }

            ItemProduceTaskRequest request = requestBuilder.build();

            Ateye.out.println("请求参数: " + JSON.toJSONString(request));
            Ateye.out.println("开始处理...");

            // 执行处理（使用内部方法获取输入参数映射）
            long startTime = System.currentTimeMillis();
            ProcessResult processResult = processOdpsDataWithLlmInternal(request);
            ItemProduceTaskResponse response = processResult.response;
            Map<String, Map<String, Object>> requestInputParamsMap = processResult.requestInputParamsMap;

            // 解析结果并生成Excel
            if (response.getSuccess() && response.getLlmResponses() != null) {
                Ateye.out.println("========== 开始解析结果并生成Excel ==========");

                List<LlmResultRow> resultRows = parseAndBuildResultRows(response, requestInputParamsMap);

                if (!resultRows.isEmpty()) {
                    // 生成Excel文件
                    excelExportUtil.exportToExcel(resultRows, null);
                }
            }

            // 输出完整响应JSON（用于详细分析）
            Ateye.out.println("========== 完整响应JSON ==========");
            Ateye.out.println(JSON.toJSONString(response));

        } catch (Exception e) {
            Ateye.out.println("执行异常: " + e.getMessage());
            logger.recordDangerException(new LogModel("ateyeProcessOdpsDataWithLlm_error")
                    .message("Ateye测试执行异常")
                    .e(e));
        }

        Ateye.out.println("========== 测试执行完成 ==========");
    }

    /**
     * 解析响应结果并构建结果行数据
     */
    private List<LlmResultRow> parseAndBuildResultRows(ItemProduceTaskResponse response,
                                                      Map<String, Map<String, Object>> requestInputParamsMap) {
        List<LlmResultRow> resultRows = new ArrayList<>();

        if (response.getLlmResponses() == null) {
            return resultRows;
        }

        for (BatchLlmGatewayResponse batchResponse : response.getLlmResponses()) {
            if (batchResponse.getResponses() == null) {
                continue;
            }

            for (BatchLlmGatewayResponse.SingleResponse singleResponse : batchResponse.getResponses()) {
                LlmResultRow resultRow = buildResultRow(singleResponse, requestInputParamsMap);
                resultRows.add(resultRow);
            }
        }

        return resultRows;
    }

    /**
     * 构建单个结果行
     */
    private LlmResultRow buildResultRow(BatchLlmGatewayResponse.SingleResponse singleResponse,
                                       Map<String, Map<String, Object>> requestInputParamsMap) {
        LlmResultRow.LlmResultRowBuilder builder = LlmResultRow.builder()
                .requestId(singleResponse.getRequestId())
                .costMs(singleResponse.getCostMs())
                .rawContent(singleResponse.getContent());

        if (singleResponse.getSuccess()) {
            builder.status("SUCCESS");

            // 从输入参数映射中获取原始数据
            Map<String, Object> inputParams = requestInputParamsMap.get(singleResponse.getRequestId());
            if (inputParams != null) {
                Map<String, String> inputFields = llmContentParser.extractInputFields(inputParams);
                builder.id(inputFields.get("id"))
                       .originalTitle(inputFields.get("originalTitle"))
                       .originalShortTitle(inputFields.get("originalShortTitle"))
                        .cateId(inputFields.get("cateId"))
                        .cateName(inputFields.get("cateName"))
                        .catePath(inputFields.get("catePath"));
            } else {
                builder.id("")
                       .originalTitle("")
                       .originalShortTitle("")
                        .cateId("")
                        .cateName("")
                        .catePath("");
            }

            // 解析大模型返回内容
            if (StringUtils.isNotBlank(singleResponse.getContent())) {
                Map<String, String> llmResult = llmContentParser.parseLlmResult(singleResponse.getContent());
                builder.title(llmResult.get("title"))
                       .benefit(llmResult.get("benefit"));
            } else {
                builder.title("").benefit("");
            }

        } else {
            builder.status("FAILED")
                   .errorMessage(singleResponse.getErrorMessage())
                   .id("")
                   .originalTitle("")
                   .originalShortTitle("")
                   .title("")
                   .benefit("");
        }

        return builder.build();
    }

    /**
     * 显示解析结果示例
     */
    private void showParseResultExamples(List<LlmResultRow> resultRows) {
        Ateye.out.println("解析结果示例（前3个成功的）:");

        int count = 0;
        for (LlmResultRow row : resultRows) {
            if ("SUCCESS".equals(row.getStatus()) && count < 3) {
                count++;
                Ateye.out.println("  [" + count + "] 请求ID: " + row.getRequestId());
                Ateye.out.println("      ID: " + row.getId());
                Ateye.out.println("      原始标题: " + row.getOriginalTitle());
                Ateye.out.println("      原始短标题: " + row.getOriginalShortTitle());
                Ateye.out.println("      生成标题: " + row.getTitle());
                Ateye.out.println("      生成卖点: " + row.getBenefit());
                Ateye.out.println("      耗时: " + row.getCostMs() + "ms");
                Ateye.out.println("");
            }
        }

        if (count == 0) {
            Ateye.out.println("  没有成功解析的结果");
        }
    }
}
