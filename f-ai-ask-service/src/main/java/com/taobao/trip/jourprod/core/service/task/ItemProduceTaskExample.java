package com.taobao.trip.jourprod.core.service.task;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 商品信息生产任务使用示例
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Component
@Slf4j
public class ItemProduceTaskExample {

    @Resource
    private ItemProduceTask itemProduceTask;

    /**
     * 示例1：从ODPS下载商品数据并生成商品描述
     */
    public void exampleGenerateProductDescriptions() {
        log.info("开始示例：生成商品描述");

        // 构建请求参数
        Map<String, Object> extraParams = Maps.newHashMap();
        extraParams.put("max_tokens", 500);
        extraParams.put("temperature", 0.7);

        ItemProduceTaskRequest request = ItemProduceTaskRequest.builder()
                // ODPS配置
                .odpsProject("trip_content")
                .odpsTable("product_info")
                .odpsPartition("ds='20240127'")
                .columnsToGet(Lists.newArrayList("product_id", "product_name", "category", "features"))
                .batchSize(1000)

                // 工作流配置
                .workflowAppId(21L)
                .workflowFlowId(1L)
                .streamOutputTask("llm_vDq0SH")

                // LLM批量处理配置
                .llmBatchSize(5)
                .llmTimeoutSeconds(120L)
                .singleRequestTimeoutSeconds(30L)

                // 任务配置
                .taskType("product_description_generation")
                .llmPrompt("根据商品信息生成吸引人的商品描述，要求突出商品特色和卖点")
                .extraParams(extraParams)
                .description("批量生成商品描述")
                .debugMode(false)
                .build();

        // 执行任务
        ItemProduceTaskResponse response = itemProduceTask.processOdpsDataWithLlm(request);

        // 处理结果
        handleResponse("生成商品描述", response);
    }

    /**
     * 示例2：从ODPS下载用户评论并进行情感分析
     */
    public void exampleAnalyzeUserComments() {
        log.info("开始示例：用户评论情感分析");

        List<String> columns = Lists.newArrayList("comment_id", "user_id", "product_id", "comment_text", "rating");

        ItemProduceTaskRequest request = ItemProduceTaskRequest.builder()
                // ODPS配置
                .odpsProject("trip_content")
                .odpsTable("user_comments")
                .odpsPartition("ds='20240127'")
                .columnsToGet(columns)
                .batchSize(2000)

                // 工作流配置
                .workflowAppId(21L)
                .workflowFlowId(2L) // 假设流程ID 2 是情感分析流程
                .streamOutputTask("sentiment_analysis_task")

                // LLM批量处理配置
                .llmBatchSize(10)
                .llmTimeoutSeconds(90L)
                .singleRequestTimeoutSeconds(20L)

                // 任务配置
                .taskType("sentiment_analysis")
                .llmPrompt("分析用户评论的情感倾向，返回正面、负面或中性，并给出置信度")
                .description("批量分析用户评论情感")
                .debugMode(false)
                .build();

        // 执行任务
        ItemProduceTaskResponse response = itemProduceTask.processOdpsDataWithLlm(request);

        // 处理结果
        handleResponse("用户评论情感分析", response);
    }

    /**
     * 示例3：从ODPS下载旅游景点数据并生成推荐理由
     */
    public void exampleGenerateTravelRecommendations() {
        log.info("开始示例：生成旅游景点推荐理由");

        Map<String, Object> extraParams = Maps.newHashMap();
        extraParams.put("style", "吸引人的");
        extraParams.put("length", "简洁");

        ItemProduceTaskRequest request = ItemProduceTaskRequest.builder()
                // ODPS配置
                .odpsProject("trip_vacation")
                .odpsTable("poi_info")
                .odpsPartition("ds='20240127',city='beijing'")
                .columnsToGet(Lists.newArrayList("poi_id", "poi_name", "category", "description", "rating", "tags"))
                .batchSize(500)

                // 工作流配置
                .workflowAppId(21L)
                .workflowFlowId(3L) // 假设流程ID 3 是内容生成流程
                .streamOutputTask("content_generation_task")

                // LLM批量处理配置
                .llmBatchSize(8)
                .llmTimeoutSeconds(150L)
                .singleRequestTimeoutSeconds(25L)

                // 任务配置
                .taskType("travel_recommendation")
                .llmPrompt("根据景点信息生成吸引游客的推荐理由，突出景点特色和游玩价值")
                .extraParams(extraParams)
                .description("批量生成旅游景点推荐理由")
                .debugMode(true)
                .build();

        // 执行任务
        ItemProduceTaskResponse response = itemProduceTask.processOdpsDataWithLlm(request);

        // 处理结果
        handleResponse("生成旅游景点推荐理由", response);
    }

    /**
     * 处理响应结果
     */
    private void handleResponse(String taskName, ItemProduceTaskResponse response) {
        log.info("========== {} 任务结果 ==========", taskName);

        if (response.getSuccess()) {
            log.info("任务执行成功！");
            log.info("ODPS数据统计：总记录数={}, 已处理记录数={}",
                    response.getTotalRecords(), response.getProcessedRecords());

            log.info("LLM批次统计：总批次数={}, 成功批次数={}, 失败批次数={}, 成功率={:.2f}%",
                    response.getTotalBatches(), response.getSuccessfulBatches(),
                    response.getFailedBatches(),
                    response.getBatchSuccessRate() != null ? response.getBatchSuccessRate() * 100 : 0);

            log.info("LLM请求统计：总请求数={}, 成功请求数={}, 失败请求数={}, 成功率={:.2f}%",
                    response.getTotalLlmRequests(), response.getSuccessfulLlmRequests(),
                    response.getFailedLlmRequests(),
                    response.getLlmRequestSuccessRate() != null ? response.getLlmRequestSuccessRate() * 100 : 0);

            log.info("任务耗时：{}ms", response.getTotalCostMs());

            // 输出部分成功结果示例
            List<BatchLlmGatewayResponse.SingleResponse> successfulResponses = response.getAllSuccessfulResponses();
            if (successfulResponses != null && !successfulResponses.isEmpty()) {
                log.info("成功响应示例（前3个）：");
                for (int i = 0; i < Math.min(3, successfulResponses.size()); i++) {
                    BatchLlmGatewayResponse.SingleResponse singleResponse = successfulResponses.get(i);
                    log.info("  请求ID: {}, 耗时: {}ms, 内容长度: {}",
                            singleResponse.getRequestId(),
                            singleResponse.getCostMs(),
                            singleResponse.getContent() != null ? singleResponse.getContent().length() : 0);

                    if (singleResponse.getContent() != null) {
                        String preview = singleResponse.getContent().length() > 100 ?
                                singleResponse.getContent().substring(0, 100) + "..." :
                                singleResponse.getContent();
                        log.info("  内容预览: {}", preview);
                    }
                }
            }

            // 输出失败结果示例
            List<BatchLlmGatewayResponse.SingleResponse> failedResponses = response.getAllFailedResponses();
            if (failedResponses != null && !failedResponses.isEmpty()) {
                log.warn("失败响应示例（前3个）：");
                for (int i = 0; i < Math.min(3, failedResponses.size()); i++) {
                    BatchLlmGatewayResponse.SingleResponse singleResponse = failedResponses.get(i);
                    log.warn("  请求ID: {}, 错误信息: {}",
                            singleResponse.getRequestId(), singleResponse.getErrorMessage());
                }
            }

        } else {
            log.error("任务执行失败：{}", response.getErrorMessage());
            log.error("任务耗时：{}ms", response.getTotalCostMs());
        }

        log.info("========== {} 任务结果结束 ==========", taskName);
    }

    /**
     * 测试所有示例
     */
    public void runAllExamples() {
        try {
            exampleGenerateProductDescriptions();
            Thread.sleep(2000); // 间隔2秒

            exampleAnalyzeUserComments();
            Thread.sleep(2000); // 间隔2秒

            exampleGenerateTravelRecommendations();
        } catch (InterruptedException e) {
            log.error("示例执行被中断", e);
        }
    }
}
