package com.taobao.trip.jourprod.core.service.task;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 商品信息生产任务请求
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ItemProduceTaskRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    // ========== ODPS相关参数 ==========

    /**
     * ODPS项目名
     */
    private String odpsProject;

    /**
     * ODPS表名
     */
    private String odpsTable;

    /**
     * ODPS分区信息，如: ds='20240127'
     */
    private String odpsPartition;

    /**
     * 需要获取的列名列表，为null时获取所有列
     */
    private List<String> columnsToGet;

    /**
     * ODPS数据下载批次大小，默认1000
     */
    @Builder.Default
    private Integer batchSize = 1000;

    // ========== 工作流相关参数 ==========

    /**
     * 工作流应用ID
     */
    private Long workflowAppId;

    /**
     * 工作流流程ID
     */
    private Long workflowFlowId;

    /**
     * 流式输出任务节点名称
     */
    private String streamOutputTask;

    // ========== LLM批量调用相关参数 ==========

    /**
     * LLM批次大小，即多少条ODPS数据组成一个LLM批次，默认10
     */
    @Builder.Default
    private Integer llmBatchSize = 10;

    /**
     * LLM批量请求超时时间（秒），默认120秒
     */
    @Builder.Default
    private Long llmTimeoutSeconds = 600L;

    /**
     * 单个LLM请求超时时间（秒），默认30秒
     */
    @Builder.Default
    private Long singleRequestTimeoutSeconds = 500L;

    // ========== 任务处理相关参数 ==========

    /**
     * 任务类型，用于标识不同的处理逻辑
     */
    private String taskType;

    /**
     * LLM处理提示词
     */
    private String llmPrompt;

    /**
     * 额外参数，会传递给LLM
     */
    private Map<String, Object> extraParams;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 是否启用调试模式
     */
    @Builder.Default
    private Boolean debugMode = false;
}
