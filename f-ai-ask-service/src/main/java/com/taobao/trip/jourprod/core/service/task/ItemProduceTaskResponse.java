package com.taobao.trip.jourprod.core.service.task;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 商品信息生产任务响应
 * 
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ItemProduceTaskResponse implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 任务是否成功
     */
    private Boolean success;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    // ========== ODPS数据统计 ==========
    
    /**
     * ODPS表总记录数
     */
    private Long totalRecords;
    
    /**
     * 已处理的记录数
     */
    private Long processedRecords;
    
    // ========== LLM批量处理统计 ==========
    
    /**
     * 成功的LLM批次数
     */
    private Long successfulBatches;
    
    /**
     * 失败的LLM批次数
     */
    private Long failedBatches;
    
    /**
     * 总LLM批次数
     */
    public Long getTotalBatches() {
        if (successfulBatches != null && failedBatches != null) {
            return successfulBatches + failedBatches;
        }
        return null;
    }
    
    /**
     * LLM批次成功率
     */
    public Double getBatchSuccessRate() {
        Long total = getTotalBatches();
        if (total != null && total > 0 && successfulBatches != null) {
            return (double) successfulBatches / total;
        }
        return null;
    }
    
    // ========== 时间统计 ==========
    
    /**
     * 任务总耗时（毫秒）
     */
    private Long totalCostMs;
    
    /**
     * 任务开始时间戳
     */
    private Long startTime;
    
    /**
     * 任务结束时间戳
     */
    private Long endTime;
    
    // ========== 详细结果 ==========
    
    /**
     * 所有LLM批量响应结果
     */
    private List<BatchLlmGatewayResponse> llmResponses;
    
    /**
     * 处理结果摘要
     */
    private Map<String, Object> resultSummary;
    
    /**
     * 扩展信息
     */
    private Map<String, Object> extInfo;
    
    // ========== 便捷方法 ==========
    
    /**
     * 获取所有成功的LLM单个响应
     */
    public List<BatchLlmGatewayResponse.SingleResponse> getAllSuccessfulResponses() {
        if (llmResponses == null) {
            return null;
        }
        
        return llmResponses.stream()
                .filter(BatchLlmGatewayResponse::getSuccess)
                .flatMap(response -> response.getResponses().stream())
                .filter(BatchLlmGatewayResponse.SingleResponse::getSuccess)
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 获取所有失败的LLM单个响应
     */
    public List<BatchLlmGatewayResponse.SingleResponse> getAllFailedResponses() {
        if (llmResponses == null) {
            return null;
        }
        
        return llmResponses.stream()
                .flatMap(response -> response.getResponses().stream())
                .filter(singleResponse -> !singleResponse.getSuccess())
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 获取总的LLM请求数
     */
    public Integer getTotalLlmRequests() {
        if (llmResponses == null) {
            return 0;
        }
        
        return llmResponses.stream()
                .mapToInt(response -> response.getResponses() != null ? response.getResponses().size() : 0)
                .sum();
    }
    
    /**
     * 获取成功的LLM请求数
     */
    public Integer getSuccessfulLlmRequests() {
        List<BatchLlmGatewayResponse.SingleResponse> successfulResponses = getAllSuccessfulResponses();
        return successfulResponses != null ? successfulResponses.size() : 0;
    }
    
    /**
     * 获取失败的LLM请求数
     */
    public Integer getFailedLlmRequests() {
        List<BatchLlmGatewayResponse.SingleResponse> failedResponses = getAllFailedResponses();
        return failedResponses != null ? failedResponses.size() : 0;
    }
    
    /**
     * 获取LLM请求成功率
     */
    public Double getLlmRequestSuccessRate() {
        Integer total = getTotalLlmRequests();
        Integer successful = getSuccessfulLlmRequests();
        
        if (total != null && total > 0 && successful != null) {
            return (double) successful / total;
        }
        return null;
    }
}
