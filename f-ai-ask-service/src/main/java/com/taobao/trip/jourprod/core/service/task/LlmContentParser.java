package com.taobao.trip.jourprod.core.service.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;

import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * LLM内容解析工具类
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Component
@Slf4j
public class LlmContentParser {

    private static final FliggyNewLogger logger = LogUtil.getFliggyNewLogger("LlmContentParser");

    /**
     * 从输入参数中提取字段
     *
     * @param inputParams 输入参数
     * @return 提取的字段映射
     */
    public Map<String, String> extractInputFields(Map<String, Object> inputParams) {
        Map<String, String> result = new HashMap<>();

        if (inputParams == null) {
            return result;
        }

        try {
            // 转换为JSONObject进行解析
            JSONObject jsonObject = new JSONObject(inputParams);

            // 提取ID
            result.put("id", extractFieldFromJson(jsonObject, "id", ""));

            // 提取原始标题 - 从 basic.title 路径
            result.put("originalTitle", extractNestedField(jsonObject, "basic", "title", ""));

            // 提取原始短标题 - 从 basic.shortTitle 路径
            result.put("originalShortTitle", extractNestedField(jsonObject, "basic", "shortTitle", ""));

            // 提取原始短标题 - 从 basic.shortTitle 路径
            result.put("cateId", extractNestedField(jsonObject, "category", "cateId", ""));

            // 提取原始短标题 - 从 basic.shortTitle 路径
            result.put("cateName", extractNestedField(jsonObject, "category", "cateName", ""));

            // 提取原始短标题 - 从 basic.shortTitle 路径
            result.put("catePath", extractNestedField(jsonObject, "category", "catePath", ""));

        } catch (Exception e) {
            logger.recordDangerException(new LogModel("extractInputFields_error")
                    .message("提取输入字段失败")
                    .request(JSON.toJSONString(inputParams))
                    .e(e));
        }

        return result;
    }

    /**
     * 从JSON对象中提取字段
     */
    private String extractFieldFromJson(JSONObject jsonObject, String fieldName, String defaultValue) {
        try {
            Object value = jsonObject.get(fieldName);
            return value != null ? value.toString().trim() : defaultValue;
        } catch (Exception e) {
            log.debug("字段提取失败: {} from {}", fieldName, jsonObject);
            return defaultValue;
        }
    }

    /**
     * 提取嵌套字段（如 basic.title）
     */
    private String extractNestedField(JSONObject jsonObject, String parentField, String childField, String defaultValue) {
        try {
            Object parentObj = jsonObject.get(parentField);
            if (parentObj instanceof JSONObject) {
                JSONObject parentJson = (JSONObject) parentObj;
                Object value = parentJson.get(childField);
                return value != null ? value.toString().trim() : defaultValue;
            } else if (parentObj instanceof String) {
                Object value = JSONPath.extract((String)parentObj, "$." + childField);
                return value != null ? value.toString().trim() : defaultValue;
            }
            return defaultValue;
        } catch (Exception e) {
            log.debug("嵌套字段提取失败: {}.{} from {}", parentField, childField, jsonObject);
            return defaultValue;
        }
    }

    /**
     * 解析大模型返回内容并提取title和benefit
     *
     * @param content 大模型返回的内容
     * @return 包含title和benefit的映射
     */
    public Map<String, String> parseLlmResult(String content) {
        Map<String, String> result = new HashMap<>();

        JSONObject jsonObject = parseLlmContentWithMultipleStrategies(content);

        result.put("title", extractFieldSafely(jsonObject, "title", ""));
        result.put("benefit", extractFieldSafely(jsonObject, "benefit", ""));

        return result;
    }

    /**
     * 多策略解析大模型内容
     */
    private JSONObject parseLlmContentWithMultipleStrategies(String content) {
        if (StringUtils.isBlank(content)) {
            return new JSONObject();
        }

        // 策略1：标准markdown格式 ```json ... ```
        JSONObject result = tryParseWithMarkdown(content, "```json");
        if (result != null) {
            log.debug("使用策略1解析成功: ```json");
            return result;
        }

        // 策略2：大写markdown格式 ```JSON ... ```
        result = tryParseWithMarkdown(content, "```JSON");
        if (result != null) {
            log.debug("使用策略2解析成功: ```JSON");
            return result;
        }

        // 策略3：简单markdown格式 ``` ... ```
        result = tryParseWithMarkdown(content, "```");
        if (result != null) {
            log.debug("使用策略3解析成功: ```");
            return result;
        }

        // 策略4：查找第一个 { 到最后一个 } 的内容
        result = tryParseJsonBlock(content);
        if (result != null) {
            log.debug("使用策略4解析成功: JSON块");
            return result;
        }

        // 策略5：直接解析整个内容
        result = tryParseDirectly(content);
        if (result != null) {
            log.debug("使用策略5解析成功: 直接解析");
            return result;
        }

        // 所有策略都失败，返回空对象
        logger.recordOutput(new LogModel("parseLlmContent_all_failed")
                .message("所有解析策略都失败")
                .request("原始内容: " + content));
        return new JSONObject();
    }

    /**
     * 尝试使用markdown标记解析
     */
    private JSONObject tryParseWithMarkdown(String content, String startMarker) {
        try {
            int startIndex = content.indexOf(startMarker);
            if (startIndex == -1) return null;

            // 找到开始标记后的内容
            int contentStart = startIndex + startMarker.length();

            // 跳过可能的换行符和空白字符
            while (contentStart < content.length() &&
                   (content.charAt(contentStart) == '\n' ||
                    content.charAt(contentStart) == '\r' ||
                    Character.isWhitespace(content.charAt(contentStart)))) {
                contentStart++;
            }

            // 查找结束标记
            int endIndex = content.indexOf("```", contentStart);
            if (endIndex == -1) {
                endIndex = content.length();
            }

            String jsonContent = content.substring(contentStart, endIndex).trim();
            jsonContent = jsonContent.replaceAll("\n", "");
            jsonContent = jsonContent.replaceAll("\r", "");
            jsonContent = jsonContent.replaceAll("\\n", "");
            jsonContent = jsonContent.replaceAll("\\r", "");
            return JSON.parseObject(jsonContent);

        } catch (Exception e) {
            log.debug("Markdown解析失败: {}, 错误: {}", startMarker, e.getMessage());
            return null;
        }
    }

    /**
     * 尝试解析JSON代码块（查找 { ... }）
     */
    private JSONObject tryParseJsonBlock(String content) {
        try {
            int startIndex = content.indexOf('{');
            int endIndex = content.lastIndexOf('}');

            if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
                String jsonContent = content.substring(startIndex, endIndex + 1);
                return JSON.parseObject(jsonContent);
            }

            return null;
        } catch (Exception e) {
            log.debug("JSON块解析失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 尝试直接解析内容
     */
    private JSONObject tryParseDirectly(String content) {
        try {
            return JSON.parseObject(content.trim());
        } catch (Exception e) {
            log.debug("直接解析失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从解析后的JSON中安全提取字段
     */
    private String extractFieldSafely(JSONObject jsonObject, String fieldName, String defaultValue) {
        if (jsonObject == null) {
            return defaultValue;
        }

        try {
            String value = jsonObject.getString(fieldName);
            return StringUtils.isNotBlank(value) ? value.trim() : defaultValue;
        } catch (Exception e) {
            log.debug("提取字段失败: {}, 错误: {}", fieldName, e.getMessage());
            return defaultValue;
        }
    }
}
