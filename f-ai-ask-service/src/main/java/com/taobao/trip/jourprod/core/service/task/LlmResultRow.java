package com.taobao.trip.jourprod.core.service.task;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * LLM处理结果行数据模型
 * 
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LlmResultRow implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * ID - 从输入参数中提取 (JSONPath: $.id)
     */
    private String id;
    
    /**
     * 原始标题 - 从输入参数中提取 (JSONPath: $.basic.title)
     */
    private String originalTitle;
    
    /**
     * 原始短标题 - 从输入参数中提取 (JSONPath: $.basic.shortTitle)
     */
    private String originalShortTitle;

    private String cateId;

    private String cateName;

    private String catePath;

    /**
     * 生成的标题 - 从大模型返回内容中解析
     */
    private String title;
    
    /**
     * 生成的卖点 - 从大模型返回内容中解析
     */
    private String benefit;
    
    /**
     * 请求ID - 用于追踪
     */
    private String requestId;
    
    /**
     * 处理状态 - 成功/失败
     */
    private String status;
    
    /**
     * 错误信息 - 如果处理失败
     */
    private String errorMessage;
    
    /**
     * 原始大模型返回内容 - 用于调试
     */
    private String rawContent;
    
    /**
     * 处理耗时（毫秒）
     */
    private Long costMs;
}
