# ODPS数据与大模型批量处理集成

## 概述

`ItemProduceTask` 是一个集成服务，实现了从ODPS离线表下载数据，并将数据批量传递给大模型网关进行处理的完整流程。该服务支持流式数据处理、批量并发调用、详细统计和异常处理。

## 核心功能

- **ODPS数据下载**：使用 `OdpsUpDownloadManager` 从指定的ODPS表中下载数据
- **流式数据收集**：实现 `DataFlowCallback` 接口，收集所有下载的数据流
- **分批LLM处理**：等待所有数据收集完成后，按 `llmBatchSize` 分批传递给 `BatchLlmGatewayService` 进行大模型处理
- **顺序批次处理**：按批次顺序调用大模型，确保处理的有序性和可控性
- **详细统计**：提供完整的处理统计信息，包括成功率、耗时等
- **异常处理**：完善的异常处理机制，确保服务稳定性

## 处理流程

### 数据处理流程
1. **数据收集阶段**：
   - 通过 `OdpsUpDownloadManager.download()` 下载ODPS数据
   - `OdpsDataProcessor.stream()` 方法接收数据流，将所有数据收集到 `allData` 列表中
   - 此阶段不进行LLM调用，只负责数据收集

2. **分批处理阶段**：
   - 数据下载完成后，调用 `processRemainingData()` 方法
   - 根据 `llmBatchSize` 参数将所有数据分成多个批次
   - 按顺序逐批调用 `processBatchWithLlm()` 方法

3. **LLM调用阶段**：
   - 每个批次构建对应的 `BatchLlmGatewayRequest`
   - 调用 `BatchLlmGatewayService.batchCall()` 进行大模型处理
   - 记录每个批次的处理结果和统计信息

### 优势
- **内存优化**：避免了边收集边处理可能导致的内存碎片
- **处理可控**：可以精确控制每批次的大小和处理顺序
- **错误隔离**：单个批次的失败不会影响其他批次
- **统计准确**：能够准确统计总体处理情况

## 主要类说明

### ItemProduceTask
主要服务类，提供 `processOdpsDataWithLlm` 方法进行数据处理。

### ItemProduceTaskRequest
请求参数类，包含：
- **ODPS配置**：项目名、表名、分区信息、列名等
- **工作流配置**：AppId、FlowId、流式输出任务节点
- **LLM配置**：批次大小、超时时间、并发数等
- **任务配置**：任务类型、提示词、额外参数等

### ItemProduceTaskResponse
响应结果类，包含：
- **执行状态**：成功/失败状态和错误信息
- **数据统计**：ODPS记录数、处理记录数
- **批次统计**：LLM批次成功率、请求成功率
- **详细结果**：所有LLM响应结果

## 使用方法

### 基本用法

```java
@Resource
private ItemProduceTask itemProduceTask;

public void processData() {
    // 构建请求
    ItemProduceTaskRequest request = ItemProduceTaskRequest.builder()
            // ODPS配置
            .odpsProject("your_project")
            .odpsTable("your_table")
            .odpsPartition("ds='20240127'")
            .columnsToGet(Arrays.asList("col1", "col2", "col3"))
            .batchSize(1000)

            // 工作流配置
            .workflowAppId(21L)
            .workflowFlowId(1L)
            .streamOutputTask("llm_vDq0SH")

            // LLM配置
            .llmBatchSize(10)
            .llmTimeoutSeconds(120L)
            .llmMaxConcurrency(5)
            .singleRequestTimeoutSeconds(30L)

            // 任务配置
            .taskType("your_task_type")
            .llmPrompt("your_prompt")
            .description("your_description")
            .build();

    // 执行处理
    ItemProduceTaskResponse response = itemProduceTask.processOdpsDataWithLlm(request);

    // 处理结果
    if (response.getSuccess()) {
        System.out.println("处理成功，总记录数: " + response.getTotalRecords());
        System.out.println("LLM请求成功率: " + response.getLlmRequestSuccessRate());
    } else {
        System.err.println("处理失败: " + response.getErrorMessage());
    }
}
```

### 商品描述生成示例

```java
public void generateProductDescriptions() {
    ItemProduceTaskRequest request = ItemProduceTaskRequest.builder()
            .odpsProject("trip_content")
            .odpsTable("product_info")
            .odpsPartition("ds='20240127'")
            .columnsToGet(Arrays.asList("product_id", "product_name", "category", "features"))
            .workflowAppId(21L)
            .workflowFlowId(1L)
            .streamOutputTask("llm_vDq0SH")
            .llmBatchSize(5)
            .taskType("product_description_generation")
            .llmPrompt("根据商品信息生成吸引人的商品描述")
            .build();

    ItemProduceTaskResponse response = itemProduceTask.processOdpsDataWithLlm(request);
    // 处理结果...
}
```

### 情感分析示例

```java
public void analyzeUserComments() {
    ItemProduceTaskRequest request = ItemProduceTaskRequest.builder()
            .odpsProject("trip_content")
            .odpsTable("user_comments")
            .odpsPartition("ds='20240127'")
            .columnsToGet(Arrays.asList("comment_id", "comment_text", "rating"))
            .workflowAppId(21L)
            .workflowFlowId(2L)
            .streamOutputTask("sentiment_analysis_task")
            .llmBatchSize(10)
            .taskType("sentiment_analysis")
            .llmPrompt("分析用户评论的情感倾向")
            .build();

    ItemProduceTaskResponse response = itemProduceTask.processOdpsDataWithLlm(request);
    // 处理结果...
}
```

## 参数配置说明

### ODPS相关参数
- `odpsProject`: ODPS项目名（必填）
- `odpsTable`: ODPS表名（必填）
- `odpsPartition`: 分区信息，如 "ds='20240127'"
- `columnsToGet`: 需要获取的列名列表，null时获取所有列
- `batchSize`: ODPS数据下载批次大小，默认1000

### 工作流相关参数
- `workflowAppId`: 工作流应用ID（必填）
- `workflowFlowId`: 工作流流程ID（必填）
- `streamOutputTask`: 流式输出任务节点名称

### LLM批量处理参数
- `llmBatchSize`: LLM批次大小，默认10
- `llmTimeoutSeconds`: LLM批量请求超时时间，默认120秒
- `llmMaxConcurrency`: LLM最大并发数，默认5
- `singleRequestTimeoutSeconds`: 单个LLM请求超时时间，默认30秒

### 任务处理参数
- `taskType`: 任务类型标识
- `llmPrompt`: LLM处理提示词
- `extraParams`: 额外参数，会传递给LLM
- `description`: 任务描述
- `debugMode`: 是否启用调试模式

## 数据流程

1. **参数校验**：验证必填参数的有效性
2. **ODPS连接**：构建ODPS表配置并建立连接
3. **数据下载**：使用流式方式下载ODPS数据
4. **数据分批**：将下载的数据按LLM批次大小分组
5. **LLM调用**：并发调用大模型网关处理每个批次
6. **结果收集**：收集所有LLM响应结果
7. **统计汇总**：生成详细的处理统计信息

## 性能优化建议

1. **合理设置批次大小**：
   - ODPS批次大小建议500-2000
   - LLM批次大小建议5-20

2. **并发控制**：
   - 根据下游服务能力设置合理的并发数
   - 避免过高并发导致服务压力

3. **超时设置**：
   - 根据数据量和处理复杂度设置合理超时时间
   - 单个请求超时建议20-60秒

4. **资源监控**：
   - 监控内存使用情况
   - 关注下游服务响应时间

## 错误处理

服务提供多层次的错误处理：

1. **参数校验错误**：返回具体的参数错误信息
2. **ODPS连接错误**：记录详细的连接失败原因
3. **LLM调用错误**：区分批次失败和单个请求失败
4. **系统异常**：捕获并记录所有未预期的异常

## 监控和日志

服务使用 `FliggyNewLogger` 记录详细的执行日志：

- **INFO级别**：正常的执行流程和统计信息
- **WARN级别**：非致命性错误和警告
- **ERROR级别**：严重错误和异常情况

建议监控以下指标：
- 任务执行成功率
- 平均处理时间
- LLM请求成功率
- 数据处理吞吐量

## 测试

使用 `ItemProduceTaskExample` 类进行功能测试：

```java
@Resource
private ItemProduceTaskExample example;

// 测试商品描述生成
example.exampleGenerateProductDescriptions();

// 测试情感分析
example.exampleAnalyzeUserComments();

// 测试旅游推荐
example.exampleGenerateTravelRecommendations();

// 运行所有示例
example.runAllExamples();
```
