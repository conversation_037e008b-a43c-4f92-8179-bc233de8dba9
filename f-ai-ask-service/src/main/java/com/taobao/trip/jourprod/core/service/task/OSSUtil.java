package com.taobao.trip.jourprod.core.service.task;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.trip.jourprod.common.lang.utils.LoggerUtil;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * TFS，现改为OSS
 * Created by kongti on 15/8/24.
 * Updated by shiguowei.sgw on 2025/1/27 - 添加新的文件上传功能
 */
@Component
@Slf4j
public class OSSUtil {

    /**
     * 单独记录模版日志
     */
    private static final FliggyNewLogger logger = LogUtil.getFliggyNewLogger("OSSUtil");

    // 原有配置（保持兼容性）
    private static String endpoint          = "http://oss-cn-beijing.aliyuncs.com";
    private static String bucketName        = "fliggy-poi-test";

    // 新的配置用于LLM结果文件上传
    private static final String NEW_ENDPOINT = "http://fliggy-poi-1933739917599826.oss-cn-beijing-internal.oss-accesspoint.aliyuncs.com";
    private static final String NEW_ACCESS_KEY_ID = "LTAI5tKobgQGXezJtnWRzt7Z";
    private static final String NEW_ACCESS_KEY_SECRET = "******************************";
    private static final String NEW_BUCKET_NAME = "fliggy-poi-test";

    /** 默认url失效时间，一年*/
    static long   defaultExpireTime = 3600L * 1000L * 24L * 365L;

    private static OSSClient client = null;

    static {
        // 初始化OSSClient，下线，删除代码中的明文秘钥
        client = new OSSClient(endpoint, "LTAI5tKobgQGXezJtnWRzt7Z", "******************************");
    }

    public static String generateUrl(String key, Long expireTime) {
        long time;
        if (expireTime == null) {
            time = defaultExpireTime;
        } else {
            time = expireTime;
        }
        Date expiration = new Date(new Date().getTime() + time);
        // 生成URL
        URL url = client.generatePresignedUrl(bucketName, key, expiration);
        return url.toString();
    }

    public static String putObject(String key, String filePath)
        throws FileNotFoundException {
        // 获取指定文件的输入流
        File file = new File(filePath);
        InputStream content = new FileInputStream(file);

        // 创建上传Object的Metadata
        ObjectMetadata meta = new ObjectMetadata();
        meta.setContentEncoding("utf-8");

        // 必须设置ContentLength
        meta.setContentLength(file.length());

        // 上传Object.
        client.putObject(bucketName, key, content, meta);

        return key;
    }

    public static void main(String[] args) {
        try {
            System.out.println(putObject("test_kongti", "test.html"));
            getObject("test_kongti");
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void getObject(String key) throws IOException {

        // 获取Object，返回结果为OSSObject对象
        OSSObject object = client.getObject(bucketName, key);
        // 获取Object的输入流
        InputStream objectContent = object.getObjectContent();
        // 处理Object
        process(objectContent);
        // 关闭流
        objectContent.close();
    }

    private static void process(InputStream objectContent) throws IOException {
        File file = new File("test2.html");
        if (!file.exists())
            file.createNewFile();
        FileOutputStream os = new FileOutputStream(file);
        byte[] b = new byte[10 * 1024];
        while (objectContent.read(b, 0, 10240) != -1) {
            os.write(b, 0, 10240);
        }
        os.flush();
        os.close();
    }

    /**
     * 上传文件到OSS（新方法，用于LLM结果文件上传）
     *
     * @param localFilePath 本地文件路径
     * @param ossKey OSS对象键（文件在OSS中的路径）
     * @return 上传成功返回OSS文件URL，失败返回null
     */
    @AteyeInvoker(description = "上传文件到OSS", paraDesc = "localFilePath&ossKey")
    public static String putObjectToNewBucket(String localFilePath, String ossKey) {
        if (StringUtils.isBlank(localFilePath) || StringUtils.isBlank(ossKey)) {
            logger.recordNormalException(new LogModel("putObjectToNewBucket_param_error")
                    .message("参数不能为空")
                    .request("localFilePath=" + localFilePath + ", ossKey=" + ossKey));
            return null;
        }

        File file = new File(localFilePath);
        if (!file.exists() || !file.isFile()) {
            logger.recordNormalException(new LogModel("putObjectToNewBucket_file_not_exist")
                    .message("文件不存在或不是文件")
                    .request("localFilePath=" + localFilePath));
            return null;
        }

        OSS ossClient = null;
        try {
            // 创建OSS客户端
            ossClient = new OSSClientBuilder().build(NEW_ENDPOINT, NEW_ACCESS_KEY_ID, NEW_ACCESS_KEY_SECRET);

            // 上传文件
            PutObjectRequest putObjectRequest = new PutObjectRequest(NEW_BUCKET_NAME, ossKey, file);
            PutObjectResult result = ossClient.putObject(putObjectRequest);

            // 构建文件URL
            String fileUrl = "https://" + NEW_BUCKET_NAME + "." + NEW_ENDPOINT.replace("https://", "") + "/" + ossKey;

            logger.recordOutput(new LogModel("putObjectToNewBucket_success")
                    .message("文件上传成功")
                    .request("localFilePath=" + localFilePath + ", ossKey=" + ossKey)
                    .response("fileUrl=" + fileUrl + ", etag=" + result.getETag()));

            return fileUrl;

        } catch (Exception e) {
            logger.recordDangerException(new LogModel("putObjectToNewBucket_error")
                    .message("文件上传失败")
                    .request("localFilePath=" + localFilePath + ", ossKey=" + ossKey)
                    .e(e));
            return null;
        } finally {
            if (ossClient != null) {
                try {
                    ossClient.shutdown();
                } catch (Exception e) {
                    logger.recordNormalException(new LogModel("putObjectToNewBucket_shutdown_error")
                            .message("OSS客户端关闭异常")
                            .e(e));
                }
            }
        }
    }

    /**
     * 生成带时间戳的OSS键
     *
     * @param prefix 前缀
     * @param fileName 文件名
     * @return OSS键
     */
    public static String generateOssKey(String prefix, String fileName) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        String datePath = sdf.format(new Date());

        String timestamp = String.valueOf(System.currentTimeMillis());

        if (StringUtils.isNotBlank(prefix)) {
            return prefix + "/" + datePath + "/" + timestamp + "_" + fileName;
        } else {
            return datePath + "/" + timestamp + "_" + fileName;
        }
    }

    /**
     * 生成Excel文件的OSS键
     *
     * @param taskType 任务类型
     * @param fileName 文件名
     * @return OSS键
     */
    public static String generateExcelOssKey(String taskType, String fileName) {
        String prefix = "llm-results";
        if (StringUtils.isNotBlank(taskType)) {
            prefix = prefix + "/" + taskType;
        }
        return generateOssKey(prefix, fileName);
    }
}
