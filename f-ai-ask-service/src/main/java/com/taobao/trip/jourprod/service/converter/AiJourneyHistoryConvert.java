package com.taobao.trip.jourprod.service.converter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AiJourneyHistoryConvert {

    public static List<Map<String, Object>> mergeCardsToLastElement(List<Map<String, Object>> inputList) {
        if (inputList == null || inputList.size() <= 1) {
            return inputList;
        }

        List<Map<String, Object>> result = new ArrayList<>();
        List<Map<String, Object>> expertInquiryElements = new ArrayList<>();
        Map<String, Object> expertElement = new HashMap<>();
        boolean findExpertElement = false;

        // 分离expertInquiry元素和其他元素
        for (Map<String, Object> element : inputList) {
            if ("expertInquiry".equals(element.get("code"))) {
                expertInquiryElements.add(new HashMap<>(element));
                if (!findExpertElement) {
                    result.add(expertElement);
                    findExpertElement = true;
                }
            } else {
                result.add(element);
            }
        }

        // 如果没有expertInquiry元素，直接返回原列表
        if (expertInquiryElements.isEmpty()) {
            return inputList;
        }

        // 获取最后一个expertInquiry元素
        Map<String, Object> lastExpertElement = expertInquiryElements.get(expertInquiryElements.size() - 1);

        // 初始化合并后的cards列表
        List<Object> mergedCards = new ArrayList<>();

        // 合并除最后一个之外的expertInquiry元素的cards
        for (int i = 0; i < expertInquiryElements.size() - 1; i++) {
            Map<String, Object> currentElement = expertInquiryElements.get(i);
            @SuppressWarnings("unchecked")
            Map<String, Object> map = (Map<String, Object>) currentElement.get("result");
            if (map != null && map.containsKey("cards")) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> currentCards = (List<Map<String, Object>>) map.get("cards");
                if (currentCards != null) {
                    mergedCards.addAll(currentCards);
                }
            }
        }

        // 添加最后一个expertInquiry元素的cards
        @SuppressWarnings("unchecked")
        Map<String, Object> lastResult = (Map<String, Object>) lastExpertElement.get("result");
        if (lastResult != null && lastResult.containsKey("cards")) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> lastCards = (List<Map<String, Object>>) lastResult.get("cards");
            if (lastCards != null) {
                mergedCards.addAll(lastCards);
            }
        }

        // 更新最后一个expertInquiry元素的cards
        if (lastResult != null) {
            lastResult.put("cards", mergedCards);
            lastExpertElement.put("result", lastResult);
        }

        // 将处理后的expertInquiry元素添加到结果中
        expertElement.putAll(lastExpertElement);

        return result;
    }

    // 测试用例
    public static void main(String[] args) {
        // 创建测试数据
        List<Map<String, Object>> inputList = new ArrayList<>();

        // 创建第一个普通元素
        Map<String, Object> element1 = new HashMap<>();
        element1.put("code", "taskSteps");
        element1.put("summary", "任务");
        Map<String, Object> result1 = new HashMap<>();
        result1.put("total", 4);
        result1.put("current", 1);
        result1.put("overView", true);
        result1.put("codeType", "overView");
        result1.put("mimeType", "statistics");
        element1.put("result", result1);
        element1.put("mimeType", "vcard/code");

        // 创建第二个expertInquiry元素
        Map<String, Object> element2 = new HashMap<>();
        element2.put("code", "expertInquiry");
        element2.put("summary", "专家咨询1");
        Map<String, Object> result2 = new HashMap<>();
        List<Map<String, Object>> cards1 = new ArrayList<>();
        Map<String, Object> card1 = new HashMap<>();
        card1.put("id", "123");
        card1.put("title", "酒店试睡员");
        cards1.add(card1);
        result2.put("cards", cards1);
        element2.put("result", result2);
// 创建第三个元素 (requirementAnalysis)
        Map<String, Object> element3 = new HashMap<>();
        element3.put("code", "requirementAnalysis");
        element3.put("summary", "需求分析");
        Map<String, Object> result3 = new HashMap<>();
        result3.put("role", "行程规划师");
        result3.put("codeType", "progress");
        result3.put("mimeType", "card");
        element3.put("result", result3);
        element3.put("status", "started");

        // 创建第四个expertInquiry元素
        Map<String, Object> element4 = new HashMap<>();
        element4.put("code", "expertInquiry");
        element4.put("summary", "专家咨询2");
        Map<String, Object> result4 = new HashMap<>();
        List<Map<String, Object>> cards2 = new ArrayList<>();
        Map<String, Object> card2 = new HashMap<>();
        card2.put("id", "456");
        card2.put("title", "酒店试睡员2");
        cards2.add(card2);
        result4.put("cards", cards2);
        element4.put("result", result4);

        // 创建第五个元素 (hotelProtection)
        Map<String, Object> element5 = new HashMap<>();
        element5.put("code", "hotelProtection");
        element5.put("summary", "风险保障护航");
        Map<String, Object> result5 = new HashMap<>();
        result5.put("role", "风险控制规划师");
        result5.put("codeType", "progress");
        result5.put("mimeType", "card");
        element5.put("result", result5);
        element5.put("status", "started");

        // 按顺序添加所有元素到输入列表
        inputList.add(element1); // taskSteps
        inputList.add(element2); // expertInquiry
        inputList.add(element3); // requirementAnalysis
        inputList.add(element4); // expertInquiry
        inputList.add(element5); // hotelProtection

        // 处理数据
        List<Map<String, Object>> result = mergeCardsToLastElement(inputList);
        System.out.println("处理结果：");
        for (Map<String, Object> map : result) {
            System.out.println(map);
        }
    }

}
