package com.taobao.trip.jourprod.service.converter;

import java.util.ArrayList;
import java.util.List;

import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanAiAssistantHistoryResult;
import com.taobao.trip.response.RecentMessageResponse;
import org.apache.commons.collections4.CollectionUtils;

/**
 * <AUTHOR>
 * @Date 2025/04/23
 **/
public class JourneyPlanAskRouteCovert {

    public static List<RecentMessageResponse> covertRecentMessageInfo(
        List<JourneyPlanAiAssistantHistoryResult.MessageInfo> result) {
        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        List<RecentMessageResponse> resultList = new ArrayList<>();
        result.forEach(messageInfo -> {
            RecentMessageResponse recentMessageResponse = new RecentMessageResponse();
            recentMessageResponse.setMessageId(messageInfo.getMessageId());
            recentMessageResponse.setOriginalMessageId(messageInfo.getOriginalMessageId());
            recentMessageResponse.setRole(messageInfo.getRole());
            recentMessageResponse.setInfo(messageInfo.getInfo());
            recentMessageResponse.setExtra(messageInfo.getExtra());
            recentMessageResponse.setType(messageInfo.getType());
            recentMessageResponse.setStatus(messageInfo.getStatus());
            recentMessageResponse.setUserChat(messageInfo.getUserChat());
            recentMessageResponse.setSessionId(messageInfo.getSessionId());
            resultList.add(recentMessageResponse);
        });
        return resultList;
    }
}
