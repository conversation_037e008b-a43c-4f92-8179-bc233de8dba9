package com.taobao.trip.jourprod.service.facade.enums.journeyplan;

import lombok.Getter;
import org.apache.commons.lang.StringUtils;

@Getter
public enum ActionJumpTypeEnum {

    URL("url", "跳转页面"),
    REQ("req", "请求接口"),
    PHONE("phone", "电话"),
    REMIND("remind", "提醒，无实际跳转，只是按钮"),
    MTOP("mtop", "调用mtop接口"),
    UNKNOWN("unKnown", "未知"),

    ;


    private final String type;

    private final String desc;

    ActionJumpTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static ActionJumpTypeEnum getByType(String type) {
        for (ActionJumpTypeEnum value : values()) {
            if (StringUtils.equals(value.getType(), type)) {
                return value;
            }
        }
        return UNKNOWN;
    }
}
