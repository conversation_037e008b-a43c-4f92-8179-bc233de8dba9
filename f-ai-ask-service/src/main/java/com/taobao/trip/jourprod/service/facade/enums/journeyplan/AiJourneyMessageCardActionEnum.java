package com.taobao.trip.jourprod.service.facade.enums.journeyplan;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description 问一问卡片动作 eg. 新增 更新
 * <AUTHOR>
 * @Date 2025/3/3
 **/
@Getter
@AllArgsConstructor
public enum AiJourneyMessageCardActionEnum {

    REPLACE("replace", "替换"),
    APPEND("append", "新增"),
    DELETE("delete", "删除"),
    ;

    private final String code;

    private final String desc;

}
