package com.taobao.trip.jourprod.service.facade.enums.journeyplan;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AiJourneyMessageItemTypeEnum {

    demo("demo", "demo"),
    HOTEL_RECOMMEND("hotel_recommend", "酒店推荐卡"),
    HOTEL_BOOKING("hotel_booking", "酒店预定卡"),
    HOTEL_PACKAGE_RECOMMEND("hotel_package_recommend", "酒店套餐推荐卡"),

    MDD_PLAY_TITLE("mdd_play_title", "目的地玩法标题卡片"),
    MDD_LINK_CARD("mdd_link_card", "目的地玩法标题卡片-新"),
    MDD_TRAFFIC("mdd_traffic", "目的地交通信息卡片"),
    POI_LINK_CARD("poi_link_card", "POI链接卡"),
    FAST_RESERVE("fast_reserve", "快捷预订卡片"),
    IMAGE_CARD("image_card", "图片卡片"),
    VISA_SKU_CARD("vacation_visa_card", "签证sku卡片"),


    /**交通**/
    TRAFFIC_CARD("traffic_card", "单程机票/火车卡片"),
    TRAFFIC_ROUND_CARD("traffic_round_card", "交通往返卡片"),
    TRAFFIC_MULTI_CARD("traffic_multi_card","多交通卡片"),
    CHEAP_FLIGHT_CARD("cheap_flight_card", "低价机票卡片"),

    /**
     * POI
     */
    VACATION_POI_CARD("vacation_poi_card", "POI卡片"),
    VACATION_POI_ITEM_CARD("vacation_poi_item_card", "商品卡片"),


    /**
     * 行程规划
     */
    JOURNEY_TRAFFIC_SLIDE_CARD("journey_traffic_slide", "行程规划交通滑动卡组"),
    JOURNEY_TEXT("journey_text", "普通文本卡"),
    ROUTER_PLAN_CARD("router_plan_card", "路径规划卡片"),
    HOTEL_LINK_CARD("hotel_link_card", "酒店link卡片"),
    ITEM_RESERVE_CARD("item_reserve_card", "跟团游商品预定卡片"),
    HOTEL_RESERVE_CARD("hotel_reserve_card", "酒店预定卡片"),
    DRAWING_MAP("drawing_map", "酒店预定卡片"),
    BUDGET_TABLE("budget_table", "预算动态表格"),
    BUDGET_CARD("budget_card", "预算调整卡片"),
    JOURNEY_TITLE_CARD("journey_title_card", "行程规划标题卡片"),
    JOURNEY_TRAFFIC("journey_traffic", "行程规划交通卡片"),
    ROUND_CARD("round_card", "容器圆角"),
    OTHER_CARD("other_card", "其他卡片"),
    ;

    private final String code;

    private final String desc;

}
