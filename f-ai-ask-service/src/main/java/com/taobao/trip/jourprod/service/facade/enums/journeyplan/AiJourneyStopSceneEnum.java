package com.taobao.trip.jourprod.service.facade.enums.journeyplan;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 停止回答原因枚举
 */
@Getter
@AllArgsConstructor
public enum AiJourneyStopSceneEnum {

    MANUAL_STOP("MANUAL_STOP", "手动停止"),

    MESSAGE_END("MESSAGE_END", "消息结束"),

    ERROR_STOP("ERROR_STOP", "错误停止"),
    ;

    private final String code;

    private final String desc;

}
