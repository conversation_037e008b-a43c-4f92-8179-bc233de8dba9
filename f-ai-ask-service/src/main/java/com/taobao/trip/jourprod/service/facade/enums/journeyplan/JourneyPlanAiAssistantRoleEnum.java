package com.taobao.trip.jourprod.service.facade.enums.journeyplan;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum JourneyPlanAiAssistantRoleEnum {

    SYSTEM("system", "系统"),

    USER("user", "用户"),
    ;

    private final String code;

    private final String desc;

    public static JourneyPlanAiAssistantRoleEnum codeOf(String code) {
        for (JourneyPlanAiAssistantRoleEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
