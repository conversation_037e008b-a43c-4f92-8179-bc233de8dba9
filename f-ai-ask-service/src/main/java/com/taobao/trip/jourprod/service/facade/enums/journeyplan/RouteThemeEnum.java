package com.taobao.trip.jourprod.service.facade.enums.journeyplan;

import lombok.Getter;

/**
 * @description: 路线类型
 * @author: huiyi
 * @create: 2025-04-11 13:42
 **/
@Getter
public enum RouteThemeEnum {
    // 通用主题
    COMMON(0, "通用"),
    // 自然风光相关主题,
    ISLAND(1, "海岛"),
    MOUNTAIN(2, "山川"),
    FLOWER_VIEWING(3, "赏花"),

    // 城市相关主题
    CITY(4, "城市"),

    // 人文历史相关主题
    HISTORY_CULTURE(5, "人文历史"),

    // 自驾游相关主题
    SELF_DRIVING(6, "自驾游");

    private final Integer code;
    private final String desc;

    RouteThemeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据 code 获取对应的枚举值
     *
     * @param code 枚举值的 code
     * @return 对应的枚举值，如果找不到则返回 null
     */
    public static RouteThemeEnum fromCode(Integer code) {
        for (RouteThemeEnum theme : values()) {
            if (theme.getCode().equals(code)) {
                return theme;
            }
        }
        return null; // 如果没有匹配的 code，返回 null
    }
}
