package com.taobao.trip.jourprod.service.facade.gaode;

import com.taobao.mtop.common.Result;
import com.taobao.trip.jourprod.service.facade.model.request.gaode.GaoDeAroundQueryRequest;
import com.taobao.trip.jourprod.service.facade.model.response.gaode.GaoDeAroundQueryResponse;

/**
 * 高德 搜索
 */
public interface JourneyGaoDeQueryFacade {

    /**
     * 周边搜索
     * @param request
     * @return
     */
    Result<GaoDeAroundQueryResponse> queryGaoDeAround(GaoDeAroundQueryRequest request);

}
