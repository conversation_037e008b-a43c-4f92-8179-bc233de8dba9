package com.taobao.trip.jourprod.service.facade.googlemap;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.hsf.app.api.util.HSFApiConsumerBean;
import com.taobao.hsf.remoting.service.GenericService;
import com.taobao.trip.jourprod.common.lang.utils.MapLogUtils;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/3
 */
@Service
public class GoogleMapService implements InitializingBean {

    private GenericService genericOrderService;
    @Value("${googlemap.config.server}")
    private String configServer;

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(GoogleMapService.class);

    /**
     * 测试google地图
     *
     * @param query
     * @return
     */
    @AteyeInvoker(description = "测试谷歌地图服务", paraDesc = "query")
    public String testGoogleMap(String query) {

        Map<String, Object> param = new HashMap();
        param.put("query", query);

        try {
            String result = (String) genericOrderService.$invoke("searchText",
                    new String[]{"com.taobao.trip.jourprod.facade.model.request.GoogleMapRequest"},
                    new Object[]{param});
            
            // 记录谷歌地图API调用日志
            MapLogUtils.logGoogleApiCall(
                MapLogUtils.GOOGLE_API_SEARCH_TEXT, 
                param, 
                result
            );
            
            return result;
        } catch (Throwable e) {
            e.printStackTrace();
            return e.toString();
        }

    }

    @AteyeInvoker(description = "测试谷歌POI查询服务", paraDesc = "poiId")
    public String testGoogleMapPoiById(String poiId) {
        return getGoogleMapPoiById(poiId).toJSONString();
    }

    public JSONObject getGoogleMapPoiById(String poiId) {
        Map<String, Object> param = new HashMap();
        param.put("poiId", poiId);

        try {
            String result = (String) genericOrderService.$invoke("getPoiById",
                    new String[]{"com.taobao.trip.jourprod.facade.model.request.GoogleMapRequest"},
                    new Object[]{param});
            
            // 记录谷歌地图API调用日志
            MapLogUtils.logGoogleApiCall(
                MapLogUtils.GOOGLE_API_GET_POI_BY_ID, 
                param, 
                result
            );
            
            if (StringUtils.isEmpty(result)) {
                return null;
            }
            return JSON.parseObject(result);
        } catch (Throwable e) {
            LOGGER.recordDangerException(new LogModel("getGoogleMapPoiById").request(poiId).e(e));
        }
        return null;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        HSFApiConsumerBean hsfApiConsumerBean = new HSFApiConsumerBean();
        hsfApiConsumerBean.setInterfaceName("com.taobao.trip.jourprod.facade.googlemap.GoogleMapFacade");
        hsfApiConsumerBean.setVersion("1.0.0");
        hsfApiConsumerBean.setGroup("HSF");

        hsfApiConsumerBean.setGeneric("true");

        //预发：
        //Lists.newArrayList("rg-sg-pre","lazada-sg-2-pre");
        //线上
        //Lists.newArrayList("rg-sg","lazada-sg-2");
        List<String> configserverCenter = Lists.newArrayList(configServer.split(","));
        hsfApiConsumerBean.setConfigserverCenter(configserverCenter);
        hsfApiConsumerBean.init(true);

        genericOrderService = (GenericService) hsfApiConsumerBean.getObject();
    }
}
