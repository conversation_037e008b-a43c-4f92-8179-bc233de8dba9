package com.taobao.trip.jourprod.service.facade.journeyplannew;

import com.taobao.trip.jourprod.service.facade.model.request.journeyplan.AiPoiIconParameter;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiPoiIconDTO;

import java.util.List;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public interface AiPoiIconService {
    /**
     * 根据参数统计总数
     * @param param
     */
    long count(AiPoiIconParameter param);

    /**
     * 根据参数查询
     * @param param
     */
    AiPoiIconDTO find(AiPoiIconParameter param);

    /**
     * 列表查询
     * @param param
     */
    List<AiPoiIconDTO> list(AiPoiIconParameter param);

    /**
     * 创建
     * @param param
     */
    Integer create(AiPoiIconParameter param);

    /**
     * 选择性修改
     * @param param
     */
    Integer updateSelective(AiPoiIconParameter param);
}