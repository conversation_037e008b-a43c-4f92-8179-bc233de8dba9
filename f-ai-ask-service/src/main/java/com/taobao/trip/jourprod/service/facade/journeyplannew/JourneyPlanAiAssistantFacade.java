package com.taobao.trip.jourprod.service.facade.journeyplannew;

import com.taobao.mtop.common.Result;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.*;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.*;

import java.util.List;

/**
 * 智能出行规划助手
 */
public interface JourneyPlanAiAssistantFacade {

    /**
     * 初始化接口，给用户返回初始化信息
     * @param request
     * @return
     */
    Result<JourneyPlanAiInitResult> init(JourneyPlanAiInitRequest request);

    /**
     * 对话接口
     * @param request
     * @return
     */
    Result<StreamMessageResult> chat(JourneyPlanAiChatRequest request);

    /**
     * 对话续接接口
     * @param request
     * @return
     */
    Result<StreamMessageResult> continueChat(JourneyPlanAiChatRequest request);

    /**
     * 停止回答
     * @param request
     * @return
     */
    Result<Boolean> stopChat(JourneyPlanAiChatRequest request);

    /**
     * 总结返回接口
     * @param request
     * @return
     */
    Result<JourneyPlanMessageSummaryResult> chatSummary(JourneyPlanAiChatRequest request);

    /**
     * 重置会话
     * @param request
     * @return
     */
    Result<Boolean> resetChat(JourneyPlanAiChatRequest request);

    /**
     * 查询历史消息，按照创建时间降序
     */
    Result<JourneyPlanAiAssistantHistoryWithCurrentChatResult> history(JourneyPlanAiHistoryRequest request);

    /**
     * 评价消息
     *
     * @param request
     * @return
     */
    Result<Boolean> wrateMessage(JourneyPlanAiChatRequest request);

    /**
     * 分享消息
     * @param request
     * @return
     */
    Result<JourneyPlanShareMessageResult> shareMessage(JourneyPlanAiChatShareRequest request);

    /**
     * 查看分享消息
     * @param request
     * @return
     */
    Result<JourneyPlanAiAssistantHistoryResult> viewShareMessage(JourneyPlanAiChatViewShareRequest request);

    /**
     * 根据消息id查看结构化线路
     */
    Result<JourneyPlanAiStructRouteResult> structRoute(JourneyPlanAiStructRouteRequest request);

    /**
     * 删除消息
     */
    Result<Boolean> deleteMessage(JourneyPlanDeleteMessageRequest request);

    /**
     * 查询poi详情用浮层展示
     */
    Result<JourneyPlanQueryPoiInfoByFloatResult> queryPoiInfoByFloat(JourneyPlanQueryPoiInfoByFloatRequest request);

    /**
     * 是否有历史消息
     * @param request
     * @return
     */
    Result<Boolean> hasHistory(JourneyPlanAiChatRequest request);

    /**
     * 查询消息（不写到历史）
     * @param request 请求参数
     * @return 消息
     */
    Result<List<JourneyPlanAiAssistantHistoryResult.MessageInfo>> getMessageInfo(JourneyPlanAiChatRequest request);

}
