package com.taobao.trip.jourprod.service.facade.journeyplannew;

import java.util.List;

import com.taobao.mtop.common.Result;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiAssistantFeedbackRequest;

/**
 * 问题反馈接口
 */
public interface JourneyPlanAiAssistantFeedBackFacade {

    /**
     * 问题收集标签列表
     */
    Result<List<String>> feedBackTagList(JourneyPlanAiAssistantFeedbackRequest request);

    /**
     * 问题收集
     */
    Result<Boolean> feedBack(JourneyPlanAiAssistantFeedbackRequest request);

}
