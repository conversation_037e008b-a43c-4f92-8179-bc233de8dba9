/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2014 All Rights Reserved.
 */
package com.taobao.trip.jourprod.service.facade.model;

import java.io.Serializable;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

/**
 * 打印字符串工具类
 * <p>说明
 * <li>主要用于log中直接打印对象,将对象中所有属性打印</li>
 * <AUTHOR>
 * @version $Id: ToString.java, v 0.1 2014-8-29 下午8:32:50 hongbao.xhb Exp $
 */
public class ToString implements Serializable {

    /**  */
    private static final long serialVersionUID = 626933953114974004L;

    /** 
     * @see Object#toString()
     */
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
