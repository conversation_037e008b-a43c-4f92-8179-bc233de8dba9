package com.taobao.trip.jourprod.service.facade.model.request.gaode;

import lombok.Data;

import java.io.Serializable;

/**
 * 高德 周边 搜索-入参
 */
@Data
public class GaoDeAroundQueryRequest implements Serializable {

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 查询poi类型
     * 多个类型用“|”分割
     */
    private String types;

}
