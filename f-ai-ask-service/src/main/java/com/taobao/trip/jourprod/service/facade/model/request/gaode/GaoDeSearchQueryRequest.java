package com.taobao.trip.jourprod.service.facade.model.request.gaode;

import java.io.Serializable;
import lombok.Data;

/**
 * 高德 搜索
 */
@Data
public class GaoDeSearchQueryRequest implements Serializable {

    private static final long serialVersionUID = 285599218892330675L;

    /**
     * 查询关键词
     */
    private String keywords;

    /**
     * 城市名
     */
    private String city;

    /**
     *仅返回指定城市数据
     */
    private Boolean citylimit;

    /**
     * 当前页数，
     */
    private Integer page;

    /**
     * 每页数量
     */
    private Integer offset;

    /**
     * 做日志
     */
    private Long userId;

    /**
     * 查询key
     */
    private String key;

    /**
     * 搜索区划，高德2.0接口才有
     */
    private String region;

    /**
     * 返回结果控制，高德2.0接口, 多个字段间采用“,”进行分割
     */
    private String showFields;

    /**
     * 当前分页展示的数据条数， 高德2.0， 1-25， 默认10
     */
    private Integer pageSize;

    /**
     * 当前页码， 高德2.0，1-100， 默认1
     */
    private Integer pageNum;

    /**
     * 是否使用2.0接口
     */
    private Boolean user2Version;

    /**
     * 中心点坐标
     * 经度和纬度用","分割，经度在前，纬度在后
     */
    private String location;

    /**
     * 查询poi类型
     * 多个类型用“|”分割
     */
    private String types;

    /**
     * 查询半径，单位米
     * 取值范围:0-50000
     */
    private Integer radius;

}
