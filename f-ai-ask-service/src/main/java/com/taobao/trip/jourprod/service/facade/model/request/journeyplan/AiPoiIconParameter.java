package com.taobao.trip.jourprod.service.facade.model.request.journeyplan;

import java.io.Serializable;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class AiPoiIconParameter implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *   主键
     */
    @Getter
    @Setter
    private Long id;

    /**
     *   poi id
     */
    @Getter
    @Setter
    private Long poiId;

    /**
     *   poi名称
     */
    @Getter
    @Setter
    private String poiName;

    /**
     *   icon图标
     */
    @Getter
    @Setter
    private String icon;

    /**
     *   状态 1 不可用 2 可用
     */
    @Getter
    @Setter
    private Short status;

    /**
     *   poiIdList
     */
    @Getter
    @Setter
    private List<Long> poiIdList;

    @Getter
    @Setter
    private String cityCode;

    @Getter
    @Setter
    private String cityName;

    @Getter
    @Setter
    private String llm;
}