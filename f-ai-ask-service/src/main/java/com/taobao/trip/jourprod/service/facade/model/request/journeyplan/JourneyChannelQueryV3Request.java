package com.taobao.trip.jourprod.service.facade.model.request.journeyplan;

import com.taobao.trip.wireless.common.mtop.domain.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 行程首页 V3请求参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
public class JourneyChannelQueryV3Request extends BaseParam {

    private static final long serialVersionUID = -2842451364707457568L;

    /**
     * 套餐的下一页 是第几页
     * -只针对酒店套餐
     */
    private Integer nextTaoCanPage;

    /**
     * 上次访问的行程卡片最后一个的 ID
     */
    private String lastCardId;

    /**
     * 经度
     */
    private String lng;

    /**
     * 纬度
     */
    private String lat;

    /**
     * lngParam
     * latParam
     * 这两个都是针对支端小程序优化的，因为支端小程序mtop拿不到经纬度，需要前端传
     */
    private String lngParam;
    private String latParam;

    /**
     * 是否查询目前的代订订单数量
     */
    private Boolean querySubOrderNum;

    /**
     * Mtop接口版本号
     */
    private String mtopVersion;

    /**
     * 客户端类型
     */
    private String clientType;

    /**
     * 要不要用缓存
     */
    private Boolean useCache;

    /**
     * 第几页
     */
    private Integer pageNo;

    /**
     * 每页几个
     */
    private Integer pageSize;

    /**
     * 选择城市
     */
    private Long divisionId;

}
