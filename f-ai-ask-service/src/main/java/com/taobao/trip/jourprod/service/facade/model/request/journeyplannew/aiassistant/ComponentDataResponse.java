package com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant;

import com.alibaba.fastjson.JSONObject;
import com.taobao.trip.wireless.common.mtop.domain.BaseParam;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @Description AI对话解析组件数据响应结果
 * <AUTHOR>
 **/
@AllArgsConstructor
@Data
public class ComponentDataResponse extends BaseParam {


    private String componentType;

    /**
     * 解析出来的容器id
     */
    private String id;

    /**
     * 标签之后的全部数据
     */
    private String allData;

    /**
     * 结构化数据
     */
    private JSONObject data;

    /**
     * 是否解析完整
     */
    private boolean complete;
}
