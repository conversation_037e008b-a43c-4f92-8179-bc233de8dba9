package com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 酒店推荐卡视图
 * <AUTHOR>
 */
@Data
public class HotelBookingCardVO implements Serializable {

    /**
     * 酒店价格
     */
    private String price;

    /**
     * 价格后缀，默认“起/晚”
     */
    private String priceSuffix;

    /**
     * 预定按钮名称
     */
    private String bookingBtnName;

    /**
     * 预定跳转链接
     */
    private String bookingBtnJumpUrl;
}
