package com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 酒店套餐推荐卡视图
 * <AUTHOR>
 */
@Data
public class HotelPackageRecommendCardVO implements Serializable {
    /**
     * 商品ID
     */
    private Long itemId;

    /**
     * 套餐标题
     */
    private String itemTitle;

    /**
     * 套餐价格
     */
    private String price;
    /**
     * 价格前面的文本描述，比如：券后价
     */
    private String pricePreDesc;
    /**
     * 价格后缀，默认“起/晚”
     */
    private String priceSuffix;

    /**
     * 推荐理由
     */
    private String recommendReason;

    /**
     * 酒店评分
     */
    private String rateScore;

    /**
     * 图片列表
     */
    private List<String> picUrls;

    /**
     * 预定按钮名称
     */
    private String bookingBtnName;

    /**
     * 跳转链接
     */
    private String jumpUrl;

    /**
     * 是否收藏
     */
    private Boolean isCollect;

    /**
     * 召回数据对象本身，用于前端直接取行业返回额数据
     */
    private Object originalData;
}
