package com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 酒店推荐卡视图
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HotelRecommendCardVO implements Serializable {

    private static final long serialVersionUID = -2617172441917564537L;
    /**
     * 酒店标题
     */
    private String title;

    /**
     * 酒店价格
     */
    private String price;
    /**
     * 价格后缀，默认“起/晚”
     */
    private String priceSuffix;
    /**
     * 价格前面的文本描述，比如：国际酒店展示“含税价”
     */
    private String pricePreDesc;

    /**
     * 酒店图片列表
     */
    private List<String> picUrls;

    /**
     * 酒店头图
     */
    private String picUrl;

    /**
     * shid
     */
    private String shid;

    /**
     * 推荐理由
     */
    private String recommendReason;

    /**
     * 酒店评分
     */
    private String rateScore;

    /**
     * 酒店星级
     */
    private String star;

    /**
     * 评价标签列表
     */
    List<RateTagVO> rateTagList;

    /**
     * 酒店距离
     */
    private String distince;

    /**
     * 酒店跳转链接
     * 示例:{
     *     "params": "",
     *     "url": ""
     *     }
     */
    private Object jumpUrl;

    /**
     * 酒店标签列表
     */
    private List<JSONObject> tagList;

    /**
     * 是否收藏
     */
    private Boolean isCollect;

    /**
     * 预定按钮名称
     */
    private String bookingBtnName;

    /**
     * 召回数据对象本身，用于前端直接取行业返回额数据
     */
    private Object originalData;

    /**
     * 预定按钮跳转链接
     */
    private String bookingBtnJumpUrl;

    /**
     * 是否编辑
     */
    private Boolean edit;
    /**
     * 点击后展示文案
     */
    private String clickText;
    /**
     * 标签
     */
    private List<Map<String, Object>> keyboardTags;
    /**
     * 标签 --上线后需要把keyboardTags字段删掉
     */
    private List<Map<String, Object>> keyboardTagsV1;

    /**
     * 入住日期
     */
    private String checkIn;

    /**
     * 离店日期
     */
    private String checkOut;

    /**
     * 底部提示文案
     */
    private String bottomTips;
    private String bottomTipIcon;

    /**
     * 优惠详情弹窗链接
     */
    private String discountDetailLayerUrl;
}
