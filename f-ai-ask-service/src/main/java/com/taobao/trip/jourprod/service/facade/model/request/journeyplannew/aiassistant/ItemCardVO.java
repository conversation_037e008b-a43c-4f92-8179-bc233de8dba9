package com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.List;

@Data
public class ItemCardVO {

    /**
     * 商品id
     */
    private String itemId;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 主图
     */
    private String mainPic;

    /**
     * 价格
     */
    private String price;

    /**
     * 已售
     */
    private String sold;

    /**
     * 跳转链接
     */
    private String jumpUrl;

    /**
     * 标签
     */
    private List<TagCardVO> tags;

    /**
     * 店铺信息
     */
    private TagCardVO shopInfo;

    /**
     * 店铺荣誉
     */
    private List<TagCardVO> shopHonorInfo;

    /**
     * 是否已收藏
     */
    private Boolean isCollected;

    /**
     * 埋点信息
     */
    private JSONObject trackInfo;

    /**
     * 价格信息
     */
    private JSONObject priceInfo;
}
