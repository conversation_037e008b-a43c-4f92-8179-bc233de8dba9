package com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * @Description 删除历史消息请求
 * <AUTHOR>
 * @Date 2025/2/23
 **/
@Data
public class JourneyPlanAiAssistantDeleteMessageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    private String userId;

    private List<String> messageIdList;

    /**
     * 要删除的sessionId
     */
    private List<String> sessionIdList;

    /**
     * 要删除所有消息
     */
    private Boolean deleteAll;

}