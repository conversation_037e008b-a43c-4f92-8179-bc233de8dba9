package com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant;

import com.taobao.trip.wireless.common.mtop.domain.BaseParam;
import lombok.Data;

/**
 * @Description 问题收集请求
 * <AUTHOR>
 * @Date 2025/3/28
 **/
@Data
public class JourneyPlanAiAssistantFeedbackRequest extends BaseParam {

    /**
     * 消息id
     */
    private String messageId;

    /**
     * 对话类型 eg. 线路规划 交通 酒店……
     */
    private String type;

    /**
     * 标签列表
     */
    private String tagList;

    /**
     * 标签
     */
    private String tag;

    /**
     * 反馈内容
     */
    private String content;

}
