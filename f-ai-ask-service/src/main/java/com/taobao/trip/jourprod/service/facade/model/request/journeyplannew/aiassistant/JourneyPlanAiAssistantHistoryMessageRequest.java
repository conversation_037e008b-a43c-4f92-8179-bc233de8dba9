package com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant;

import com.taobao.trip.tripjourneyop.domain.request.BaseJourneyOpRequest;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description ai行程规划查询历史消息请求
 * <AUTHOR>
 * @Date 2025/2/10
 **/
@Data
public class JourneyPlanAiAssistantHistoryMessageRequest  extends BaseJourneyOpRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 只查询某个会话的消息
     */
    private String sessionId;

    /**
     * 开始查询的key
     */
    private String startKey;

    /**
     * 一页数量，默认10个
     */
    private Integer pageSize = 10;

    /**
     * 开始时间
     */
    private Date leftTime;

    /**
     * 结束时间
     */
    private Date rightTime;

    /**
     * 指定要查询的消息列表
     */
    private List<String> messageIdList;

    /**
     * 是否只查询结构化消息
     */
    private Boolean onlyStructRoute;

    private List<String> orignalMessageIdList;

}
