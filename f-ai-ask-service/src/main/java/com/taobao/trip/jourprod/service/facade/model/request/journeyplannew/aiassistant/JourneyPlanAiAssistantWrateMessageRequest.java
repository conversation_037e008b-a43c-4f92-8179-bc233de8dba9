package com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant;

import java.io.Serializable;
import java.util.Date;

import com.taobao.trip.tripjourneyop.domain.request.BaseJourneyOpRequest;
import lombok.Data;

/**
 * @Description ai行程规划查询历史消息请求
 * <AUTHOR>
 * @Date 2025/2/10
 **/
@Data
public class JourneyPlanAiAssistantWrateMessageRequest extends BaseJourneyOpRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    private String messageId;

    private String wrate;

}