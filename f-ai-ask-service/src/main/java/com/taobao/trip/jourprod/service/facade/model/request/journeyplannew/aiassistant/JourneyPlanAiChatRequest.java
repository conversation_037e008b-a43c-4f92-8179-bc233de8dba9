package com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant;

import com.google.common.collect.Maps;
import com.taobao.trip.common.AskType;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyStopSceneEnum;
import com.taobao.trip.wireless.common.mtop.domain.BaseParam;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;

/**
 * @Description 问一问对话请求参数
 * <AUTHOR>
 * @Date 2025/1/26
 **/
@Data
public class JourneyPlanAiChatRequest extends BaseParam {

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 用户指令
     */
    private String chat;

    /**
     * 当前会话窗口ID
     */
    private String sessionId;


    /**
     * 要处理的消息id
     */
    private String currentMessageId;


    /**
     * 系统消息id
     */
    private String systemMessageId;

    /**
     * 用户消息id
     */
    private String userMessageId;


    /**
     * 评价动作 点/踩
     */
    private String action;

    /**
     * 序列号
     */
    private Integer seq;

    /**
     * 是否重试，如果是重试，就要在prompt上加下说明
     */
    private Boolean retry;

    /**
     * 停止回答时 要配置的状态
     * stop-仍然会出现在历史记录
     * end-直接设置成结束，为了防止机器挂掉，没有及时设置结束
     */
    private String status;

    /**
     * 请求时间
     */
    private Date requestTime = new Date();

    /**
     * 分享消息id
     */
    private String originalMessageIds;


    private List<String> messageIds;

    /**
     * 扩展字段逻辑
     */
    private String ext;

    /**
     * 来源 journey_ask行程问一问，search_ask首页搜索问一问
     */
    private String source = AskType.JOURNEY_ASK.getCode();

    /**
     * 停止的场景 @{@link AiJourneyStopSceneEnum}
     */
    private String stopScene;

    /**
     * 停止的原因
     */
    private String stopReason;

    public Map<String, Object> extractExtInfo() {
        if (StringUtils.isBlank(ext)) {
            return Maps.newHashMap();
        }
        try {
            return JSON.parseObject(ext, new TypeReference<>() {});
        } catch (Exception e) {
            return Maps.newHashMap();
        }
    }

}
