package com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant;

import java.util.List;

import com.taobao.trip.wireless.common.mtop.domain.BaseParam;
import lombok.Data;

/**
 * @Description 历史消息请求数据
 * <AUTHOR>
 * @Date 2025/2/3
 **/
@Data
public class JourneyPlanAiHistoryRequest extends BaseParam {

    /**
     * 开始查询的key
     */
    private String startKey;

    /**
     * 一页数量，默认2个
     */
    private Integer pageSize = 2;

    /**
     * 开始时间
     */
    private String leftTime;

    /**
     * 结束时间
     */
    private String rightTime;

    /**
     * 指定消息id查询
     */
    private List<String> messageIds;

    /**
     * 会话id
     */
    private String sessionId;

}
