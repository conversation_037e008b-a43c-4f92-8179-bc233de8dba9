package com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant;

import com.taobao.trip.wireless.common.mtop.domain.BaseParam;
import lombok.Data;

/**
 * @Description 问一问初始化接口请求参数
 * <AUTHOR>
 * @Date 2025/1/21
 **/
@Data
public class JourneyPlanAiInitRequest extends BaseParam {

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 消息信息
     */
    private String token;


    /**
     * 页面类型，首页欢迎页，如：homepageCommon
     */
    private String source;

    /**
     * 消息ids
     */
    private String originalMessageIds;
}
