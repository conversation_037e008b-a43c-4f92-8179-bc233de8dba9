package com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PoiCardVO implements Serializable {

    /**
     * poiId
     */
    private String poiId;

    /**
     * 名称
     */
    private String name;

    /**
     * 跳转链接
     */
    private String jumpUrl;

    /**
     * 主图
     */
    private String mainPic;

    /**
     * 价格
     */
    private String price;

    /**
     * 标签
     */
    private List<TagCardVO> tags;

    /**
     * 已售数据
     */
    private String sold;

    /**
     * 是否已收藏
     */
    private Boolean isCollected;

    /**
     * 价格类型:如 门票/跟团游等
     */
    private String priceCateType;

    /**
     * 埋点信息
     */
    private JSONObject trackInfo;

    /**
     * 价格信息
     */
    private JSONObject priceInfo;

}
