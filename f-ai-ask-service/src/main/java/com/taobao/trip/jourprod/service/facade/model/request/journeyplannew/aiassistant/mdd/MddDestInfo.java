package com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.mdd;

import lombok.Data;

import java.util.List;

@Data
public class MddDestInfo {

    private Double popularity = -1.0;
    private Double relevanceScore = -1.0;
    private Double finalScore = -1.0;

    private String treeId;

    private String treeName;

    /**
     * 目的地id
     */
    private String destId;

    /**
     * 目的地名称
     */
    private String destName;

    /**
     * 目的地图片
     */
    private String destImg;

    /**
     * 父级目的地id
     */
    private String parentDestId;

    /**
     * 父级目的地名称
     */
    private String parentDestName;

    /**
     * 搜索提升量
     */
    private String searchIncr;

    /**
     * 搜索uv
     */
    private String searchCount;

    /**
     * 子玩法集合
     */
    private List<SubPlayInfo> subPlayInfoList;

    /**
     * 商圈信息
     */
    private List<BusinessDistrict> businessDistrictList;

    /**
     * 目的地游玩天数
     */
    private String playDayNum;

    /**
     * 签证政策
     */
    private String visaPolicy;

    /**
     * 目的地简称
     */
    private String nameAddr;

    /**
     * 国家id
     */
    private String countryId;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 国内/国家标识 0-国内；1-国际
     */
    private Integer abroad;

    private String recommendReason;


    @Data
    public static class SubPlayInfo {

        /**
         * 子玩法id
         */
        private String subPlayInfoId;

        /**
         * 子玩法名称
         */
        private String subPlayInfoName;

        /**
         * 子玩法图片原始数据
         */
        private String subPlayInfoImgStr;

        /**
         * 子玩法图片
         */
        private List<String> subPlayInfoImg;

        /**
         * 子玩法描述
         */
        private String subPlayInfoDesc;

        /**
         * 子玩法在主玩法下topX
         */
        private String hotRanking;

        /**
         * 第几名
         */
        private String hotRankingNum;

        /**
         * 主玩法信息
         */
        private MainPlayInfo mainPlayInfo;

        /**
         * poi信息
         */
        private List<DestPoiInfo> poiInfoList;
    }

    @Data
    public static class MainPlayInfo {

        /**
         * 主玩法id
         */
        private String mainPlayId;

        /**
         * 主玩法名称
         */
        private String mainPlayName;

        /**
         * 主玩法描述
         */
        private String mainPlayDesc;

        /**
         * 旅行分类
         */
        private String category;
    }

    @Data
    public static class DestPoiInfo {

        /**
         * poi id
         */
        private String poiId;

        /**
         * poi名称
         */
        private String poiName;

        /**
         * poi评分
         */
        private String poiRating;

        /**
         * poi标签
         */
        private String poiTag;

        /**
         * poi图片
         */
        private String poiImg;

        /**
         * poi描述
         */
        private String poiDesc;
    }

    @Data
    public static class BusinessDistrict {

        private Long id;

        /**
         * 商圈名称
         */
        private String businessName;

        /**
         * 商圈推荐语
         */
        private String businessSummary;

        /**
         * 商圈标签，英文逗号分割
         */
        private String businessTags;

        /**
         * 商圈区域
         */
        private String businessRegion;

        /**
         * 是否是主要的
         */
        private Integer isMajor;

        /**
         * 经度
         */
        private String longitude;

        /**
         * 纬度
         */
        private String latitude;

        /**
         * 热度
         */
        private String hot;
    }
}
