package com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.mdd;

import lombok.Data;

import java.io.Serializable;

/**
 * 目的地 玩法-目的地 数据
 */
@Data
public class MddPlayMethodDestinationCardDTO implements Serializable {

    /**
     * 目的地名称
     */
    private String destName;

    /**
     * 父级目的地名称
     */
    private String parentName;

    /**
     * 子玩法图片
     */
    private String subPlayMethodImg;

    /**
     * 子玩法名称
     */
    private String subPlayMethodName;

    /**
     * 交通信息
     */
    private String trafficInfo;

    /**
     * 热门标签
     */
    private String hotTag;

    /**
     * 签证信息
     */
    private String visaPolicy;

}
