package com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.mdd;

import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.KeyboardTagDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 目的地 玩法标题 卡视图
 */
@Data
public class MddPlayTitleCardVO implements Serializable {

    /**
     * 序号
     */
    private String index;

    /**
     * 目的地名称
     */
    private FieldCardVO destName;

    /**
     * 点击文本
     */
    private String clickText;

    /**
     * 国家名称
     */
    private FieldCardVO countryName;

    /**
     * 政策信息
     */
    private FieldCardVO visaPolicy;

    /**
     * 热度信息
     */
    private FieldCardVO hotTag;

    /**
     * 关键词
     */
    private List<KeyboardTagDTO> keyboardTags;

    /**
     * 埋点
     */
    private TrackInfo trackInfo;


}
