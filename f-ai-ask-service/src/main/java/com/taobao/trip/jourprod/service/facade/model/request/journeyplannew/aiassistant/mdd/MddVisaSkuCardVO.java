package com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.mdd;

import com.fliggy.travelvc.sbc.dilu.client.model.country.VisaItemLabelFeature;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 目的地 签证sku 卡视图
 */
@Data
public class MddVisaSkuCardVO implements Serializable {

    /**
     * 商品ID
     */
    private Long itemId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 国旗
     */
    private String countryLogo;
    /**
     * 国家vid
     */
    private Long countryVid;

    /**
     * 国家总办理人数
     */
    private String handledNum;


    /**
     * 出签率
     */
    private String approveRate;

    /**
     * 服务标签
     */
    private List<VisaItemLabelFeature> serviceTag;

    /**
     * sku标题
     */
    private String skuTitle;

    /**
     * sku办理人数
     */
    private String skuHandledNum;

    /**
     * 价格
     */
    private String priceText;

    /**
     * 预定跳转链接
     */
    private String jumpUrl;

    /**
     * 处理时间
     */
    private String handleTime;
    /**
     * 处理时间描述
     */
    private String handleTimeDesc;

    /**
     * 材料描述
     */
    private String materialDesc;

    /**
     * 材料数量
     */
    private String materialNum;

    /**
     * 材料简化描述
     */
    private String simplifyMaterialDesc;

    /**
     * 最快xx签证到手
     */
    private String visaReceiveExpectDate;

    /**
     * 最快出签
     */
    private String fastSignOutTag;

    /**
     * 卖家名称
     */
    private String sellerName;

    /**
     * 卖家logo
     */
    private String brandLogo;

    /**
     * 推荐理由列表
     */
    private List<RecommendReason> recommend;


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RecommendReason {
        private String key;
        private String value;
    }

}
