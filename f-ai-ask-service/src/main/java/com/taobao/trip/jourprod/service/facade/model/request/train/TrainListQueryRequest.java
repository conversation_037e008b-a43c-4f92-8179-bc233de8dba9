package com.taobao.trip.jourprod.service.facade.model.request.train;

import com.taobao.trip.wireless.common.mtop.domain.BaseParam;
import java.util.List;
import lombok.Data;

/**
 * 火车搜索列表接口
 * <AUTHOR>
 * @time 2023/11/27 15:51
 */
@Data
public class TrainListQueryRequest extends BaseParam {

    private static final long serialVersionUID = -4690916344242903584L;

    /**
     * 出发地名称
     */
    private String depAreaName;

    /**
     * 到达地名称
     */
    private String arrAreaName;

    /**
     * 出发站
     */
    private String depLocation;

    /**
     * 到达站
     */
    private String arrLocation;

    /**
     * 出发类型
     * 0:火车，1:汽车，2:城市，3:区县
     */
    private Integer depType;

    /**
     * 到达类型
     * 0:火车，1:汽车，2:城市，3:区县
     */
    private Integer arrType;

    /**
     * 要查询的火车类型
     * 1=普快, 2=新空普快, 3=普客, 4=快速, 5=新空普客, 6=城际高速, 7=动车组, 8=高速动车, 9=新空快速, 10=新空特快, 11=特快, 12=新空直达
     */
    private List<String> trainTypeList;

    /**
     * 出发日期 2023-12-10
     */
    private String depDate;

    /**
     * 查询唯一索引
     */
    private String uniqueIndex;

}
