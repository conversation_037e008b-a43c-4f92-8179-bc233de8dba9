package com.taobao.trip.jourprod.service.facade.model.response;

import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/15
 */
@Data
public class EnumItemVO implements Serializable {
    private static final long serialVersionUID = 8058951254182484831L;

    private String name;

    private String code;

    public EnumItemVO() {

    }

    public EnumItemVO(String name, String code) {
        this.name = name;
        this.code = code;
    }
}
