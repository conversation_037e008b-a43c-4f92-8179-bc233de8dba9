package com.taobao.trip.jourprod.service.facade.model.response;

/**
 * Created by mojin on 2018/9/3.
 */
public class JourPlanError {
    public static final JourPlanError SYSTEM_ERROR = new JourPlanError("SYSTEM_ERROR","系统很累,小二很忙");
    public static final JourPlanError PARAM_INVALID = new JourPlanError("PARAM_INVALID","参数错误");

    /**************** 分享错误提示-日志查看使用 ****************/
    public static final JourPlanError PLAN_INDEX_QUERY_ERROR = new JourPlanError("PLAN_INDEX_QUERY_ERROR", "行程索引信息查询错误");

    /**************** 火车列表搜索错误提示-前端提示使用 ****************/
    public static final JourPlanError QUERY_TRAIN_LIST_ERROR = new JourPlanError("QUERY_TRAIN_LIST_ERROR", "查询直达火车列表失败");

    /**************** AI问一问-前端提示使用 ****************/
    public static final JourPlanError AI_JOURNEY_CONTENT_NULL_ERROR = new JourPlanError("AI_JOURNEY_CONTENT_NULL_ERROR", "模型返回结果为空,请重试");
    public static final JourPlanError AI_JOURNEY_CHAT_ERROR = new JourPlanError("AI_JOURNEY_CHAT_ERROR", "对话接口异常");
    public static final JourPlanError AI_JOURNEY_SESSION_DELETED_ERROR = new JourPlanError("AI_JOURNEY_SESSION_DELETED_ERROR", "会话已被删除");
    public static final JourPlanError AI_JOURNEY_CHAT_IN_PROGREE_NUMBER_MORE_ERROR = new JourPlanError("AI_JOURNEY_CHAT_IN_PROGREE_NUMBER_MORE_ERROR", "进行中对话数量超过上限");

    /**************** AI问一问-对话接口日志排查使用 ****************/
    public static final JourPlanError CHAT_BIZ_ERROR = new JourPlanError("CHAT_BIZ_ERROR","对话接口业务异常");

    private String msgInfo;
    private String msgCode;

    public JourPlanError(String msgCode, String msgInfo) {
        this.msgCode = msgCode;
        this.msgInfo = msgInfo;
    }

    public String getMsgInfo() {
        return msgInfo;
    }

    public String getMsgCode() {
        return msgCode;
    }
}
