package com.taobao.trip.jourprod.service.facade.model.response.fliggyhome;

import java.io.Serializable;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2021/4/8 
 */
@Getter
@ToString
public class FliggyHomeResult<T> implements Serializable {
    private static final long serialVersionUID = -6640421566033835443L;

    private T data;

    private String code;

    private String msg;

    private boolean success;

    public FliggyHomeResult(T data, String code, String msg, boolean success) {
        this.data = data;
        this.code = code;
        this.msg = msg;
        this.success = success;
    }
}
