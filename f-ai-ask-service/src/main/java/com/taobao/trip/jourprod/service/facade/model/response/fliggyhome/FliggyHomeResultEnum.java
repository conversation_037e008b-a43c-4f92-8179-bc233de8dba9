package com.taobao.trip.jourprod.service.facade.model.response.fliggyhome;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/4/8
 */
@Getter
public enum FliggyHomeResultEnum {
    /**
     * 成功
     */
    SUCCESS("SUCCESS", "success"),

    /**
     * 失败
     */
    FAIL("OSC-DEF-001", "fail"),

    /**
     * 参数错误
     */
    PARAM_ERROR("OSC-PARAM-ERROR", "参数错误"),

    TEST_REQUEST_CHECK_FAILED("OSC-TEST-REQUEST-CHECK-FAILED", "内部压测接口，内部流量检查失败"),

    /**
     * 业务类型解析错误
     */
    BIZCODE_PARSE_ERROR("OSC-PARSE-ERROR", "业务类型异常"),

    /**
     * 外部接口调用异常
     */
    EXTERNAL_INVOKE_ERROR("OSC-INVOKE-001", "外部接口调用异常"),

    /**
     * 外部接口返回参数异常
     */
    EXTERNAL_INVOKE_RESULT_ERROR("OSC-INVOKE-002", "外部接口返回值异常"),

    /**
     * 组件未命中切流逻辑，不透出
     */
    HIDE_DUE2_MISS_ROUTER("OSC-NOSHOW-001", "未命中切流"),

    /**
     * 不满足业务规则逻辑，不透出
     */
    HIDE_DUE2_BIZ_RULE("OSC-NOSHOW-002", "不满足条件"),

    /**
     * 用户在黑名单中，不透出
     */
    HIDE_DUE2_IN_BLACKLIST("OSC-NOSHOW-003","在黑名单中"),

    /**
     * 没有满足条件的卡片展示
     */
    NO_SHOW_HOMECARD("OSC-NOSHOW-003", "没有满足条件的卡片展示"),

    /**
     * 未命中ABTEST分桶
     */
    MIS_MATCH_ABTEST_BUCKET("OSC-NOSHOW-001", "未命中abtest分桶"),

    /**
     * 外部接口无数据返回
     */
    EXTERNAL_RESULT_EMPTY("OSC-NODATA-001", "无数据"),

    /**
     * 服务器内部故障
     */
    SYSTEM_ERROR("OSC-ERROR-500", "服务器内部错误"),

    ;

    /**
     * 状态码
     */
    private String code;

    /**
     * 状态信息
     */
    private String msg;

    FliggyHomeResultEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
