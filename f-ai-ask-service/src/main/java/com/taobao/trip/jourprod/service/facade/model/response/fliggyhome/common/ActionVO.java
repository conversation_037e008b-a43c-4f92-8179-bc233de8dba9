package com.taobao.trip.jourprod.service.facade.model.response.fliggyhome.common;

import com.taobao.trip.jourprod.service.facade.enums.journeyplan.ActionJumpTypeEnum;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/4/8
 */
@Data
@AllArgsConstructor
public class ActionVO {

    private String id;

    private String key;

    private String text;

    private String jumpUrl;

    private String phoneNum;

    private String bubbleText;

    private String color;

    /**
     * 背景颜色
     */
    private String backgroundColor;

    /**
     * 按钮类型，跳转链接或者电话
     * @see ActionJumpTypeEnum
     */
    private String type;

    /**
     * 按钮样式类型，【1-有圆边框，2-纯文字带箭头】
     * @see ActionStyleTypeEnum
     */
    private Integer styleType;

    /**
     * 业务类型，前端用来拼接埋点信息
     */
    private String business;

    private Map<String, Object> extInfo;

    private Map<String, Object> trackArgs;

    /**
     * 给前端传的原始埋点信息
     */
    private String trackInfo;

    private String spm;

    private String image;

    private String icon;

    /**
     * 气泡-小红块文字 最多不超过四个字
     */
    private String smallRedBlockTips;

    /**
     * 按钮边框的颜色
     */
    private String btnOutlineColor;

    /**
     * 按钮所属服务类型-对于行程V3版本
     */
    private String btnServiceCardTypeV3;

    private boolean disable;

    /**
     * 是否包含红点
     */
    private Boolean redDot;

    /**
     * 是否要预加载
     */
    private Boolean preLoad;

    public ActionVO() {}

    public ActionVO(String text, String jumpUrl) {
        this.text = text;
        this.jumpUrl = jumpUrl;
        this.type = "url";
    }

    public ActionVO(String text, String jumpUrl, String backgroundColor) {
        this.text = text;
        this.jumpUrl = jumpUrl;
        this.backgroundColor = backgroundColor;
        this.type = "url";
    }

    public ActionVO(String text, String phoneNum, String key, ActionJumpTypeEnum type) {
        this.text = text;
        this.phoneNum = phoneNum;
        this.type = type.getType();
        this.key = key;
        this.business = key;
    }

    public ActionVO(String text, String jumpUrl, String backgroundColor, String key) {
        this.text = text;
        this.jumpUrl = jumpUrl;
        this.backgroundColor = backgroundColor;
        this.type = "url";
        this.key = key;
        this.business = key;
    }

    public ActionVO(String text, String jumpUrl, String backgroundColor, String key, String spm) {
        this.text = text;
        this.jumpUrl = jumpUrl;
        this.backgroundColor = backgroundColor;
        this.type = "url";
        this.key = key;
        this.business = key;
        this.spm = spm;
    }

}
