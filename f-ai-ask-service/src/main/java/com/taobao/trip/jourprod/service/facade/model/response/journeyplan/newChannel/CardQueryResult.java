package com.taobao.trip.jourprod.service.facade.model.response.journeyplan.newChannel;

import com.taobao.trip.jourprod.service.facade.model.response.JourPlanError;
import lombok.Data;

@Data
public class CardQueryResult {

    private boolean success;
    /**
     * 失败时填写的错误信息
     */
    private JourPlanError planError;
    /**
     * 数据查询的行数
     */
    private int rows;

    /**
     * 查询的最后一个 ID 数据，一定要填写值
     */
    private String lastCardId;

    /**
     * 是否有下一页
     */
    private boolean hasNextPage;
}
