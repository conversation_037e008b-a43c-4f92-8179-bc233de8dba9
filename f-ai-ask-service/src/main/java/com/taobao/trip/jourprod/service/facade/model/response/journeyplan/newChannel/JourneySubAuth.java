package com.taobao.trip.jourprod.service.facade.model.response.journeyplan.newChannel;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 行程-代订-授权信息
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JourneySubAuth {

    /**
     * 是否已授权
     */
    private Boolean authorized;

    /**
     * 是否展示认证引导
     */
    private Boolean showAuthGuide;

}
