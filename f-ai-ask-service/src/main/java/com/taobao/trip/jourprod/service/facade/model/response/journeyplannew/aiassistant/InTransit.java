package com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant;

import java.io.Serializable;
import lombok.Data;

@Data
public class InTransit implements Serializable {
    private static final long serialVersionUID = -5766876893210982812L;

    /**
     * 是否位移态，true:是，false:否
     */
    private Boolean hasMove = false;

    /**
     * 当前城市code
     */
    private String cityCode;

    /**
     * 当前城市名称
     */
    private String cityName;

    /**
     * 当前城市名称简写
     */
    private String cityNameAbbr;

    /**
     * 常驻城市code
     */
    private String permanentCityCode;

    /**
     * 时区id
     */
    private String timeZoneId;
}