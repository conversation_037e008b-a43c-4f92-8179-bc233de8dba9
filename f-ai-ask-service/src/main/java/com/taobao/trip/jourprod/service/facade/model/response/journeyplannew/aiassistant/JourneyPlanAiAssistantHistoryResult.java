package com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant;

import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneyMessageContentDTO.*;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult.StreamMessageCardModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Description 历史消息返回结果
 * <AUTHOR>
 * @Date 2025/2/10
 **/
@Data
public class JourneyPlanAiAssistantHistoryResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 下一次查询开始的key，即messageId
     */
    private String startKey;

    /**
     * 分享者用户头像
     */
    private String userAvatar;

    /**
     * 消息列表
     */
    private List<JourneyPlanAiAssistantHistoryResult.MessageInfo> messageList;

    public static JourneyPlanAiAssistantHistoryResult empty() {
        return new JourneyPlanAiAssistantHistoryResult();
    }

    /**
     * 消息体
     */
    @Data
    public static class MessageInfo implements Serializable {

        private static final long serialVersionUID = -5766876893210982812L;

        /**
         * 消息id
         */
        private String messageId;

        /**
         * 用户id
         */
        private String userId;

        /**
         * 关联的消息
         */
        private String originalMessageId;

        /**
         * 发送角色
         */
        private String role;


        /**
         * 消息内容
         */
        private String info;

        /**
         * 扩展字段
         */
        private String extra;

        /**
         * 思考内容
         */
        private String thinking;

        /**
         * 思考内容 有没有出完
         */
        private Boolean thinkingAllOut;

        /**
         * 结构化路线和文本格式的内容互斥
         */
        private StructRoute structRoute;

        /**
         * 关联卡片内容
         */
        private List<CardInfo> relatedCard;

        /**
         * 关联链接
         */
        private List<LinkInfo> relatedLink;

        /**
         * 关联链接
         */
        private List<RelatedHotel> relatedHotel;

        /**
         * 关联交通卡
         */
        private TrafficCard trafficCard;

        /**
         * 消息发送时间戳
         */
        private Long timestamp;

        /**
         * 点赞 or 踩
         */
        private String wrate;

        /**
         * 消息类型
         */
        private String type;

        /**
         * 消息状态
         */
        private String status;

        /**
         * 是否手动停止
         */
        private Boolean manuallyStop;

        /**
         * 是否是澄清query
         */
        private Boolean isClearQuery;

        /**
         * 用户指令
         */
        private String userChat;

        /**
         * 组件列表
         */
        private List<StreamMessageCardModel> components;

        /**
         * 思考链数据
         */
        private List<Map<String, Object>> thinkingThought;

        /**
         * 来源
         */
        private String source;

        /**
         * 会话id
         */
        private String sessionId;

    }

}
