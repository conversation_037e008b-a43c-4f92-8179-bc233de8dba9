package com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant;

import com.taobao.trip.jourprod.service.converter.AiJourneyHistoryConvert;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * @Description 包含当前正在回答的消息的结构
 * <AUTHOR>
 * @Date 2025/2/18
 **/
@Data
public class JourneyPlanAiAssistantHistoryWithCurrentChatResult extends JourneyPlanAiAssistantHistoryResult {

    public JourneyPlanAiAssistantHistoryWithCurrentChatResult() {
    }

    public JourneyPlanAiAssistantHistoryWithCurrentChatResult(JourneyPlanAiAssistantHistoryResult journeyPlanAiAssistantHistoryResult) {
        this.setStartKey(journeyPlanAiAssistantHistoryResult.getStartKey());
        List<MessageInfo> messageList = journeyPlanAiAssistantHistoryResult.getMessageList();
        if (CollectionUtils.isNotEmpty(messageList)) {
            for (MessageInfo messageInfo : messageList) {
                List<Map<String, Object>> afterMergeThinkingThought = AiJourneyHistoryConvert.mergeCardsToLastElement(messageInfo.getThinkingThought());
                messageInfo.setThinkingThought(afterMergeThinkingThought);
            }
            this.setMessageList(messageList);
        }

    }

    /**
     * 当前正在进行的对话，如果有的话，就带着会话id请求续接接口
     */
    private String currentSystemMessageId;

    private String currentUserMessageId;

    /**
     * 当前正在进行的对话对应的用户指令
     */
    private String currentChat;

    /**
     * 底部bottom tab
     */
    private List<BottomTab> bottomTabList;

    /**
     * 会话id
     */
    private String sessionId;
}
