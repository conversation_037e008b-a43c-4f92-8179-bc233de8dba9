package com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant;

import com.taobao.trip.jourprod.service.facade.model.response.BaseResult;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Description ai问一问初始化服务返回数据
 * <AUTHOR>
 * @Date 2025/1/20
 **/
@Data
public class JourneyPlanAiInitResult extends BaseResult {

    private static final long serialVersionUID = -5766876893210982812L;

    /**
     * 是否有历史问题
     */
    private Boolean hasHistoryMessage;

    /**
     * 推荐问题
     */
    private List<AiJourneyMessageModel> recommendMessage;

    /**
     * 推荐目的地卡片
     */
    private List<RecommendCard> recommendCard;

    /**
     * 当前正在进行的对话，如果有的话，就带着会话id请求续接接口
     */
    private String currentSystemMessageId;

    private String currentUserMessageId;

    /**
     * 当前正在进行的对话对应的用户指令
     */
    private String currentChat;

    /**
     * 主标题
     */
    private String mainTitle;

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 是否是行中态 true 行中态 false 非行中态
     */
    private boolean hasInTransit = false;

    /**
     * 行中初始化页面信息
     */
    private InTransitInfo inTransit;

    /**
     * 初始欢迎页类型 普通：journeyPlan ； 行中： journeyInTransit
     */
    private String welcomeType = "journeyPlan";

    /**
     * 底部bottom tab
     */
    private List<BottomTab> bottomTabList;

    /**
     * 底部bottom tab
     */
    private List<WelcomeCard> welcomeCardList;

    /**
     * fpt参数
     */
    private Map<String, String> fptParam;

    /**
     * 分享消息内容
     */
    private List<JourneyPlanAiAssistantHistoryResult.MessageInfo> messageInfos;

    /**
     * 会话id
     */
    private String sessionId;

    @Data
    public static class WelcomeCard implements Serializable {
        private static final long serialVersionUID = -5766876893210982812L;

        /**
         * 标题
         */
        private String title;

        /**
         * 图标
         */
        private String icon;

        /**
         * 查询
         */
        private String query;
    }

    @Data
    public static class RecommendCard implements Serializable {
        private static final long serialVersionUID = -5766876893210982812L;

        /**
         * 目的地图片
         */
        private String picture;

        /**
         * 目的地名称
         */
        private String destinationName;

        /**
         * 目的地英文名称
         */
        private String destinationNameEn;

        /**
         * 提示问题
         */
        private String message;
    }

    /**
     * 行中初始化页面信息
     */
    @Data
    public static class InTransitInfo implements Serializable {
        private static final long serialVersionUID = -5766876893210982812L;

        /**
         * 定位poi标题
         */
        private String poiTitle;

        /**
         * 引导词列表
         */
        private CueWordList cueWordList;

        /**
         * poiId 冗余
         */
        private String poiId;

        /**
         * poi名称 冗余
         */
        private String poiName;

        /**
         * 城市code 冗余
         */
        private String cityCode;

        /**
         * 城市名称 冗余
         */
        private String cityName;
    }

    /**
     * 引导词列表
     */
    @Data
    public static class CueWordList implements Serializable {
        private static final long serialVersionUID = -5766876893210982812L;

        /**
         * 带图片的引导词
         */
        private List<CueWord> textPicList;

        /**
         * 文本引导词
         */
        private List<CueWord> textList;
    }

    /**
     * 引导词
     */
    @Data
    public static class CueWord implements Serializable {
        private static final long serialVersionUID = -5766876893210982812L;

        /**
         * 展示标题
         */
        private String showTitle;

        /**
         * 图片地址
         */
        private String picUrl;

        /**
         * 视频地址
         */
        private String videoUrl;

        /**
         * 视频的话需要封面图
         */
        private String thumbPic;

        /**
         * 图片：pic 视频：video
         */
        private String type;
    }
}
