package com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant;

import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneyMessageContentDTO.StructRoute;
import com.taobao.trip.jourprod.service.facade.model.response.BaseResult;
import lombok.Data;

/**
 * @Description 结构化行程对应的结构
 * <AUTHOR>
 * @Date 2025/2/24
 **/
@Data
public class JourneyPlanAiStructRouteResult extends BaseResult {

    /**
     * 结构化行程信息，用于前端展示，因为前端不能解析markdown文本
     */
    private StructRoute structRoute;

}
