package com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant;

import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneyMessageContentDTO.StructRoute;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.KeyboardTagDTO;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.KeyboardTagDTOV2;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.KeyboardTagValueDTOV2;
import com.taobao.trip.jourprod.service.facade.model.response.BaseResult;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description 消息总结接口
 * <AUTHOR>
 * @Date 2025/1/26
 **/
@Data
public class JourneyPlanMessageSummaryResult extends BaseResult {

    private static final long serialVersionUID = -5766876893210982812L;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 替换components定义
     */
    private Map<String, Object> componentsRefreshMap = new HashMap<>();

    /**
     * 结构化行程信息，用于前端展示，因为前端不能解析markdown文本
     */
    private StructRoute structRoute;

    /**
     * 回答的文本内容
     */
    private String info;

    /**
     * 更多诉求
     */
    private String moreDemands;

    /**
     * 灵感词
     */
    private List<String> inspirations;

    /**
     * 灵感词
     * 把inspirations复制到content中
     */
    private List<KeyboardTagValueDTOV2> inspirationsV2;

    /**
     * 更多诉求对应的键盘展示的标签
     */
    private List<KeyboardTagDTO> keyboardTags;

    /**
     * 更多诉求对应的键盘展示的标签v2
     *
     */
    private List<KeyboardTagDTOV2> keyboardTagsV2;

    /**
     * 类型
     */
    private String type;

    /**
     * 用户指令，这里是换一个行程时候的提问
     */
    private String userChat;

    /**
     * 是否是澄清query
     */
    private Boolean isClearQuery;
}
