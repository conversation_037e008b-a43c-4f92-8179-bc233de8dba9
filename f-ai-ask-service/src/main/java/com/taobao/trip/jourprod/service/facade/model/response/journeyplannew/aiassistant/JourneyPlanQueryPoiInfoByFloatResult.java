package com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant;

import com.taobao.trip.jourprod.service.facade.model.response.BaseResult;
import java.util.List;
import lombok.Data;

/**
 * @Description 查询poi详情 展示在浮层-出参
 **/
@Data
public class JourneyPlanQueryPoiInfoByFloatResult extends BaseResult {

    /**
     * poiId
     */
    private String poiId;

    /**
     * poi类型
     */
    private String poiType;

    /**
     * 用户当前经度
     */
    private String userLongitude;

    /**
     * 用户当前纬度
     */
    private String userLatitude;

    /**
     * poi经度
     */
    private String longitude;

    /**
     * poi纬度
     */
    private String latitude;

    /**
     * 是否国外
     */
    private Boolean isAbroad;

    /**
     * 名称
     */
    private String name;

    /**
     * 国外名称
     */
    private String abroadName;

    /**
     * 直线距离当前地点-内容
     */
    private String straightDistance;

    /**
     * 类型
     */
    private String type;

    /**
     * 地址
     */
    private String address;

    /**
     * 图片列表
     */
    private List<String> imgList;

    /**
     * 相册数量
     */
    private Integer photoTotal;

    /**
     * 相册地址
     */
    private String photoUrl;

    /**
     * 收费信息
     */
    private String freeInfo;

    /**
     * 营业时间
     */
    private String businessHours;

    /**
     * 联系电话
     */
    private List<String> contactNumber;

    /**
     * 跳详情页-链接
     */
    private String jumpUrl;

    /**
     * 去购票-链接
     */
    private String bookTicketsUrl;

    /**
     * 坐标类型：WGS84、GCJ-02
     */
    private String geoType;

}
