package com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant;

import com.taobao.trip.jourprod.service.facade.model.response.BaseResult;
import lombok.Data;

/**
 * @Description 分享接口返回结果
 * <AUTHOR>
 * @Date 2025/2/3
 **/
@Data
public class JourneyPlanShareMessageResult extends BaseResult {

    private static final long serialVersionUID = -5766876893210982812L;

    /**
     * 分享生成的token，回流时验证
     */
    private String token;

    /**
     * 主标题
     */
    private String title;

    /**
     * 副标题
     */
    private String subTitle;
}
