package com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant;

import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneyMessageContentDTO.CardInfo;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneyMessageContentDTO.StructRoute;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * @Description 消息内容
 * <AUTHOR>
 * @Date 2025/2/3
 **/
@Data
public class MessageInfo implements Serializable {

    private static final long serialVersionUID = -5766876893210982812L;

    /**
     * 消息id
     */
    private String messageId;

    /**
     * 发送角色
     */
    private String role;

    /**
     * 消息内容
     */
    private String info;

    /**
     * 结构化路线和文本格式的内容互斥
     */
    private StructRoute structRoute;

    /**
     * 关联卡片内容
     */
    private List<CardInfo> relatedCard;

    /**
     * 消息发送时间戳
     */
    private Long timestamp;

    /**
     * 点赞 or 踩 { com.taobao.trip.jourprod.service.facade.enums.journeyplan.JourneyPlanAiAssistantWrateEnum}
     */
    private String wrate;

}
