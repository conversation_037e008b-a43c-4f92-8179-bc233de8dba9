package com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant;

import java.io.Serializable;
import java.util.Objects;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description poi信息
 * <AUTHOR>
 * @Date 2025/2/24
 **/
@Data
public class Poi implements Serializable {

    private static final long serialVersionUID = -5766876893210982812L;

    private String address;

    private double latitude;

    private double longitude;

    private String poiId;

    private String shid;

    private String poiName;

    /**
     * poi来源类型，poi、gaode
     */
    private String type;

    /**
     * poi类型，poi、酒店
     * 默认是poi
     */
    private String poiType;

    private String desc;

    /**
     * 图片地址
     */
    private String picUrl;

    /**
     * XX人去过
     */
    private String halfYearUv;

    /**
     * poiGrade
     */
    private String poiGrade;

    /**
     * 建议游玩时长
     */
    private String suggestPlayTime;

    /**
     * 榜单信息
     */
    private PoiRank poiRank;
    /**
     * poi目的地名称树 以逗号分隔，从大到小
     */
    private String poiDestTreeName;

    /**
     * poi目的地id树  以逗号分隔，从大到小
     */
    private String poiDestTreeId;

    /**
     * 清理id信息
     */
    public void clearIdInfo() {
        this.poiId = null;
    }

    /**
     * 复制poi信息
     */
    public void copyPoiInfo(Poi poi) {
        if (Objects.isNull(poi)) {
            return;
        }
        this.address = poi.getAddress();
        this.latitude = poi.getLatitude();
        this.longitude = poi.getLongitude();
        this.poiId = poi.getPoiId();
        this.type = poi.getType();
        if (StringUtils.isNotBlank(poi.getDesc())) {
            this.desc = poi.getDesc();
        }
    }
}