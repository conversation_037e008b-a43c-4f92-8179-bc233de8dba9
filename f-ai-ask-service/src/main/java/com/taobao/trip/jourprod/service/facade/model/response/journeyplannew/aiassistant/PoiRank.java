package com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant;

import java.io.Serializable;

import lombok.Data;

@Data
public class PoiRank implements Serializable {

    private static final long serialVersionUID = -5889752464041632219L;
    /**
     * 榜单链接
     */
    private String h5Url;
    /**
     * 榜单id
     */
    private String poiListId;    // 对应 poi_list_id
    /**
     * 榜单名称
     */
    private String poiListName;
    /**
     * 榜单排名
     */
    private int rank;
    @Override
    public String toString() {
        return "PoiRank{" +
            "h5Url='" + h5Url + '\'' +
            ", poiListId='" + poiListId + '\'' +
            ", poiListName='" + poiListName + '\'' +
            ", rank=" + rank +
            '}';
    }
}
