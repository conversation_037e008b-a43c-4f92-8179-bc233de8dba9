package com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant;

import com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageCardActionEnum;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageCardStatusEnum;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageCardTypeEnum;
import com.taobao.trip.jourprod.service.facade.model.response.BaseResult;
import com.taobao.trip.jourprod.service.facade.model.response.JourPlanError;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Description 流式返回结果
 * <AUTHOR>
 * @Date 2025/1/26
 **/
@Data
public class StreamMessageResult extends BaseResult {

    private static final long serialVersionUID = -5766876893210982812L;

    /**
     * 系统消息id
     */
    private String systemMessageId;

    /**
     * 用户消息id
     */
    private String userMessageId;

    /**
     * 会话id
     */
    private String sessionId;

    /**
     * 消息内容
     */
    private String message;

    /**
     * 卡片列表
     */
    private List<StreamMessageCardModel> components;

    /**
     * 状态
     */
    private String status;

    /**
     * 文本思考内容
     */
    private String thinking;

    /**
     * 思考链内容
     */
    private Map<String, Object> thinkingThought;
    // 此List用于对话续接场景，一次性返回目前所有输出数据
    private List<Map<String, Object>> thinkingThoughtList;

    /**
     * 序列号 下一条消息的序列号
     */
    private Integer seq;

    /**
     * 是否是心跳
     */
    private Boolean heartBeat;

    public StreamMessageResult(String systemMessageId, String userMessageId, String thinking, String message, String status, Integer seq) {
        this.systemMessageId = systemMessageId;
        this.userMessageId = userMessageId;
        this.message = message;
        this.thinking = thinking;
        this.status = status;
        this.seq = seq;
    }

    public StreamMessageResult(String systemMessageId, String userMessageId, String thinking, String message, List<StreamMessageCardModel> components, String status, Integer seq) {
        this.systemMessageId = systemMessageId;
        this.userMessageId = userMessageId;
        this.message = message;
        this.thinking = thinking;
        this.components = components;
        this.status = status;
        this.seq = seq;
    }

    public StreamMessageResult(String systemMessageId, String userMessageId, List<StreamMessageCardModel> components, String status) {
        this.systemMessageId = systemMessageId;
        this.userMessageId = userMessageId;
        this.components = components;
        this.status = status;
    }

    public StreamMessageResult(String systemMessageId, String userMessageId, String thinking, String message, List<StreamMessageCardModel> components, String status) {
        this.systemMessageId = systemMessageId;
        this.userMessageId = userMessageId;
        this.message = message;
        this.thinking = thinking;
        this.components = components;
        this.status = status;
    }

    public StreamMessageResult(String systemMessageId, String userMessageId, Map thinkingThought, String message, List<StreamMessageCardModel> cardModelList,  String status) {
        this.systemMessageId = systemMessageId;
        this.userMessageId = userMessageId;
        this.message = message;
        this.thinkingThought = thinkingThought;
        this.status = status;
        this.components = cardModelList;
    }

    public StreamMessageResult(String systemMessageId, String userMessageId, String thinking, String message,  String status) {
        this.systemMessageId = systemMessageId;
        this.userMessageId = userMessageId;
        this.message = message;
        this.thinking = thinking;
        this.status = status;
    }

    public StreamMessageResult(String systemMessageId, String userMessageId, Map thinkingThought, String status) {
        this.systemMessageId = systemMessageId;
        this.userMessageId = userMessageId;
        this.status = status;
        this.thinkingThought = thinkingThought;
    }

    public StreamMessageResult() {
    }

    public static StreamMessageResult heartBeat() {
        StreamMessageResult streamMessageResult = new StreamMessageResult();
        streamMessageResult.setHeartBeat(true);
        return streamMessageResult;
    }

    public static StreamMessageResult failed(JourPlanError jourPlanError) {
        StreamMessageResult streamMessageResult = new StreamMessageResult();
        streamMessageResult.setSuccess(false);
        streamMessageResult.setPlanError(jourPlanError);
        return streamMessageResult;
    }

    public static StreamMessageResult succeed() {
        StreamMessageResult streamMessageResult = new StreamMessageResult();
        streamMessageResult.setSuccess(true);
        return streamMessageResult;
    }

    /**
     * 消息中的卡片结构
     */
    @Data
    public static class StreamMessageCardModel<T> {

        private static final long serialVersionUID = 1L;

        /**
         * 卡片id，同样的内容解出来的卡片id务必保持一致
         */
        private String id;

        /**
         * 类型 @{@link AiJourneyMessageCardTypeEnum}
         */
        private String itemType;

        /**
         * 状态 @{@link AiJourneyMessageCardStatusEnum}
         */
        private String status = AiJourneyMessageCardStatusEnum.FINISH.getCode();

        /**
         * 动作 @{@link AiJourneyMessageCardActionEnum}
         */
        private String action = AiJourneyMessageCardActionEnum.APPEND.getCode();

        /**
         * 内容
         */
        private T data;

        /**
         * 子卡片
         */
        private List<StreamMessageCardModel<?>> children;


        public static StreamMessageCardModel finishAndReplace(String id, String itemType, Object data) {
            StreamMessageCardModel model = new StreamMessageCardModel();
            model.setId(id);
            model.setItemType(itemType);
            model.setData(data);
            model.setStatus(AiJourneyMessageCardStatusEnum.FINISH.getCode());
            model.setAction(AiJourneyMessageCardActionEnum.REPLACE.getCode());
            return model;
        }
    }

}
