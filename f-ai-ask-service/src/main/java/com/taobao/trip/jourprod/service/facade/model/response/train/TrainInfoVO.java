package com.taobao.trip.jourprod.service.facade.model.response.train;

import lombok.Data;

/**
 * 火车票视图
 * 这里和行业字段保持一致了，方便查询之后直接赋值
 * <AUTHOR>
 * @time 2023/11/27 15:57
 */
@Data
public class TrainInfoVO {

    /**
     * 到达日期
     * 2022-10-20
     */
    private String arrDate;

    /**
     * 到达时间
     * 10:56
     */
    private String arrTime;

    /**
     * 到达时间完整字段
     * 2022-10-20 10:56:00
     */
    private String arrTimeFull;

    /**
     * 到达站点
     * 北京丰台站
     */
    private String arriveStation;

    /**
     * 耗时
     * 2时26分
     */
    private String costTime;

    /**
     * 出发时间
     * 08:30
     */
    private String depTime;

    /**
     * 出发时间完整字段
     * 2022-10-20 08:30:00
     */
    private String depTimeFull;

    /**
     * 出发站点
     * 太原南站
     */
    private String departStation;

    /**
     * 是否是复兴号
     * 0:不是，1:是
     */
    private Integer revivalTrain;

    /**
     * 是否是静音车厢
     * 0:不是，1:是
     */
    private String silenceCompartment;

    /**
     * 列车车次
     */
    private String trainNo;

    /**
     * 列车类型
     * 列车类型的编号。1=普快, 2=新空普快, 3=普客, 4=快速, 5=新空普客, 6=城际高速, 7=动车组, 8=高速动车, 9=新空快速, 10=新空特快, 11=特快, 12=新空直达
     */
    private String trainType;

    /**
     * 是否支持身份
     */
    private Boolean supportIdCard;

    /**
     * 站_站_短车次_长车次唯一索引
     */
    private String uniqueIndex;

    /**
     * 是否关注
     */
    private boolean focus;

    /**
     * 是否满足出发、到达站的筛选，没有筛选项认为满足
     */
    private boolean stationIsMatched = true;

}
