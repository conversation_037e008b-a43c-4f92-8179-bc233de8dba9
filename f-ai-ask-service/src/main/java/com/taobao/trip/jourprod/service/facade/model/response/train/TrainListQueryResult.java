package com.taobao.trip.jourprod.service.facade.model.response.train;

import com.taobao.trip.jourprod.service.facade.model.response.JourPlanError;
import java.util.List;
import lombok.Data;

/**
 * 火车列表查询结果
 * <AUTHOR>
 * @time 2023/11/27 15:55
 */
@Data
public class TrainListQueryResult {

    private Boolean success;

    /**
     * 错误提示信息
     */
    private JourPlanError planError;

    /**
     * 火车信息列表
     */
    private List<TrainInfoVO> trainInfoVOList;

    /**
     * 是否有下一页
     */
    private boolean hasNextPage;

    /**
     * 查询唯一索引
     */
    private String uniqueIndex;

    /**
     * 成功请求
     */
    public static TrainListQueryResult success(List<TrainInfoVO> trainInfoVOList, Boolean hasNextPage, String uniqueIndex) {
        TrainListQueryResult trainListQueryResult = new TrainListQueryResult();
        trainListQueryResult.setSuccess(true);
        trainListQueryResult.setTrainInfoVOList(trainInfoVOList);
        trainListQueryResult.setHasNextPage(hasNextPage);
        trainListQueryResult.setUniqueIndex(uniqueIndex);
        return trainListQueryResult;
    }

    /**
     * 失败请求
     */
    public static TrainListQueryResult failed(JourPlanError planError) {
        TrainListQueryResult trainListQueryResult = new TrainListQueryResult();
        trainListQueryResult.setSuccess(false);
        trainListQueryResult.setPlanError(planError);
        return trainListQueryResult;
    }

}
