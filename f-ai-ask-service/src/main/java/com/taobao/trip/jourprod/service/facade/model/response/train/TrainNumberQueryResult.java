package com.taobao.trip.jourprod.service.facade.model.response.train;

import com.taobao.trip.jourprod.service.facade.model.response.BaseResult;
import com.taobao.trip.jourprod.service.facade.model.response.JourPlanError;
import java.util.Map;
import java.util.Set;
import lombok.Data;

/**
 * <AUTHOR>
 * @time 2024/1/10 11:45
 */
@Data
public class TrainNumberQueryResult extends BaseResult {

    /**
     * 请求城市之间的余票信息
     */
    private Integer ticketNumber;

    /**
     * 车次号list
     */
    private Set<String> trainNoSet;

    /**
     * 扩展信息，其中存放原始车票信息
     */
    private Map<String, Object> extInfo;

    /**
     * 成功请求
     */
    public static TrainNumberQueryResult success(Integer ticketNumber, Set<String> trainNoSet, Map<String, Object> extInfo) {
        TrainNumberQueryResult trainNumberQueryResult = new TrainNumberQueryResult();
        trainNumberQueryResult.setTicketNumber(ticketNumber);
        trainNumberQueryResult.setTrainNoSet(trainNoSet);
        trainNumberQueryResult.setExtInfo(extInfo);
        trainNumberQueryResult.setSuccess(true);
        return trainNumberQueryResult;
    }

    /**
     * 失败请求
     */
    public static TrainNumberQueryResult failed(JourPlanError planError) {
        TrainNumberQueryResult trainNumberQueryResult = new TrainNumberQueryResult();
        trainNumberQueryResult.setSuccess(false);
        trainNumberQueryResult.setPlanError(planError);
        return trainNumberQueryResult;
    }

}
