package com.taobao.trip.jourprod.service.impl;

import com.alitrip.pedia.tripplan.engine.exception.TripPlanError;
import com.alitrip.pedia.tripplan.engine.exception.TripPlanException;
import com.taobao.mtop.common.Result;
import com.taobao.trip.jourprod.common.lang.enums.BizErrorCodeEnum;
import com.taobao.trip.jourprod.common.sal.exception.TripJourneyException;
import com.taobao.trip.jourprod.service.facade.model.response.JourPlanError;
import com.taobao.trip.jourprod.service.facade.model.response.fliggyhome.FliggyHomeResult;
import com.taobao.trip.jourprod.service.facade.model.response.fliggyhome.FliggyHomeResultEnum;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * Created by mojin on 2018/9/3.
 */
public abstract class BaseMtopService {
    protected  <T> Result<T> mtopSuccess(T model){
        Result<T> result = new Result<T>();
        result.setModel(model);
        return result;
    }

    protected  <T> Result<T> mtopSuccess(T model, String msgCode){
        Result<T> result = new Result<T>();
        result.setModel(model);
        result.setMsgCode("FAIL_BIZ_"+msgCode);
        return result;
    }

    protected Result<Void> mtopSuccess(){
        return new Result<Void>();
    }

    /**
     * 前端无法读出head信息
     * @param info
     * @param <T>
     * @return
     */
    @Deprecated
    protected <T> Result<T> mtopSuccess(JourPlanError info) {
        Result<T> result = new Result<T>();
        result.setSuccess(true);
        // success设置为true时，msgCode和msgnfo无法带到前端
        // 为了降低改动成本，把信息塞到响应头中，需要与前端提前进行沟通
        result.addHeader("BIZ-MSG-CODE", "FAIL_BIZ_" + info.getMsgCode());
        try {
            result.addHeader("BIZ-MSG-INFO", URLEncoder.encode(info.getMsgInfo(), StandardCharsets.UTF_8.toString()));
        } catch (UnsupportedEncodingException ignored) { }
        return result;
    }

    protected  <T> Result<T> mtopFailed(String msgInfo,String msgCode){
        Result<T> result = new Result<T>();
        result.setSuccess(false);
        result.setMsgCode("FAIL_BIZ_"+msgCode);
        result.setMsgInfo(msgInfo);
        return result;
    }

    protected  <T> Result<T> mtopFailed(BizErrorCodeEnum bizErrorCodeEnum){
        Result<T> result = new Result<T>();
        result.setSuccess(false);
        result.setMsgCode(bizErrorCodeEnum.getCode());
        result.setMsgInfo(bizErrorCodeEnum.getMsg());
        return result;
    }

    protected <T> Result<T> mtopFailed(TripPlanException exception) {
        TripPlanError planError = exception.getTripPlanError();
        if(planError != null){
            return mtopFailed(exception.getTripPlanError().getErrorMessage(),exception.getTripPlanError().getErrorCode());
        }
        return mtopFailed(JourPlanError.SYSTEM_ERROR);
    }

    protected <T> Result<T> mtopFailed(TripJourneyException tripJourneyException){
        Result<T> result = new Result<T>();
        result.setSuccess(false);
        result.setMsgCode("FAIL_BIZ_"+tripJourneyException.getErrorCode());
        result.setMsgInfo(tripJourneyException.getErrorMsg());
        return result;
    }


    protected <T> Result<T> mtopFailed(JourPlanError error){
        return mtopFailed(error.getMsgInfo(),error.getMsgCode());
    }

    protected <T> FliggyHomeResult<T> fliggyHomeSuccess(T data) {
        return new FliggyHomeResult<>(data, FliggyHomeResultEnum.SUCCESS.getCode(),
                FliggyHomeResultEnum.SUCCESS.getMsg(), true);
    }

    protected <T> FliggyHomeResult<T> fliggyHomeFail() {
        return new FliggyHomeResult<>(null, FliggyHomeResultEnum.FAIL.getCode(),
                FliggyHomeResultEnum.FAIL.getMsg(), false);
    }

    protected <T> FliggyHomeResult<T> fliggyHomeFail(FliggyHomeResultEnum resultEnum) {
        return new FliggyHomeResult<>(null, resultEnum.getCode(), resultEnum.getMsg(), false);
    }

    protected <T> FliggyHomeResult<T> fliggyHomeFail(FliggyHomeResultEnum resultEnum, String resultMsg) {
        return new FliggyHomeResult<>(null, resultEnum.getCode(), resultMsg, false);
    }

    protected <T> FliggyHomeResult<T> fliggyHomeFail(String code, String msg) {
        return new FliggyHomeResult<>(null, code, msg, false);
    }
}
