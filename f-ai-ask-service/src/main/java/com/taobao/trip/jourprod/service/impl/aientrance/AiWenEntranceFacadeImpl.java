package com.taobao.trip.jourprod.service.impl.aientrance;

import com.alibaba.fastjson.JSON;
import com.alibaba.fliggy.award.client.model.dto.award.UserAwardRecordsQueryResp;
import com.alibaba.fliggy.award.client.model.response.award.runtime.AwardApplyResponse;
import com.alibaba.fliggy.marketing.play.client.model.dto.RightBaseDTO;
import com.fliggy.crowd.service.domain.profile.ProfileValue;
import com.fliggy.ffalevel.client.service.LevelQueryService;
import com.fliggy.ffalevel.model.common.FlResult;
import com.fliggy.ffalevel.model.level.TripUserDTO;
import com.fliggy.fliggyplaycore.client.model.ServiceResult;
import com.fliggy.fliggyplaycore.client.redeem.model.RedeemCodeRedeemRsp;
import com.fliggy.graph.common.service.GraphExecutorService;
import com.fliggy.graph.core.DataCarrier;
import com.fliggy.graph.core.node.ProcessParam;
import com.fliggy.graph.core.node.param.RouterStrategy;
import com.fliggy.graph.core.process.FunctionProcessor;
import com.fliggy.graph.core.process.ProcessResult;
import com.fliggy.graph.support.logger.LoggerUtils;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.eagleeye.EagleEye;
import com.taobao.mtop.common.Result;
import com.taobao.trip.facade.AiWenEntranceFacade;
import com.taobao.trip.jourprod.award.SendCouponInput;
import com.taobao.trip.jourprod.award.UserAwardGateway;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.JourneyPlanAiAssistantFrt;
import com.taobao.trip.jourprod.common.sal.hsf.crowds.TripCrowdService;
import com.taobao.trip.jourprod.common.sal.hsf.userRelated.UserProfileManager;
import com.taobao.trip.jourprod.graph.UserParam;
import com.taobao.trip.jourprod.service.impl.BaseMtopService;
import com.taobao.trip.request.AiWenEntranceRequest;
import com.taobao.trip.request.JourneyPlanAiAssistantWhiteUserRequest;
import com.taobao.trip.response.ActionResult;
import com.taobao.trip.response.AiWenEntranceInfo;
import com.taobao.trip.wireless.annotation.SsifMtop;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.MDC;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/1
 */
@Slf4j
@Component
@SsifMtop(serviceInterface = AiWenEntranceFacade.class)
public class AiWenEntranceFacadeImpl extends BaseMtopService implements AiWenEntranceFacade, InitializingBean {
    private final static String CHANNEL_KEY = "channel";
    private final static String SOURCE_KEY = "source";
    private final static String CHANNEL_JUST_CHECK = "check";
    private final static String AI_USER_INFO_SERVICE = "ai-ask-entrance";
    private final static String RESERVE_TAG = "e5043f6945";

    @AppSwitch(des = "AI问一问入口鉴权配置", level = Switch.Level.p2)
    public static Map<String, String> AI_WEN_AUTHORIZE_CONFIG = new HashMap<>();
    @AppSwitch(des = "AI问一问入口面板配置", level = Switch.Level.p2)
    public static Map<String, Object> AI_WEN_PANEL_CONFIG = new HashMap<>();
    @Resource
    private UserProfileManager userProfileManager;
    @Resource
    private TripCrowdService tripCrowdService;
    @Resource
    private LevelQueryService levelQueryService;
    @Resource
    private UserAwardGateway userAwardGateway;
    @Resource
    private GraphExecutorService graphExecutorService;
    @Resource
    private JourneyPlanAiAssistantFrt journeyPlanAiAssistant;

    private AiWenEntranceInfo doAuthProcess(Long userId, Map<String, Object> params) {
        MDC.put("EAGLEEYE_TRACE_ID", EagleEye.getTraceId());
        MDC.put("userId", String.valueOf(userId));
        UserParam param = UserParam.builder().userId(userId).params(params).build();
        AiWenEntranceInfo main = AiWenEntranceInfo.init();
        try {
            this.graphExecutorService.execute(AI_USER_INFO_SERVICE, main, param);
        } catch (Exception e) {
            LoggerUtils.error("查询AI问一问信息失败", e);
        }
        return main;
    }

    @Override
    public Result<AiWenEntranceInfo> getUserInfo(AiWenEntranceRequest request) {
        if (request.getUserId() == null || request.getUserId() <= 0) {
            return new Result<>(true, "error", "请先登录");
        }
        Map<String, Object> params = new ConcurrentHashMap<>();
        params.put(SOURCE_KEY, request.getSource());
        AiWenEntranceInfo main = doAuthProcess(request.getUserId(), params);
        Map<String, Object> content = buildContent(request.getUserId(), main);
        main.setContent(content);
        return build(main);
    }

    @Override
    public Result<AiWenEntranceInfo> checkAuthorization(AiWenEntranceRequest request) {
        Map<String, Object> params = new ConcurrentHashMap<>();
        params.put(CHANNEL_KEY, CHANNEL_JUST_CHECK);
        params.put("source", request.getSource());
        AiWenEntranceInfo main = doAuthProcess(request.getUserId(), params);
        return build(main);
    }

    @Override
    public Result<ActionResult> reserve(AiWenEntranceRequest request) {
        try {
            Map<String, ProfileValue> map = userProfileManager.userProfile(request.getUserId(), Collections.singletonList(RESERVE_TAG));
            ProfileValue tag = MapUtils.getObject(map, RESERVE_TAG);
            if (StringUtils.isNotBlank(tag.getValue())) {
                return build(ActionResult.success("预约成功，我们将按顺序逐步开放体验，请耐心等待"));
            }
            long millis = System.currentTimeMillis();
            String value = String.valueOf(millis);
            String crmId = EagleEye.getTraceId();
            userProfileManager.addUserTag(String.valueOf(request.getUserId()), RESERVE_TAG, value, crmId);
            return build(ActionResult.success("预约成功，我们将按顺序逐步开放体验，请耐心等待"));
        } catch (Exception e) {
            LoggerUtils.error("预约异常", e);
            return build(ActionResult.error("500", "服务异常，请稍后重试"));
        }
    }

    @Override
    public Result<ActionResult> register(AiWenEntranceRequest request) {
        if (request.getUserId() == null || request.getUserId() <= 0) {
            return build(ActionResult.success());
        }
        if (StringUtils.isBlank(request.getCode())) {
            return build(ActionResult.error("400", "邀请码不能为空"));
        }
        boolean isrRegistered = isUserRegisterSuccess(request.getUserId());
        if (isrRegistered) {
            return build(ActionResult.success("验证成功\n恭喜你获取体验资格"));
        }
        ServiceResult<RedeemCodeRedeemRsp> response = userAwardGateway.redeem(request.getUserId(), request.getCode());
        if (response == null) {
            return build(ActionResult.error("500", "网络繁忙，请稍后重试"));
        }
        if (response.isSuccess()) {
            return build(ActionResult.success("验证成功\n恭喜你获取体验资格"));
        }
        if (StringUtils.isNotBlank(response.getMsgInfo())) {
            return build(ActionResult.error(response.getErrorCode(), response.getMsgInfo()));
        }
        return build(ActionResult.error("500", "网络繁忙，请稍后重试"));
    }

    @FunctionProcessor(value = "is_user_flevel_in_range", services = "ai-ask-entrance")
    public ProcessResult<Set<String>> isUserFLevelInRange(DataCarrier c, ProcessParam f) {
        AiWenEntranceInfo main = c.main();
        UserParam param = c.params();
        String channel = MapUtils.getString(param.getParams(), CHANNEL_KEY);
        String source = MapUtils.getString(param.getParams(), "source");
        if (CHANNEL_JUST_CHECK.equals(channel)) {
            LoggerUtils.title("仅鉴权");
        } else {
            LoggerUtils.title("页面请求:{}", source);
        }
        LoggerUtils.info("userId:{}", param.getUserId());
        boolean isUserInCrowd = isUserLevelInRange(param);
        RouterStrategy config = (RouterStrategy) f;
        if (isUserInCrowd) {
            main.getExtensions().put("is_high_level", true);
            return ProcessResult.success(config.router("Y"));
        } else {
            return ProcessResult.success(config.router("N"));
        }
    }

    @FunctionProcessor(value = "send_invitation_code", services = "ai-ask-entrance")
    public ProcessResult<Void> sendInvitationCodes(DataCarrier c, ProcessParam f) {
        UserParam param = c.params();
        String sources = MapUtils.getString(AI_WEN_AUTHORIZE_CONFIG, "SEND_CODE_SOURCES", "journey,homepage");
        String source = MapUtils.getString(param.getParams(), SOURCE_KEY);
        if (!StringUtils.contains(sources, source)) {
            LoggerUtils.info("skip thi step");
            return ProcessResult.success();
        }
        String channel = MapUtils.getString(param.getParams(), CHANNEL_KEY);
        if (CHANNEL_JUST_CHECK.equals(channel)) {
            LoggerUtils.info("skip thi step");
            return ProcessResult.success();
        }
        sendInvitationCodes(param);
        return ProcessResult.success();
    }

    @FunctionProcessor(value = "tag_has_authorized", services = "ai-ask-entrance")
    public ProcessResult<Void> tagAuthorized(DataCarrier c, ProcessParam f) {
        AiWenEntranceInfo main = c.main();
        main.setAuthorized(true);
        return ProcessResult.success();
    }

    @FunctionProcessor(value = "query_invitation_code", services = "ai-ask-entrance")
    public ProcessResult<Void> queryUserInvitationCodes(DataCarrier c, ProcessParam f) {
        UserParam param = c.params();
        String channel = MapUtils.getString(param.getParams(), CHANNEL_KEY);
        if (CHANNEL_JUST_CHECK.equals(channel)) {
            LoggerUtils.info("skip thi step");
            return ProcessResult.success();
        }
        AiWenEntranceInfo main = c.main();
        String activityId = activityId();
        if (StringUtils.isBlank(activityId)) {
            LoggerUtils.info("活动配置为空");
            return ProcessResult.success();
        }
        List<Map<String, String>> codes = queryInvitationCodes(param);
        main.setInvitationCodes(codes);
        return ProcessResult.success();
    }

    @FunctionProcessor(value = "is_early_user", services = "ai-ask-entrance")
    public ProcessResult<Void> isUserInEarlyUse(DataCarrier c, ProcessParam f) {
        AiWenEntranceInfo main = c.main();
        UserParam param = c.params();
        boolean isUserEarlyUse = isUserEarlyUse(param.getUserId());
        if (isUserEarlyUse) {
            main.getExtensions().put("is_early_user", true);
            return ProcessResult.success();
        } else {
            return ProcessResult.fail();
        }
    }

    @FunctionProcessor(value = "is_register_success", services = "ai-ask-entrance")
    public ProcessResult<Void> isUserRegisterSuccess(DataCarrier c, ProcessParam f) {
        UserParam param = c.params();
        boolean success = isUserRegisterSuccess(param.getUserId());
        if (success) {
            return ProcessResult.success();
        } else {
            return ProcessResult.fail();
        }
    }

    @FunctionProcessor(value = "is_reserve_pass", services = "ai-ask-entrance")
    public ProcessResult<Void> isUserReservePass(DataCarrier c, ProcessParam f) {
        AiWenEntranceInfo main = c.main();
        UserParam param = c.params();
        DateTime time = queryUserReverseTime(param.getUserId());
        if (time == null) {
            return ProcessResult.fail();
        }
        String t = time.toString("yyyy-MM-dd HH:mm:ss");
        LoggerUtils.error("预约时间:{}", t);
        main.getExtensions().put("reserve_time", t);
        DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
        String RESERVE_ACCEPT_TIME = MapUtils.getString(AI_WEN_AUTHORIZE_CONFIG, "RESERVE_ACCEPT_TIME", "2025-03-01 12:00:00");
        DateTime point = formatter.parseDateTime(RESERVE_ACCEPT_TIME);
        return time.isBefore(point) ? ProcessResult.success() : ProcessResult.fail();
    }

    private boolean isUserLevelInRange(UserParam param) {
        Long F_LEVEL_CROWD_ID = MapUtils.getLong(AI_WEN_AUTHORIZE_CONFIG, "F_LEVEL_CROWD_ID", 120792L);
        Boolean inCrowd = tripCrowdService.isUserInCrowd(param.getUserId(), F_LEVEL_CROWD_ID);
        if (inCrowd == null) {
            LoggerUtils.error("查询人群返回为空");
        }
        return inCrowd != null && inCrowd;
    }

    private String queryUserLevel(Long userId) {
        try {
            FlResult<TripUserDTO> response = levelQueryService.getTripUser(userId);
            if (response == null || !response.isSuccess() || response.getData() == null) {
                log.error("查询会员等级失败,{}:{}", userId, JSON.toJSONString(response));
                return null;
            }
            TripUserDTO data = response.getData();
            log.info("会员等级,{}:{}", userId, data.getLevelName());
            return data.getLevelName();
        } catch (Exception e) {
            log.error("查询会员等级异常", e);
            return null;
        }
    }

    private void sendInvitationCodes(UserParam param) {
        SendCouponInput input = new SendCouponInput();
        input.setUserId(param.getUserId());
        String activityId = activityId();
        input.setSubActId(activityId);
        input.setProdChannelCode(channelCode());
        AwardApplyResponse response = userAwardGateway.apply(input);
        if (response == null) {
            LoggerUtils.error("发券失败");
        }
    }

    private List<Map<String, String>> queryInvitationCodes(UserParam param) {
        try {
            SendCouponInput input = new SendCouponInput();
            input.setUserId(param.getUserId());
            String activityId = activityId();
            input.setSubActId(activityId);
            UserAwardRecordsQueryResp response = userAwardGateway.queryUserAwardRecords(input);
            if (response == null || response.getAwardActRecordsDTOS() == null) {
                LoggerUtils.error("无记录");
                return Collections.emptyList();
            }
            LoggerUtils.error("record:{}", JSON.toJSONString(response));
            List<String> codes = response.getAwardActRecordsDTOS().stream().flatMap(s -> s.getAwardRecordDTOS().stream())
                    .flatMap(s -> s.getRightGroupDTOS().stream()).flatMap(s -> s.getRightDTOS().stream()).map(RightBaseDTO::getExtInfo)
                    .map(s -> MapUtils.getString(s, "couponInstanceCode")).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(codes)) {
                LoggerUtils.error("邀请码列表为空");
                return Collections.emptyList();
            }
            return userAwardGateway.queryStatus(codes);
        } catch (Throwable e) {
            LoggerUtils.error("查询邀请码异常", e);
            return Collections.emptyList();
        }
    }

    private boolean isUserEarlyUse(Long userId) {
        JourneyPlanAiAssistantWhiteUserRequest request = new JourneyPlanAiAssistantWhiteUserRequest();
        request.setUserId(userId);
        Boolean inWhiteList = journeyPlanAiAssistant.inWhiteList(request);
        return inWhiteList != null && inWhiteList;
    }

    private boolean isUserRegisterSuccess(Long userId) {
        Long registerCrowdId = MapUtils.getLong(AI_WEN_AUTHORIZE_CONFIG, "REGISTER_CROWD_ID", 120834L);
        if (registerCrowdId == null) {
            return false;
        }
        Boolean inCrowd = tripCrowdService.isUserInCrowd(userId, registerCrowdId);
        return inCrowd != null && inCrowd;
    }

    private DateTime queryUserReverseTime(Long userId) {
        try {
            Map<String, ProfileValue> map = userProfileManager.userProfile(userId, Collections.singletonList(RESERVE_TAG));
            ProfileValue tag = MapUtils.getObject(map, RESERVE_TAG);
            if (StringUtils.isNumeric(tag.getValue())) {
                return new DateTime(Long.parseLong(tag.getValue()));
            } else {
                LoggerUtils.info("未预约");
            }
        } catch (Exception e) {
            LoggerUtils.error("查询预约时间失败", e);
        }
        return null;
    }

    private Map<String, Object> buildContent(Long userId, AiWenEntranceInfo main) {
        boolean isHighLevel = MapUtils.getBooleanValue(main.getExtensions(), "is_high_level", false);
        Map<String, Object> config = (Map<String, Object>) MapUtils.getObject(AI_WEN_PANEL_CONFIG, isHighLevel ? "highLevel" : "lowLevel");
        Map<String, Object> content = new HashMap<>(config);
        String title = MapUtils.getString(content, "title");
        String flevel = null;
        if (StringUtils.contains(title, "${flevel}")) {
            flevel = queryUserLevel(userId);
            if (StringUtils.isBlank(flevel)) {
                flevel = "";
            }
            title = title.replace("${flevel}", flevel);
            content.put("title", title);
        }
        String invitationPopDesc = MapUtils.getString(content, "invitationPopDesc");
        if (StringUtils.contains(invitationPopDesc, "${flevel}")) {
            String level = flevel == null ? queryUserLevel(userId) : flevel;
            if (StringUtils.isBlank(level)) {
                level = "";
            }
            invitationPopDesc = invitationPopDesc.replace("${flevel}", level);
            content.put("invitationPopDesc", invitationPopDesc);
        }
        String invitationTitle = MapUtils.getString(content, "invitationTitle");
        if (StringUtils.contains(invitationTitle, "${number}")) {
            List<Map<String, String>> list = main.getInvitationCodes();
            int number = list.size();
            String num = number == 0 ? "" : String.valueOf(number);
            invitationTitle = invitationTitle.replace("${number}", num);
            content.put("invitationTitle", invitationTitle);
        }
        return content;
    }

    private String activityId() {
        return MapUtils.getString(AI_WEN_AUTHORIZE_CONFIG, "activityId", "56da4f25363b4c2fac0ce016fe4e160c");
    }

    private String channelCode() {
        return MapUtils.getString(AI_WEN_AUTHORIZE_CONFIG, "channelCode", "2000142001");
    }

    private AiWenEntranceInfo mock() {
        String json = "{\"authorized\":true,\"invitationCodes\":[{\"code\":\"1111\",\"status\":\"1\"},{\"code\":\"2222\",\"status\":\"0\"}],\"extensions\":{\"is_early_user\":true,\"reserve_time\":\"2025-03-31 12:30:30\",\"register_time\":\"2025-03-31 12:30:30\"},\"content\":{\"logo\":\"https://gw.alicdn.com/imgextra/i3/O1CN01LUzLdf1dNBXFMJiPi_!!6000000003723-2-tps-140-140.png\",\"title\":\"尊敬的飞猪F5会员\\n恭喜获得AI旅行助手限量体验资格\",\"invitationTitle\":\"额外赠送你5张邀请码\\n可邀请好友一起体验\",\"invitationDesc\":\"邀请码限量发放给飞猪F4F5F6会员用户，可向好友索取\",\"invitationPlaceholder\":\"请在此输入您的邀请码\",\"invitationButtonText\":\"抢先体验\",\"startText\":\"进入旅行助手\",\"appointmentText\":\"预约等待\",\"appointmentSuccess\":\"预约成功\",\"highlightList\":[{\"title\":\"输入想法 攻略立成\",\"videoUrl\":\"https://gw.alicdn.com/imgextra/i3/O1CN01iBVzQh1RrA4NQtQug_!!6000000002164-2-tps-140-140.png\"},{\"title\":\"手绘地图 一目了然\",\"imageUrl\":\"https://gw.alicdn.com/imgextra/i3/O1CN01iBVzQh1RrA4NQtQug_!!6000000002164-2-tps-140-140.png\"},{\"title\":\"输入想法 攻略立成\",\"imageUrl\":\"https://gw.alicdn.com/imgextra/i3/O1CN01iBVzQh1RrA4NQtQug_!!6000000002164-2-tps-140-140.png\"},{\"title\":\"输入想法 攻略立成\",\"imageUrl\":\"https://gw.alicdn.com/imgextra/i3/O1CN01iBVzQh1RrA4NQtQug_!!6000000002164-2-tps-140-140.png\"}]}}";
        return JSON.parseObject(json, AiWenEntranceInfo.class);
    }

    public static <T> Result<T> build(T modal) {
        Result<T> result = new Result<>();
        result.setSuccess(true);
        result.setModel(modal);
        return result;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        SwitchManager.init(AiWenEntranceFacadeImpl.class);
    }
}
