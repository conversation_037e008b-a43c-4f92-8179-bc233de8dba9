package com.taobao.trip.jourprod.service.impl.journeyplannew;


import java.util.Objects;

import com.alibaba.boot.hsf.annotation.HSFProvider;

import com.taobao.mtop.common.Result;
import com.taobao.trip.facade.AiAskWhiteUserFacade;
import com.taobao.trip.facade.AiWenEntranceFacade;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantWhiteHelper;
import com.taobao.trip.request.AiWenEntranceRequest;
import com.taobao.trip.response.AiWenEntranceInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@HSFProvider(serviceInterface = AiAskWhiteUserFacade.class, serviceVersion = "1.0.0")
@Component
public class AiAskWhiteUserFacadeImpl implements AiAskWhiteUserFacade {

    @Autowired
    private JourneyPlanAiAssistantWhiteHelper journeyPlanAiAssistantWhiteHelper;

    @Autowired
    private AiWenEntranceFacade aiWenEntranceFacade;

    @Override
    public Boolean inWhiteList(Long userId) {
        if (Objects.isNull(userId)) {
            return false;
        }
        return journeyPlanAiAssistantWhiteHelper.isInDynamicWhiteList(userId);
    }

    @Override
    public Boolean hasAuthorization(Long userId) {
        if (Objects.isNull(userId)) {
            return false;
        }
        AiWenEntranceRequest request = new AiWenEntranceRequest();
        request.setUserId(userId);
        request.setSource("homepage");
        Result<AiWenEntranceInfo> aiWenEntranceInfoResult = aiWenEntranceFacade.checkAuthorization(request);
        if (aiWenEntranceInfoResult == null || !aiWenEntranceInfoResult.isSuccess() || aiWenEntranceInfoResult.getModel() == null) {
            return false;
        }
        return aiWenEntranceInfoResult.getModel().isAuthorized();
    }
}
