package com.taobao.trip.jourprod.service.impl.journeyplannew;

import com.alibaba.fastjson.JSON;

import com.google.common.collect.Lists;
import com.taobao.trip.facade.AiJourneyMessageFacade;
import com.taobao.trip.jourprod.biz.common.annotation.MtopThrowing;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantMessageHelper;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.jourprod.service.impl.BaseMtopService;
import com.taobao.trip.request.AiJourneyQueryMessageRequest;
import com.taobao.trip.request.AiJourneySessionListRequest;
import com.taobao.trip.response.AiJourneyMessageSearchResponse;
import com.taobao.trip.response.AiJourneySessionListResponse;
import com.taobao.trip.wireless.annotation.SsifMtop;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.Objects;

/**
 * @Description 问一问会话相关接口实现
 * <AUTHOR>
 * @Date 2025/4/25
 **/
@SsifMtop(serviceInterface = AiJourneyMessageFacade.class, clientTimeout = 30000)
@Service
@MtopThrowing(errorMsg = "系统异常")
public class AiJourneyMessageFacadeImpl extends BaseMtopService implements AiJourneyMessageFacade {

    private static final FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(AiJourneyMessageFacadeImpl.class);

    @Resource
    private JourneyPlanAiAssistantMessageHelper aiJourneyMessageHelper;

    @Resource
    private JourneyAssistantDataOperateService journeyAssistantDataOperateService;

    /**
     * 查询会话列表
     */
    @Override
    public AiJourneySessionListResponse querySessionList(AiJourneySessionListRequest request) {
        LOGGER.recordEntry(new LogModel("querySessionList")
                .request(JSONUtil.toJSONString(request)));
        
        if (request == null || Objects.isNull(request.getUserId())) {
            LOGGER.recordNormalException(new LogModel("querySessionList").message("param is null"));
            throw new RuntimeException("请求参数为空");
        }
        
        try {
            // 调用底层服务获取会话列表
            AiJourneySessionListResponse response = aiJourneyMessageHelper.querySessionList(request);
            LOGGER.recordOutput(new LogModel("querySessionList").response(JSON.toJSONString(response)));
            return response;
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("querySessionList_exception").e(e));
            throw new RuntimeException("查询会话列表异常", e);
        }
    }

    /**
     * 删除会话
     * todo 要同步删除对话逻辑
     */
    @Override
    public Boolean deleteSession(AiJourneySessionListRequest request) {
        LOGGER.recordEntry(new LogModel("deleteSession")
                .request(JSONUtil.toJSONString(request)));
        
        if (request == null || Objects.isNull(request.getUserId())) {
            LOGGER.recordNormalException(new LogModel("deleteSession").message("param is null"));
            throw new RuntimeException("请求参数为空");
        }

        // 解析会话ID列表
        if (StringUtils.isNotBlank(request.getSessionListStr())) {
            String[] sessionIdArray = request.getSessionListStr().split(",");
            request.setSessionIdList(Lists.newArrayList(sessionIdArray));
        }

        try {
            // 调用底层服务删除会话
            Boolean result = aiJourneyMessageHelper.deleteSession(request);
            LOGGER.recordOutput(new LogModel("deleteSession").response(String.valueOf(result)));
            return result;
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("deleteSession_exception").e(e));
            throw new RuntimeException("删除会话异常", e);
        }
    }

    /**
     * 搜索历史消息
     */
    @Override
    public AiJourneyMessageSearchResponse queryMessageList(AiJourneyQueryMessageRequest request) {
        LOGGER.recordEntry(new LogModel("queryMessageList")
                .request(JSONUtil.toJSONString(request)));
        
        if (request == null || Objects.isNull(request.getUserId())) {
            LOGGER.recordNormalException(new LogModel("queryMessageList").message("param is null"));
            throw new RuntimeException("请求参数为空");
        }
        
        try {
            // 调用底层服务搜索历史消息
            AiJourneyMessageSearchResponse response = aiJourneyMessageHelper.queryMessageList(request);
            LOGGER.recordOutput(new LogModel("queryMessageList").response(JSON.toJSONString(response)));
            return response;
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("queryMessageList_exception").e(e));
            throw new RuntimeException("搜索历史消息异常", e);
        }
    }
} 