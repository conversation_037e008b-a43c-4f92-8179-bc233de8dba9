package com.taobao.trip.jourprod.service.impl.journeyplannew;

import java.util.Map;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;

import com.taobao.trip.facade.AiJourneyPlanEntranceFacade;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiEntranceSwitch;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.response.AiHonorResponse;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.dubbo.common.stream.StreamObserver;

/**
 * @description: 类描述
 * @create: 2025-04-08 21:41
 **/
@HSFProvider(serviceInterface = AiJourneyPlanEntranceFacade.class, serviceVersion = "1.0.0", protocols = "tri", serviceGroup = "TRI", clientTimeout = 8000)
public class AiJourneyPlanChatEntranceServiceImpl implements AiJourneyPlanEntranceFacade {
    private static final FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(AiJourneyPlanChatEntranceServiceImpl.class);
    @Override
    public void ask(Map<String, Object> param, StreamObserver<AiHonorResponse> response) {
        LOGGER.recordEntry(new LogModel("entranceFacade").request(JSONUtil.toJSONString(param)));
        try {
            String requestId = (String)param.get("requestId");
            AiHonorResponse hr1 = JSON.parseObject(AiEntranceSwitch.RY_TEXT_RESPONSE, AiHonorResponse.class);
            hr1.setRequestId(requestId);
            response.onNext(hr1);

            AiHonorResponse hr2 = JSON.parseObject(AiEntranceSwitch.RY_RESPONSE, AiHonorResponse.class);
            hr2.setRequestId(requestId);
            response.onNext(hr2);

            response.onCompleted();
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("entranceFacade").e(e));
            response.onError(e);
        }
    }
}
