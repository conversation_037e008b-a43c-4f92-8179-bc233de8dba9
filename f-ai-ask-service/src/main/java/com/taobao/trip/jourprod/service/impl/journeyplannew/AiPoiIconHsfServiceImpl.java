package com.taobao.trip.jourprod.service.impl.journeyplannew;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alitrip.travel.common.util.QuaDateUtils;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.trip.facade.AiPoiIconFacade;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneyMessageContentDTO;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.jourprod.common.sal.hsf.config.Switcher;
import com.taobao.trip.jourprod.domain.AiPoiIconDO;
import com.taobao.trip.jourprod.domain.AiPoiIconParam;
import com.taobao.trip.jourprod.domain.JourneyPlanAiChatMessageDO;
import com.taobao.trip.jourprod.domain.JourneyPlanAiChatMessageParam;
import com.taobao.trip.jourprod.mapper.AiPoiIconDAO;
import com.taobao.trip.jourprod.mapper.JourneyPlanAiChatMessageDAO;
import com.taobao.trip.jourprod.odps.OdpsConfig;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult;
import com.taobao.trip.response.AiPoiIconResponse;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.testng.collections.Lists;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 类描述
 * @author: huiyi
 * @create: 2025-04-08 21:41
 **/
@HSFProvider(serviceInterface = AiPoiIconFacade.class, serviceVersion = "1.0.0.main", clientTimeout = 100000)
public class AiPoiIconHsfServiceImpl implements AiPoiIconFacade {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(AiPoiIconHsfServiceImpl.class);


    @Resource
    private AiPoiIconDAO aiPoiIconDAO;

    @Resource
    private JourneyPlanAiChatMessageDAO journeyPlanAiChatMessageDAO;

    @Override
    public AiPoiIconResponse queryById(Long poiId) {
        if (poiId == null) {
            return null;
        }
        AiPoiIconParam param = new AiPoiIconParam();
        param.createCriteria().andPoiIdEqualToWhenPresent(poiId);
        List<AiPoiIconDO> aiPoiIconDOS = aiPoiIconDAO.selectByParam(param);
        if (CollectionUtils.isEmpty(aiPoiIconDOS)) {
            return null;
        }
        AiPoiIconDO aiPoiIconDO = aiPoiIconDOS.get(0);
        if (aiPoiIconDO == null) {
            return null;
        }
        AiPoiIconResponse aiPoiIconResponse = BeanUtil.copyProperties(aiPoiIconDO, AiPoiIconResponse.class);
        return aiPoiIconResponse;
    }

    /**
     * 获取当前时间（Date）
     *
     * @return
     */
    public Date getDate(){
        Date nowTime = new Date();
        if (StringUtils.isNotEmpty(Switcher.AI_ASK_MOCK_DRAW_TIME)){
            try {
                nowTime = QuaDateUtils.stringToDate(Switcher.AI_ASK_MOCK_DRAW_TIME, "yyyy-MM-dd");
            } catch (ParseException e) {

            }
        }

        return nowTime;
    }

    /**
     * 获取昨天的数据时间
     *
     * @return
     */
    public String getYesterDayStartTime() {
        //设置时区
        try {
            Calendar calendar = new GregorianCalendar();
            calendar.setTime(new Date());
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.add(Calendar.DATE, -1);
            Date time = calendar.getTime();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            return sdf.format(time);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("getYesterDayStartTime error").e(e).message("getYesterDayStartTime error"));
            return null;
        }
    }



    @Override
    public List queryPlanData(Integer pageNo, Integer pageSize, String cityName, String day) {
        List<JourneyPlanAiChatMessageDO> journeyPlanAiChatMessageDOS = Lists.newArrayList();
        try {
            if(Switcher.DRAW_ODPS){
                return queryODPSPlanData(pageNo, pageSize, cityName, day);
            }

            Assert.notNull(pageNo, "pageNo is null");
            Assert.notNull(pageSize, "pageSize is null");
            if (pageSize > 5) {
                pageSize = 5;
            }
            JourneyPlanAiChatMessageParam param = new JourneyPlanAiChatMessageParam();
            JourneyPlanAiChatMessageParam.Criteria criteria = param.createCriteria();
            criteria.andContentLikeWhenPresent("drawing_map").andContentLikeWhenPresent(cityName);
            if(StringUtils.isNotBlank(day)){
                criteria.andContentLikeWhenPresent("Day " + day);
                criteria.andContentNotLikeWhenPresent("Day " + (Integer.parseInt(day) + 1));
            }

            // 每次都是慢sql，加个判断逻辑，尽量减小下开销
            criteria.andGmtCreateGreaterThan(getDate());
            param.appendOrderByClause(JourneyPlanAiChatMessageParam.OrderCondition.ID, JourneyPlanAiChatMessageParam.SortType.DESC);
            param.setPagination(pageNo, pageSize);
            journeyPlanAiChatMessageDOS = journeyPlanAiChatMessageDAO.selectByParam(param);
            if (CollectionUtils.isEmpty(journeyPlanAiChatMessageDOS)) {
                return Lists.newArrayList();
            }
            List<StreamMessageResult.StreamMessageCardModel> resultList = journeyPlanAiChatMessageDOS.stream().filter(Objects::nonNull).map(e -> {
                String content = e.getContent();
                if (StringUtils.isBlank(content)) {
                    return null;
                }
                AiJourneyMessageContentDTO aiJourneyMessageContentDTO = JSONObject.parseObject(content, AiJourneyMessageContentDTO.class);
                if (aiJourneyMessageContentDTO == null) {
                    return null;
                }
                List<StreamMessageResult.StreamMessageCardModel> components = aiJourneyMessageContentDTO.getComponents();
                if (CollectionUtils.isEmpty(components)) {
                    return null;
                }
                StreamMessageResult.StreamMessageCardModel drawingMap = components.stream().filter(Objects::nonNull).filter(item -> Objects.equals("drawing_map", item.getItemType())).findFirst().orElse(null);
                if (drawingMap == null || Objects.isNull(drawingMap.getData())) {
                    return null;
                }
                return drawingMap;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            return resultList;
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("AiPoiIconFacade.queryPlanData").message("exception").request("pageNo=" + pageNo + ",pageSize=" + pageSize + ",cityName=" + cityName + ",day=" + day).e(e));
        } finally {
            LOGGER.recordOutput(new LogModel("AiPoiIconFacade.queryPlanData")
                    .request("pageNo=" + pageNo + ",pageSize=" + pageSize + ",cityName=" + cityName + ",day=" + day)
                    .response(JSONUtil.toJSONStringForLog(journeyPlanAiChatMessageDOS)));
        }
        return Lists.newArrayList();
    }

    @Override
    public List queryPlanDataBySql(String sql) {
        if(StringUtils.isBlank(sql)){
            return null;
        }
        try {
            List<Map<String, Object>> list = OdpsConfig.executeSql(sql, null);
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)) {
                return null;
            }
            List<String> contents = new LinkedList<>();
            for (Map<String, Object> map : list) {
                contents.add((String)map.get("content"));
            }
            List<StreamMessageResult.StreamMessageCardModel> resultList = contents.stream().filter(Objects::nonNull).map(e -> {

                AiJourneyMessageContentDTO aiJourneyMessageContentDTO = JSONObject.parseObject(e, AiJourneyMessageContentDTO.class);
                if (aiJourneyMessageContentDTO == null) {
                    return null;
                }
                List<StreamMessageResult.StreamMessageCardModel> components = aiJourneyMessageContentDTO.getComponents();
                if (CollectionUtils.isEmpty(components)) {
                    return null;
                }
                StreamMessageResult.StreamMessageCardModel drawingMap = components.stream().filter(Objects::nonNull).filter(item -> Objects.equals("drawing_map", item.getItemType())).findFirst().orElse(null);
                if (drawingMap == null || Objects.isNull(drawingMap.getData())) {
                    return null;
                }
                return drawingMap;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            return resultList;
        } catch (Exception ex) {
            LOGGER.recordNormalException(new LogModel("queryPlanDataBySql").message("exception").request("sql=" + sql).e(ex));
        }
        return null;
    }

    public List queryODPSPlanData(Integer pageNo, Integer pageSize, String cityName, String day){

        if(pageNo == null){
            pageNo = 0;
        }
        if(pageSize == null){
            pageSize = 3;
        }
        String table = "s_journey_plan_ai_chat_message_f_ai_ask";
        Long start = System.currentTimeMillis();
        String sql = "SELECT * from trip_vacation." + table + " where content like '%drawing_map%'";
        String ds = getYesterDayStartTime();
        if(StringUtils.isNotBlank(ds)){
            sql = sql + " and ds = " + ds;
        }
        if(StringUtils.isNotBlank(cityName)){
            sql  = sql + " and content like '%" + cityName + "%'";
        }

        if(StringUtils.isNotBlank(day)){
            sql  = sql + " and content like '%Day " + day + "%'";
            sql  = sql + " and content not like '%Day " + (Integer.parseInt(day) + 1) + "%'";
        }


        sql = sql + " limit " + pageNo * pageSize + "," + pageSize + ";";
        List<Map<String, Object>> list = OdpsConfig.executeSql(sql, null);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<String> contents = new LinkedList<>();
        for (Map<String, Object> map : list) {
            contents.add((String)map.get("content"));
        }
        List<StreamMessageResult.StreamMessageCardModel> resultList = contents.stream().filter(Objects::nonNull).map(e -> {

            AiJourneyMessageContentDTO aiJourneyMessageContentDTO = JSONObject.parseObject(e, AiJourneyMessageContentDTO.class);
            if (aiJourneyMessageContentDTO == null) {
                return null;
            }
            List<StreamMessageResult.StreamMessageCardModel> components = aiJourneyMessageContentDTO.getComponents();
            if (CollectionUtils.isEmpty(components)) {
                return null;
            }
            StreamMessageResult.StreamMessageCardModel drawingMap = components.stream().filter(Objects::nonNull).filter(item -> Objects.equals("drawing_map", item.getItemType())).findFirst().orElse(null);
            if (drawingMap == null || Objects.isNull(drawingMap.getData())) {
                return null;
            }
            return drawingMap;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        return resultList;
    }

    @AteyeInvoker(description = "测试手绘地图离线mock数据", paraDesc = "userId&itemIdList")
    public String testQueryODPSPlanData(Integer pageNo, Integer pageSize, String cityName, String day){
        return JSON.toJSONString(queryODPSPlanData(pageNo, pageSize, cityName, day));

    }


    @AteyeInvoker(description = "测试手绘地图离线mock数据sql版本", paraDesc = "sql")
    public String testQueryPlanDataBySql(String sql){
        return JSON.toJSONString(queryPlanDataBySql(sql));

    }
}
