package com.taobao.trip.jourprod.service.impl.journeyplannew;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.taobao.trip.jourprod.biz.frt.llm.converter.AiPoiIconConverter;
import com.taobao.trip.jourprod.service.facade.journeyplannew.AiPoiIconService;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplan.AiPoiIconParameter;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiPoiIconDTO;
import com.taobao.trip.jourprod.domain.AiPoiIconDO;
import com.taobao.trip.jourprod.domain.AiPoiIconParam;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import com.taobao.trip.jourprod.mapper.AiPoiIconDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
@HSFProvider(serviceInterface = AiPoiIconService.class, serviceVersion = "1.0.0", clientTimeout = 800)
public class AiPoiIconServiceImpl implements AiPoiIconService {
    @Autowired
    private AiPoiIconDAO aiPoiIconDAO;

    @Autowired
    private AiPoiIconConverter aiPoiIconConverter;

    /**
     * 根据参数统计总数
     * @param param
     */
    @Override
    public long count(AiPoiIconParameter param) {
        AiPoiIconParam aiPoiIconParam = new AiPoiIconParam();
        aiPoiIconParam.createCriteria().andStatusEqualToWhenPresent(param.getStatus());;
        return aiPoiIconDAO.countByParam(aiPoiIconParam);
    }

    /**
     * 根据参数查询
     * @param param
     */
    @Override
    public AiPoiIconDTO find(AiPoiIconParameter param) {
        AiPoiIconParam aiPoiIconParam = new AiPoiIconParam();
        aiPoiIconParam.createCriteria().andPoiIdEqualToWhenPresent(param.getPoiId()).andStatusEqualToWhenPresent(param.getStatus());
        List<AiPoiIconDO> list = aiPoiIconDAO.selectByParam(aiPoiIconParam);
        if (null == list || list.isEmpty()) {
                return null;
        }
        return aiPoiIconConverter.convertFromDO(list.get(0));
    }

    /**
     * 列表查询
     * @param param
     */
    @Override
    public List<AiPoiIconDTO> list(AiPoiIconParameter param) {
        AiPoiIconParam aiPoiIconParam = new AiPoiIconParam();
        aiPoiIconParam.createCriteria().andPoiIdInWhenPresent(param.getPoiIdList()).andStatusEqualToWhenPresent(param.getStatus());
        List<AiPoiIconDO> list = aiPoiIconDAO.selectByParam(aiPoiIconParam);
        if (null == list || list.isEmpty()) {
                return null;
        }
        List<AiPoiIconDTO> result = new ArrayList<>();
        for (AiPoiIconDO record : list) {
            AiPoiIconDTO aiPoiIconDTO = aiPoiIconConverter.convertFromDO(record);
                result.add(aiPoiIconDTO);
        }
        return result;
    }

    /**
     * 创建
     * @param param
     */
    @Override
    public Integer create(AiPoiIconParameter param) {
        AiPoiIconDO record = new AiPoiIconDO();
        record.setId(param.getId());
        record.setGmtCreate(new Date());
        record.setGmtModified(new Date());
        record.setPoiId(param.getPoiId());
        record.setPoiName(param.getPoiName());
        record.setIcon(param.getIcon());
        record.setStatus(param.getStatus());
        record.setCityCode(param.getCityCode());
        record.setCityName(param.getCityName());
        record.setLlm(param.getLlm());
        int insert = aiPoiIconDAO.insert(record);
        return insert;
    }

    /**
     * 修改
     * @param param
     */
    @Override
    public Integer updateSelective(AiPoiIconParameter param) {
        Assert.notNull(param, "param is null");
        Assert.notNull(param.getId(), "id is null");
        AiPoiIconDO aiPoiIconDO = aiPoiIconConverter.convertFromParam(param);
        aiPoiIconDO.setGmtModified(new Date());
        int count = aiPoiIconDAO.updateByPrimaryKeySelective(aiPoiIconDO);
        return count;
    }
}