package com.taobao.trip.jourprod.service.impl.journeyplannew;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.eagleeye.EagleEye;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantMessageHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneyMessageContentDTO;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneyMessageDTO;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.jourprod.common.sal.hsf.collection.CollectionHelper;
import com.taobao.trip.jourprod.common.sal.hsf.common.EnvSwitch;
import com.taobao.trip.jourprod.common.sal.hsf.config.Switcher;
import com.taobao.trip.jourprod.common.sal.hsf.tair.MdbTairHelper;
import com.taobao.trip.jourprod.domain.JourneyPlanAiChatMessageDO;
import com.taobao.trip.jourprod.domain.JourneyPlanAiChatMessageParam;
import com.taobao.trip.jourprod.domain.JourneyPlanAiChatMessageParam.OrderCondition;
import com.taobao.trip.jourprod.domain.JourneyPlanAiChatMessageParam.SortType;
import com.taobao.trip.jourprod.mapper.JourneyPlanAiChatMessageDAO;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.JourneyPlanAiAssistantWrateEnum;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.*;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanAiAssistantHistoryResult;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanAiAssistantHistoryResult.MessageInfo;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult;
import com.taobao.trip.request.QueryRecentMessageRequest;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantMessageHelper.MANUALLY_STOP_KEY;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantMessageHelper.MANUALLY_STOP_TTL_S;

/**
 * @Description 问一问历史消息操作接口实现类
 * <AUTHOR>
 * @Date 2025/2/8
 **/
@Service
public class JourneyAssistantDataOperateService {

    public final static String MESSAGE_TYPE_KEY = "MESSAGE_TYPE";

    public final static String MESSAGE_STATUS_KEY = "MESSAGE_STATUS";

    public final static String MESSAGE_USER_CHAT_KEY = "MESSAGE_USER_CHAT";

    public final static String MESSAGE_SUMMARIZED_CHAT = "MESSAGE_SUMMARIZED_CHAT";

    public final static String MESSAGE_FEEDBACK_KEY = "MESSAGE_FEEDBACK";

    public final static String MANUALLY_STOP = "MANUALLY_STOP";

    public final static String COST_KEY = "COST";

    public final static String RETRY_KEY = "RETRY";

    // 澄清query
    public final static String IS_CLEAR_QUERY = "IS_CLEAR_QUERY";

    public final static String AI_ASK_CHAT_REQUEST = "AI_ASK_CHAT_REQUEST";

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneyAssistantDataOperateService.class);

    @Resource
    private JourneyPlanAiChatMessageDAO journeyPlanAiChatMessageDAO;
    @Resource
    private CollectionHelper collectionHelper;
    @Resource
    private MdbTairHelper mdbTairHelper;

    @Resource
    private EnvSwitch envSwitch;


    public Boolean hasHistoryMessage(Long userId, String sessionId) {
        JourneyPlanAiChatMessageParam journeyPlanAiChatMessageParam = new JourneyPlanAiChatMessageParam();
        journeyPlanAiChatMessageParam.createCriteria()
                .andSessionIdEqualToWhenPresent(sessionId)
                .andUserIdEqualTo(String.valueOf(userId));
        long count = journeyPlanAiChatMessageDAO.countByParam(journeyPlanAiChatMessageParam);
        return count > 0;
    }

    public Boolean insertHistoryMessage(List<AiJourneyMessageDTO> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return false;
        }

        List<JourneyPlanAiChatMessageDO> doList = messageList.stream().map(this::generateMessageDO)
                .collect(Collectors.toList());
        Lists.partition(doList, 5).forEach(journeyPlanAiChatMessageDAO::batchInsert);
        return true;
    }


    public List<MessageInfo> insertOriginalMessage(JourneyPlanAiInitRequest request, List<String> originalMessageList) {
        if (CollectionUtils.isEmpty(originalMessageList)) {
            return Lists.newArrayList();
        }
        JourneyPlanAiChatMessageParam journeyPlanAiChatMessageParam = new JourneyPlanAiChatMessageParam();
        journeyPlanAiChatMessageParam.createCriteria().andMessageIdIn(originalMessageList);
        journeyPlanAiChatMessageParam.appendOrderByClause(OrderCondition.ID, SortType.ASC);
        List<JourneyPlanAiChatMessageDO> shareMessageDoList = journeyPlanAiChatMessageDAO.selectByParam(journeyPlanAiChatMessageParam);
        if (CollectionUtils.isEmpty(shareMessageDoList)) {
            return Lists.newArrayList();
        }
        List<JourneyPlanAiChatMessageDO> newMessageDoList = Lists.newArrayList();
        for (JourneyPlanAiChatMessageDO shareMessage : shareMessageDoList) {
            JourneyPlanAiChatMessageDO newMessage = generateNewMessageByShare(request, shareMessage);
            newMessageDoList.add(newMessage);
        }
        Lists.partition(newMessageDoList, 10).forEach(journeyPlanAiChatMessageDAO::batchInsert);

        // 查询收藏状态
        Map<String, Boolean> collectMap;
        if (request != null) {
            collectMap = getCollectMap(request.getUserId(), newMessageDoList);
        } else {
            collectMap = Maps.newHashMap();
        }
        return newMessageDoList.stream()
                .map(item -> generateMessageInfo(item, collectMap, false))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public List<MessageInfo> getMessageInfo(List<String> messageIdList, Long userId) {
        if (CollectionUtils.isEmpty(messageIdList)) {
            return null;
        }
        JourneyPlanAiChatMessageParam journeyPlanAiChatMessageParam = new JourneyPlanAiChatMessageParam();
        journeyPlanAiChatMessageParam.createCriteria().andMessageIdIn(messageIdList);
        List<JourneyPlanAiChatMessageDO> messageDoList = journeyPlanAiChatMessageDAO.selectByParam(journeyPlanAiChatMessageParam);
        if (CollectionUtils.isEmpty(messageDoList)) {
            return Lists.newArrayList();
        }
        Map<String, Boolean> collectMap = getCollectMap(userId, messageDoList);
        List<MessageInfo> messageInfos = messageDoList.stream()
                .map(item -> generateMessageInfo(item, collectMap, false))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return messageInfos;
    }


    /**
     * 查询历史消息
     *
     * @param request
     * @return 如果有下一页，会返回start key
     */
    public JourneyPlanAiAssistantHistoryResult queryHistoryMessage(
            JourneyPlanAiAssistantHistoryMessageRequest request) {
        // 没有userid 返回空
        if (StringUtils.isBlank(request.getUserId())) {
            return JourneyPlanAiAssistantHistoryResult.empty();
        }

        // 填充默认参数
        if (Objects.isNull(request.getPageSize())) {
            // 默认10条
            request.setPageSize(10);
        }
        if (Objects.isNull(request.getRightTime())) {
            // 默认右侧区间从当前时间开始
            request.setRightTime(new Date());
        }
        if (Objects.nonNull(request.getStartKey()) && !NumberUtils.isDigits(request.getStartKey())) {
            request.setStartKey(null);
        }
        JourneyPlanAiChatMessageParam journeyPlanAiChatMessageParam = new JourneyPlanAiChatMessageParam();
        journeyPlanAiChatMessageParam
                .createCriteria()
                .andUserIdEqualTo(request.getUserId())
                .andTimestampGreaterThanOrEqualToWhenPresent(request.getLeftTime())
                .andTimestampLessThanOrEqualToWhenPresent(request.getRightTime())
                .andIdLessThanOrEqualToWhenPresent(NumberUtils.createLong(request.getStartKey()))
                .andSessionIdEqualToWhenPresent(request.getSessionId());
        journeyPlanAiChatMessageParam.setPagination(0, request.getPageSize() + 1);
        journeyPlanAiChatMessageParam.appendOrderByClause(OrderCondition.ID, SortType.DESC);
        List<JourneyPlanAiChatMessageDO> journeyPlanAiChatMessageDOS = journeyPlanAiChatMessageDAO.selectByParam(
                journeyPlanAiChatMessageParam);
        if (CollectionUtils.isEmpty(journeyPlanAiChatMessageDOS)) {
            return JourneyPlanAiAssistantHistoryResult.empty();
        }
        JourneyPlanAiAssistantHistoryResult result = new JourneyPlanAiAssistantHistoryResult();
        // 翻转顺序，展示的时候是从远到近来的
        List<JourneyPlanAiChatMessageDO> reverse = Lists.reverse(journeyPlanAiChatMessageDOS);
        if (reverse.size() > request.getPageSize()) {
            result.setStartKey(String.valueOf(reverse.get(0).getId()));
            reverse = reverse.subList(1, request.getPageSize() + 1);
        }
        // 查询收藏状态
        Map<String, Boolean> collectMap = getCollectMap(Long.valueOf(request.getUserId()), reverse);
        result.setMessageList(reverse.stream()
                .map(item -> generateMessageInfo(item, collectMap, false))
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));
        return result;
    }


    /**
     * 根据消息id，查询历史数据
     */
    public JourneyPlanAiAssistantHistoryResult getHistoryByMessageIds(
            JourneyPlanAiAssistantHistoryMessageRequest request) {
        // 没有userid 返回空
        if (StringUtils.isBlank(request.getUserId()) || CollectionUtils.isEmpty(request.getMessageIdList())) {
            return JourneyPlanAiAssistantHistoryResult.empty();
        }
        JourneyPlanAiChatMessageParam journeyPlanAiChatMessageParam = new JourneyPlanAiChatMessageParam();
        journeyPlanAiChatMessageParam
                .createCriteria()
                .andUserIdEqualTo(request.getUserId())
                .andMessageIdIn(request.getMessageIdList());
        journeyPlanAiChatMessageParam.appendOrderByClause(OrderCondition.ID, SortType.DESC);
        List<JourneyPlanAiChatMessageDO> journeyPlanAiChatMessageDOS = journeyPlanAiChatMessageDAO.selectByParam(
                journeyPlanAiChatMessageParam);
        if (CollectionUtils.isEmpty(journeyPlanAiChatMessageDOS)) {
            return JourneyPlanAiAssistantHistoryResult.empty();
        }
        JourneyPlanAiAssistantHistoryResult result = new JourneyPlanAiAssistantHistoryResult();
        // 翻转顺序，展示的时候是从远到近来的
        List<JourneyPlanAiChatMessageDO> reverse = Lists.reverse(journeyPlanAiChatMessageDOS);
        // 查询收藏状态
        Map<String, Boolean> collectMap = getCollectMap(Long.valueOf(request.getUserId()), reverse);
        result.setMessageList(reverse.stream()
                .map(item -> generateMessageInfo(item, collectMap, false))
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));
        return result;
    }


    /**
     * 评价消息
     *
     * @param request
     */

    public Boolean wrateMessage(JourneyPlanAiAssistantWrateMessageRequest request) {
        if (StringUtils.isAnyBlank(request.getUserId(), request.getMessageId(), request.getWrate())) {
            return false;
        }

        JourneyPlanAiChatMessageParam journeyPlanAiChatMessageParam = new JourneyPlanAiChatMessageParam();
        journeyPlanAiChatMessageParam.createCriteria()
                .andUserIdEqualTo(request.getUserId())
                .andMessageIdEqualTo(request.getMessageId());
        JourneyPlanAiChatMessageDO journeyPlanAiChatMessageDO = new JourneyPlanAiChatMessageDO();
        journeyPlanAiChatMessageDO.setWrate(request.getWrate());
        int count = journeyPlanAiChatMessageDAO.updateByParamSelective(journeyPlanAiChatMessageDO,
                journeyPlanAiChatMessageParam);
        return count > 0;
    }

    /**
     * 查询指定消息
     *
     * @param request
     */

    public JourneyPlanAiAssistantHistoryResult querySpecifiedMessage(
            JourneyPlanAiAssistantHistoryMessageRequest request) {
        if (CollectionUtils.isEmpty(request.getMessageIdList())) {
            return JourneyPlanAiAssistantHistoryResult.empty();
        }

        JourneyPlanAiChatMessageParam journeyPlanAiChatMessageParam = new JourneyPlanAiChatMessageParam();
        journeyPlanAiChatMessageParam
                .createCriteria()
                .andMessageIdIn(request.getMessageIdList());
        journeyPlanAiChatMessageParam.appendOrderByClause(OrderCondition.ID, SortType.ASC);
        List<JourneyPlanAiChatMessageDO> journeyPlanAiChatMessageDOS = journeyPlanAiChatMessageDAO.selectByParam(
                journeyPlanAiChatMessageParam);
        if (CollectionUtils.isEmpty(journeyPlanAiChatMessageDOS)) {
            return JourneyPlanAiAssistantHistoryResult.empty();
        }
        JourneyPlanAiAssistantHistoryResult result = new JourneyPlanAiAssistantHistoryResult();
        // 查询收藏状态
        Map<String, Boolean> collectMap = new HashMap<>();
        if (StringUtils.isNotEmpty(request.getUserId())) {
            collectMap = getCollectMap(Long.valueOf(request.getUserId()), journeyPlanAiChatMessageDOS);
        }
        Map<String, Boolean> finalCollectMap = collectMap;
        result.setMessageList(journeyPlanAiChatMessageDOS
                .stream()
                .map(item -> generateMessageInfo(item, finalCollectMap, request.getOnlyStructRoute()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));
        return result;
    }

    /**
     * 删除历史消息
     *
     * @param request
     */
    public Boolean deleteMessage(JourneyPlanAiAssistantDeleteMessageRequest request) {
        if (StringUtils.isBlank(request.getUserId()) || CollectionUtils.isEmpty(request.getMessageIdList())) {
            return false;
        }

        JourneyPlanAiChatMessageParam journeyPlanAiChatMessageParam = new JourneyPlanAiChatMessageParam();
        journeyPlanAiChatMessageParam.createCriteria()
                .andUserIdEqualTo(request.getUserId())
                .andMessageIdIn(request.getMessageIdList());
        int count = journeyPlanAiChatMessageDAO.deleteByParam(journeyPlanAiChatMessageParam);
        return count == request.getMessageIdList().size();
    }

    public Boolean feedBack(JourneyPlanAiAssistantFeedbackRequest request) {
        if (Objects.isNull(request.getUserId()) || StringUtils.isBlank(request.getMessageId())) {
            return false;
        }

        // 找到对应的消息
        JourneyPlanAiChatMessageParam journeyPlanAiChatMessageParam = new JourneyPlanAiChatMessageParam();
        journeyPlanAiChatMessageParam.createCriteria()
                .andUserIdEqualTo(String.valueOf(request.getUserId()))
                .andMessageIdEqualTo(request.getMessageId());
        List<JourneyPlanAiChatMessageDO> journeyPlanAiChatMessageDOS = journeyPlanAiChatMessageDAO.selectByParam(journeyPlanAiChatMessageParam);
        if (CollectionUtils.isEmpty(journeyPlanAiChatMessageDOS)) {
            return false;
        }
        JourneyPlanAiChatMessageDO journeyPlanAiChatMessageDO = journeyPlanAiChatMessageDOS.get(0);

        journeyPlanAiChatMessageParam.clear();
        journeyPlanAiChatMessageParam.createCriteria()
                .andUserIdEqualTo(String.valueOf(request.getUserId()))
                .andMessageIdEqualTo(request.getMessageId());
        String extra = journeyPlanAiChatMessageDO.getExtra();
        JSONObject extInfo = JSONUtil.genJSONObjectFromString(extra);
        extInfo.put(MESSAGE_FEEDBACK_KEY, request);
        extra = JSON.toJSONString(extInfo);
        journeyPlanAiChatMessageDO.setExtra(extra);
        journeyPlanAiChatMessageDO.setWrate(JourneyPlanAiAssistantWrateEnum.DOWN.getCode());
        int count = journeyPlanAiChatMessageDAO.updateByParamSelective(journeyPlanAiChatMessageDO,
                journeyPlanAiChatMessageParam);
        return count > 0;
    }

    /**
     * 更新消息内容
     */
    public void updateContent(String messageId, String oldValue, String newValue) {
        if (StringUtils.isAnyBlank(messageId, oldValue, newValue)) {
            return;
        }
        JourneyPlanAiChatMessageParam journeyPlanAiChatMessageParam = new JourneyPlanAiChatMessageParam();
        journeyPlanAiChatMessageParam.createCriteria()
                .andMessageIdEqualTo(messageId);
        List<JourneyPlanAiChatMessageDO> journeyPlanAiChatMessageDOS = journeyPlanAiChatMessageDAO.selectByParam(journeyPlanAiChatMessageParam);
        if (CollectionUtils.isEmpty(journeyPlanAiChatMessageDOS)) {
            return;
        }
        JourneyPlanAiChatMessageDO journeyPlanAiChatMessageDO = journeyPlanAiChatMessageDOS.get(0);
        String content = journeyPlanAiChatMessageDO.getContent();
        if (StringUtils.isNotBlank(content) && content.contains(oldValue)) {
            content = content.replaceAll(oldValue, newValue).replaceAll("drawPicForReplace", "routeMapImg");
            journeyPlanAiChatMessageDO.setContent(content);
            journeyPlanAiChatMessageDAO.updateByPrimaryKey(journeyPlanAiChatMessageDO);
        }
    }

    private Map<String, Boolean> getCollectMap(Long userId, List<JourneyPlanAiChatMessageDO> list) {
        if (CollectionUtils.isEmpty(list) || userId == null) {
            return Maps.newHashMap();
        }
        try {
            // 提取需要查询收藏的物料
            List<String> collectBizIdList = list.stream()
                    .map(JourneyPlanAiChatMessageDO::getContent)
                    .filter(StringUtils::isNotEmpty)
                    .map(JSONObject::parseObject)
                    .filter(Objects::nonNull)
                    .map(jsonObject -> jsonObject.getJSONArray("components"))
                    .filter(CollectionUtils::isNotEmpty)
                    .map(components -> {
                        ArrayList<String> res = Lists.newArrayList();
                        for (int i = 0; i < components.size(); i++) {
                            JSONObject component = components.getJSONObject(i);
                            Object data = component.get("data");
                            if (data instanceof List) {
                                List<JSONObject> dataJsonList = JSONArray.parseArray(JSON.toJSONString(data), JSONObject.class);
                                for (JSONObject dataJson : dataJsonList) {
                                    Object bizIdObj = JSONUtil.getObject(dataJson, "collectData.bizId");
                                    Object bizTypeObj = JSONUtil.getObject(dataJson, "collectData.bizType");
                                    if (bizTypeObj == null || bizIdObj == null) {
                                        continue;
                                    }
                                    res.add(bizTypeObj + "_" + bizIdObj);
                                }
                            } else {
                                Object bizIdObj = JSONUtil.getObject(component, "data.collectData.bizId");
                                Object bizTypeObj = JSONUtil.getObject(component, "data.collectData.bizType");
                                if (bizTypeObj == null || bizIdObj == null) {
                                    continue;
                                }
                                res.add(bizTypeObj + "_" + bizIdObj);
                            }
                        }
                        return res;
                    }).flatMap(List::stream)
                    .collect(Collectors.toList());
            // 查询收藏
            return collectionHelper.isCollected(userId, collectBizIdList);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("getCollectMap").request(JSON.toJSONString(list)).e(e));
        }
        return Maps.newHashMap();
    }

    private MessageInfo generateMessageInfo(JourneyPlanAiChatMessageDO journeyPlanAiChatMessageDO, Map<String, Boolean> collectMap, Boolean onlyStructRoute) {
        MessageInfo messageInfo = new MessageInfo();
        AiJourneyMessageContentDTO contentDTO = JSON.parseObject(journeyPlanAiChatMessageDO.getContent(),
                AiJourneyMessageContentDTO.class);
        if (Objects.isNull(contentDTO)) {
            return null;
        }
        // 构建收藏状态
        messageInfo.setComponents(contentDTO.getComponents());
        if (contentDTO.getComponents() != null && MapUtils.isNotEmpty(collectMap)) {
            try {
                List<StreamMessageResult.StreamMessageCardModel> newComponents = contentDTO.getComponents().stream()
                        .map(component -> {
                            if (component == null) {
                                return null;
                            }
                            StreamMessageResult.StreamMessageCardModel<Object> objectStreamMessageCardModel = new StreamMessageResult.StreamMessageCardModel<>();
                            objectStreamMessageCardModel.setData(component.getData());
                            objectStreamMessageCardModel.setId(component.getId());
                            objectStreamMessageCardModel.setAction(component.getAction());
                            objectStreamMessageCardModel.setItemType(component.getItemType());
                            objectStreamMessageCardModel.setChildren(component.getChildren());
                            objectStreamMessageCardModel.setStatus(component.getStatus());
                            Object data = component.getData();
                            if (data == null) {
                                return objectStreamMessageCardModel;
                            }
                            if (data instanceof List) {
                                List<JSONObject> dataJsonList = JSONArray.parseArray(JSON.toJSONString(data), JSONObject.class);
                                List<JSONObject> newDataJsonList = Lists.newArrayList();
                                for (JSONObject dataJson : dataJsonList) {
                                    JSONObject jsonObject = parseCollectData(dataJson, collectMap);
                                    if (jsonObject == null) {
                                        newDataJsonList.add(dataJson);
                                    } else {
                                        newDataJsonList.add(jsonObject);
                                    }
                                }
                                objectStreamMessageCardModel.setData(newDataJsonList);
                            } else {
                                JSONObject dataJson = parseCollectData(data, collectMap);
                                if (dataJson == null) {
                                    return objectStreamMessageCardModel;
                                }
                                objectStreamMessageCardModel.setData(dataJson);
                            }
                            return objectStreamMessageCardModel;
                        }).collect(Collectors.toList());
                messageInfo.setComponents(newComponents);
            } catch (Exception e) {
                LOGGER.recordDangerException(new LogModel("parseCollectData").request(JSON.toJSONString(contentDTO)));
            }
        }
        messageInfo.setStructRoute(contentDTO.getStructRoute());
        if (BooleanUtils.isTrue(onlyStructRoute)) {
            return messageInfo;
        }
        messageInfo.setMessageId(journeyPlanAiChatMessageDO.getMessageId());
        messageInfo.setUserId(journeyPlanAiChatMessageDO.getUserId());
        messageInfo.setOriginalMessageId(journeyPlanAiChatMessageDO.getOriginalMessageId());
        messageInfo.setTimestamp(journeyPlanAiChatMessageDO.getTimestamp().getTime());
        messageInfo.setRole(journeyPlanAiChatMessageDO.getRole());
        messageInfo.setWrate(journeyPlanAiChatMessageDO.getWrate());
        messageInfo.setInfo(contentDTO.getContent());
        messageInfo.setThinkingAllOut(StringUtils.isNotBlank(contentDTO.getContent()));
        messageInfo.setThinking(contentDTO.getThinking());
        messageInfo.setRelatedCard(contentDTO.getRelatedCard());
        messageInfo.setRelatedLink(contentDTO.getRelatedLink());
        messageInfo.setThinkingThought(contentDTO.getThinkingThought());
        messageInfo.setExtra(journeyPlanAiChatMessageDO.getExtra());
        // 将MESSAGE_USER_CHAT单独抽出来
        messageInfo.setUserChat(journeyPlanAiChatMessageDO.getUserChat());
        String extra = journeyPlanAiChatMessageDO.getExtra();
        if (StringUtils.isNotBlank(extra)) {
            Map<String, String> extInfo = JSON.parseObject(extra,
                    new TypeReference<Map<String, String>>() {
                    });
            messageInfo.setType(MapUtils.getString(extInfo, MESSAGE_TYPE_KEY));
            messageInfo.setStatus(MapUtils.getString(extInfo, MESSAGE_STATUS_KEY));
            if (StringUtils.isBlank(messageInfo.getUserChat())) {
                messageInfo.setUserChat(MapUtils.getString(extInfo, MESSAGE_USER_CHAT_KEY));
            }
            messageInfo.setManuallyStop(MapUtils.getBoolean(extInfo, MANUALLY_STOP));
            messageInfo.setIsClearQuery(MapUtils.getBoolean(extInfo, IS_CLEAR_QUERY));
        }
        return messageInfo;
    }

    private JSONObject parseCollectData(Object data, Map<String, Boolean> collectMap) {
        JSONObject dataJson = JSONObject.parseObject(JSON.toJSONString(data));
        boolean collectEnabled = dataJson.getBooleanValue("collectEnable");
        Object bizId = JSONUtil.getObject(dataJson, "collectData.bizId");
        Object bizType = JSONUtil.getObject(dataJson, "collectData.bizType");
        if (bizId == null || bizType == null || !collectEnabled) {
            return null;
        }
        boolean collectStatus = MapUtils.getBooleanValue(collectMap, bizType + "_" + bizId, false);
        dataJson.put("collectStatus", collectStatus);
        if (!Switcher.AI_ASK_COLLECTION_ENABLED) {
            dataJson.put("collectEnabled", false);
        }
        return dataJson;
    }


    private JourneyPlanAiChatMessageDO generateNewMessageByShare(JourneyPlanAiInitRequest request, JourneyPlanAiChatMessageDO shareMessageDo) {
        JourneyPlanAiChatMessageDO newMessageDo = new JourneyPlanAiChatMessageDO();
        Date current = new Date();
        newMessageDo.setGmtCreate(current);
        newMessageDo.setGmtModified(current);
        newMessageDo.setUserId(String.valueOf(request.getUserId()));
        newMessageDo.setMessageId(UUID.randomUUID().toString());
        newMessageDo.setSessionId(UUID.randomUUID().toString());
        newMessageDo.setContent(shareMessageDo.getContent());
        newMessageDo.setRole(shareMessageDo.getRole());
        newMessageDo.setTimestamp(current);
        newMessageDo.setTtid(request.getTtid());
        newMessageDo.setUtdid(request.getUtdid());
        newMessageDo.setOriginalMessageId(shareMessageDo.getMessageId());
        newMessageDo.setExtra(shareMessageDo.getExtra());
        newMessageDo.setTraceId(EagleEye.getTraceId());
        newMessageDo.setEnv(envSwitch.getEnv());
        newMessageDo.setSource("share");
        return newMessageDo;
    }


    private JourneyPlanAiChatMessageDO generateMessageDO(AiJourneyMessageDTO message) {
        JourneyPlanAiChatMessageDO journeyPlanAiChatMessageDO = new JourneyPlanAiChatMessageDO();
        Date current = new Date();
        journeyPlanAiChatMessageDO.setGmtCreate(current);
        journeyPlanAiChatMessageDO.setGmtModified(current);
        journeyPlanAiChatMessageDO.setMessageId(message.getMessageId());
        journeyPlanAiChatMessageDO.setUserId(message.getUserId());
        journeyPlanAiChatMessageDO.setUtdid(message.getUtdid());
        journeyPlanAiChatMessageDO.setTtid(message.getTtid());
        journeyPlanAiChatMessageDO.setSessionId(message.getSessionId());
        journeyPlanAiChatMessageDO.setRole(message.getRole());
        journeyPlanAiChatMessageDO.setWrate(message.getWrate());
        journeyPlanAiChatMessageDO.setTraceId(EagleEye.getTraceId());
        journeyPlanAiChatMessageDO.setEnv(envSwitch.getEnv());
        journeyPlanAiChatMessageDO.setUserChat(message.getUserChat());

        // 构建地图图片
        buildContentMapImpUrl(message.getMessageId(), message.getContent());
        journeyPlanAiChatMessageDO.setContent(JSON.toJSONString(message.getContent(), SerializerFeature.DisableCircularReferenceDetect));
        journeyPlanAiChatMessageDO.setExtra(message.getExtra());
        journeyPlanAiChatMessageDO.setTimestamp(message.getTimestamp());
        Map<String, Object> extInfo = Maps.newHashMap();
        extInfo.put(MESSAGE_STATUS_KEY, message.getStatus());
        extInfo.put(MESSAGE_TYPE_KEY, message.getType());
        extInfo.put(MESSAGE_USER_CHAT_KEY, message.getUserChat());
        extInfo.put(MESSAGE_SUMMARIZED_CHAT, message.getSummarizedChat());
        extInfo.put(COST_KEY, String.valueOf(message.getCost()));
        extInfo.put(RETRY_KEY, message.getRetry());
        extInfo.put(IS_CLEAR_QUERY, message.getIsClearQuery());
        if (message.getAiaskChatRequest() != null) {
            extInfo.put(AI_ASK_CHAT_REQUEST, JSON.toJSONString(message.getAiaskChatRequest()));
        }
        String key = MANUALLY_STOP_KEY + message.getMessageId();
        Object value = mdbTairHelper.getValue(key);
        if (Objects.nonNull(value)) {
            extInfo.put(MANUALLY_STOP, value.toString());
        }
        journeyPlanAiChatMessageDO.setExtra(JSON.toJSONString(extInfo));
        journeyPlanAiChatMessageDO.setSource(message.getSource());
        return journeyPlanAiChatMessageDO;
    }

    private void buildContentMapImpUrl(String systemMessageId, AiJourneyMessageContentDTO content) {
        if (Objects.isNull(content) || StringUtils.isBlank(systemMessageId)) {
            return;
        }
        List<StreamMessageResult.StreamMessageCardModel> components = content.getComponents();
        if (CollectionUtils.isEmpty(components)) {
            return;
        }
        for (StreamMessageResult.StreamMessageCardModel component : components) {
            if (!Objects.equals(component.getItemType(), "drawing_map")) {
                continue;
            }
            String cardId = component.getId();
            if (component.getData() == null) {
                continue;
            }
            Map<String, Object> data = (Map<String, Object>) component.getData();
            // 先把历史消息塞进去
            data.put("isHistory", true);

            // 地图图片为空情况
            String mapUrl = getMapUrl(systemMessageId, cardId);
            if (StringUtils.isBlank(mapUrl)) {
                continue;
            }
            if (MapUtils.isEmpty(data)) {
                continue;
            }
            data.put("routeMapImg", mapUrl);
            data.remove("drawPicForReplace");
            component.setData(data);
        }
    }

    /**
     * 获取地图链接
     */
    public String getMapUrl(String messageId, String cardId) {
        String key = JourneyPlanAiAssistantMessageHelper.MAP_URL_KEY + messageId + "_" + cardId;
        Object value = mdbTairHelper.getValue(key);
        if (Objects.isNull(value)) {
            return StringUtils.EMPTY;
        }
        return String.valueOf(value);
    }

    public void addStopFlag(String currentMessageId) {
        if (StringUtils.isBlank(currentMessageId)) {
            return;
        }
        String key = MANUALLY_STOP_KEY + currentMessageId;
        Object value = mdbTairHelper.getValue(key);
        if (Objects.isNull(value)) {
            mdbTairHelper.putValue(key, true, MANUALLY_STOP_TTL_S);
        }
    }


    /**
     * 更新是否被分享状态
     *
     * @param messageIds
     */
    public void updateExtraByShare(List<String> messageIds) {
        try {
            JourneyPlanAiChatMessageParam param = new JourneyPlanAiChatMessageParam();
            param.createCriteria().andMessageIdIn(messageIds);
            List<JourneyPlanAiChatMessageDO> journeyPlanAiChatMessageDOS = journeyPlanAiChatMessageDAO.selectByParam(param);
            if (CollectionUtils.isEmpty(journeyPlanAiChatMessageDOS)) {
                return;
            }
            Date modifiedTime = new Date();
            for (JourneyPlanAiChatMessageDO journeyPlanAiChatMessageDO : journeyPlanAiChatMessageDOS) {
                JourneyPlanAiChatMessageDO updateDO = new JourneyPlanAiChatMessageDO();
                String extra = journeyPlanAiChatMessageDO.getExtra();
                JSONObject jsonObject = StringUtils.isBlank(extra) ? new JSONObject() : JSON.parseObject(extra);
                jsonObject.put("hasBeenShared", true);
                updateDO.setExtra(jsonObject.toJSONString());
                updateDO.setId(journeyPlanAiChatMessageDO.getId());
                updateDO.setGmtModified(modifiedTime);
                journeyPlanAiChatMessageDAO.updateByPrimaryKeySelective(updateDO);
            }
        } catch (Exception e) {
            LOGGER.recordNormalException(new LogModel("JourneyAssistantDataOperateService.updateExtraByShare").e(e).message("分享更新share状态出错"));
        }
    }

    public List<MessageInfo> getUserRecentMessageBySource(QueryRecentMessageRequest request) {
        String userId = request.getUserId();
        String source = request.getSource();
        if (StringUtils.isBlank(userId)) {
            return Lists.newArrayList();
        }
        List<JourneyPlanAiChatMessageDO> messageDoList = journeyPlanAiChatMessageDAO.getRecentQueryInfoBySource(userId, source);
        if (CollectionUtils.isEmpty(messageDoList)) {
            return Lists.newArrayList();
        }
        return messageDoList.stream()
                .map(this::generateSingleMessageInfo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取user角色下最近一条用户消息，
     *
     * @param userId 用户id
     * @return 最近一条用户消息
     */
    public MessageInfo getLastOneUserRoleMessageIgnoreSource(String userId) {
        List<JourneyPlanAiChatMessageDO> recentQueryInfoBySource
                = journeyPlanAiChatMessageDAO.getRecentQueryInfoBySource(userId, null);
        if (CollectionUtils.isEmpty(recentQueryInfoBySource)) {
            return null;
        }
        //使用id降序， 取最近一条即可
        JourneyPlanAiChatMessageDO journeyPlanAiChatMessageDO = recentQueryInfoBySource.get(0);
        return generateSingleMessageInfo(journeyPlanAiChatMessageDO);
    }

    private MessageInfo generateSingleMessageInfo(JourneyPlanAiChatMessageDO journeyPlanAiChatMessageDO) {
        MessageInfo messageInfo = new MessageInfo();
        AiJourneyMessageContentDTO contentDTO = JSON.parseObject(journeyPlanAiChatMessageDO.getContent(),
                AiJourneyMessageContentDTO.class);
        if (Objects.isNull(contentDTO)) {
            return null;
        }
        messageInfo.setMessageId(journeyPlanAiChatMessageDO.getMessageId());
        messageInfo.setComponents(contentDTO.getComponents());
        messageInfo.setStructRoute(contentDTO.getStructRoute());
        messageInfo.setOriginalMessageId(journeyPlanAiChatMessageDO.getOriginalMessageId());
        messageInfo.setTimestamp(journeyPlanAiChatMessageDO.getTimestamp().getTime());
        messageInfo.setRole(journeyPlanAiChatMessageDO.getRole());
        messageInfo.setWrate(journeyPlanAiChatMessageDO.getWrate());
        messageInfo.setInfo(contentDTO.getContent());
        messageInfo.setThinkingAllOut(StringUtils.isNotBlank(contentDTO.getContent()));
        messageInfo.setThinking(contentDTO.getThinking());
        messageInfo.setRelatedCard(contentDTO.getRelatedCard());
        messageInfo.setRelatedLink(contentDTO.getRelatedLink());
        messageInfo.setThinkingThought(contentDTO.getThinkingThought());
        messageInfo.setExtra(journeyPlanAiChatMessageDO.getExtra());
        messageInfo.setSource(journeyPlanAiChatMessageDO.getSource());
        String extra = journeyPlanAiChatMessageDO.getExtra();
        if (StringUtils.isNotBlank(extra)) {
            Map<String, String> extInfo = JSON.parseObject(extra,
                    new TypeReference<Map<String, String>>() {
                    });
            messageInfo.setType(MapUtils.getString(extInfo, MESSAGE_TYPE_KEY));
            messageInfo.setStatus(MapUtils.getString(extInfo, MESSAGE_STATUS_KEY));
            messageInfo.setUserChat(MapUtils.getString(extInfo, MESSAGE_USER_CHAT_KEY));
            messageInfo.setManuallyStop(MapUtils.getBoolean(extInfo, MANUALLY_STOP));
            messageInfo.setIsClearQuery(MapUtils.getBoolean(extInfo, IS_CLEAR_QUERY));
        }
        messageInfo.setSessionId(journeyPlanAiChatMessageDO.getSessionId());
        return messageInfo;
    }

    @AteyeInvoker(description = "获取用户最近10条消息", paraDesc = "userId&source")
    public List<MessageInfo> getUserRecentMessageBySource(String userId, String source) {
        QueryRecentMessageRequest queryRecentMessageRequest = new QueryRecentMessageRequest();
        queryRecentMessageRequest.setUserId(userId);
        queryRecentMessageRequest.setSource(source);
        return getUserRecentMessageBySource(queryRecentMessageRequest);
    }

}
