package com.taobao.trip.jourprod.service.impl.journeyplannew;

import com.alibaba.boot.switchcenter.annotation.SwitchGroup;
import com.alibaba.fastjson.JSON;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.trip.jourprod.domain.JourneyPlanAiChatSessionDO;
import com.taobao.trip.jourprod.domain.JourneyPlanAiChatSessionParam;
import com.taobao.trip.jourprod.domain.JourneyPlanAiChatSessionParam.OrderCondition;
import com.taobao.trip.jourprod.mapper.JourneyPlanAiChatSessionDAO;
import com.taobao.trip.request.AiJourneySessionListRequest;
import com.taobao.trip.response.AiJourneySessionListResponse;
import com.taobao.trip.response.AiJourneySessionListResponse.SessionItem;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description 问一问会话操作服务类
 * <AUTHOR>
 * @Date 2025/4/25
 **/
@Service
@SwitchGroup
public class JourneyAssistantSessionOperateService {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneyAssistantSessionOperateService.class);

    @AppSwitch(des = "最大查询的页码数量", level = Level.p4)
    public static Integer MAX_PAGE_NUM = 50;

    @AppSwitch(des = "一页查询的大小", level = Level.p4)
    public static Integer PAGE_SIZE = 14;

    @Resource
    private JourneyPlanAiChatSessionDAO journeyPlanAiChatSessionDAO;

    /**
     * 查询会话列表
     *
     * @param request 请求参数
     * @return 会话列表响应
     */
    public AiJourneySessionListResponse querySessionList(AiJourneySessionListRequest request) {
        LOGGER.recordEntry(new LogModel("querySessionList_service"));
        
        // 1. 校验参数
        if (request == null || request.getUserId() == null) {
            LOGGER.recordNormalException(new LogModel("querySessionList")
                .message("param is null")
                .request(JSON.toJSONString(request)));
            return new AiJourneySessionListResponse();
        }

        // 2. 处理分页参数
        if (Objects.isNull(request.getPageSize()) ||  request.getPageSize() <= 0) {
            // 默认每页10条
            request.setPageSize(PAGE_SIZE);
        }
        if (StringUtils.isNotBlank(request.getStartKey()) && NumberUtils.isDigits(request.getStartKey())) {
            request.setPageNum(NumberUtils.toInt(request.getStartKey()));
        } else {
            request.setPageNum(1);
        }
        if (request.getPageNum() > MAX_PAGE_NUM) {
            // 如果超过最大页码 直接返回
            LOGGER.recordDangerException(new LogModel("querySessionList")
                .message("pageNum is too large")
                .request(JSON.toJSONString(request)));
            return new AiJourneySessionListResponse();
        }
        
        try {
            // 3. 创建查询参数
            JourneyPlanAiChatSessionParam sessionParam = new JourneyPlanAiChatSessionParam();
            
            sessionParam.createCriteria()
                .andDeleteFlagEqualTo("0")
                .andUserIdEqualTo(request.getUserId().toString());
            
            // 4. 设置分页和排序
            sessionParam.setPagination(request.getPageNum(), request.getPageSize()); // 多查一条用于判断是否有下一页
            sessionParam.appendOrderByClause(OrderCondition.GMTMODIFIED, JourneyPlanAiChatSessionParam.SortType.DESC); // 按照修改时间降序排列
            
            // 5. 执行查询
            List<JourneyPlanAiChatSessionDO> sessions = journeyPlanAiChatSessionDAO.selectByParam(sessionParam);
            
            // 6. 处理查询结果
            AiJourneySessionListResponse response = new AiJourneySessionListResponse();
            if (CollectionUtils.isNotEmpty(sessions)) {
                // 7. 处理分页结果，判断是否有下一页
                if (CollectionUtils.isNotEmpty(sessions) && sessions.size() == request.getPageSize()) {
                    // 如果结果大于等于pageSize，则说明还有下一页
                    response.setStartKey(String.valueOf(request.getPageNum() + 1));
                }

                // 8. 转换数据为响应格式 - 直接使用内部类
                // 添加会话列表
                List<SessionItem> sessionItems = sessions.stream()
                    .map(this::convertToSessionItem)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
                response.setSessionList(sessionItems);
            }
            return response;
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("querySessionList_service_exception").e(e));
            throw new RuntimeException("查询会话列表异常", e);
        }
    }

    public boolean exist(Long userId, String sessionId) {
        if (userId == null || StringUtils.isBlank(sessionId)) {
            return false;
        }

        try {
            JourneyPlanAiChatSessionParam sessionParam = new JourneyPlanAiChatSessionParam();
            sessionParam.createCriteria()
                .andUserIdEqualTo(userId.toString())
                .andSessionIdEqualTo(sessionId)
                .andDeleteFlagEqualTo("0");

            List<JourneyPlanAiChatSessionDO> sessions = journeyPlanAiChatSessionDAO.selectByParam(sessionParam);
            return CollectionUtils.isNotEmpty(sessions);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("exist_service_exception").e(e));
            throw new RuntimeException("查询会话是否存在异常", e);
        }
    }

    /**
     * 删除会话
     * @param request 请求参数
     * @return 是否删除成功
     */
    public Boolean deleteSession(AiJourneySessionListRequest request) {
        LOGGER.recordEntry(new LogModel("deleteSession_service"));
        
        if (request == null || request.getUserId() == null) {
            return false;
        }
        
        try {
            if (CollectionUtils.isEmpty(request.getSessionIdList()) && BooleanUtils.isNotTrue(request.getDeleteAll())) {
                return false;
            }
            
            // 创建删除条件
            JourneyPlanAiChatSessionParam sessionParam = new JourneyPlanAiChatSessionParam();
            sessionParam.createCriteria()
                .andUserIdEqualTo(request.getUserId().toString())
                .andSessionIdInWhenPresent(request.getSessionIdList());
            
            // 执行删除
            JourneyPlanAiChatSessionDO journeyPlanAiChatSessionDO = new JourneyPlanAiChatSessionDO();
            journeyPlanAiChatSessionDO.setDeleteFlag("1");
            int count = journeyPlanAiChatSessionDAO.updateByParamSelective(journeyPlanAiChatSessionDO, sessionParam);
            return count > 0;
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("deleteSession_service_exception").e(e));
            return false;
        }
    }

    /**
     * 更新会话最近更新时间
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @return 是否更新成功
     */
    public Boolean updateSessionLastModifiedTime(Long userId, String sessionId) {
        LOGGER.recordEntry(new LogModel("updateSessionLastModifiedTime_service"));
        
        if (userId == null || StringUtils.isBlank(sessionId)) {
            return false;
        }
        
        try {
            // 创建查询条件
            JourneyPlanAiChatSessionParam sessionParam = new JourneyPlanAiChatSessionParam();
            sessionParam.createCriteria()
                .andUserIdEqualTo(userId.toString())
                .andSessionIdEqualTo(sessionId)
                .andDeleteFlagEqualTo("0");
            
            // 创建更新数据
            JourneyPlanAiChatSessionDO sessionDO = new JourneyPlanAiChatSessionDO();
            sessionDO.setGmtModified(new Date());
            
            // 执行更新操作
            int count = journeyPlanAiChatSessionDAO.updateByParamSelective(sessionDO, sessionParam);
            
            LOGGER.recordOutput(new LogModel("updateSessionLastModifiedTime_service")
                    .request("userId:" + userId + ",sessionId:" + sessionId)
                    .response("更新会话最近更新时间" + (count > 0 ? "成功" : "失败"))
                    .success(count > 0));
            
            return count > 0;
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("updateSessionLastModifiedTime_service_exception")
                    .request("userId:" + userId + ",sessionId:" + sessionId)
                    .e(e));
            return false;
        }
    }

    // 将数据库对象转换为SessionItem
    private SessionItem convertToSessionItem(JourneyPlanAiChatSessionDO sessionDO) {
        SessionItem item = new SessionItem();
        item.setSessionId(sessionDO.getSessionId());
        String sessionName = sessionDO.getSessionName();
        if (StringUtils.isNotBlank(sessionName)) {
            sessionName = sessionName.replaceAll("\\n", "");
            item.setSessionName(sessionName);
            item.setTimeStamp(String.valueOf(sessionDO.getGmtModified().getTime()));
            item.setTimeStr(DateFormatUtils.format(sessionDO.getGmtModified(), "yyyy-MM-dd HH:mm:ss"));
            return item;
        }
        return null;
    }

    @AteyeInvoker(description = "更新字段")
    public void updateDeleteFlag() {
        JourneyPlanAiChatSessionDO journeyPlanAiChatSessionDO = new JourneyPlanAiChatSessionDO();
        journeyPlanAiChatSessionDO.setDeleteFlag("0");
        JourneyPlanAiChatSessionParam journeyPlanAiChatSessionParam = new JourneyPlanAiChatSessionParam();
        journeyPlanAiChatSessionParam.createCriteria().andDeleteFlagIsNull();
        journeyPlanAiChatSessionDAO.updateByParamSelective(journeyPlanAiChatSessionDO, journeyPlanAiChatSessionParam);
    }

}