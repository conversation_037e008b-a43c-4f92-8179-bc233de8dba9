package com.taobao.trip.jourprod.service.impl.journeyplannew;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.taobao.mtop.common.Result;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.GaoDePoiTypeEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.GaoDePoiModel;
import com.taobao.trip.jourprod.biz.frt.search.GaoDeSearchService;
import com.taobao.trip.jourprod.core.service.gaode.AMapService;
import com.taobao.trip.jourprod.service.facade.gaode.JourneyGaoDeQueryFacade;
import com.taobao.trip.jourprod.service.facade.model.request.gaode.GaoDeAroundQueryRequest;
import com.taobao.trip.jourprod.service.facade.model.request.gaode.GaoDeSearchQueryRequest;
import com.taobao.trip.jourprod.service.facade.model.response.gaode.GaoDeAroundQueryResponse;
import com.taobao.trip.wireless.annotation.SsifMtop;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch.AI_SEARCH_AROUND_RADIUS;
import static com.taobao.trip.jourprod.biz.frt.search.GaoDeSearchService.*;
import static com.taobao.trip.jourprod.core.service.gaode.AMapService.DefaultShowFields;

@SsifMtop(serviceInterface = JourneyGaoDeQueryFacade.class)
@Service
public class JourneyGaoDeQueryFacadeImpl implements JourneyGaoDeQueryFacade {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneyGaoDeQueryFacadeImpl.class);

    @Resource
    private AMapService aMapService;

    @Resource
    private GaoDeSearchService gaoDeSearchService;


    @Override
    public Result<GaoDeAroundQueryResponse> queryGaoDeAround(GaoDeAroundQueryRequest request) {
        Result<GaoDeAroundQueryResponse> result = new Result<>();
        result.setSuccess(true);
        try {
            String longitude = request.getLongitude();
            String latitude = request.getLatitude();
            String types = request.getTypes();
            if (StringUtils.isAnyBlank(longitude, latitude, types)) {
                return result;
            }
            GaoDeSearchQueryRequest gaoDeRequest = new GaoDeSearchQueryRequest();
            gaoDeRequest.setLocation(request.getLongitude() + SPLIT + request.getLatitude());
            gaoDeRequest.setRadius(AI_SEARCH_AROUND_RADIUS);
            gaoDeRequest.setShowFields(DefaultShowFields);
            if (StringUtils.isNotBlank(types)) {
                types = GaoDePoiTypeEnum.nameOf(types).getCode();
            }
            gaoDeRequest.setTypes(types);

            List<Map<String, Object>> poiList = aMapService.queryGaoDeSearchAround(gaoDeRequest, true);
            if (CollectionUtils.isEmpty(poiList)) {
                return result;
            }
            List<GaoDePoiModel> gaodeList = poiList.stream().map(poi -> {
                GaoDePoiModel model = new GaoDePoiModel();
                model.setId(MapUtils.getString(poi, ID));
                model.setName(MapUtils.getString(poi, NAME));
                String location = MapUtils.getString(poi, LOCATION);
                String[] split = location.split(SPLIT);
                model.setLongitude(split[0]);
                model.setLatitude(split[1]);
                model.setType(MapUtils.getString(poi, TYPE));
                model.setAddress(MapUtils.getString(poi, ADDRESS));
                List<Map<String, Object>> photos = (List<Map<String, Object>>) poi.get(PHOTOS);
                model.setImgList(gaoDeSearchService.getDealImgList(photos));

                Map<String, Object> business = (Map<String, Object>) poi.get(BUSINESS);
                if (MapUtils.isNotEmpty(business)) {
                    model.setOpenTime(MapUtils.getString(business, OPEN_TIME_WEEK));
                    model.setTag(MapUtils.getString(business, TAG));
                    model.setCost(MapUtils.getString(business, COST));
                    model.setRating(MapUtils.getString(business, RATING));
                }
                return model;
            }).collect(Collectors.toList());

            GaoDeAroundQueryResponse response = new GaoDeAroundQueryResponse();
            response.setList(gaodeList);
            result.setModel(response);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("queryGaoDeAround").request(JSON.toJSONString(request)).e(e));
        }
        return result;
    }
}
