package com.taobao.trip.jourprod.service.impl.journeyplannew;

import com.alibaba.boot.switchcenter.annotation.SwitchGroup;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.mtop3.invocation.MtopStream;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.mtop.api.agent.MtopContext;
import com.taobao.mtop.common.Result;
import com.taobao.trip.common.AskType;
import com.taobao.trip.jourprod.biz.common.annotation.MtopThrowing;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.JourneyPlanAiAssistantFrt;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanMessageStatusEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantMessageHelper;
import com.taobao.trip.jourprod.common.lang.enums.BizErrorCodeEnum;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.jourprod.common.lang.utils.RateLimiterUtil;
import com.taobao.trip.jourprod.common.sal.exception.TripJourneyException;
import com.taobao.trip.jourprod.service.facade.journeyplannew.JourneyPlanAiAssistantFacade;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.*;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.*;
import com.taobao.trip.jourprod.service.impl.BaseMtopService;
import com.taobao.trip.wireless.annotation.SsifMtop;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description 智能出行规划助手
 * <AUTHOR>
 * @Date 2025/1/13
 **/
@SsifMtop(serviceInterface = JourneyPlanAiAssistantFacade.class, clientTimeout = 600000, version = "1.0.0.ask")
@Service
@MtopThrowing(errorMsg = "系统异常")
@SwitchGroup
public class JourneyPlanAiAssistantFacadeImpl extends BaseMtopService implements JourneyPlanAiAssistantFacade {


    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneyPlanAiAssistantFacadeImpl.class);

    @AppSwitch(des = "初始化接口mock", level = Level.p4)
    public static String INIT_MOCK_DATA = "";

    @AppSwitch(des = "总结接口mock", level = Level.p4)
    public static String SUMMARY_MOCK_DATA = "";

    @AppSwitch(des = "历史消息接口mock", level = Level.p4)
    public static String HISTORY_MOCK_DATA = "";

    @AppSwitch(des = "查看分享数据mock数据", level = Level.p4)
    public static String VIEW_SHARE_MOCK_DATA = "";

    @AppSwitch(des = "查看查询poi详情用浮层展示数据mock数据", level = Level.p4)
    public static String QUERY_POI_BY_FLOAT_MOCK_DATA = "";

    @Resource
    private JourneyPlanAiAssistantFrt journeyPlanAiAssistantFrt;

    @Resource
    private JourneyPlanAiAssistantHelper journeyPlanAiAssistantHelper;

    @Resource
    private JourneyAssistantDataOperateService journeyAssistantDataOperateFacade;

    @Resource
    private JourneyPlanAiAssistantMessageHelper journeyPlanAiAssistantMessageHelper;

    @Resource
    private JourneyAssistantDataOperateService journeyAssistantDataOperateService;



    /**
     * 初始化接口，给用户返回初始化信息
     */
    @Override
    public Result<JourneyPlanAiInitResult> init(JourneyPlanAiInitRequest request) {
        LOGGER.recordEntry(new LogModel("init")
                .request(JSONUtil.toJSONString(request)));
        if (request == null || Objects.isNull(request.getUserId())) {
            return mtopFailed("请求参数为空", "请求参数为空");
        }

        if (StringUtils.isNotBlank(INIT_MOCK_DATA)) {
            return mtopSuccess(JSONUtil.parseObject(INIT_MOCK_DATA, JourneyPlanAiInitResult.class));
        }
        try {
            JourneyPlanAiInitResult result = journeyPlanAiAssistantFrt.init(request);
            return mtopSuccess(result);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("init_exception").e(e));
            return mtopFailed(BizErrorCodeEnum.SYSTEM_ERROR);
        }

    }

    /**
     * 对话接口，取到返回结果后，会把数据放到缓存中
     *
     * @param request
     * @return
     */
    @Override
    public Result<StreamMessageResult> chat(JourneyPlanAiChatRequest request) {
        LOGGER.recordEntry(new LogModel("chat")
                .request(JSONUtil.toJSONString(request)));
        dealUserId(request);
        // 三方调用，无登录态
        String source = request.getSource();
        if (request.getUserId() == null && StringUtils.equals(source, AskType.HONOR_ASK.getCode())) {
            request.setUserId(0L);
        }
        if (request == null || ObjectUtils.anyNull(request.getUserId(), request.getChat())) {
            LOGGER.recordNormalException(new LogModel("chat").message("param is null"));
            return mtopFailed("请求参数为空", "请求参数为空");
        }
        // 判断是否支持流式返回
        if (!MtopContext.acceptStream()) {
            LOGGER.recordNormalException(new LogModel("chat_noStream").request(JSONUtil.toJSONString(request)));
            return mtopFailed("没有开启流式", "没有开启流式");
        }
        // 临时处理H5不在端容器时保存到数据库时的非空校验
        if (StringUtils.isBlank(request.getTtid())) {
            request.setTtid(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(request.getUtdid())) {
            request.setUtdid(StringUtils.EMPTY);
        }
        Semaphore rateLimiter = RateLimiterUtil.getRateLimiter();
        try {
            // 尝试获取令牌，如果获取不到立即返回 false
            if (!rateLimiter.tryAcquire(100, TimeUnit.MILLISECONDS)) {
                MtopStream mtopStream = MtopContext.startStream();
                StreamMessageResult result = new StreamMessageResult(UUID.randomUUID().toString(),UUID.randomUUID().toString() , StringUtils.EMPTY, RateLimiterUtil.limitMessage, AiJourneyPlanMessageStatusEnum.NORMAL.getStatusCode(), 0);
                mtopStream.writeAndEnd(JSON.toJSONString(result));
                LOGGER.recordNormalException(new LogModel("chat_limit").request(JSONUtil.toJSONString(request)));
                return mtopFailed(BizErrorCodeEnum.SYSTEM_ERROR);
            }
            try {
                StreamMessageResult chat = journeyPlanAiAssistantFrt.streamChat(request);
                return mtopSuccess(chat);
            }finally {
                rateLimiter.release();
            }

        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("chat").message("chat_error").e(e));
            return mtopFailed(BizErrorCodeEnum.SYSTEM_ERROR);
        } finally {
            LOGGER.recordOutput(new LogModel("chat").message("chat_end"));
        }
    }


    private void dealUserId(JourneyPlanAiChatRequest req) {
        try {
            if (req == null) {
                return;
            }

            if (req.getUserId() == null || req.getUserId() <= 0) {
                req.setUserId(MtopContext.getUserId());
            }

            if (req.getUserId() == null || req.getUserId() <= 0) {
                String ext = req.getExt();
                if (StringUtils.isBlank(ext)) {
                    return;
                }
                JSONObject jsonObject = JSONObject.parseObject(ext);
                if (jsonObject != null && StringUtils.isNumeric(jsonObject.getString("userId"))) {
                    req.setUserId(Long.valueOf(jsonObject.getString("userId")));
                }
                return;
            }
        } catch (Exception e){
            LOGGER.recordDangerException(new LogModel("chat").message("dealUserId_error").e(e));
        }
    }

    /**
     * 对话续接接口
     *
     * @param request
     * @return
     */
    @Override
    public Result<StreamMessageResult> continueChat(JourneyPlanAiChatRequest request) {
        LOGGER.recordEntry(new LogModel("continueChat")
                .request(JSONUtil.toJSONString(request)));
        // 判断是否支持流式返回
        if (!MtopContext.acceptStream()) {
            LOGGER.recordNormalException(new LogModel("continueChat").request(JSONUtil.toJSONString(request)));
            return mtopFailed("没有开启流式", "没有开启流式");
        }

        try {
            StreamMessageResult streamMessageResult = journeyPlanAiAssistantFrt.continueChat(request);
            return mtopSuccess(streamMessageResult);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("continueChat").e(e));
            return mtopFailed(BizErrorCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 停止回答
     *
     * @param request
     * @return
     */
    @Override
    public Result<Boolean> stopChat(JourneyPlanAiChatRequest request) {
        LOGGER.recordEntry(new LogModel("stopChatInterface")
                .code(request.getStopScene())
                .request(JSONUtil.toJSONString(request)));
        try {
            Boolean result = journeyPlanAiAssistantFrt.stopChat(request);
            //手动停止的数据加个标志位
            journeyAssistantDataOperateService.addStopFlag(request.getCurrentMessageId());

            return mtopSuccess(result);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("stopChat").e(e));
            return mtopFailed(BizErrorCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 总结返回接口
     *
     * @param request
     * @return
     */
    @Override
    public Result<JourneyPlanMessageSummaryResult> chatSummary(JourneyPlanAiChatRequest request) {
        LOGGER.recordEntry(new LogModel("chatSummary")
                .request(JSONUtil.toJSONString(request)));
        if (StringUtils.isNotBlank(SUMMARY_MOCK_DATA)) {
            return mtopSuccess(JSONUtil.parseObject(SUMMARY_MOCK_DATA, JourneyPlanMessageSummaryResult.class));
        }
        try {
            JourneyPlanMessageSummaryResult result = journeyPlanAiAssistantFrt.chatSummary(request);
            return mtopSuccess(result);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("chatSummary").e(e));
            return mtopFailed(BizErrorCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 重置会话
     *
     * @param request
     * @return
     */
    @Override
    public Result<Boolean> resetChat(JourneyPlanAiChatRequest request) {
        LOGGER.recordEntry(new LogModel("resetChat")
                .request(JSONUtil.toJSONString(request)));
        try {
            Boolean result = journeyPlanAiAssistantFrt.resetChat(request);
            return mtopSuccess(result);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("resetChat").e(e));
            return mtopFailed(BizErrorCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 查询历史消息，按照创建时间降序
     *
     * @param request
     */
    @Override
    public Result<JourneyPlanAiAssistantHistoryWithCurrentChatResult> history(JourneyPlanAiHistoryRequest request) {
        LOGGER.recordEntry(new LogModel("history")
                .request(JSONUtil.toJSONString(request)));
        if (StringUtils.isNotBlank(HISTORY_MOCK_DATA)) {
            return mtopSuccess(JSONUtil.parseObject(HISTORY_MOCK_DATA, JourneyPlanAiAssistantHistoryWithCurrentChatResult.class));
        }
        try {
            JourneyPlanAiAssistantHistoryWithCurrentChatResult result = journeyPlanAiAssistantFrt.history(request);
            return mtopSuccess(result);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("history").e(e));
            return mtopFailed(BizErrorCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 评价消息
     *
     * @param request
     * @return
     */
    @Override
    public Result<Boolean> wrateMessage(JourneyPlanAiChatRequest request) {
        LOGGER.recordEntry(new LogModel("wrateMessage")
                .request(JSONUtil.toJSONString(request)));
        try {
            Boolean result = journeyPlanAiAssistantFrt.wrateMessage(request);
            return mtopSuccess(result);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("wrateMessage").e(e));
            return mtopFailed(BizErrorCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 分享消息
     *
     * @param request
     * @return
     */
    @Override
    public Result<JourneyPlanShareMessageResult> shareMessage(JourneyPlanAiChatShareRequest request) {
        LOGGER.recordEntry(new LogModel("shareMessage")
                .request(JSONUtil.toJSONString(request)));
        try {
            JourneyPlanShareMessageResult result = journeyPlanAiAssistantFrt.shareMessage(request);
            return mtopSuccess(result);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("shareMessage").e(e));
            return mtopFailed(BizErrorCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 查看分享消息
     *
     * @param request
     * @return
     */
    @Override
    public Result<JourneyPlanAiAssistantHistoryResult> viewShareMessage(JourneyPlanAiChatViewShareRequest request) {
        LOGGER.recordEntry(new LogModel("viewShareMessage")
                .request(JSONUtil.toJSONString(request)));
        if (StringUtils.isNotBlank(VIEW_SHARE_MOCK_DATA)) {
            return mtopSuccess(JSONUtil.parseObject(VIEW_SHARE_MOCK_DATA, JourneyPlanAiAssistantHistoryResult.class));
        }
        try {
            JourneyPlanAiAssistantHistoryResult result = journeyPlanAiAssistantFrt.viewShareMessage(request);
            return mtopSuccess(result);
        } catch (TripJourneyException e) {
            return mtopFailed(e);
        }catch (Exception e){
            LOGGER.recordDangerException(new LogModel("viewShareMessage").e(e));
            return mtopFailed(BizErrorCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 根据消息id查看结构化线路
     *
     * @param request
     */
    @Override
    public Result<JourneyPlanAiStructRouteResult> structRoute(JourneyPlanAiStructRouteRequest request) {
        LOGGER.recordEntry(new LogModel("structRoute")
                .request(JSONUtil.toJSONString(request)));
        try {
            JourneyPlanAiStructRouteResult result = journeyPlanAiAssistantFrt.structRoute(request);
            return mtopSuccess(result);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("structRoute").e(e));
            return mtopFailed(BizErrorCodeEnum.SYSTEM_ERROR);
        }

    }

    /**
     * 删除消息
     *
     * @param request
     */
    @Override
    public Result<Boolean> deleteMessage(JourneyPlanDeleteMessageRequest request) {
        LOGGER.recordEntry(new LogModel("deleteMessage")
                .request(JSONUtil.toJSONString(request)));
        if (StringUtils.isBlank(request.getMessageIdList())) {
            LOGGER.recordNormalException(new LogModel("deleteMessage").message("MessageIdList is null"));
            return mtopFailed("请求参数为空", "请求参数为空");
        }

        try {
            String messageIdList = request.getMessageIdList();
            List<String> list = Arrays.stream(messageIdList.split(",")).collect(Collectors.toList());
            JourneyPlanAiAssistantDeleteMessageRequest journeyPlanAiAssistantDeleteMessageRequest = new JourneyPlanAiAssistantDeleteMessageRequest();
            journeyPlanAiAssistantDeleteMessageRequest.setMessageIdList(list);
            journeyPlanAiAssistantDeleteMessageRequest.setUserId(String.valueOf(request.getUserId()));
            Boolean result = journeyAssistantDataOperateFacade.deleteMessage(journeyPlanAiAssistantDeleteMessageRequest);
            return mtopSuccess(result);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("deleteMessage").e(e));
            return mtopFailed(BizErrorCodeEnum.SYSTEM_ERROR);
        }
    }

    @Override
    public Result<JourneyPlanQueryPoiInfoByFloatResult> queryPoiInfoByFloat(JourneyPlanQueryPoiInfoByFloatRequest request) {
        LOGGER.recordEntry(new LogModel("queryPoiInfoByFloat")
                .request(JSONUtil.toJSONString(request)));
        if (StringUtils.isNotBlank(QUERY_POI_BY_FLOAT_MOCK_DATA)) {
            return mtopSuccess(JSONUtil.parseObject(QUERY_POI_BY_FLOAT_MOCK_DATA, JourneyPlanQueryPoiInfoByFloatResult.class));
        }
        try {
            JourneyPlanQueryPoiInfoByFloatResult result = journeyPlanAiAssistantFrt.queryPoiInfoByFloat(request);
            return mtopSuccess(result);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("queryPoiInfoByFloat").e(e));
            return mtopFailed(BizErrorCodeEnum.SYSTEM_ERROR);
        }
    }

    @Override
    public Result<Boolean> hasHistory(JourneyPlanAiChatRequest request) {
        LOGGER.recordEntry(new LogModel("hasHistory")
                .request(JSONUtil.toJSONString(request)));
        try {
            Boolean result = journeyPlanAiAssistantFrt.hasHistory(request);
            return mtopSuccess(result);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("hasHistory").e(e));
            return mtopFailed(BizErrorCodeEnum.SYSTEM_ERROR);
        }
    }

    @Override
    public Result<List<JourneyPlanAiAssistantHistoryResult.MessageInfo>> getMessageInfo(JourneyPlanAiChatRequest request) {
        LOGGER.recordEntry(new LogModel("getMessageInfo")
                .request(JSONUtil.toJSONString(request)));
        try {
            return mtopSuccess(journeyPlanAiAssistantMessageHelper.getMessageInfo(request.getMessageIds(), request.getUserId()));
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("getMessageInfo").e(e));
            return mtopFailed(BizErrorCodeEnum.SYSTEM_ERROR);
        }
    }
}
