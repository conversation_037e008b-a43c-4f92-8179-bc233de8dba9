package com.taobao.trip.jourprod.service.impl.journeyplannew;

import java.util.List;

import javax.annotation.Resource;

import com.alibaba.boot.switchcenter.annotation.SwitchGroup;

import com.google.common.collect.Lists;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch.Level;
import com.taobao.mtop.common.Result;
import com.taobao.trip.jourprod.service.facade.journeyplannew.JourneyPlanAiAssistantFeedBackFacade;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiAssistantFeedbackRequest;
import com.taobao.trip.jourprod.service.impl.BaseMtopService;
import com.taobao.trip.wireless.annotation.SsifMtop;
import org.springframework.stereotype.Component;

/**
 * @Description 问题反馈接口
 * <AUTHOR>
 * @Date 2025/3/28
 **/
@SsifMtop(serviceInterface = JourneyPlanAiAssistantFeedBackFacade.class)
@Component
@SwitchGroup
public class JourneyPlanAiAssistantFeedBackFacadeImpl extends BaseMtopService implements JourneyPlanAiAssistantFeedBackFacade {

    @AppSwitch(des = "问题收集标签列表", level = Level.p4)
    public static List<String> feedBackTagList = Lists.newArrayList("有害/不安全", "虚假信息", "没有帮助", "其他");

    @Resource
    private JourneyAssistantDataOperateService journeyAssistantDataOperateService;

    /**
     * 问题收集标签列表
     *
     * @param request
     */
    @Override
    public Result<List<String>> feedBackTagList(JourneyPlanAiAssistantFeedbackRequest request) {
        return mtopSuccess(feedBackTagList);
    }

    /**
     * 问题收集
     *
     * @param request
     */
    @Override
    public Result<Boolean> feedBack(JourneyPlanAiAssistantFeedbackRequest request) {
        Boolean result = journeyAssistantDataOperateService.feedBack(request);
        return mtopSuccess(result);
    }
}
