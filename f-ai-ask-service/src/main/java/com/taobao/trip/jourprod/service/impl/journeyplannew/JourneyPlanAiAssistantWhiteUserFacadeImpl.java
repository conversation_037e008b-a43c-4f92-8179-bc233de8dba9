package com.taobao.trip.jourprod.service.impl.journeyplannew;

import javax.annotation.Resource;

import com.taobao.mtop.common.Result;
import com.taobao.trip.facade.JourneyPlanAiAssistantWhiteUserFacade;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.JourneyPlanAiAssistantFrt;
import com.taobao.trip.jourprod.service.impl.BaseMtopService;
import com.taobao.trip.request.JourneyPlanAiAssistantWhiteUserRequest;
import com.taobao.trip.wireless.annotation.SsifMtop;
import org.springframework.stereotype.Component;

/**
 * @Description 白名单接口
 * <AUTHOR>
 * @Date 2025/3/31
 **/
@Component
@SsifMtop(serviceInterface = JourneyPlanAiAssistantWhiteUserFacade.class)
public class JourneyPlanAiAssistantWhiteUserFacadeImpl extends BaseMtopService implements JourneyPlanAiAssistantWhiteUserFacade {

    @Resource
    private JourneyPlanAiAssistantFrt journeyPlanAiAssistantFrt;

    /**
     * 是否在白名单
     *
     * @param request
     */
    @Override
    public Result<Boolean> inWhiteList(JourneyPlanAiAssistantWhiteUserRequest request) {
        Boolean result = journeyPlanAiAssistantFrt.inWhiteList(request);
        return mtopSuccess(result);
    }

    /**
     * 添加白名单
     *
     * @param request
     */
    @Override
    public Result<Boolean> addWhiteList(JourneyPlanAiAssistantWhiteUserRequest request) {
        Boolean result = journeyPlanAiAssistantFrt.addWhiteList(request);
        return mtopSuccess(result);
    }
}
