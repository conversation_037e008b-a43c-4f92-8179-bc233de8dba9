package com.taobao.trip.jourprod.service.impl.journeyplannew;

import com.alibaba.boot.switchcenter.annotation.SwitchGroup;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.taobao.mtop.common.Result;
import com.taobao.trip.facade.JourneyPlanAskRouteFacade;
import com.taobao.trip.jourprod.biz.common.annotation.MtopThrowing;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.JourneyPlanAiAssistantFrt;
import com.taobao.trip.jourprod.common.lang.enums.BizErrorCodeEnum;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.jourprod.service.converter.JourneyPlanAskRouteCovert;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiAssistantHistoryMessageRequest;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanAiAssistantHistoryResult;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanAiAssistantHistoryResult.MessageInfo;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanAiStructRouteResult;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.StreamMessageResult.StreamMessageCardModel;
import com.taobao.trip.jourprod.service.impl.BaseMtopService;
import com.taobao.trip.request.JourneyPlanAiStructRouteRequest;
import com.taobao.trip.request.JourneyPlanAiUrlCallbackRequest;
import com.taobao.trip.request.QueryFavoriteParamReq;
import com.taobao.trip.request.QueryRecentMessageRequest;
import com.taobao.trip.response.RecentMessageResponse;
import com.taobao.trip.rsp.collect.TitleCollectDTO;
import com.taobao.trip.wireless.annotation.SsifMtop;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description 智能出行规划助手
 * <AUTHOR>
 * @Date 2025/1/13
 **/
@SsifMtop(serviceInterface = JourneyPlanAskRouteFacade.class, clientTimeout = 600000, version = "1.0.0.ask")
@Service
@MtopThrowing(errorMsg = "系统异常")
@SwitchGroup
public class JourneyPlanAskRouteFacadeImpl extends BaseMtopService implements JourneyPlanAskRouteFacade {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneyPlanAskRouteFacadeImpl.class);

    @Resource
    private JourneyPlanAiAssistantFrt journeyPlanAiAssistantFrt;

    @Resource
    private JourneyAssistantDataOperateService journeyAssistantDataOperateService;



    /**
     * 根据消息id查看结构化线路
     *
     * @param request
     */
    @Override
    public Result<com.taobao.trip.rsp.JourneyPlanAiStructRouteResult> structRoute(
        JourneyPlanAiStructRouteRequest request) {
        LOGGER.recordEntry(new LogModel("structRoute")
                .request(JSONUtil.toJSONString(request)));
        com.taobao.trip.rsp.JourneyPlanAiStructRouteResult resultObject = new com.taobao.trip.rsp.JourneyPlanAiStructRouteResult();
        try {
            String messageId = request.getMessageId();
            com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiStructRouteRequest
                journeyPlanAiStructRouteRequest
                = new com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiStructRouteRequest();
            journeyPlanAiStructRouteRequest.setMessageId(messageId);
            JourneyPlanAiStructRouteResult result = journeyPlanAiAssistantFrt.structRoute(journeyPlanAiStructRouteRequest);
            resultObject = JSON.parseObject(
                JSON.toJSONString(result), com.taobao.trip.rsp.JourneyPlanAiStructRouteResult.class);
            return mtopSuccess(resultObject);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("structRoute").e(e));
            return mtopFailed(BizErrorCodeEnum.SYSTEM_ERROR);
        }finally {
            LOGGER.recordOutput(new LogModel("structRoute")
                .request(JSONUtil.toJSONString(request)).response(JSONUtil.toJSONString(resultObject)));
        }

    }

    @Override
    public Result<TitleCollectDTO> queryFavoriteParamByMessageId(QueryFavoriteParamReq paramReq) {
        String messageId = paramReq.getMessageId();
        if (StringUtils.isBlank(messageId)) {
            return mtopFailed(BizErrorCodeEnum.PARAM_ERROR);
        }
        JourneyPlanAiAssistantHistoryMessageRequest request = new JourneyPlanAiAssistantHistoryMessageRequest();
        request.setMessageIdList(Lists.newArrayList(messageId));
        request.setUserId(paramReq.getUserId());
        JourneyPlanAiAssistantHistoryResult historyByMessageIds = null;
        try {
            historyByMessageIds
                = journeyAssistantDataOperateService.getHistoryByMessageIds(request);
            List<MessageInfo> messageList = historyByMessageIds.getMessageList();
            if (CollectionUtils.isEmpty(messageList)) {
                return mtopFailed(BizErrorCodeEnum.MESSAGEID_NOT_EXIST);
            }
            List<StreamMessageCardModel> components = messageList.get(0).getComponents();
            for (StreamMessageCardModel<?> component : components) {
                if (component.getItemType().equals("journey_title_card")) {
                    return mtopSuccess(JSON.parseObject(JSONUtil.toJSONString(component.getData()), TitleCollectDTO.class));
                }
            }
            LOGGER.recordEntry(new LogModel("queryFavoriteParamByMessageId")
                .request(" not match journey_title_card messageId : " + messageId));
            return mtopSuccess(null);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("queryFavoriteParamByMessageId").e(e));
            return mtopFailed(BizErrorCodeEnum.SYSTEM_ERROR);
        } finally {
            LOGGER.recordOutput(new LogModel("queryFavoriteParamByMessageId")
                .request(JSONUtil.toJSONString(paramReq)).response(JSONUtil.toJSONString(historyByMessageIds)));
        }

    }

    /**
     * 手绘地图回流逻辑
     *
     *
     * @param request
     * @return
     */
    @Override
    public Result<Boolean> mapUrlCallBack(JourneyPlanAiUrlCallbackRequest request) {
        try {
            if (request == null || StringUtils.isAnyBlank(request.getUrl(), request.getMessageId())) {
                return mtopFailed(BizErrorCodeEnum.PARAM_ERROR);
            }
            Boolean result = journeyPlanAiAssistantFrt.mapUrlCallBack(request);
            return mtopSuccess(result);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("mapUrlCallBack").e(e));
            return mtopFailed(BizErrorCodeEnum.SYSTEM_ERROR);
        } finally {
            LOGGER.recordOutput(new LogModel("mapUrlCallBack")
                .request(JSONUtil.toJSONString(request)));
        }
    }

    @Override
    public Result<List<RecentMessageResponse>> getUserRecentMessageBySource(QueryRecentMessageRequest request) {
        try {
            if (request == null || StringUtils.isBlank(request.getUserId())) {
                return mtopFailed(BizErrorCodeEnum.PARAM_ERROR);
            }
            List<JourneyPlanAiAssistantHistoryResult.MessageInfo> result = journeyPlanAiAssistantFrt.getUserRecentMessageBySource(request);
            return mtopSuccess(JourneyPlanAskRouteCovert.covertRecentMessageInfo(result));
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("getUserRecentMessageBySource").request(JSONUtil.toJSONString(request)).e(e));
            return mtopFailed(BizErrorCodeEnum.SYSTEM_ERROR);
        } finally {
            LOGGER.recordOutput(new LogModel("getUserRecentMessageBySource")
                .request(JSONUtil.toJSONString(request)));
        }


    }
}
