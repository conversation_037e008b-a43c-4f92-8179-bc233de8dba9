package com.taobao.trip.jourprod.service.impl.journeyplannew;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.fliggy.fai.client.FaiResponse;
import com.fliggy.fai.client.fsg.response.AISearchData;
import com.fliggy.fai.client.fsg.service.FspAISearchService;
import com.taobao.trip.facade.JourneyPlanContentFacade;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.stream.NodeStreamCollector;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.stream.impl.ModelXmlOutputStreamProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.biz.frt.llm.converter.FusionSearchBaseConvert;
import com.taobao.trip.jourprod.biz.frt.llm.domain.LlmRequest;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.JourneyPlanAiChatRequest;
import com.taobao.trip.request.JourneyPlanContentParseRequest;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@HSFProvider(serviceInterface = JourneyPlanContentFacade.class, serviceVersion = "1.0.0", clientTimeout = 800)
public class JourneyPlanContentFacadeImpl implements JourneyPlanContentFacade{

    @Resource
    private FspAISearchService fspAISearchService;

    private final static FliggyNewLogger logger = LogUtil.getFliggyNewLogger(JourneyPlanContentFacadeImpl.class);


    @Override
    public String parseContent(JourneyPlanContentParseRequest request){
        logger.recordEntry(new LogModel("parseContent").request(JSON.toJSONString(request)));
        ChatContext chatContext = new ChatContext();
        try{
            LlmRequest llmRequest = new LlmRequest();
            JourneyPlanAiChatRequest journeyPlanAiChatRequest = new JourneyPlanAiChatRequest();

            journeyPlanAiChatRequest.setTtid(request.getTtid());

            chatContext.setUserMessageId(request.getMessageId());
            chatContext.setSystemMessageId(request.getMessageId() + "-SYS");
            journeyPlanAiChatRequest.setUserId(1L);
            chatContext.setRequest(journeyPlanAiChatRequest);
            llmRequest.setJourneyPlanAiChatRequest(journeyPlanAiChatRequest);

            llmRequest.setChatContext(chatContext);
            handlerAiSearchData(llmRequest);
            AiJourneyPlanSceneEnum aiJourneyPlanSceneEnum = AiJourneyPlanSceneEnum.getByCode(request.getSceneCode());
            ModelXmlOutputStreamProcessor modelXmlOutputStreamProcessor = new ModelXmlOutputStreamProcessor(journeyPlanAiChatRequest, response -> {
                NodeStreamCollector processor = new NodeStreamCollector(aiJourneyPlanSceneEnum);
                processor.collect(response, journeyPlanAiChatRequest, null, chatContext);
            });
            // 依次处理消息
            modelXmlOutputStreamProcessor.onTokenReceived(request.getXmlContent());

            // 提取要输出的消息
            List<String> messageList = chatContext.getMessageList();
            String allMessage = String.join("", messageList);
            chatContext.getMessageList().clear();

            List<CompletableFuture<Void>>  futures = chatContext.getOrCreateFutures(request.getMessageId());
            // 等待所有的 CompletableFuture 完成
            CompletableFuture<Void> allOfFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

            // 等待 allOfFuture 完成
            allOfFuture.join();

            return JSON.toJSONString(chatContext.getComponents());
        } catch (Exception e) {
            logger.recordNormalException(new LogModel("handlerAiSearchData").message("parseContent失败").e(e));
        } finally {
            Optional.ofNullable(chatContext.getInternalData()).ifPresent(t -> t.clear());
        }
        return null;


    }

    public void handlerAiSearchData(LlmRequest request) {
        try {
            FaiResponse<List<AISearchData>> faiResponse = fspAISearchService.queryAiSearchV2Result(request.getChatContext().getUserMessageId());
            logger.recordOutput(new LogModel("handlerAiSearchData").request(request.getChatContext().getUserMessageId()).response(JSON.toJSONString(faiResponse)));
            List<AISearchData> aiSearchDataList = faiResponse.getData();
            if (CollectionUtils.isEmpty(aiSearchDataList)) {
                return;
            }
            Map<String, Object> internalData = request.getChatContext().getInternalData();
            for (AISearchData aiSearchData : aiSearchDataList) {
                Map<String, Object> convertData = FusionSearchBaseConvert.convert(aiSearchData.getCategory(), aiSearchData);
                if (MapUtils.isEmpty(convertData)) {
                    continue;
                }
                internalData.putAll(convertData);
            }
        } catch (Throwable throwable) {
            logger.recordNormalException(new LogModel("handlerAiSearchData").message("处理数据上下文失败").e(throwable));
        }

    }



}
