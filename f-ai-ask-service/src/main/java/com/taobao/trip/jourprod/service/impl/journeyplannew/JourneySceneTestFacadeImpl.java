package com.taobao.trip.jourprod.service.impl.journeyplannew;

import com.alibaba.boot.switchcenter.annotation.SwitchGroup;
import com.alibaba.dashscope.app.ApplicationParam;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.taobao.eagleeye.EagleEye;
import com.taobao.mtop.common.Result;
import com.taobao.trip.facade.JourneySceneTestFacade;
import com.taobao.trip.jourprod.biz.common.annotation.MtopThrowing;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.ChitChatHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantHelper;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiJourneySceneModel;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch;
import com.taobao.trip.jourprod.intent.IntentRecognitionService;
import com.taobao.trip.jourprod.intent.domain.IntentRecognitionRequest;
import com.taobao.trip.jourprod.intent.domain.IntentRecognitionResponse;
import com.taobao.trip.jourprod.intent.enums.IntentProviderTypeEnum;
import com.taobao.trip.request.JourneySceneTestRequest;
import com.taobao.trip.response.JourneySceneTestResponse;
import com.taobao.trip.wireless.annotation.SsifMtop;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.UUID;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantHelper.AI_JOURNEY_SCENE_ANALYSIS_APP_ID_V2;
import static com.taobao.trip.jourprod.biz.frt.journeyplannew.helper.JourneyPlanAiAssistantHelper.AI_JOURNEY_SCENE_NAME_2_CODE_MAP;

/**
 * <AUTHOR>
 * @Date: 2025/4/12 20:23
 */
@SsifMtop(serviceInterface = JourneySceneTestFacade.class, clientTimeout = 600000, version = "1.0.0.ask")
@Service
@MtopThrowing(errorMsg = "系统异常")
@SwitchGroup
public class JourneySceneTestFacadeImpl implements JourneySceneTestFacade {
    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneySceneTestFacade.class);

    @Resource
    JourneyPlanAiAssistantHelper journeyPlanAiAssistantHelper;
    @Resource
    ChitChatHelper chitChatHelper;
    @Resource
    private IntentRecognitionService  intentRecognitionService;
    @Override
    public Result<JourneySceneTestResponse> testScene(JourneySceneTestRequest request) {
        Result<JourneySceneTestResponse> rrrrr = new Result<JourneySceneTestResponse>();
        JourneySceneTestResponse response = new JourneySceneTestResponse();

        ChatContext chatContext = new ChatContext();
        chatContext.setSessionId(UUID.randomUUID().toString());
        String appId = StringUtils.isBlank(request.getAppId()) ? AI_JOURNEY_SCENE_ANALYSIS_APP_ID_V2 : request.getAppId();

        try {
            String text = null;
            long startTime = System.currentTimeMillis();
            if (request.getFlowType() == JourneySceneTestRequest.FlowType.FAI) {
                IntentRecognitionRequest intentRequest = new IntentRecognitionRequest();
                intentRequest.setQuery(request.getUserInput());
                intentRequest.setHistoryMessageList(Lists.newArrayList());
                intentRequest.setProviderType(IntentProviderTypeEnum.FAI);
                IntentRecognitionResponse recognizeRes = intentRecognitionService.recognize(intentRequest);
                text = recognizeRes.getResult();
            } else {
                text = journeyPlanAiAssistantHelper.syncCallAgent(request.getUid(), request.getUserInput(), appId, chatContext);
            }
            long endTime = System.currentTimeMillis();

            AiJourneySceneModel result = JSON.parseObject(text, AiJourneySceneModel.class);
            if (result == null) {
                rrrrr.setMsgInfo("意图识别返回结果为空，可能有报错");
                rrrrr.setSuccess(false);
            } else {
                String type = result.getType();
                response.setUid(request.getUid());
                response.setJourneyScene(type);
                response.setUserInput(request.getUserInput());
                response.setEagleEyeTraceId(EagleEye.getTraceId());
                response.setSummaryQuery(result.getSummaryQuery());

                String sceneCode = AI_JOURNEY_SCENE_NAME_2_CODE_MAP.get(type);
                if (StringUtils.isBlank(sceneCode)) {
                    response.setJourneyAnswer(AiJourneySwitch.UNKNOWN_SCENE_CONTENT);
                    ApplicationParam param = chitChatHelper.buildParam(AiJourneySwitch.OTHER_SCENE_CHIT_CHAT_APP_ID, request.getUserInput(), chatContext.getSessionId(), type);
                    String answer = chitChatHelper.getAnswer(request.getUid(), param);
                    response.setJourneyAnswer(answer);
                }
            }

            // 计算执行耗时并设置到响应中
            long executionTime = endTime - startTime;
            response.setExecutionTime(executionTime);

            rrrrr.setModel(response);
            return rrrrr;
        } catch (Exception ex) {
            LOGGER.recordNormalException(new LogModel("testScene")
                    .success(false)
                    .message("评测接口调用异常")
                    .request(JSON.toJSONString(request))
                    .e(ex));
            rrrrr.setMsgInfo(ex.getMessage());
            rrrrr.setSuccess(false);
            return rrrrr;
        }
    }
}
