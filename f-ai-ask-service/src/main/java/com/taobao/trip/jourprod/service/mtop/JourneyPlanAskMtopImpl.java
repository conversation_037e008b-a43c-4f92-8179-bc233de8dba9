package com.taobao.trip.jourprod.service.mtop;

import com.alibaba.boot.switchcenter.annotation.SwitchGroup;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.mtop.common.Result;
import com.taobao.trip.common.AskType;
import com.taobao.trip.jourprod.biz.common.AskHistoryCommon;
import com.taobao.trip.jourprod.biz.common.annotation.MtopThrowing;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.AiPageStaticInfoDTO;
import com.taobao.trip.jourprod.common.lang.enums.BizErrorCodeEnum;
import com.taobao.trip.jourprod.utils.JSONUtil;
import com.taobao.trip.jourprod.common.sal.tair.impl.MdbTairCommonHelper;
import com.taobao.trip.jourprod.dto.SugDTO;
import com.taobao.trip.jourprod.mainsearch.MainSearchService;
import com.taobao.trip.jourprod.service.facade.model.response.journeyplannew.aiassistant.JourneyPlanAiAssistantHistoryResult.MessageInfo;
import com.taobao.trip.jourprod.service.impl.BaseMtopService;
import com.taobao.trip.jourprod.service.impl.journeyplannew.JourneyAssistantDataOperateService;
import com.taobao.trip.jourprod.static_source.StaticResourceDataHelper;
import com.taobao.trip.mtop.JourneyPlanAskMtop;
import com.taobao.trip.request.AiPageStaticInfoRequest;
import com.taobao.trip.request.QueryRecentSourceRequest;
import com.taobao.trip.response.AiPageStaticInfoResponse;
import com.taobao.trip.response.RecentSourceResponse;
import com.taobao.trip.response.sug.InputSugReq;
import com.taobao.trip.response.sug.InputSugResponse;
import com.taobao.trip.response.sug.SugResponse;
import com.taobao.trip.wireless.annotation.SsifMtop;
import fliggy.content.model.FliggyNewLogger;
import fliggy.content.model.LogModel;
import fliggy.content.utils.LogUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.taobao.trip.jourprod.biz.frt.journeyplannew.switcher.AiJourneySwitch.PAGE_STATIC_RESOURCE_CONFIG_ID;

/**
 * @Description 智能出行规划助手
 * <AUTHOR>
 * @Date 2025/1/13
 **/
@SsifMtop(serviceInterface = JourneyPlanAskMtop.class, clientTimeout = 4000, version = "1.0.0.ask")
@Service
@MtopThrowing(errorMsg = "系统异常")
@SwitchGroup
public class JourneyPlanAskMtopImpl extends BaseMtopService implements JourneyPlanAskMtop {

    private final static FliggyNewLogger LOGGER = LogUtil.getFliggyNewLogger(JourneyPlanAskMtopImpl.class);

    @Resource
    private MdbTairCommonHelper mdbTairCommonHelper;

    @Resource
    private JourneyAssistantDataOperateService journeyAssistantDataOperateService;

    @Resource
    private MainSearchService mainSearchService;
    @Resource
    private StaticResourceDataHelper staticResourceDataHelper;

    @Override
    public Result<RecentSourceResponse> getRecentMessageSource(QueryRecentSourceRequest request) {

        Result<RecentSourceResponse> recentSourceResponseResult = new Result<>();
        try {
            RecentSourceResponse recentSourceResponse = new RecentSourceResponse();
            if (request == null || StringUtils.isAnyBlank(request.getUserId())) {
                return mtopFailed(BizErrorCodeEnum.PARAM_ERROR);
            }
            //查询如果key不存在， 查history，最近一条的类型是什么。如果key存在，看对应的value类型是什么。
            Object value = mdbTairCommonHelper.getValue(AskHistoryCommon.getLastAskSourceRedisKey(request.getUserId()));
            if (Objects.nonNull(value)) {
                recentSourceResponse.setSource(value.toString());
                return mtopSuccess(recentSourceResponse);
            }
            //若最近30分钟未访问，需要查询数据库
            MessageInfo lastMessage = journeyAssistantDataOperateService.getLastOneUserRoleMessageIgnoreSource(
                request.getUserId());
            if (Objects.isNull(lastMessage)) {
                //默认返回搜索， 会跳转到搜索
                recentSourceResponse.setSource(AskType.SEARCH_ASK.getCode());
                return mtopSuccess(recentSourceResponse);
            }
            recentSourceResponse.setSource(lastMessage.getSource());
            return mtopSuccess(recentSourceResponse);
        } catch (Exception e) {
            LOGGER.recordDangerException(new LogModel("getRecentMessageSource").request(
                JSONUtil.toJSONString(request)).e(e));
            return mtopFailed(BizErrorCodeEnum.SYSTEM_ERROR);
        } finally {
            LOGGER.recordOutput(new LogModel("getRecentMessageSource")
                .request(JSONUtil.toJSONString(request)).response(JSONUtil.toJSONString(recentSourceResponseResult)));
        }
    }

    @Override
    public Result<InputSugResponse> getInputSug(InputSugReq request) {
        List<SugDTO> inputSug = mainSearchService.getInputSug(request);
        return mtopSuccess(convertRsp(inputSug));
    }

    private InputSugResponse convertRsp(List<SugDTO> inputSug) {
        InputSugResponse inputSugResponse = new InputSugResponse();
        if(CollectionUtils.isEmpty(inputSug)) {
            return inputSugResponse;
        }
        List<SugResponse> sourceList = new ArrayList<>();
        for (SugDTO sugDTO : inputSug) {
            SugResponse sugResponse = new SugResponse();
            sugResponse.setSuggestName(sugDTO.getSuggestName());
            sugResponse.setJumpUrl(sugDTO.getJumpUrl());
            sourceList.add(sugResponse);
        }
        inputSugResponse.setSuggestVOList(sourceList);
        return inputSugResponse;
    }

    @Override
    public Result<AiPageStaticInfoResponse> getPageStaticInfo(AiPageStaticInfoRequest request) {
        AiPageStaticInfoDTO staticInfoDTO = staticResourceDataHelper.getConfig(PAGE_STATIC_RESOURCE_CONFIG_ID, AiPageStaticInfoDTO.class);
        AiPageStaticInfoResponse response = new AiPageStaticInfoResponse();
        if (staticInfoDTO != null) {
            response.setLogoUrl(staticInfoDTO.getLogo());
        }
        return mtopSuccess(response);
    }

    @AteyeInvoker(description = "测试查询最近一次source", paraDesc = "userId")
    public String test(String userId) {
        QueryRecentSourceRequest queryRecentSourceRequest = new QueryRecentSourceRequest();
        queryRecentSourceRequest.setUserId(userId);
        Result<RecentSourceResponse> recentMessageSource = getRecentMessageSource(queryRecentSourceRequest);
        return JSONUtil.toJSONString(recentMessageSource);
    }
}
