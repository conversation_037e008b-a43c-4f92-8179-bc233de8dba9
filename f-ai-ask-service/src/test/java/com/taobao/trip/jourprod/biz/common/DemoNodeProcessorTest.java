package com.taobao.trip.jourprod.biz.common;

import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.NodeProcessorFactory;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.node.impl.DemoNodeProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.stream.NodeStreamCollector;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.chat.stream.impl.ModelXmlOutputStreamProcessor;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.service.facade.enums.journeyplan.AiJourneyMessageComponentTypeEnum;
import com.taobao.trip.jourprod.service.facade.model.request.journeyplannew.aiassistant.ComponentDataResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2025/3/16
 */
public class DemoNodeProcessorTest {
    @BeforeEach
    void setUp() {
        NodeProcessorFactory.register(AiJourneyPlanSceneEnum.TEST, "TEST", new DemoNodeProcessor());
        NodeProcessorFactory.register(AiJourneyPlanSceneEnum.TEST, "description", new DemoNodeProcessor());
        NodeProcessorFactory.register(AiJourneyPlanSceneEnum.TEST, "card", new DemoNodeProcessor());
        NodeProcessorFactory.register(AiJourneyPlanSceneEnum.TEST, "container", new DemoNodeProcessor());
        NodeProcessorFactory.register(AiJourneyPlanSceneEnum.TEST, "table", new DemoNodeProcessor());
    }

    @Test
    public void runDemo() {

        //final ChatContext chatContext = new ChatContext();
        //
        //ModelXmlOutputStreamProcessor jsonStreamProcessor = new ModelXmlOutputStreamProcessor((cardType, data) -> {
        //    //NodeStreamCollector processor = new NodeStreamCollector(chatContext,AiJourneyPlanSceneEnum.TEST);
        //    //processor.collect(cardType, data);
        //});


        String[] tokens = {
                "前置测试文本{",
                "  \"type\": \"description\",",
                "  \"content\": \"这次北京3日游将带你深度体验古都的历史文化与现代都市的活力。从故宫的皇家气派到长城的雄伟壮丽，再到胡同里的烟火气息，每一天都充满惊喜与探索。",
                " 222\"},中间测试文本",
                "{",
                "  \"type\": \"overview\",",
                "  \"content\": {",
                "    \"travelDays\": 3,",
                "    \"days\": [",
                "      {",
                "        \"dayNumber\": 1,",
                "        \"startToEnd\": \"北京首都国际机场-天安门广场\",",
                "        \"detailPoints\": [\"天安门广场\", \"故宫\", \"景山公园\"]",
                "      },",
                "      {",
                "        \"dayNumber\": 2,",
                "        \"startToEnd\": \"酒店-八达岭长城\",",
                "        \"detailPoints\": [\"八达岭长城\", \"明十三陵\", \"南锣鼓巷\"]",
                "      },",
                "      {",
                "        \"dayNumber\": 3,",
                "        \"startToEnd\": \"酒店-颐和园\",",
                "        \"detailPoints\": [\"颐和园\", \"798艺术区\", \"鸟巢\"]",
                "      }",
                "    ]",
                "  }",
                "}后置测试文本"

        };

        //tokens 调用下面的processor
        for (String t : tokens) {
            // jsonStreamProcessor.(t);
        }
    }
    @Test
    public void runDemoForJiuhui() {

        final ChatContext chatContext = new ChatContext();

        ModelXmlOutputStreamProcessor modelXmlOutputStreamProcessor = new ModelXmlOutputStreamProcessor(null, response -> {
            NodeStreamCollector processor = new NodeStreamCollector(AiJourneyPlanSceneEnum.TEST);
            if (response.isComplete()) {
                System.out.println("触发回调: " + response.getComponentType() + " ID: " + response.getId());
            }
            processor.collect(response, null, null, chatContext);
        });

        char[] tokens = getMockOutput().toCharArray();

        //tokens 调用下面的processor
        for (char t : tokens) {
            modelXmlOutputStreamProcessor.onTokenReceived(String.valueOf(t));
        }
    }
    @Test
    public void runDemoForJiuhui2() {

        final ChatContext chatContext = new ChatContext();

        ModelXmlOutputStreamProcessor modelXmlOutputStreamProcessor = new ModelXmlOutputStreamProcessor(null, response -> {
            NodeStreamCollector processor = new NodeStreamCollector(AiJourneyPlanSceneEnum.TEST);
            processor.collect(response, null, null, chatContext);
        });

        char[] tokens = getMockOutput2().toCharArray();

        //tokens 调用下面的processor
        for (char t : tokens) {
            modelXmlOutputStreamProcessor.onTokenReceived(String.valueOf(t));
        }
    }

    private static String getMockOutput() {
        String output = "# 方案一：住故宫/市中心区域\\n\n" +
                "**优势分析**：适合深度游览故宫、天安门、国家博物馆等景点，且餐饮购物丰富，推荐选择王府井、前门门附近的酒店。\\n\n" +
                "<card&0><card&1>≌type:overview≌</card&1>\\n\n" +
                "<table&2># 徐汇石库门的独栋别墅，私汤庭院配法式银器早餐，沉浸体验法式风情\\n\\n\n" +
                "|------------|----------------------------------------------------------------------|\\n\n" +
                "| 用户评价   | 240条点评提到“管家响应快，细节贴心”                                  |\\n\n" +
                "| 特色体验   | 米其林二星餐厅「紫膳」、屋顶露台酒吧                                |\\n\n" +
                "| 位置       | 位于东城区东长安街1号（地铁1号线王府井站B口直达）                    |\\n\n" +
                "</table&2>\\n\n" +
                "<card&3>≌type:1≌</card&3>\\n\n" +
                "</card&0>\\n\n" +
                "\n" +
                "# 方案二：住环球影城区域\\n\n" +
                "**优势分析**：适合深度游玩环球影城，选择园区内酒店或地铁7号线沿线酒店，可享受提前入园特权（节省1-2小时排队时间）\\n\n" +
                "<card&4>\\n\n" +
                "<card&5>{\"shid\":\"456\"}</card&5>\\n\n" +
                "<table&6>\\n\\n\n" +
                "# 住进好莱坞片场主题房，睁眼零距离穿越魔法世界\\n\n" +
                "|------------|----------------------------------------------------------------------|\\n\n" +
                "| 用户评价   | 39条评价认为该酒店\"房间隔音效果好\"                                   |\\n\n" +
                "| 特色体验   | 唯一可提前1小时入园的酒店！可至少多玩一个热门项目！                  |\\n\n" +
                "| 位置       | 位于北京市通州区北京环球度假区                                      |\\n\n" +
                "</table&6><card&7>{\"shid\":\"123\"}</card&7></card&4>\\n\n" +
                "\\n\n" +
                "# 方案三：住折中区域\\n\n" +
                "**优势分析**：可平衡故宫和环球影城的行程，推荐住在国贸/大望路附近，到故宫约20分钟，到环球影城约50分钟。\\n\n" +
                "<card&8><card&9>{\"shid\":\"789\"}</card&9><table&10>\\n\\n\n" +
                "# 住进好莱坞片场主题房，睁眼零距离穿越魔法世界\\n\n" +
                "|------------|----------------------------------------------------------------------|\\n\n" +
                "| 用户评价   | 39条评价认为该酒店\"房间隔音效果好\"                                   |\\n\n" +
                "| 特色体验   | 唯一可提前1小时入园的酒店！可至少多玩一个热门项目！                  |\\n\n" +
                "| 位置       | 位于北京市通州区北京环球度假区                                      |\\n\n" +
                "</table&10>\\n\n" +
                "<card&11>{\"shid\":\"789\"}</card&11>\\n\n" +
                "</card&8>\\n\n" +
                "\\n\n" +
                "# 方案对比\\n\n" +
                "\\n\\n\n" +
                "| 维度       | 住市中心               | 住环影                | 住折中区域            |\\n\n" +
                "|------------|-----------------------|----------------------|---------------------|\\n\n" +
                "| 酒店价格   | ¥4000起/晚            | ¥1500起/晚           | ¥2000起/晚          |\\n\n" +
                "| 适合行程   | 主市区                | 主环球影城           | 两者均衡            |\\n\n" +
                "| 核心优势   | 餐饮丰富<br>交通便利  | 乐园直达<br>亲子畅玩  | 商旅枢纽<br>双景兼顾 |\\n\n" +
                "| 推荐指数   | ⭐\uFE0F⭐\uFE0F⭐\uFE0F⭐\uFE0F                | ⭐\uFE0F⭐\uFE0F⭐\uFE0F⭐\uFE0F               | ⭐\uFE0F⭐\uFE0F⭐\uFE0F⭐\uFE0F⭐\uFE0F             |\\n\n" +
                "\n" +
                "\n" +
                "\n";
        return output;
    }

    private static String getMockOutput2() {
        //String output = "<container&0>≌type:1≌<card&1>≌type:overview≌</card&1>xxx</container&0>";
        String output = "<container&0><card&1></card&1></container&0>";
        return output;
    }
}
