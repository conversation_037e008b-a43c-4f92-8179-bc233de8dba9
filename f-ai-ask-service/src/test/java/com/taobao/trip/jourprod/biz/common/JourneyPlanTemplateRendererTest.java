package com.taobao.trip.jourprod.biz.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.enums.AiJourneyPlanSceneEnum;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.model.ChatContext;
import com.taobao.trip.jourprod.biz.frt.journeyplannew.template.JourneyPlanTemplateRenderer;
import freemarker.template.Configuration;
import freemarker.template.TemplateExceptionHandler;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

/**
 * @Description 行程规划模板渲染器测试类
 * <AUTHOR>
 * @Date 2025/1/30
 **/
@RunWith(MockitoJUnitRunner.class)
public class JourneyPlanTemplateRendererTest {
    
    //@InjectMocks
    //private JourneyPlanTemplateRenderer templateRenderer;
    //
    //@Mock
    //private Configuration freemarkerConfig;
    //
    //private JSONObject testJsonData;
    //private ChatContext testChatContext;
    //
    //@Before
    //public void setUp() throws IOException {
    //    // 设置Freemarker配置
    //    Configuration config = new Configuration(Configuration.VERSION_2_3_31);
    //    config.setClassForTemplateLoading(this.getClass(), "/templates/");
    //    config.setDefaultEncoding("UTF-8");
    //    config.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
    //    config.setLogTemplateExceptions(false);
    //    config.setWrapUncheckedExceptions(true);
    //
    //    when(freemarkerConfig.getTemplate("journey_plan_main.ftl")).thenReturn(
    //        config.getTemplate("journey_plan_main.ftl"));
    //    when(freemarkerConfig.getTemplate("daily_plan_template.ftl")).thenReturn(
    //        config.getTemplate("daily_plan_template.ftl"));
    //    when(freemarkerConfig.getTemplate("card_template.ftl")).thenReturn(
    //        config.getTemplate("card_template.ftl"));
    //
    //    // 加载测试数据
    //    loadTestData();
    //
    //    // 设置测试上下文
    //    testChatContext = new ChatContext();
    //    testChatContext.setSceneEnum(AiJourneyPlanSceneEnum.JOURNEY_PLAN);
    //    testChatContext.setSessionId("test-session-123");
    //    testChatContext.setUserMessageId("test-user-msg-123");
    //    testChatContext.setSystemMessageId("test-system-msg-123");
    //}
    //
    //private void loadTestData() throws IOException {
    //    // 从文件加载测试JSON数据
    //    ClassPathResource resource = new ClassPathResource("docs/complete-journey-plan-example.json");
    //    String jsonContent = new String(Files.readAllBytes(Paths.get(resource.getURI())));
    //    testJsonData = JSON.parseObject(jsonContent);
    //}
    //
    //@Test
    //public void testRenderJourneyPlan() {
    //    // 测试完整的行程规划渲染
    //    String result = templateRenderer.renderJourneyPlan(testJsonData, testChatContext);
    //
    //    assertNotNull("渲染结果不应为空", result);
    //    assertTrue("结果应包含行程描述", result.contains("马来西亚五日海岛休闲之旅"));
    //    assertTrue("结果应包含标题卡片", result.contains("f_card&1"));
    //    assertTrue("结果应包含地图卡片", result.contains("f_card&2"));
    //    assertTrue("结果应包含每日行程", result.contains("Day 1 嘉兴 - 吉隆坡"));
    //    assertTrue("结果应包含timeline标签", result.contains("f_timeline"));
    //    assertTrue("结果应包含POI卡片", result.contains("poi_link_card"));
    //    assertTrue("结果应包含酒店卡片", result.contains("hotel_link_card"));
    //
    //    System.out.println("渲染结果长度: " + result.length());
    //    System.out.println("渲染结果预览: " + result.substring(0, Math.min(500, result.length())));
    //}
    //
    //@Test
    //public void testValidateAndProcessIds() {
    //    // 测试ID验证功能
    //    try {
    //        templateRenderer.renderJourneyPlan(testJsonData, testChatContext);
    //        // 如果没有抛出异常，说明ID验证通过
    //    } catch (IllegalArgumentException e) {
    //        fail("ID验证失败: " + e.getMessage());
    //    }
    //}
    //
    //@Test
    //public void testMultipleCardsInActivity() {
    //    // 测试单个activity包含多个card的情况
    //    String result = templateRenderer.renderJourneyPlan(testJsonData, testChatContext);
    //
    //    // 检查Day 2的下午活动，应该包含两个卡片
    //    assertTrue("应包含热浪岛海滩卡片", result.contains("热浪岛海滩"));
    //    assertTrue("应包含路线规划卡片", result.contains("router_plan_card"));
    //
    //    // 检查Day 3的上午活动，也应该包含两个卡片
    //    assertTrue("应包含海洋生态公园卡片", result.contains("热浪岛海洋生态公园"));
    //    assertTrue("应包含另一个路线规划卡片", result.contains("103.006501,5.785744"));
    //}
    //
    //@Test
    //public void testCardWithoutItemType() {
    //    // 测试没有itemType的card（纯文本描述）
    //    String result = templateRenderer.renderJourneyPlan(testJsonData, testChatContext);
    //
    //    // 检查去程活动，应该只有描述文本，没有XML标签
    //    assertTrue("应包含去程描述", result.contains("建议乘坐飞机前往吉隆坡"));
    //
    //    // 验证该描述不会生成f_card标签
    //    String[] lines = result.split("\n");
    //    boolean foundDescription = false;
    //    boolean foundCardAfterDescription = false;
    //
    //    for (int i = 0; i < lines.length; i++) {
    //        if (lines[i].contains("建议乘坐飞机前往吉隆坡")) {
    //            foundDescription = true;
    //            // 检查前后几行是否有f_card标签
    //            for (int j = Math.max(0, i-2); j < Math.min(lines.length, i+3); j++) {
    //                if (lines[j].contains("f_card&6")) {
    //                    foundCardAfterDescription = true;
    //                    break;
    //                }
    //            }
    //            break;
    //        }
    //    }
    //
    //    assertTrue("应找到去程描述", foundDescription);
    //    assertFalse("纯文本描述不应生成f_card标签", foundCardAfterDescription);
    //}
    //
    //@Test
    //public void testIdContinuity() {
    //    // 测试ID连续性
    //    String result = templateRenderer.renderJourneyPlan(testJsonData, testChatContext);
    //
    //    // 验证ID从1开始连续
    //    for (int i = 1; i <= 32; i++) {
    //        assertTrue("应包含ID " + i,
    //            result.contains("&" + i + ">") || result.contains("&" + i + " "));
    //    }
    //}
    //
    //@Test(expected = IllegalArgumentException.class)
    //public void testInvalidIdSequence() {
    //    // 测试无效的ID序列
    //    JSONObject invalidData = JSON.parseObject(testJsonData.toJSONString());
    //
    //    // 修改一个ID，破坏连续性
    //    invalidData.getJSONObject("journeyIntro").getJSONObject("titleCard").put("id", 99);
    //
    //    templateRenderer.renderJourneyPlan(invalidData, testChatContext);
    //}
    //
    //@Test
    //public void testEmptyCardsArray() {
    //    // 测试空的cards数组
    //    JSONObject testData = JSON.parseObject(testJsonData.toJSONString());
    //
    //    // 清空第一个activity的cards
    //    testData.getJSONArray("dailyPlans")
    //        .getJSONObject(0)
    //        .getJSONArray("activities")
    //        .getJSONObject(0)
    //        .put("cards", JSON.parseArray("[]"));
    //
    //    String result = templateRenderer.renderJourneyPlan(testData, testChatContext);
    //
    //    assertNotNull("即使cards为空，渲染也应成功", result);
    //    assertTrue("应包含timeline标签", result.contains("f_timeline&5"));
    //}
    //
    //@Test
    //public void testRenderModule() {
    //    // 测试单独渲染模块
    //    JSONObject introData = testJsonData.getJSONObject("journeyIntro");
    //
    //    String result = templateRenderer.renderModule("intro_section.ftl", introData);
    //
    //    assertNotNull("模块渲染结果不应为空", result);
    //    // 注意：这个测试需要intro_section.ftl模板文件存在
    //}
}
