package com.taobao.trip.jourprod.biz.common;

import static org.junit.jupiter.api.Assertions.assertEquals;

class JsonStreamProcessorTest {

    //private List<String> receivedMessages;
    //private ModelOutputStreamProcessor processor;
    //
    //@BeforeEach
    //void setUp() {
    //    receivedMessages = new ArrayList<>();
    //    processor = new ModelOutputStreamProcessor((cardType, data) -> receivedMessages.add(jsonObj.toJSONString()), s -> {
    //        System.out.println("非JSON内容: " + s);
    //    });
    //}
    //
    //@Test
    //void shouldProcessSingleJsonObject() throws Exception {
    //    processor.onTokenReceived("json```{ \"key\": \"value\" }ddd");
    //
    //    assertEquals(1, receivedMessages.size());
    //    assertEquals("{\"key\":\"value\"}", receivedMessages.get(0));
    //}
    //
    //@Test
    //void shouldProcessMultipleJsonObjects() throws Exception {
    //    processor.onTokenReceived("{ \"key1\": \"value1\" }{ \"key2\": \"value2\" }");
    //
    //    assertEquals("[{\"key1\":\"value1\"}, {\"key2\":\"value2\"}]", receivedMessages.toString());
    //}
    //
    //@Test
    //void shouldIgnoreInvalidJsonTokens() throws Exception {
    //    processor.onTokenReceived("{ \"invalid\": \"json\" ");
    //
    //    assertEquals(0, receivedMessages.size());
    //}
    //
    //@Test
    //void shouldHandleNestedJsonObjects() throws Exception {
    //    processor.onTokenReceived("{ \"nested\": { \"key\": \"value\" } }");
    //
    //    assertEquals(1, receivedMessages.size());
    //    assertEquals("{\"nested\":{\"key\":\"value\"}}", receivedMessages.get(0));
    //}

    // 更多测试...
}