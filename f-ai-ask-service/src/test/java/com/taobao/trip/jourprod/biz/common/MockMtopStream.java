package com.taobao.trip.jourprod.biz.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.mtop3.invocation.MtopStream;

/**
 * <AUTHOR>
 * @date 2025/3/16
 */
public class MockMtopStream implements MtopStream {
    @Override
    public MtopStream write(Object var1) {
        return null;
    }
    @Override
    public void writeAndEnd(Object var1) {
        System.out.println(JsonUtil.toJSONString(var1));
    }
    @Override
    public void end() {
        System.out.println("=====end=======");
    }
    @Override
    public void endExceptionally(Throwable e) {
        e.printStackTrace();
        System.out.println(e.toString());
    }
}
