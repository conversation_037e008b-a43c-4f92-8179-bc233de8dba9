package com.taobao.trip.jourprod.biz.frt.journeyplannew.template.config;

import com.taobao.trip.jourprod.biz.frt.journeyplannew.template.FreemarkerConfiguration;
import freemarker.template.Configuration;
import freemarker.template.Template;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * @Description Freemarker配置测试
 * <AUTHOR>
 * @Date 2025/1/30
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = FreemarkerConfiguration.class)
public class FreemarkerConfigurationTest {
    
    @Resource
    private Configuration freemarkerConfig;
    
    @Test
    public void testFreemarkerConfigNotNull() {
        assertNotNull("Freemarker配置不应为空", freemarkerConfig);
    }
    
    @Test
    public void testFreemarkerConfigProperties() {
        assertEquals("编码应为UTF-8", "UTF-8", freemarkerConfig.getDefaultEncoding());
        assertEquals("版本应为2.3.31", Configuration.VERSION_2_3_31, freemarkerConfig.getIncompatibleImprovements());
    }
    
    @Test
    public void testTemplateProcessing() throws Exception {
        // 创建一个简单的模板内容
        String templateContent = "Hello ${name}! Today is ${date}.";
        Template template = new Template("test", templateContent, freemarkerConfig);
        
        // 准备数据
        Map<String, Object> data = new HashMap<>();
        data.put("name", "World");
        data.put("date", "2025-01-30");
        
        // 渲染模板
        StringWriter writer = new StringWriter();
        template.process(data, writer);
        
        String result = writer.toString();
        assertEquals("模板渲染结果应正确", "Hello World! Today is 2025-01-30.", result);
    }
    
    @Test
    public void testTemplateWithComplexData() throws Exception {
        // 测试复杂数据结构的模板渲染
        String templateContent = "<#list items as item>${item.name}: ${item.value}<#if item_has_next>, </#if></#list>";
        Template template = new Template("complex", templateContent, freemarkerConfig);
        
        // 准备复杂数据
        Map<String, Object> data = new HashMap<>();
        java.util.List<Map<String, Object>> items = new java.util.ArrayList<>();
        
        Map<String, Object> item1 = new HashMap<>();
        item1.put("name", "item1");
        item1.put("value", "value1");
        items.add(item1);
        
        Map<String, Object> item2 = new HashMap<>();
        item2.put("name", "item2");
        item2.put("value", "value2");
        items.add(item2);
        
        data.put("items", items);
        
        // 渲染模板
        StringWriter writer = new StringWriter();
        template.process(data, writer);
        
        String result = writer.toString();
        assertEquals("复杂模板渲染结果应正确", "item1: value1, item2: value2", result);
    }
}
