<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.taobao.trip.jourprod</groupId>
		<artifactId>f-ai-ask</artifactId>
		<version>1.0.0</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>f-ai-ask-start</artifactId>
	<packaging>jar</packaging>
	<name>f-ai-ask-start</name>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-actuator-autoconfigure</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.boot</groupId>
			<artifactId>pandora-sentinel-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.boot</groupId>
			<artifactId>pandora-tddl-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.pandora</groupId>
			<artifactId>pandora-boot-bootstrap</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.pandora</groupId>
			<artifactId>taobao-hsf.sar</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.trip.jourprod</groupId>
			<artifactId>f-ai-ask-service</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.trip.jourprod</groupId>
			<artifactId>f-ai-ask-dal</artifactId>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jcl-over-slf4j</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.wireless.mtop</groupId>
			<artifactId>mtop-pandora-export-agent</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.taobao.pandora</groupId>
			<artifactId>pandora-boot-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.taobao.travel</groupId>
			<artifactId>travelvc-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.taobao.tripwf</groupId>
			<artifactId>tripwf-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.taobao.tripmdd</groupId>
			<artifactId>tripmdd-facade</artifactId>
		</dependency>

		<dependency>
			<groupId>com.taobao.trip.wireless</groupId>
			<artifactId>ssif-spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>com.alibaba.tripc.logicengine</groupId>
			<artifactId>logicengine-facade</artifactId>
		</dependency>

	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>com.taobao.pandora</groupId>
				<artifactId>pandora-boot-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>pandora-boot-maven-plugin</id>
						<phase>package</phase>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>maven-antrun-plugin</id>
						<phase>package</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<tasks>
								<unzip src="${project.build.directory}/${project.build.finalName}.${project.packaging}"  dest="${project.build.directory}/f-ai-ask"/>
							</tasks>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
