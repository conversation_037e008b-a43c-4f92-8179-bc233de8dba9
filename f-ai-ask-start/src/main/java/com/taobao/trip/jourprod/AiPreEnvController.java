package com.taobao.trip.jourprod;

import java.util.Map;

import com.alibaba.dashscope.app.Application;
import com.alibaba.dashscope.app.ApplicationParam;
import com.alibaba.dashscope.app.ApplicationResult;

import com.google.common.collect.Maps;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/4/12
 */
@RestController
@RequestMapping("/pre/ai")
public class AiPreEnvController {

    @PostMapping("/syncCallAgent")
    public Map<String, String> syncCallAgent(@RequestBody Map<String, String> param) throws Exception {
        // 请求大模型
        Application application = new Application();
        ApplicationParam applicationParam = ApplicationParam.builder()
            .apiKey(param.get("apiKey"))
            .appId(param.get("appId"))
            .prompt(param.get("prompt"))
            .sessionId(param.get("sessionId"))
            .build();
        ApplicationResult call = application.call(applicationParam);
        Map<String, String> mapResult = Maps.newHashMap();
        mapResult.put("text", call.getOutput().getText());
        mapResult.put("sessionId", call.getOutput().getSessionId());
        mapResult.put("requestId", call.getRequestId());
        return mapResult;
    }
}
