package com.taobao.trip.jourprod;

import com.taobao.ateye.servlet.AteyeServlet;
import com.taobao.pandora.boot.PandoraBootstrap;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.ImportResource;

/**
 * Pandora Boot应用的入口类
 * <p>
 * 其中导入sentinel-tracer.xml是加sentinel限流，详情见
 * http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-sentinel
 * <p>
 */
@SpringBootApplication(scanBasePackages = {"com.taobao.trip.jourprod", "com.alibaba.fliggypoi.domain.convert"})
@ImportResource({ "classpath*:sentinel-tracer.xml", "classpath*:ateye.xml", "classpath:beans/springbeans-static-resource.xml"})
@EnableAspectJAutoProxy
public class Application {

    public static void main(String[] args) {
        PandoraBootstrap.run(args);
        SpringApplication.run(Application.class, args);
        PandoraBootstrap.markStartupAndWait();
    }

    @Bean
    public ServletRegistrationBean servletRegistrationBean() {
        AteyeServlet ateyeServlet = new AteyeServlet();
        ServletRegistrationBean servletRegistrationBean = new ServletRegistrationBean(ateyeServlet, "/agent.ateye");
        servletRegistrationBean.setLoadOnStartup(1);
        servletRegistrationBean.addInitParameter("app", "f-ai-ask");
        return servletRegistrationBean;
    }
}

