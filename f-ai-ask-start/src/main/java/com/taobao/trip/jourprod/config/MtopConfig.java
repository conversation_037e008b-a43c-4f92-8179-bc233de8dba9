package com.taobao.trip.jourprod.config;

import com.taobao.mtop.api.agent.MtopAgent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/3/15
 */
@Configuration
public class MtopConfig {
    @Bean(initMethod = "init")
    public MtopAgent mtopHsfAgent() {
        MtopAgent mtopHsfAgent = new MtopAgent();
        mtopHsfAgent.setAppName("f-ai-ask");
        mtopHsfAgent.setEnableStream(true);
        mtopHsfAgent.setRuntimeMode("DECENTER");
        return mtopHsfAgent;
    }
}
