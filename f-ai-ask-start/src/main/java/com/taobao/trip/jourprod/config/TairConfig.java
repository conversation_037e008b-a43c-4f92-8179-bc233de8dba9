package com.taobao.trip.jourprod.config;

import com.taobao.tair.impl.mc.MultiClusterTairManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/3/15
 */
@Configuration
public class TairConfig {
    @Value("${ask.mdb.tair}")
    private String mdbTairUserName;

    @Value("${ask.ldb.tair}")
    private String ldbTairUserName;

    @Value("${tripw.mdb.tair}")
    private String tripwMdbTairUserName;

    /**
     * od版本的tair，用户查询历史数据
     */
    @Bean(initMethod = "init")
    public MultiClusterTairManager odMdbTairManager() {
        MultiClusterTairManager tairManager = new MultiClusterTairManager();
        tairManager.setUserName(tripwMdbTairUserName);
        tairManager.setTimeout(50);
        return tairManager;
    }

    @Bean(initMethod = "init")
    public MultiClusterTairManager mdbTairManager() {
        MultiClusterTairManager tairManager = new MultiClusterTairManager();
        tairManager.setUserName(mdbTairUserName);
        tairManager.setTimeout(50);
        return tairManager;
    }

    @Bean(initMethod = "init")
    public MultiClusterTairManager ldbTair() {
        MultiClusterTairManager tairManager = new MultiClusterTairManager();
        tairManager.setUserName(ldbTairUserName);
        tairManager.setTimeout(90);
        return tairManager;
    }
}
