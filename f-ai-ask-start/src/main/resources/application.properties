project.name=f-ai-ask

# httpæå¡å¨ç«¯å£
server.port=7001
# endpointéç½®
management.server.address=127.0.0.1
management.server.port=7002
management.endpoints.web.exposure.include=*

# hsféç½®ï¼è¯¦è§ http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-hsf
spring.hsf.group=HSF
spring.hsf.version=1.0.0.DAILY
spring.hsf.timeout=4000

# tddléç½®ï¼è¯¦è§ https://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-tddl
spring.tddl.app=TRIP_JOURNEY_APP
spring.tddl.sharding=false
# mybatis
mybatis.config-location=classpath:/mybatis/mybatis-config.xml


# tairéç½®ï¼è¯¦è§ https://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-tair
spring.tair.user-name=9ed882569b0c433a
spring.tair.dynamic-config=true


# sentinel web filteréç½®
spring.sentinel.filter.urlPatterns=*.htm


ask.mdb.tair=b2c6c81b8d46481f
ask.ldb.tair=2e50e0e2e3494175
tripw.mdb.tair=174c0486dbb84042


hsf.remind.version=1.0.0
fai.hsf.version=1.0.0
fai.client.timeout=600000
hotel.hsf.provider.version=1.0.0.hwsearch
multiTransport.hsf.version=1.0.0

visa.hsf.timeout=1000

hwsearch.hsf.timeout=3000
