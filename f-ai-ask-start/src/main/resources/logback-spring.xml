<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- https://github.com/spring-projects/spring-boot/blob/v2.5.12/spring-boot-project/spring-boot/src/main/resources/org/springframework/boot/logging/logback/defaults.xml -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <include resource="user-product-log-appender.xml" />
    <include resource="map-api-log-appender.xml" />

    <property name="APP_NAME" value="f-ai-ask" />
    <property name="LOG_PATH" value="${user.home}/${APP_NAME}/logs" />
    <property name="LOG_FILE" value="${LOG_PATH}/application.log" />
    <!-- 监控日志 -->
    <property name="LOG_FILE_MONITOR" value="${LOG_PATH}/ask_monitor.log"/>

    <appender name="APPLICATION"
        class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE}</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}|%p|%t|%X{EAGLEEYE_TRACE_ID}|%logger{5}|%msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- 监控日志 -->
    <appender name="ask_monitor_appender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE_MONITOR}</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}|%p|%logger{2}|%X{EAGLEEYE_TRACE_ID}|%X{userId}|%X{utdid}|%X{spm}|%X{client}|%m%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE_MONITOR}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>2</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    <appender name="ASYNC_ask_monitor_appender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="ask_monitor_appender"/>
    </appender>
    <logger name="ASK_MONITOR_LOG" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_ask_monitor_appender"/>
    </logger>


    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="APPLICATION" />
    </root>
</configuration>
