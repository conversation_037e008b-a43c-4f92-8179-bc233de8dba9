<?xml version="1.0" encoding="UTF-8"?>
<included>
    <property name="MAP_API_LOG_FILE" value="${user.home:-/home/<USER>/${APP_NAME}/logs/map-api/map-api.log" />

    <!-- 文件输出appender -->
    <appender name="MAP-API-FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${MAP_API_LOG_FILE}</file>
        <encoder>
            <!-- 不添加任何格式，直接输出原始日志内容 -->
            <pattern>%msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${MAP_API_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <maxFileSize>100MB</maxFileSize>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 异步appender配置 -->
    <appender name="ASYNC-MAP-API-FILE" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 引用上面定义的文件appender -->
        <appender-ref ref="MAP-API-FILE" />
        <!-- 队列的最大容量 -->
        <queueSize>512</queueSize>
        <!-- 当队列剩余容量低于此值时，日志将被丢弃 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 队列满时，是否阻塞而不是丢弃日志 -->
        <neverBlock>false</neverBlock>
        <!-- 是否包含调用者数据 -->
        <includeCallerData>false</includeCallerData>
    </appender>

    <!-- map-api-logger的配置，使用异步appender -->
    <logger name="map-api-logger" level="INFO" additivity="false">
        <appender-ref ref="ASYNC-MAP-API-FILE" />
    </logger>
</included> 