package com.taobao.test;

/**
 * <AUTHOR>
 * @date 2025/4/4
 */
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import com.fasterxml.jackson.databind.ObjectMapper;

public class UrlParamConverter {

    /**
     * 将JSON字符串转换为URL参数
     * @param jsonStr JSON格式的字符串
     * @return URL参数字符串
     */
    public static String convertJsonToUrlParams(String jsonStr) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> map = mapper.readValue(jsonStr, Map.class);
            return convertMapToUrlParams(map);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 将Map转换为URL参数
     * @param map 包含参数的Map
     * @return URL参数字符串
     */
    private static String convertMapToUrlParams(Map<String, Object> map) {
        StringBuilder paramBuilder = new StringBuilder();
        boolean isFirst = true;

        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (!isFirst) {
                paramBuilder.append("&");
            } else {
                isFirst = false;
            }

            try {
                String key = entry.getKey();
                String value = String.valueOf(entry.getValue());

                // URL编码参数值
                String encodedValue = URLEncoder.encode(value, StandardCharsets.UTF_8.toString());

                paramBuilder.append(key).append("=").append(encodedValue);
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }

        return paramBuilder.toString();
    }

    public static void main(String[] args) {
        String jsonStr = "{\n" +
                "  \"searchType\": \"route_recall\",\n" +
                "  \"lbsLatitude\": \"30.284973\",\n" +
                "  \"query\": \"杭州\",\n" +
                "  \"lbsCityCode\": \"330100\",\n" +
                "  \"lbsLongitude\": \"120.023163\",\n" +
                "  \"userId\": \"2211077077869\",\n" +
                "  \"deviceId\": \"AsWSGSmXow62oHmIkG7rkschfZclw24lbtx5rhuBNzto\",\n" +
                "  \"ttid\": \"201200@travel_iphone_9.10.17.105.1011585\",\n" +
                "  \"scene\": \"ai_search\"\n" +
                "}";

        String urlParams = convertJsonToUrlParams(jsonStr);
        System.out.println(urlParams);
    }
}