<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.taobao</groupId>
		<artifactId>parent</artifactId>
		<version>2.0.0</version>
	</parent>
	<groupId>com.taobao.trip.jourprod</groupId>
	<artifactId>f-ai-ask</artifactId>
	<version>1.0.0</version>
	<packaging>pom</packaging>
	<name>f-ai-ask</name>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<jacoco-plugin.version>0.8.11</jacoco-plugin.version>
		<java.version>11</java.version>
		<maven-antrun.version>1.8</maven-antrun.version>
		<maven-compile-plugin.version>3.11.0</maven-compile-plugin.version>
		<maven-surefire-plugin.version>3.2.2</maven-surefire-plugin.version>
		<maven.compiler.source>11</maven.compiler.source>
		<maven.compiler.target>11</maven.compiler.target>
		<mybatis-starter.version>2.1.0</mybatis-starter.version>
		<csi-common-bom.version>2.2.45</csi-common-bom.version>
		<pandora-boot-maven-plugin.version>********</pandora-boot-maven-plugin.version>
		<pandora-boot.version>2024-09-release-fix-hsf</pandora-boot.version>
		<spring-boot.version>2.7.18</spring-boot.version>
		<tripjourneyop.service.facade.version>1.9.8</tripjourneyop.service.facade.version>
		<ssif-spring-boot-starter.version>2.1.5</ssif-spring-boot-starter.version>
		<f-ai-ask.facade.version>1.0.8</f-ai-ask.facade.version>
		<tripguide-ai-client.version>1.0.1</tripguide-ai-client.version>
	</properties>

	<modules>
		<module>f-ai-ask-service</module>
		<module>f-ai-ask-start</module>
		<module>f-ai-ask-dal</module>
		<module>f-ai-ask-sal</module>
		<module>f-ai-ask-facade</module>
        <module>f-ai-ask-core</module>
        <module>f-ai-ask-chat</module>
    </modules>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.jayway.jsonpath</groupId>
				<artifactId>json-path</artifactId>
				<version>2.9.0</version> <!-- 建议使用最新版本 -->
			</dependency>

			<!-- JsonPath 需要一个 JSON 处理库，例如 json-smart (默认) -->
			<dependency>
				<groupId>net.minidev</groupId>
				<artifactId>json-smart</artifactId>
				<version>2.5.0</version>
			</dependency>
			<dependency>
				<groupId>com.taobao.pandora</groupId>
				<artifactId>pandora-boot-starter-bom</artifactId>
				<version>${pandora-boot.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>com.taobao.csi</groupId>
				<artifactId>csi-common-bom</artifactId>
				<version>${csi-common-bom.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>com.alibaba.boot</groupId>
				<artifactId>pandora-diamond-spring-boot-starter</artifactId>
				<version>2024-09-release-fix-hsf</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>2021.0.9</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.middleware</groupId>
				<artifactId>mtop-uncenter-sdk</artifactId>
				<version>1.3.4--2024-09-release-fix-hsf</version>
			</dependency>
			<dependency>
				<groupId>com.taobao.trip.jourprod</groupId>
				<artifactId>f-ai-ask-service</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.taobao.trip.jourprod</groupId>
				<artifactId>f-ai-ask-sal</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.taobao.trip.jourprod</groupId>
				<artifactId>f-ai-ask-dal</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.taobao.trip.jourprod</groupId>
				<artifactId>f-ai-ask-facade</artifactId>
				<version>${f-ai-ask.facade.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>*</artifactId>
						<groupId>*</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.taobao.trip.jourprod</groupId>
				<artifactId>f-ai-ask-core</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.taobao.trip.jourprod</groupId>
				<artifactId>f-ai-ask-chat</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>1.2.83</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-dependencies</artifactId>
				<version>${spring-boot.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-jcl</artifactId>
				<version>999-not-exist</version>
			</dependency>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>jcl-over-slf4j</artifactId>
				<version>1.7.26</version>
			</dependency>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-log4j12</artifactId>
				<version>999-not-exist-v3</version>
			</dependency>
			<dependency>
				<groupId>javax.servlet</groupId>
				<artifactId>servlet-api</artifactId>
				<version>99.0-does-not-exist</version>
			</dependency>
			<dependency>
				<groupId>servlet-api</groupId>
				<artifactId>servlet-api</artifactId>
				<version>999-not-exist</version>
			</dependency>
			<dependency>
				<groupId>javax.servlet</groupId>
				<artifactId>javax.servlet-api</artifactId>
				<version>999-not-exist</version>
			</dependency>
			<dependency>
				<groupId>com.taobao.diamond</groupId>
				<artifactId>diamond-client</artifactId>
				<version>999-not-exist</version>
			</dependency>
			<dependency>
				<groupId>org.mybatis.spring.boot</groupId>
				<artifactId>mybatis-spring-boot-starter</artifactId>
				<version>${mybatis-starter.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-lang</groupId>
				<artifactId>commons-lang</artifactId>
				<version>2.6</version>
			</dependency>
			<dependency>
				<groupId>com.taobao.trip.wireless</groupId>
				<artifactId>tripw-common</artifactId>
				<version>1.8.21</version>
				<exclusions>
					<exclusion>
						<artifactId>netty</artifactId>
						<groupId>org.jboss.netty</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<!-- 行程2.0 依赖-->
			<dependency>
				<groupId>com.taobao.trip</groupId>
				<artifactId>tripjourneyop-service-facade</artifactId>
				<version>${tripjourneyop.service.facade.version}</version>
				<exclusions>
					<exclusion>
						<groupId>com.taobao.trip.jourprod</groupId>
						<artifactId>tripwjourprod-service-facade</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.taobao.hsf</groupId>
						<artifactId>hsf.app.spring</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-core</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.taobao.trip.wireless</groupId>
				<artifactId>ssif-spring-boot-starter</artifactId>
				<version>${ssif-spring-boot-starter.version}</version>
			</dependency>
			<dependency>
				<groupId>com.taobao.trippedia.tripplan</groupId>
				<artifactId>planengine-domain</artifactId>
				<version>1.0.5</version>
				<exclusions>
					<exclusion>
						<groupId>org.projectlombok</groupId>
						<artifactId>lombok</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.httpcomponents</groupId>
						<artifactId>httpcore</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.projectlombok</groupId>
				<artifactId>lombok</artifactId>
				<version>1.18.36</version>
			</dependency>
			<!-- 日志工具 -->
			<dependency>
				<groupId>fliggy.content</groupId>
				<artifactId>utils</artifactId>
				<version>2.0.5</version>
				<exclusions>
					<exclusion>
						<groupId>org.springframework.boot</groupId>
						<artifactId>spring-boot</artifactId>
					</exclusion>
					<exclusion>
						<artifactId>mtop-hsf-agent</artifactId>
						<groupId>com.taobao.wireless</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.alibaba.trip.trippoi</groupId>
				<artifactId>fliggypoi-client</artifactId>
				<version>1.4.6</version>
				<exclusions>
					<exclusion>
						<artifactId>mtop-hsf-agent</artifactId>
						<groupId>com.taobao.wireless</groupId>
					</exclusion>
					<exclusion>
						<artifactId>utils</artifactId>
						<groupId>fliggy.content</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<!--新行政区划-->
			<dependency>
				<groupId>com.alibaba.trip.tripdivision</groupId>
				<artifactId>tripdivision-client</artifactId>
				<version>1.2.13</version>
				<exclusions>
					<exclusion>
						<artifactId>mtop-hsf-agent</artifactId>
						<groupId>com.taobao.wireless</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.alibaba.trippoi</groupId>
				<artifactId>trippoiadmin-facade</artifactId>
				<version>1.3.2</version>
				<exclusions>
					<exclusion>
						<artifactId>*</artifactId>
						<groupId>*</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.alibaba.trippoi</groupId>
				<artifactId>trippoiadmin-domain</artifactId>
				<version>1.0.9</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>dashscope-sdk-java</artifactId>
				<version>2.17.1</version>
			</dependency>
			<dependency>
				<groupId>com.squareup.okhttp3</groupId>
				<artifactId>okhttp</artifactId>
				<version>4.12.0</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.fliggypoi</groupId>
				<artifactId>poi-dal</artifactId>
				<version>1.5.9</version>
				<exclusions>
					<exclusion>
						<groupId>com.alibaba.fliggypoi</groupId>
						<artifactId>poi-domain</artifactId>
					</exclusion>
					<exclusion>
						<artifactId>utils</artifactId>
						<groupId>fliggy.content</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.alibaba.fliggypoi</groupId>
				<artifactId>poi-domain</artifactId>
				<version>1.5.4</version>
			</dependency>
			<dependency>
				<groupId>com.fliggy.forge</groupId>
				<artifactId>forge-client</artifactId>
				<version>4.4.35</version>
				<exclusions>
					<exclusion>
						<artifactId>atpr-client</artifactId>
						<groupId>com.taobao.at</groupId>
					</exclusion>
					<exclusion>
						<groupId>org.opensymphony.quartz</groupId>
						<artifactId>quartz</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.taobao.ateye</groupId>
				<artifactId>ateye-client</artifactId>
				<version>2.6.0</version>
			</dependency>
			<!-- 圈人平台-->
			<dependency>
				<groupId>com.fliggy</groupId>
				<artifactId>crowd-client</artifactId>
				<version>1.3.1</version>
			</dependency>
            <!--会员相关-->
			<dependency>
				<groupId>com.fliggy.ffalevel</groupId>
				<artifactId>ffalevel-client</artifactId>
				<version>2.2.5</version>
				<exclusions>
					<exclusion>
						<groupId>*</groupId>
						<artifactId>*</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.fliggy.ffalevel</groupId>
				<artifactId>ffalevel-model</artifactId>
				<version>2.3.5</version>
				<exclusions>
					<exclusion>
						<groupId>*</groupId>
						<artifactId>*</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
            <!--青鸟编排-->
			<dependency>
				<groupId>com.fliggy.graph</groupId>
				<artifactId>fliggy-graph-springboot-starter</artifactId>
				<version>1.0.4</version>
			</dependency>
			<dependency>
				<groupId>com.aliyun.oss</groupId>
				<artifactId>aliyun-sdk-oss</artifactId>
				<!-- aliyun-sdk-oss在2.1.0~3.10.2可以支持 -->
				<version>3.10.2</version>
				<exclusions>
					<exclusion>
						<groupId>com.aliyun</groupId>
						<artifactId>aliyun-java-sdk-kms</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.aliyun</groupId>
						<artifactId>aliyun-java-sdk-core</artifactId>
					</exclusion>
					<exclusion>
						<groupId>commons-codec</groupId>
						<artifactId>commons-codec</artifactId>
					</exclusion>
					<exclusion>
						<groupId>commons-logging</groupId>
						<artifactId>commons-logging</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<!--玩法互动-->
			<dependency>
				<groupId>com.alibaba.fliggy</groupId>
				<artifactId>fliggy-award-upgrade-client</artifactId>
				<version>1.2.1</version>
				<exclusions>
					<exclusion>
						<groupId>*</groupId>
						<artifactId>*</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.alibaba.fliggy</groupId>
				<artifactId>f-marketing-play-client</artifactId>
				<version>1.4.71</version>
				<exclusions>
					<exclusion>
						<groupId>*</groupId>
						<artifactId>*</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.fliggy.fliggyplaycore</groupId>
				<artifactId>fliggyplaycore-client</artifactId>
				<version>1.1.69</version>
				<exclusions>
					<exclusion>
						<groupId>*</groupId>
						<artifactId>*</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.volcengine</groupId>
				<artifactId>volcengine-java-sdk-ark-runtime</artifactId>
				<version>0.1.153</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.whale</groupId>
				<artifactId>whale-sdk</artifactId>
				<version>1.2.20-BETA</version>
			</dependency>
			<!-- FAI sdk -->
			<dependency>
				<groupId>com.fliggy.fai</groupId>
				<artifactId>fai-client</artifactId>
				<version>1.1.38</version>
			</dependency>
			<dependency>
				<groupId>com.alitrip.recommend</groupId>
				<artifactId>fliggy-recommend-client</artifactId>
				<version>1.0.11</version>
			</dependency>
			<dependency>
				<groupId>com.taobao.tripca</groupId>
				<artifactId>tripca-facade</artifactId>
				<version>1.2.5</version>
				<exclusions>
					<exclusion>
						<artifactId>mtop-hsf-agent</artifactId>
						<groupId>com.taobao.wireless</groupId>
					</exclusion>
					<exclusion>
						<artifactId>ip-client</artifactId>
						<groupId>com.taobao.inventory</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<!-- 火车票时刻表查询 -->
			<dependency>
				<groupId>com.taobao.trip.wireless</groupId>
				<artifactId>wtrs-client</artifactId>
				<version>*******-newCrossAndTrans-onePage</version>
				<exclusions>
					<exclusion>
						<groupId>org.quartz-scheduler</groupId>
						<artifactId>quartz</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.taobao.notify</groupId>
						<artifactId>notify-tr-client</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.fliggy.fce</groupId>
						<artifactId>fce-client</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.taobao.hsf</groupId>
						<artifactId>hsf.app.spring</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.taobao.trip.wireless</groupId>
						<artifactId>tripw-sensible-if-client</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.taobao.trip.wireless</groupId>
				<artifactId>hwsearch-client</artifactId>
				<version>2.6.0</version>
				<exclusions>
					<exclusion>
						<groupId>org.quartz-scheduler</groupId>
						<artifactId>quartz</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework.boot</groupId>
						<artifactId>spring-boot-starter-quartz</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.taobao.inventory</groupId>
						<artifactId>ip-client</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.alibaba.middleware</groupId>
						<artifactId>hsf-sdk</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.taobao.hsf</groupId>
						<artifactId>hsf.services</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.taobao.trip.wireless</groupId>
						<artifactId>tripw-sensible-if-client</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.json</groupId>
						<artifactId>json</artifactId>
					</exclusion>
					<exclusion>
						<artifactId>trippe-ump-client</artifactId>
						<groupId>com.taobao.trippe</groupId>
					</exclusion>
					<exclusion>
						<groupId>com.fliggy.hotel</groupId>
						<artifactId>hpricecenter-calculate-client</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.alibaba.fastvalidator</groupId>
						<artifactId>fastvalidator-spring-boot-starter</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.alibaba.business.qa</groupId>
						<artifactId>biz-simulator-sdk</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.taobao.wireless.mtop</groupId>
				<artifactId>mtop-pandora-export-agent</artifactId>
				<version>1.3.14</version>
				<scope>provided</scope>
			</dependency>
			<dependency>
				<groupId>org.junit.jupiter</groupId>
				<artifactId>junit-jupiter-engine</artifactId>
				<version>5.8.2</version> <!-- 或者最新的版本 -->
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.junit.jupiter</groupId>
				<artifactId>junit-jupiter-api</artifactId>
				<version>5.8.2</version> <!-- 或者最新的版本 -->
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-lang3</artifactId>
				<version>3.17.0</version>
			</dependency>
			<dependency>
				<groupId>com.taobao.trip.wireless</groupId>
				<artifactId>tripw-sensible-if-client</artifactId>
				<version>2.1.0</version>
			</dependency>
			<dependency>
				<groupId>com.taobao.csp</groupId>
				<artifactId>sentinel-resource-hook</artifactId>
				<version>3.9.26</version>
			</dependency>
			<dependency>
				<groupId>com.alitrip.ai</groupId>
				<artifactId>tripalgo-llm-client</artifactId>
				<version>1.0.14</version>
			</dependency>

			<dependency>
				<groupId>com.fliggy.magnet</groupId>
				<artifactId>magnet-client</artifactId>
				<version>1.1.50</version>
			</dependency>
			<dependency>
				<groupId>com.fliggy.magnet</groupId>
				<artifactId>magnet-domain</artifactId>
				<version>1.1.49</version>
			</dependency>
            <!-- 智慧交通 -->
			<dependency>
				<groupId>com.fliggy.flyway</groupId>
				<artifactId>flyway-model</artifactId>
				<version>1.0.41</version>
			</dependency>

            <dependency>
                <groupId>com.fliggy.flyway</groupId>
                <artifactId>flyway-client</artifactId>
                <version>1.0.41</version>
            </dependency>

			<!-- 算法工程给的召回模型 -->
			<dependency>
				<groupId>com.alitrip.tpp</groupId>
				<artifactId>ai-xsearch-model</artifactId>
				<version>1.0.2</version>
				<exclusions>
					<exclusion>
						<groupId>*</groupId>
						<artifactId>*</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.alibaba.trip.tripdivision</groupId>
				<artifactId>tripdivision-client</artifactId>
				<version>1.2.10</version>
				<exclusions>
					<exclusion>
						<groupId>*</groupId>
						<artifactId>*</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.fliggy.aiworks</groupId>
				<artifactId>aiworks-client</artifactId>
				<version>1.0.7-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.taobao.travel</groupId>
				<artifactId>travelvc-client</artifactId>
				<version>1.1.64</version>
			</dependency>
			<dependency>
				<groupId>com.taobao.travel</groupId>
				<artifactId>travelvc-common</artifactId>
				<version>1.3.35</version>
			</dependency>

			<dependency>
				<groupId>com.taobao.trip.hotelsearch</groupId>
				<artifactId>platform-model</artifactId>
				<version>1.4.14</version>
			</dependency>
            <!-- 收藏 -->
            <dependency>
                <groupId>com.taobao.tripwf</groupId>
                <artifactId>tripwf-client</artifactId>
                <version>1.0.27</version>
            </dependency>

			<dependency>
				<groupId>com.taobao.trip.hotelsearch</groupId>
				<artifactId>platform-model</artifactId>
				<version>1.4.14</version>
			</dependency>
			<!-- 酒店套餐 -->
			<dependency>
				<groupId>com.alibaba.fliggy</groupId>
				<artifactId>tuan-gateway-api</artifactId>
				<version>1.2.33</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.fliggy</groupId>
				<artifactId>tuan-commons</artifactId>
				<version>1.0.22</version>
			</dependency>

			<dependency>
				<groupId>com.taobao.tripmdd</groupId>
				<artifactId>tripmdd-facade</artifactId>
				<version>1.2.9</version>
			</dependency>

			<!-- 会员基础信息 -->
			<dependency>
				<groupId>com.fliggy.ffa.touch</groupId>
				<artifactId>ffa-customize-touch-client</artifactId>
				<version>1.5.20</version>
				<exclusions>
					<exclusion>
						<groupId>com.aliyun</groupId>
						<artifactId>aliyun-java-sdk-core</artifactId>
					</exclusion>
					<exclusion>
						<artifactId>aliyun-sdk-opensearch</artifactId>
						<groupId>com.aliyun.opensearch</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.fliggy.ffa</groupId>
				<artifactId>ffa-client</artifactId>
				<version>1.47.29</version>
				<exclusions>
					<exclusion>
						<groupId>com.alitrip.tripservice.trace</groupId>
						<artifactId>trace-annotation</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.alitrip.tripservice.trace</groupId>
						<artifactId>trace</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.taobao.trip.jourprod</groupId>
				<artifactId>tripwjourprod-global-facade</artifactId>
				<version>1.0.1</version>
			</dependency>
			<dependency>
                <groupId>com.alibaba.tripc.logicengine</groupId>
                <artifactId>logicengine-facade</artifactId>
                <version>1.1.1</version>
            </dependency>

			<!--主搜-->
			<dependency>
				<groupId>com.alitrip.search</groupId>
				<artifactId>tripguide-ai-client</artifactId>
				<version>${tripguide-ai-client.version}</version>
			</dependency>

			<!-- 静态配置 -->
			<dependency>
				<groupId>com.fliggy.fceadmin</groupId>
				<artifactId>fceadmin-client-static-resource</artifactId>
				<version>1.0.6</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-antrun-plugin</artifactId>
					<version>${maven-antrun.version}</version>
				</plugin>
				<plugin>
					<groupId>com.taobao.pandora</groupId>
					<artifactId>pandora-boot-maven-plugin</artifactId>
					<version>${pandora-boot-maven-plugin.version}</version>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-surefire-plugin</artifactId>
					<version>${maven-surefire-plugin.version}</version>
					<configuration>
						<argLine>${argLine} -Dfile.encoding=UTF-8</argLine>
					</configuration>
				</plugin>
				<plugin>
					<groupId>org.jacoco</groupId>
					<artifactId>jacoco-maven-plugin</artifactId>
					<version>${jacoco-plugin.version}</version>
					<configuration>
						<propertyName>argLine</propertyName>
					</configuration>
					<executions>
						<execution>
							<id>default-prepare-agent</id>
							<goals>
								<goal>prepare-agent</goal>
							</goals>
						</execution>
						<execution>
							<id>default-report</id>
							<phase>test</phase>
							<goals>
								<goal>report</goal>
								<goal>report-aggregate</goal>
							</goals>
						</execution>
					</executions>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
					<version>${maven-compile-plugin.version}</version>
					<configuration>
						<source>${java.version}</source>
						<target>${java.version}</target>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
		<plugins>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>
