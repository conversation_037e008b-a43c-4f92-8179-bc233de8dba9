# 源码自动生成模板 pandora-boot-initializr

### 概述

* 模板: pandora-boot-initializr
* 模板使用时间: 2025-03-15 10:45:11

### Docker
* Image: reg.docker.alibaba-inc.com/bootstrap2/pandora-boot
* Tag: 0.2
* SHA256: eb7881697d25310df646b4a2a4cf9bc53392274e2812eca4a8dffa289654c95c

### 用户输入参数
* bootVersion: "2.7.18" 
* repoUrl: "**************************:tripwjourprod/f-ai-ask.git" 
* ownerFullName: "牧熵(77605)" 
* javaVersion: "11" 
* appName: "f-ai-ask" 
* groupId: "com.taobao.trip.jourprod" 
* dockerfile: "dockerfile" 
* sarVersion: "2024-09-release-fix-hsf" 
* operator: "77605" 
* objectType: "Application" 
* ownerEmail: "<EMAIL>" 
* appId: "274354" 
* objectSubtype: "NORMAL" 
* artifactId: "f-ai-ask" 
* style: "hsf,tddl,tair,sentinel" 
* applicationName: "f-ai-ask" 

### 上下文参数
* appName: f-ai-ask
* operator: 77605
* gitUrl: **************************:tripwjourprod/f-ai-ask.git
* branch: master


### 命令行
	sudo docker run --rm -v /home/<USER>/76_20250315104500106_555939219_code/f-ai-ask/1742006696354_f-ai-ask:/workspace -e bootVersion="2.7.18" -e repoUrl="**************************:tripwjourprod/f-ai-ask.git" -e ownerFullName="牧熵(77605)" -e javaVersion="11" -e appName="f-ai-ask" -e groupId="com.taobao.trip.jourprod" -e dockerfile="dockerfile" -e sarVersion="2024-09-release-fix-hsf" -e operator="77605" -e objectType="Application" -e ownerEmail="<EMAIL>" -e appId="274354" -e objectSubtype="NORMAL" -e artifactId="f-ai-ask" -e style="hsf,tddl,tair,sentinel" -e applicationName="f-ai-ask"  reg.docker.alibaba-inc.com/bootstrap2/pandora-boot:0.2

